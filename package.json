{"name": "sun-web-dict", "private": true, "version": "1.0.0", "author": {"name": "jiayi.hu", "email": "<EMAIL>"}, "homepage": "https://git.taihealth.cn/sun-his/web/sun-web-dict", "repository": {"type": "git", "url": "********************:sun-his/web/sun-web-dict.git"}, "scripts": {"build": "rsbuild build", "build:war": "BUILD_FLAG=WAR rsbuild build", "dev": "rsbuild dev --open", "format": "prettier --write .", "lint:eslint": "eslint --fix", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "preinstall": "node script/disallow-npm.js", "preview": "rsbuild preview", "prepare": "husky"}, "dependencies": {"@antv/x6": "^2.18.1", "@ckpack/vue-color": "^1.6.0", "@element-sun/icons-vue": "^1.0.6", "@popperjs/core": "^2.11.8", "@sun-toolkit/enums": "1.0.0-beta.36", "@sun-toolkit/micro-app": "0.0.2-beta.8", "@sun-toolkit/request": "1.0.0-beta.22", "@sun-toolkit/shared": "0.0.1-beta.8", "@tiptap/core": "^2.14.0", "@tiptap/pm": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@tiptap/vue-3": "^2.14.0", "@types/sortablejs": "^1.15.8", "@types/splitpanes": "^2.2.6", "element-sun": "0.0.0-dev.8", "es-toolkit": "^1.39.3", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^2.7.3", "i18next-vue": "^5.3.0", "js-base64": "^3.7.7", "js-pinyin": "^0.2.7", "lint-staged": "^15.5.2", "lodash": "^4.17.21", "pinia": "^2.3.1", "sortablejs": "^1.15.6", "splitpanes": "^4.0.4", "stylelint": "^16.20.0", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-scss": "^13.1.0", "sun-biz": "0.0.4-beta.21", "tailwindcss": "^3.4.17", "v-calendar": "^3.1.2", "vue": "^3.5.16", "vue-draggable-plus": "^0.5.6", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@rsbuild/core": "^1.3.22", "@sun-create/config": "0.0.7", "@types/lodash": "^4.17.20", "@types/node": "^22.15.31", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "eslint": "^9.29.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-vue": "^9.33.0", "glob": "^11.0.3", "globals": "^15.15.0", "husky": "^9.1.7", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "stylelint-config-tailwindcss": "^1.0.0", "typescript": "^5.8.3", "typescript-eslint": "^8.34.0", "vue-eslint-parser": "^9.4.3"}, "lint-staged": {"*.{vue,js,jsx,ts,tsx}": ["eslint --fix"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "*.{scss,less}": ["stylelint --fix"], "*.vue": ["stylelint --fix", "eslint --fix"]}}