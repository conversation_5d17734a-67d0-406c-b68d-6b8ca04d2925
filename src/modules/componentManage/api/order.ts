import { dictRequest } from '@sun-toolkit/request';
import { ordertableConfig } from '@/modules/componentManage/typings/orderFormType';
interface enabledFlagparams {
  enabledFlag?: number;
}

interface updateOrderFormTypeByIdparams {
  orderFormTypeId?: string;
  orderFormTypeName: string;
  orderFormType2ndName?: string;
  orderFormTypeExtName: string;
  bizNoGenerateRuleId?: string;
  enabledFlag: number;
}
interface updateOrderFormTypeByIdReq {
  orderFormTypeId: string;
}

interface delTypeIdParams {
  orderFormTypeId: string;
}
/**
 * [1-10489-1]  根据条件查询临床服务列表及计费设置
 * @param data
 * @returns
 */
export const queryOrderFormTypeByExample = (params: enabledFlagparams) => {
  return dictRequest<ordertableConfig[]>(
    '/OrderFormType/queryOrderFormTypeByExample',
    params,
  );
};
/**
 * [1-10488-1]  根据条件查询临床服务列表及计费设置
 * @param data
 * @returns
 */
export const deleteOrderFormTypeById = (params: delTypeIdParams) => {
  return dictRequest<boolean>('/OrderFormType/deleteOrderFormTypeById', params);
};
/**
 * [1-10487-1]  根据条件查询临床服务列表及计费设置
 * @param data
 * @returns
 */
export const updateOrderFormTypeById = (
  params: updateOrderFormTypeByIdparams,
) => {
  return dictRequest<boolean>('/OrderFormType/updateOrderFormTypeById', params);
};

/**
 * [1-10486-1]  根据条件查询临床服务列表及计费设置
 * @param data
 * @returns
 */
export const addOrderFormType = (params: updateOrderFormTypeByIdparams) => {
  return dictRequest<updateOrderFormTypeByIdReq>(
    '/OrderFormType/addOrderFormType',
    params,
  );
};
