<script setup lang="ts">
  import { ref, watch } from 'vue';
  import type { FormInstance } from 'element-sun';
  import { useSchemeFromBar } from '../config/useSchemeFromBar';
  import { ProForm } from 'sun-biz';
  import { CONTROL_TYPE_CODE } from '@/utils/constant';
  const FULL_WIDTH = [
    CONTROL_TYPE_CODE.TABLE,
    CONTROL_TYPE_CODE.GROUP,
    CONTROL_TYPE_CODE.COLLAPSE,
    CONTROL_TYPE_CODE.SET,
  ];
  type Props = {
    schemeInfo?: FormDesign.SchemeInfo;
    formId?: string;
  };

  const props = defineProps<Props>();
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: string };
  }>();
  const infoModel = ref<FormDesign.FormDesignInfo>({
    formDesignName: '',
    formDesign2ndName: '',
    formDesignExtName: '',
    layoutTypeCode: '',
    layoutTypeDesc: '',
    enabledFlag: 1,
    defaultFlag: 1,
    menuIds: [],
  });
  const formData = useSchemeFromBar(infoModel);

  const emit = defineEmits<{
    (e: 'update-data', data: FormDesign.FormControlInfo[]): void;
  }>();

  watch(
    () => props.schemeInfo?.detail,
    () => {
      if (props.schemeInfo?.detail) {
        infoModel.value = {
          ...props.schemeInfo?.detail,
          menuIds: (props.schemeInfo?.detail?.formDesignXMenuList || []).map(
            (item) => item.menuId,
          ),
        };
      }
    },
  );

  // 递归更新 displayWidth
  function updateDisplayWidth(
    items: FormDesign.FormControlInfo[],
    layoutType: string,
  ): FormDesign.FormControlInfo[] {
    return items.map((item) => ({
      ...item,
      displayWidth: FULL_WIDTH.includes(item.controlTypeCode)
        ? Number(layoutType)
        : Math.min(item.displayWidth || 1, Number(layoutType)),
      subFormControlList: item.subFormControlList
        ? updateDisplayWidth(item.subFormControlList, layoutType)
        : [],
    }));
  }

  watch(
    () => infoModel.value.layoutTypeCode,
    (newLayoutType) => {
      if (!newLayoutType || !props.schemeInfo) return;

      const updatedScheme = { ...props.schemeInfo };
      updatedScheme.detail.layoutTypeCode = newLayoutType;

      // 更新所有层级的表单项的 displayWidth
      let tableData = updateDisplayWidth(
        updatedScheme.tableData,
        newLayoutType,
      );
      emit('update-data', tableData);
    },
  );

  async function saveDesign() {
    return new Promise((resolve) => {
      formRef.value?.ref.validate().then(async () => {
        resolve({
          ...infoModel.value,
          formId: props.formId,
        });
      });
    });
  }
  defineExpose({
    saveDesign,
  });
</script>

<template>
  <ProForm ref="formRef" v-model="infoModel" :column="4" :data="formData">
  </ProForm>
</template>
