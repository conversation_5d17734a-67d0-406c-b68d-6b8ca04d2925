<script setup lang="ts" name="employeeDetail">
  import { ref, watch } from 'vue';
  import { useDesignTableColumns } from '../config/useDesignTableColumns.tsx';
  import { queryFormDesignDetailByExample } from '@/modules/componentManage/api/formDesign';
  import { debounce } from '@sun-toolkit/shared';
  import { Search } from '@element-sun/icons-vue';
  import type { FormInstance } from 'element-sun';
  import SchemeFromBar from './SchemeFromBar.vue';
  import VisualizeFromDesign from './VisualizeFromDesign/index.vue';
  import { filterData, setTableDefaultKey } from '../utils';
  import { ProTable, type AnyObject } from 'sun-biz';
  type Props = {
    schemeInfo?: FormDesign.SchemeInfo;
    isVisualize: boolean;
    formId?: string;
    isCloudEnv: boolean | undefined;
    changeSchemeList: (
      detailOrTable:
        | FormDesign.SchemeInfoDetail
        | FormDesign.FormDesignDetailInfo[],
      type?: string,
    ) => void;
    changeSortData: (data: FormDesign.FormControlInfo[]) => void;
  };
  const props = defineProps<Props>();
  const detail = ref<FormDesign.SchemeInfoDetail>();
  const formRefBar = ref<{ saveDesign: () => void }>();

  const loading = ref(false);
  const keyWord = ref<string>('');
  const tableRef = ref<{ formRef: FormInstance }>();
  const expandedRowKeys = ref<string[]>([]); // 展开行

  let inputChange = debounce(handleEnter, 500);

  const designColumns = useDesignTableColumns(detail);

  function handleEnter() {
    queryFormDesignDetail();
  }

  function handleExpandChange(row: AnyObject) {
    let findObj = expandedRowKeys.value.find(
      (item) => row.formControlId === item,
    );
    if (!findObj) {
      expandedRowKeys.value.push(row.formControlId);
    } else {
      expandedRowKeys.value = expandedRowKeys.value.filter(
        (item) => item !== row.formControlId,
      );
    }
  }

  function changeSourceData(
    formDesignDetailList: FormDesign.FormDesignDetailInfo[],
  ) {
    let data = setTableDefaultKey(
      filterData(formDesignDetailList, keyWord.value),
      props.schemeInfo?.detail.layoutTypeCode as unknown as string,
    );

    props.changeSchemeList(
      data.map((item) => {
        return {
          ...item,
          focusAutoDropDownFlag: item.focusAutoDropDownFlag ?? 0,
          cursorStayFlag: item.cursorStayFlag ?? 0,
          displayWidth: item.displayWidthRatio
            ? Math.round(
                item.displayWidthRatio *
                  Number(props.schemeInfo?.detail.layoutTypeCode),
              )
            : 1,
          editable: true,
        };
      }),
      'table',
    );
  }

  async function queryFormDesignDetail() {
    loading.value = true;
    let [, result] = await queryFormDesignDetailByExample({
      formDesignId: props.schemeInfo?.detail.formDesignId || '',
    });
    loading.value = false;
    if (result?.success) {
      changeSourceData(result.data.formDesignDetailList);
    }
  }

  watch(
    () =>
      props.schemeInfo?.detail.formDesignId ||
      props.schemeInfo?.detail.layoutTypeCode,
    () => {
      if (
        props.schemeInfo?.detail.formDesignId &&
        !props.schemeInfo?.tableData.length
      ) {
        queryFormDesignDetail();
      }
      detail.value = props.schemeInfo?.detail;
    },
  );

  function convertDesignParamList(
    tableData: FormDesign.FormControlInfo[] = [],
    result: FormDesign.DesignDetailInfo[],
  ): FormDesign.DesignDetailInfo[] {
    tableData.forEach((item, index) => {
      result.push({
        formDesignDetailId: item.formDesignDetailId,
        formDesignId: detail.value?.formDesignId,
        labelName: item.labelName || '',
        label2ndName: item.label2ndName,
        labelExtName: item.labelExtName,
        hintMsg: item.hintMsg,
        displayFlag: item.displayFlag,
        focusAutoDropDownFlag: item.focusAutoDropDownFlag ?? 0,
        mustInputFlag: item.mustInputFlag ?? 0,
        cursorStayFlag: item.cursorStayFlag ?? 0,
        defaultValue: item.defaultValue,
        sort: index,
        formControlId: item?.formControlId,
        allowModifyFlag: item.allowModifyFlag,
        displayWidthRatio: +(
          Number(item.displayWidth) / Number(detail.value?.layoutTypeCode)
        ).toFixed(2),
      });

      if (item?.subFormControlList?.length) {
        convertDesignParamList(item?.subFormControlList, result);
      }
    });
    return result;
  }

  function saveContent() {
    return new Promise((resolve) => {
      Promise.all([
        tableRef?.value?.formRef?.validateField(),
        formRefBar?.value?.saveDesign(),
      ]).then(async (result) => {
        resolve({
          ...(result[1] || {}),
          formDesignDetailList: convertDesignParamList(
            props.schemeInfo?.tableData,
            [],
          ),
        });
      });
    });
  }

  const handleSortEnd = async (
    data: FormDesign.FormControlInfo[],
    path?: string,
  ) => {
    console.log(path, 'path');
    let newData = data.map((item, index) => {
      return {
        ...item,
        subFormControlList: (item.subFormControlList || []).map((cur, i) => ({
          ...cur,
          sort: i + 1,
        })),
        sort: index + 1,
      };
    });
    props.changeSortData(newData);
  };

  function cellClick(row: AnyObject, col: { property: string }) {
    let { property } = col;
    if (property === 'indexNo' && row?.subFormControlList?.length) {
      handleExpandChange(row);
    }
  }

  function cellStyle({
    row,
    column,
    columnIndex,
  }: {
    row: AnyObject;
    column: { property: string };
    rowIndex: number;
    columnIndex: number;
  }) {
    if (
      columnIndex === 0 &&
      column.property === 'indexNo' &&
      row?.subFormControlList?.length
    ) {
      return { cursor: 'pointer' };
    }

    return {};
  }

  defineExpose({
    saveDesign: () => {
      return formRefBar?.value?.saveDesign();
    },
    saveContent,
    tableRef,
  });
</script>

<template>
  <SchemeFromBar
    ref="formRefBar"
    @update-data="props.changeSortData"
    :scheme-info="props?.schemeInfo"
    :form-id="props.formId"
  />
  <div class="mb-5 flex justify-between" v-show="!props.isVisualize">
    <span>
      <el-input
        v-model="keyWord"
        @input="inputChange"
        @keydown.enter="handleEnter"
        class="mr-5 w-72"
        :placeholder="$t('global:placeholder.keyword')"
        :suffix-icon="Search"
      />
      <el-button class="mr-2" @click="handleEnter">
        {{ $t('global:search') }}
      </el-button>
    </span>
  </div>
  <div class="flex h-full flex-1 flex-col overflow-hidden">
    <div
      v-if="props.isVisualize"
      v-loading="loading"
      class="flex flex-1 overflow-hidden"
    >
      <VisualizeFromDesign
        v-if="detail?.layoutTypeCode"
        :key="detail?.identification"
        @update-data="handleSortEnd"
        :data="props.schemeInfo?.tableData"
        :layout-type-code="detail?.layoutTypeCode as string"
      />
    </div>
    <pro-table
      v-if="!props.isVisualize"
      :highlight-current-row="true"
      row-class-name="cursor-pointer"
      :draggable="true"
      @drag-end="handleSortEnd"
      :expand-row-keys="expandedRowKeys"
      :tree-props="{
        children: 'subFormControlList',
      }"
      @expand-change="handleExpandChange"
      @cell-click="cellClick"
      :cell-style="cellStyle"
      ref="tableRef"
      :editable="true"
      :columns="designColumns"
      row-key="formControlId"
      :data="props.schemeInfo?.tableData"
      :loading="loading"
    />
  </div>
</template>
