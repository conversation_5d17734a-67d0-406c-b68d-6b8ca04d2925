<script setup lang="ts">
  import { ref, watch, computed } from 'vue';
  import { ElForm, ElFormItem, ElInput, ElCheckbox } from 'element-sun';
  import type { SelectedItem } from './type';
  import DynamicComponent from '../../components/DynamicComponent.vue';
  import { getComponentType, getPlaceholder, getExtraProps } from '../../utils';
  import { FLAG } from '@/utils/constant';

  const props = defineProps<{
    selectedFormCtlNo: string | null;
    form: FormDesign.FormControlInfo[] | undefined;
    layoutTypeCode: string;
  }>();

  const emit = defineEmits<{
    (
      e: 'update',
      selectedItem: FormDesign.FormControlInfo,
      isSort?: boolean,
    ): void;
  }>();

  const localField = ref<FormDesign.FormControlInfo | null>(null);

  // 使用 computed 优化属性提取
  const selectedItemProps = computed(() => {
    if (!props.selectedFormCtlNo || !props.form) return null;

    // 在 form 数据中查找选中的项
    for (const group of props.form) {
      if (group.formCtlNo === props.selectedFormCtlNo) {
        return group;
      }
      if (group.subFormControlList) {
        const found = group.subFormControlList.find(
          (item) => item.formCtlNo === props.selectedFormCtlNo,
        );
        if (found) return found;
      }
    }
    return null;
  });

  watch(
    () => selectedItemProps.value,
    (val) => {
      localField.value = val ? { ...val } : null;
    },
    { immediate: true },
  );

  function updateField(isSort?: boolean) {
    if (localField.value) {
      const updatedItem = { ...localField.value };
      emit('update', updatedItem, isSort);
    }
  }
</script>

<template>
  <div class="flex h-full flex-col overflow-hidden">
    <div class="border-b border-gray-200 pb-2 pl-4 pr-2 pt-2 font-bold">
      <span>{{
        $t('formDesign.right.panel.basicProperties', '基本属性')
      }}</span>
    </div>
    <div class="pr-4 pt-4">
      <el-form v-if="localField" label-width="120px" size="small">
        <div class="mb-4">
          <el-form-item
            :label="$t('formDesign.right.panel.controlName', '控件名称')"
          >
            <p>{{ localField.formCtlName }}</p>
          </el-form-item>
          <el-form-item
            :label="$t('formDesign.right.panel.controlCode', '控件编码')"
          >
            <p>{{ localField.formCtlNo }}</p>
          </el-form-item>
          <el-form-item
            :label="
              $t('formDesign.right.panel.bindingFieldCode', '绑定字段编码')
            "
          >
            <p>{{ localField.bindingFieldNo }}</p>
          </el-form-item>
          <el-form-item
            :label="$t('formDesign.right.panel.controlType', '控件类型')"
          >
            <p>{{ localField.controlTypeName }}</p>
          </el-form-item>
          <el-form-item
            :label="$t('formDesign.right.panel.maxLength', '最大长度')"
          >
            <p>{{ localField.maxLength }}</p>
          </el-form-item>
          <el-form-item
            :label="
              $t('formDesign.right.panel.searchBizIdType', '检索业务标识类型')
            "
            v-if="localField.dataSearchBizIdTypeDesc"
          >
            <p>{{ localField.dataSearchBizIdTypeDesc }}</p>
          </el-form-item>
          <el-form-item
            :label="$t('formDesign.right.panel.codeSystem', '编码体系')"
            v-if="localField.codeSystemName"
          >
            <p>{{ localField.codeSystemName }}</p>
          </el-form-item>
        </div>
      </el-form>
      <div v-else class="py-8 text-center text-gray-400">
        {{ $t('formDesign.right.panel.selectFormItem', '请选择表单项') }}
      </div>
    </div>
    <div
      class="border-b border-t border-gray-200 pb-2 pl-4 pr-2 pt-2 font-bold"
    >
      <span>{{
        $t('formDesign.right.panel.designProperties', '设计属性')
      }}</span>
    </div>
    <div class="flex-1 flex-col overflow-hidden">
      <el-scrollbar height="100%" class="pb-3 pr-4 pt-3">
        <el-form v-if="localField" label-width="120px" size="small">
          <div>
            <el-form-item
              :label="$t('formDesign.right.panel.labelName', '标签名称')"
            >
              <el-input
                v-model="localField.labelName"
                @input="
                  (val) => {
                    (localField as SelectedItem)['labelName'] = val;
                    updateField();
                  }
                "
              />
            </el-form-item>
            <el-form-item
              :label="$t('formDesign.right.panel.label2ndName', '标签辅助名称')"
            >
              <el-input
                v-model="localField.label2ndName"
                @input="
                  (val) => {
                    (localField as SelectedItem)['label2ndName'] = val;
                    updateField();
                  }
                "
              />
            </el-form-item>
            <el-form-item
              :label="$t('formDesign.right.panel.labelExtName', '标签扩展名称')"
            >
              <el-input
                v-model="localField.labelExtName"
                @input="
                  (val) => {
                    (localField as SelectedItem)['labelExtName'] = val;
                    updateField();
                  }
                "
              />
            </el-form-item>
            <el-form-item
              :label="$t('formDesign.right.panel.mustInput', '是否必填')"
            >
              <el-checkbox
                v-model="localField.mustInputFlag"
                :true-label="FLAG.YES"
                :false-label="FLAG.NO"
                @change="
                  (val) => {
                    (localField as SelectedItem)['mustInputFlag'] =
                      val as number;
                    updateField();
                  }
                "
              />
            </el-form-item>
            <el-form-item
              :label="$t('formDesign.right.panel.display', '是否显示')"
            >
              <el-checkbox
                v-model="localField.displayFlag"
                :true-label="FLAG.YES"
                :false-label="FLAG.NO"
                @change="
                  (val) => {
                    (localField as SelectedItem)['displayFlag'] = val as number;
                    updateField();
                  }
                "
              />
            </el-form-item>
            <el-form-item
              :label="$t('formDesign.right.panel.cursorStay', '光标是否停留')"
            >
              <el-checkbox
                v-model="localField.cursorStayFlag"
                :true-label="FLAG.YES"
                :false-label="FLAG.NO"
                @change="
                  (val) => {
                    (localField as SelectedItem)['cursorStayFlag'] =
                      val as number;
                    updateField();
                  }
                "
              />
            </el-form-item>
            <el-form-item
              :label="
                $t('formDesign.right.panel.autoDropdown', '聚焦后自动下拉')
              "
            >
              <el-checkbox
                v-model="localField.focusAutoDropDownFlag"
                :true-label="FLAG.YES"
                :false-label="FLAG.NO"
                @change="
                  (val) => {
                    (localField as SelectedItem)['focusAutoDropDownFlag'] =
                      val as number;
                    updateField();
                  }
                "
              />
            </el-form-item>
            <el-form-item
              :label="$t('formDesign.right.panel.widthRatio', '宽高占比')"
            >
              <el-input
                v-model.number="localField.displayWidth"
                :max="layoutTypeCode"
                :min="1"
                type="number"
                @input="
                  (val) => {
                    (localField as SelectedItem)['displayWidth'] =
                      val as unknown as number;
                    updateField();
                  }
                "
              />
            </el-form-item>
            <el-form-item
              :label="$t('formDesign.right.panel.sortNumber', '排序序号')"
            >
              <el-input
                v-model.number="localField.sort"
                @input="
                  (val) => {
                    (localField as SelectedItem)['sort'] =
                      val as unknown as number;
                    updateField(true);
                  }
                "
                type="number"
                :min="1"
              />
            </el-form-item>
            <el-form-item
              :label="$t('formDesign.right.panel.defaultValue', '默认值')"
            >
              <DynamicComponent
                v-bind="{
                  component: getComponentType(localField),
                  label: localField.labelName,
                  onChange: (value: string, name: string) => {
                    (localField as SelectedItem).defaultValueName = name;
                    (localField as SelectedItem).defaultValue = value;
                    updateField();
                  },
                  placeholder: getPlaceholder(localField, $t),
                  ...getExtraProps(localField),
                }"
                v-model="localField.defaultValue"
              />
            </el-form-item>
            <el-form-item
              :label="$t('formDesign.right.panel.hintMessage', '提示信息')"
            >
              <el-input
                v-model="localField.hintMsg"
                @input="
                  (val) => {
                    (localField as SelectedItem)['hintMsg'] = val;
                    updateField();
                  }
                "
                :placeholder="
                  $t(
                    'formDesign.right.panel.enterHintMessage',
                    '请输入提示信息',
                  )
                "
              />
            </el-form-item>
          </div>
        </el-form>
        <div v-else class="py-8 text-center text-gray-400">
          {{ $t('formDesign.right.panel.selectFormItem', '请选择表单项') }}
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>
