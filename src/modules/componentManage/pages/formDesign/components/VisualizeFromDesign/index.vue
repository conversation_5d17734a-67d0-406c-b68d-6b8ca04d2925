<script setup lang="ts">
  import { ref, watch, nextTick, computed } from 'vue';
  import LeftPanel from './LeftPanel.vue';
  import CenterPanel from './CenterPanel.vue';
  import RightPanel from './RightPanel.vue';
  import { ElScrollbar } from 'element-sun';
  import { debounce } from '@sun-toolkit/shared';
  import { sortFormData, updateFieldSort, updateFieldValue } from '../../utils';

  const props = defineProps<{
    data: FormDesign.FormControlInfo[] | undefined;
    layoutTypeCode: string;
  }>();

  const emit = defineEmits<{
    (e: 'update-data', data: FormDesign.FormControlInfo[]): void;
  }>();

  // 使用 shallowRef 优化性能
  const selectedFormCtlNo = ref<string | null>(null);
  const leftPanelRef = ref();
  const centerPanelRef = ref();

  // 使用 Map 缓存查找结果
  const fieldCache = new Map<string, FormDesign.FormControlInfo>();

  // 使用防抖优化更新
  const debouncedUpdateData = debounce((data: FormDesign.FormControlInfo[]) => {
    emit('update-data', data);
  }, 100);

  // 缓存排序后的表单数据
  const sortedForm = computed(() => {
    if (!props.data) return [];
    return props.data
      .slice()
      .sort((a, b) => (a.sort as number) - (b.sort as number));
  });

  // 统一的选中处理函数
  const handleSelect = (
    formCtlNo: string,
    groupFormCtlNo?: string,
    isScroll?: boolean,
  ) => {
    selectedFormCtlNo.value = formCtlNo;

    // 使用 nextTick 确保 DOM 更新后再设置树形控件的选中状态
    nextTick(() => {
      if (leftPanelRef.value) {
        leftPanelRef.value.setSelectedNode(formCtlNo);
      }
      // 如果有分组信息，滚动到对应分组
      if (groupFormCtlNo && centerPanelRef.value && isScroll) {
        centerPanelRef.value.scrollToGroup(groupFormCtlNo);
      }
    });
  };

  const handleFormUpdate = (form: FormDesign.FormControlInfo[]) => {
    // 创建新的数组引用，避免直接修改 props
    const updatedForm = form.map((group) => ({
      ...group,
      subFormControlList: group.subFormControlList?.map((item, index) => ({
        ...item,
        sort: index + 1,
      })),
    }));

    debouncedUpdateData(updatedForm);
  };

  const handleFieldUpdate = (
    field: FormDesign.FormControlInfo,
    isSort?: boolean,
  ) => {
    if (!selectedFormCtlNo.value) return;

    let newData: FormDesign.FormControlInfo[] | undefined;

    // 如果是修改排序序号
    if (isSort) {
      newData = updateFieldSort(props.data || [], field, true);
    } else {
      // 其他字段的更新
      newData = updateFieldValue(
        props.data || [],
        field,
        selectedFormCtlNo.value,
      );
    }

    if (newData) {
      // 清除缓存
      fieldCache.clear();
      // 对数据进行排序
      const sortedData = sortFormData(newData);
      debouncedUpdateData(sortedData);
    }
  };

  function selectFirstItem() {
    // 优先选中表单区
    for (const group of sortedForm.value) {
      const sortedChildren = (group.subFormControlList || [])
        .slice()
        .sort((a, b) => (a.sort as number) - (b.sort as number));

      if (sortedChildren.length > 0) {
        selectedFormCtlNo.value = sortedChildren[0].formCtlNo as string;
        return;
      }
    }
    selectedFormCtlNo.value = null;
  }

  watch(
    () => props.data,
    () => {
      if (!selectedFormCtlNo.value) {
        selectFirstItem();
      }
    },
    { immediate: true },
  );
</script>

<template>
  <div class="flex flex-1 overflow-hidden">
    <div class="h-full w-72 border-gray-200 bg-white">
      <LeftPanel
        ref="leftPanelRef"
        :controls="data"
        :selected-form-ctl-no="selectedFormCtlNo"
        @select="handleSelect"
      />
    </div>
    <div class="round h-full flex-1 border border-gray-200 bg-gray-100">
      <el-scrollbar height="100%" class="p-3">
        <CenterPanel
          ref="centerPanelRef"
          :form="data"
          :selected-form-ctl-no="selectedFormCtlNo"
          :layout-type-code="layoutTypeCode"
          @select="handleSelect"
          @update:form="handleFormUpdate"
        />
      </el-scrollbar>
    </div>
    <div
      class="ml-4 h-full w-72 rounded border border-gray-200 bg-white shadow-sm"
    >
      <RightPanel
        :selected-form-ctl-no="selectedFormCtlNo"
        :form="data"
        :layout-type-code="layoutTypeCode"
        @update="handleFieldUpdate"
      />
    </div>
  </div>
</template>
