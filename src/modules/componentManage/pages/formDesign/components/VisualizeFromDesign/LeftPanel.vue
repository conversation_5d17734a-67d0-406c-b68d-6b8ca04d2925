<script setup lang="ts">
  import { ElTree, ElScrollbar } from 'element-sun';
  import { computed, ref } from 'vue';
  import { Search } from '@element-sun/icons-vue';
  import { debounce } from '@sun-toolkit/shared';
  import type { TreeNodeData } from 'element-sun';
  import { FLAG } from '@sun-toolkit/enums';

  const props = defineProps<{
    controls: FormDesign.FormControlInfo[] | undefined;
    selectedFormCtlNo: string | null;
  }>();
  const emit = defineEmits<{
    (e: 'select', formCtlNo: string, group?: string, isScroll?: boolean): void;
  }>();

  const filterText = ref('');
  const treeRef = ref();
  const expandedKeys = ref<string[]>([]);

  // 添加设置选中节点的方法
  function setSelectedNode(formCtlNo: string) {
    if (treeRef.value) {
      treeRef.value.setCurrentKey(formCtlNo);
    }
  }

  // 暴露方法给父组件
  defineExpose({
    setSelectedNode,
  });

  // Convert controls to tree data format
  const treeData = computed(() => {
    if (!props.controls) return [];
    return props.controls.map((item) => ({
      id: item.formCtlNo,
      label: item.formCtlName,
      ...item,
      children:
        item.subFormControlList?.map((subItem) => ({
          id: subItem.formCtlNo,
          label: subItem.formCtlName,
          group: item.formCtlName,
          parentFormCtlNo: item.formCtlNo,
          ...subItem,
        })) || [],
    }));
  });

  type NodeData = TreeNodeData & {
    formCtlNo: string;
    group?: string;
    labelName?: string;
    parentFormCtlNo?: string;
  };

  const handleNodeClick = (data: NodeData) => {
    // 如果是子节点（标签），使用父节点的 formCtlNo 作为分组标识
    const groupFormCtlNo = data.group ? data.parentFormCtlNo : data.formCtlNo;
    emit('select', data.formCtlNo, groupFormCtlNo, true);
  };

  // 添加过滤方法
  const filterNode = (value: string, data: TreeNodeData) => {
    if (!value) return true;
    return data.label?.toLowerCase().includes(value.toLowerCase());
  };

  function handleEnter(keyWord: string) {
    // 根据用户输入过滤树节点
    treeRef.value?.filter(keyWord);
  }

  const handleNodeExpand = (
    data: TreeNodeData & { formCtlNo: string },
    expanded: boolean,
  ) => {
    if (expanded) {
      if (!expandedKeys.value.includes(data.formCtlNo)) {
        expandedKeys.value = [...expandedKeys.value, data.formCtlNo];
      }
    } else {
      expandedKeys.value = expandedKeys.value.filter(
        (key) => key !== data.formCtlNo,
      );
    }
  };

  let inputChange = debounce(handleEnter, 700);
</script>

<template>
  <div
    class="mr-4 flex h-full flex-1 flex-col overflow-hidden rounded border border-gray-200 bg-white p-4 shadow-sm"
  >
    <el-input
      @input="inputChange"
      @keydown.enter="handleEnter"
      v-model="filterText"
      class="mb-3 w-full"
      :placeholder="$t('global:placeholder.keyword', '请输入关键字')"
      :suffix-icon="Search"
    />
    <el-scrollbar class="flex-1">
      <el-tree
        ref="treeRef"
        :data="treeData"
        :props="{
          children: 'children',
          label: 'label',
        }"
        node-key="id"
        @node-click="handleNodeClick"
        @node-expand="handleNodeExpand"
        :highlight-current="true"
        :current-node-key="selectedFormCtlNo as string"
        :default-expanded-keys="expandedKeys"
        :filter-node-method="filterNode"
      >
        <template #default="{ node, data }">
          <span :class="{ 'text-gray-400': data.displayFlag === FLAG.NO }">
            {{ node.label }}
          </span>
        </template>
      </el-tree>
    </el-scrollbar>
  </div>
</template>
