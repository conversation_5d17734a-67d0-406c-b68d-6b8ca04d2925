<script setup lang="tsx">
  import { ref, computed, watch, onUnmounted } from 'vue';
  import type { SelectedItem } from './type';
  import DynamicComponent from '../../components/DynamicComponent.vue';
  import { getComponentType, getPlaceholder, getExtraProps } from '../../utils';
  import { CONTROL_TYPE_CODE } from '@/utils/constant';
  import { debounce } from '@sun-toolkit/shared';
  import { FLAG } from '@sun-toolkit/enums';

  const props = defineProps<{
    selected?: boolean;
    group?: string;
    layoutTypeCode: string;
    selectedItem: SelectedItem;
    form: FormDesign.FormControlInfo[];
  }>();

  const emit = defineEmits<{
    (e: 'select'): void;
    (e: 'update:form', form: FormDesign.FormControlInfo[]): void;
  }>();

  // 使用本地ref来存储值
  const localValue = ref(props.selectedItem.defaultValue);
  const tagRef = ref<HTMLElement | null>(null);

  // 监听props变化
  watch(
    () => props.selectedItem.defaultValue,
    (newVal) => {
      localValue.value = newVal;
    },
    { immediate: true },
  );

  // 临时宽度状态
  const tempWidths = ref<Record<string, number>>({});

  // 缓存计算结果
  const fontSize = computed(() =>
    parseFloat(getComputedStyle(document.documentElement).fontSize),
  );

  // 缓存布局类型
  const layoutType = computed(() => Number(props.layoutTypeCode) || 4);

  // 缓存边距计算
  const marginPx = computed(() => getRemToPx(0.5));

  function getRemToPx(rem: number) {
    return rem * fontSize.value;
  }

  const tagWidth = computed(() => {
    const n = layoutType.value;
    const formCtlNo = props.selectedItem.formCtlNo;

    if (formCtlNo && tempWidths.value[formCtlNo] != null) {
      return `calc(${tempWidths.value[formCtlNo]} / ${n} * (100% - ${n * marginPx.value}px) + ${(props.selectedItem.displayWidth - 1) * marginPx.value}px)`;
    }

    return `calc(${props.selectedItem.displayWidth || 1} / ${n} * (100% - ${n * marginPx.value}px) + ${(props.selectedItem.displayWidth - 1) * marginPx.value}px)`;
  });

  // 拉伸相关
  const resizing = ref<{ group: string; formCtlNo: string } | null>(null);
  const startX = ref(0);
  const startDisplayWidth = ref(1);
  const parentWidth = ref(0);

  // 使用 RAF 优化拖拽性能
  let rafId: number | null = null;

  // 使用 Map 缓存查找结果
  const fieldCache = new Map<string, FormDesign.FormControlInfo>();

  function findField(
    formCtlNo: string,
    group?: string,
  ): FormDesign.FormControlInfo | undefined {
    const cacheKey = `${group || ''}-${formCtlNo}`;
    if (fieldCache.has(cacheKey)) {
      return fieldCache.get(cacheKey);
    }

    let field = props.form.find((f) => f.formCtlNo === formCtlNo);
    if (!field && group) {
      const groupObj = props.form.find((g) => g.labelName === group);
      field = groupObj?.subFormControlList?.find(
        (f) => f.formCtlNo === formCtlNo,
      );
    }

    if (field) {
      fieldCache.set(cacheKey, field);
    }
    return field;
  }

  function onTagMouseMove(e: MouseEvent, tagEl: HTMLElement) {
    if (resizing.value) return;
    const rect = tagEl.getBoundingClientRect();
    tagEl.style.cursor =
      e.clientX > rect.right - 8 && e.clientX < rect.right + 2
        ? 'ew-resize'
        : 'pointer';
  }

  function onTagMouseLeave(e: MouseEvent, tagEl: HTMLElement) {
    if (resizing.value) return;
    tagEl.style.cursor = 'pointer';
  }

  function onTagMouseDown(
    e: MouseEvent,
    group: string,
    field: SelectedItem,
    tagEl: HTMLElement,
  ) {
    const rect = tagEl.getBoundingClientRect();
    if (e.clientX > rect.right - 8 && e.clientX < rect.right + 2) {
      e.stopPropagation();
      e.preventDefault();
      resizing.value = { group, formCtlNo: field.formCtlNo as string };
      startX.value = e.clientX;
      startDisplayWidth.value = field.displayWidth || 1;
      parentWidth.value =
        tagEl.parentElement?.offsetWidth || rect.width * layoutType.value;
      tempWidths.value[field.formCtlNo as string] = field.displayWidth || 1;
      document.addEventListener('mousemove', onResizing);
      document.addEventListener('mouseup', onResizeEnd);
    }
  }

  // 使用 RAF 优化拖拽性能
  function onResizing(e: MouseEvent) {
    if (!resizing.value || !props.form) return;

    if (rafId) {
      cancelAnimationFrame(rafId);
    }

    rafId = requestAnimationFrame(() => {
      const { group, formCtlNo } = resizing.value!;
      const n = layoutType.value;

      const field = findField(formCtlNo, group);
      if (!field) return;

      const delta = e.clientX - startX.value;
      const totalWidth = parentWidth.value;
      const baseWidth = (startDisplayWidth.value / n) * totalWidth;
      const newPx = baseWidth + delta;
      const ratio = newPx / totalWidth;
      let floatWidth = ratio * n;

      floatWidth = Math.max(1, Math.min(n, floatWidth));
      tempWidths.value[formCtlNo] = floatWidth;
    });
  }

  // 使用防抖优化更新
  const debouncedUpdateForm = debounce((form: FormDesign.FormControlInfo[]) => {
    emit('update:form', form);
  }, 100);

  function onResizeEnd() {
    if (!resizing.value || !props.form) return;

    if (rafId) {
      cancelAnimationFrame(rafId);
      rafId = null;
    }

    const { group, formCtlNo } = resizing.value;
    const n = layoutType.value;

    const field = findField(formCtlNo, group);
    if (!field) return;

    const floatWidth = tempWidths.value[formCtlNo] || field.displayWidth || 1;
    let snapped = Math.round(floatWidth);
    snapped = Math.max(1, Math.min(n, snapped));

    // Update both displayWidth and displayWidthRatio
    field.displayWidth = snapped;
    field.displayWidthRatio = +(snapped / n).toFixed(2);

    // 清除缓存
    fieldCache.clear();
    delete tempWidths.value[formCtlNo];
    debouncedUpdateForm(props.form);
    resizing.value = null;
    document.removeEventListener('mousemove', onResizing);
    document.removeEventListener('mouseup', onResizeEnd);
  }

  const handleMouseMove = (e: MouseEvent) =>
    onTagMouseMove(e, e.currentTarget as HTMLElement);
  const handleMouseLeave = (e: MouseEvent) =>
    onTagMouseLeave(e, e.currentTarget as HTMLElement);
  const handleMouseDown = (e: MouseEvent) =>
    onTagMouseDown(
      e,
      props.group || '',
      props.selectedItem,
      e.currentTarget as HTMLElement,
    );

  // 使用防抖优化更新
  const handleChange = (value: string, name: string) => {
    const updatedItem = {
      ...props.selectedItem,
      defaultValueName: name,
      defaultValue: value,
    };
    // 更新表单数据
    const updatedForm = props.form.map((group) => {
      if (group.formCtlNo === props.selectedItem.formCtlNo) {
        return { ...group, ...updatedItem };
      } else if (group.subFormControlList) {
        return {
          ...group,
          subFormControlList: group.subFormControlList.map((item) =>
            item.formCtlNo === props.selectedItem.formCtlNo
              ? { ...item, ...updatedItem }
              : item,
          ),
        };
      }
      return group;
    });
    emit('update:form', updatedForm);
  };

  const eventType = computed(() =>
    props.selectedItem.controlTypeCode === CONTROL_TYPE_CODE.INPUT
      ? 'input'
      : 'change',
  );

  // 清理事件监听
  onUnmounted(() => {
    if (rafId) {
      cancelAnimationFrame(rafId);
    }
    document.removeEventListener('mousemove', onResizing);
    document.removeEventListener('mouseup', onResizeEnd);
  });
</script>

<template>
  <el-tag
    ref="tagRef"
    size="large"
    effect="plain"
    :type="selected ? 'primary' : 'info'"
    :style="{
      width: tagWidth,
      transition: resizing ? 'none' : 'width 0.2s',
    }"
    :class="`relative mb-4 mr-2 box-border inline-flex w-full cursor-pointer select-none items-center overflow-hidden rounded border border-dashed border-blue-500 text-left ${
      selected ? '!bg-sky-100' : 'bg-white'
    }`"
    @click="emit('select')"
    @mousemove="handleMouseMove"
    @mouseleave="handleMouseLeave"
    @mousedown="handleMouseDown"
  >
    <div class="flex w-full items-center">
      <span class="w-[70px] shrink-0">
        <span class="mr-0.5 text-red-500">{{
          props.selectedItem.mustInputFlag === FLAG.YES ? '*' : '&nbsp;'
        }}</span>
        {{ props.selectedItem.labelName }}
      </span>
      <DynamicComponent
        class="flex-1"
        size="small"
        :component="getComponentType(props.selectedItem)"
        :label="props.selectedItem.labelName"
        :placeholder="getPlaceholder(props.selectedItem, $t)"
        v-bind="getExtraProps(props.selectedItem)"
        @[eventType]="handleChange"
        v-model="localValue"
      />
    </div>
  </el-tag>
</template>

<style scoped>
  :deep(.el-tag__content) {
    display: flex !important;
    align-items: center;
    width: 100% !important;
  }

  :deep(.el-tag) {
    position: relative;
  }

  :deep(.el-tag::after) {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    width: 8px;
    height: 100%;
    cursor: ew-resize;
    content: '';
  }
</style>
