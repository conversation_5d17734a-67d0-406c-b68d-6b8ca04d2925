<script setup lang="tsx">
  import { ProTable } from 'sun-biz';
  import DynamicComponent from '../../components/DynamicComponent.vue';
  import { getComponentType, getPlaceholder, getExtraProps } from '../../utils';
  import { ref, watch, computed } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { FLAG } from '@/utils/constant';
  const { t } = useTranslation();

  const props = defineProps<{
    formControlList: FormDesign.FormControlInfo[];
    selectedFormCtlNo: string | null;
    layoutTypeCode: string;
  }>();

  const emit = defineEmits<{
    (e: 'select', formCtlNo: string): void;
    (e: 'update:form', form: FormDesign.FormControlInfo[]): void;
  }>();

  const forceUpdate = ref(0);

  // 生成表格列配置
  const tableColumns = computed(() => {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    forceUpdate.value; // 添加依赖，使计算属性在 forceUpdate 变化时重新计算
    return props.formControlList
      .filter((item) => item.displayFlag !== FLAG.NO)
      .map((item) => {
        return {
          prop: item.formCtlNo,
          label: item.labelName,
          showOverflowTooltip: true,
          required: item.mustInputFlag === FLAG.YES,
          render: () => {
            return (
              <DynamicComponent
                {...{
                  class: 'flex-1',
                  size: 'small',
                  component: getComponentType(item),
                  label: item.labelName,
                  placeholder: getPlaceholder(item, t),
                  ...getExtraProps(item),
                  onChange: (value: string, name: string) => {
                    item['defaultValue'] = value;
                    item['defaultValueName'] = name;
                    handleValueChange(item, value, name);
                  },
                  modelValue: item['defaultValue'],
                }}
              />
            );
          },
        };
      });
  });

  // 获取单元格类名
  const getCellClassName = ({ column }: { column: { property: string } }) => {
    return props.selectedFormCtlNo === column.property ? 'selected-cell' : '';
  };

  // 获取表头单元格类名
  const getHeaderCellClassName = ({
    column,
  }: {
    column: { property: string };
  }) => {
    return props.selectedFormCtlNo === column.property ? 'selected-header' : '';
  };

  // 处理值变化
  const handleValueChange = (
    item: FormDesign.FormControlInfo,
    value: string,
    name: string,
  ) => {
    const updatedControlList = props.formControlList.map((control) =>
      control.formCtlNo === item.formCtlNo
        ? {
            ...control,
            defaultValue: value,
            defaultValueName: name,
          }
        : control,
    );
    emit('update:form', updatedControlList);
  };

  // 监听表单数据变化，强制更新表格列
  watch(
    () => props.formControlList,
    () => {
      forceUpdate.value++; // 强制更新表格列
    },
    { immediate: true, deep: true },
  );

  // 处理列头点击
  const handleHeaderClick = (column: { property: string }, event: Event) => {
    event.stopPropagation();
    if (column?.property) {
      emit('select', column.property);
    }
  };
</script>

<template>
  <div @click.stop>
    <ProTable
      :key="forceUpdate"
      :columns="tableColumns"
      :data="[{}]"
      class="mb-2"
      :editable="true"
      :cell-class-name="getCellClassName"
      :header-cell-class-name="getHeaderCellClassName"
      @header-click="handleHeaderClick"
    />
  </div>
</template>

<style scoped>
  :deep(.selected-header) {
    background-color: #e0f2fe !important;
  }

  :deep(.selected-cell) {
    background-color: #e0f2fe !important;
  }

  :deep(.el-table__header th) {
    @apply cursor-pointer hover:bg-gray-50;
  }

  :deep(.el-table__body td) {
    @apply cursor-pointer;
  }

  :deep(.el-table__body tr:hover > td) {
    @apply bg-gray-50;
  }
</style>
