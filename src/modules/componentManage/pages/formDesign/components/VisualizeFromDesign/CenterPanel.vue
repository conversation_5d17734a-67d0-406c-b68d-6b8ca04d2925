<script setup lang="tsx">
  import draggable from 'vuedraggable';
  import FormTag from './FormTag.vue';
  import FormTable from './FormTable.vue';
  import { FLAG } from '@/utils/constant';
  import { CONTROL_TYPE_CODE } from '@/utils/constant';
  import { ref, nextTick } from 'vue';

  const props = defineProps<{
    form: FormDesign.FormControlInfo[] | undefined;
    selectedFormCtlNo: string | null;
    layoutTypeCode: string;
  }>();

  const emit = defineEmits<{
    (e: 'select', formCtlNo: string, groupFormCtlNo?: string): void;
    (e: 'update:form', form: FormDesign.FormControlInfo[]): void;
  }>();

  // 存储分组refs的Map
  const groupRefs = ref<Map<string, HTMLElement>>(new Map());

  // 滚动到指定分组
  const scrollToGroup = async (formCtlNo: string) => {
    await nextTick();
    const groupElement = groupRefs.value.get(formCtlNo);
    if (groupElement) {
      groupElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // 暴露方法给父组件
  defineExpose({
    scrollToGroup,
  });

  function updateSortAndEmit() {
    if (!props.form) return;
    // Create a copy of the form to avoid direct mutation
    const updatedForm = [...props.form];

    // Update sort for groups
    updatedForm.forEach((g, idx) => (g.sort = idx + 1));

    // Update sort for items within groups
    updatedForm.forEach((g) => {
      if (g.subFormControlList) {
        g.subFormControlList = g.subFormControlList
          .filter((item) => item.displayFlag !== FLAG.NO) // Filter out items with displayFlag = FLAG.NO
          .map((c, cidx) => ({ ...c, sort: cidx + 1 }));
      }
    });

    // Sort the form based on sort property
    updatedForm.sort((a, b) => (a.sort || 0) - (b.sort || 0));
    updatedForm.forEach((g) => {
      if (g.subFormControlList) {
        g.subFormControlList.sort((a, b) => (a.sort || 0) - (b.sort || 0));
      }
    });

    emit('update:form', updatedForm);
  }

  function onAdd() {
    updateSortAndEmit();
  }

  function handleSelect(formCtlNo: string, groupFormCtlNo?: string) {
    emit('select', formCtlNo, groupFormCtlNo);
  }

  function handleTableUpdate(
    item: FormDesign.FormControlInfo,
    updatedForm: FormDesign.FormControlInfo[],
  ) {
    if (!props.form) return;

    const newForm = props.form.map((group) => {
      if (group.formCtlNo === item.formCtlNo) {
        return {
          ...group,
          subFormControlList: updatedForm, // 直接使用更新后的 formControlList
        };
      }
      return group;
    });
    emit('update:form', newForm);
  }
</script>

<template>
  <draggable
    :list="form"
    group="form-group"
    item-key="formCtlNo"
    :animation="120"
    ghost-class="draggable-ghost"
    chosen-class="draggable-chosen"
    drag-class="draggable-drag"
    @change="updateSortAndEmit"
  >
    <template
      #item="{ element: item }: { element: FormDesign.FormControlInfo }"
    >
      <span v-if="item.displayFlag !== FLAG.NO">
        <!-- 分组情况 -->
        <div
          v-if="(item.subFormControlList || [])?.length > 0"
          :ref="(el) => el && groupRefs.set(item.formCtlNo, el as HTMLElement)"
          :class="`mb-4 rounded border ${props.selectedFormCtlNo === item.formCtlNo ? 'border-blue-500' : 'border-gray-200'} bg-white shadow-sm`"
        >
          <div
            class="cursor-pointer border-b border-gray-200 pb-2 pl-4 pt-2 font-medium hover:bg-gray-50"
            @click="handleSelect(item.formCtlNo, item.formCtlNo)"
          >
            <span>{{ item.labelName }}</span>
          </div>
          <div class="pl-4 pr-2 pt-4">
            <!-- 表格类型渲染 -->
            <template v-if="item.controlTypeCode === CONTROL_TYPE_CODE.TABLE">
              <FormTable
                :form-control-list="item.subFormControlList || []"
                :selected-form-ctl-no="selectedFormCtlNo"
                :layout-type-code="layoutTypeCode"
                :form="form as FormDesign.FormControlInfo[]"
                @select="handleSelect"
                @update:form="
                  (updatedForm) => handleTableUpdate(item, updatedForm)
                "
              />
            </template>
            <template v-else>
              <draggable
                :list="item.subFormControlList"
                :group="{
                  name: `form-field-${item.labelName}`,
                  pull: true,
                  put: false,
                }"
                item-key="formCtlNo"
                :animation="100"
                ghost-class="draggable-ghost"
                chosen-class="draggable-chosen"
                drag-class="draggable-drag"
                :clone="(el: FormDesign.FormControlInfo) => ({ ...el })"
                @change="updateSortAndEmit"
                @add="onAdd"
              >
                <template
                  #item="{ element: subItem }: { element: FormDesign.FormControlInfo }"
                >
                  <FormTag
                    v-if="subItem.displayFlag !== FLAG.NO"
                    :selected-item="subItem"
                    :selected="props.selectedFormCtlNo === subItem.formCtlNo"
                    :group="item.labelName"
                    :layout-type-code="layoutTypeCode"
                    :form="form as FormDesign.FormControlInfo[]"
                    class="mb-2"
                    @select="
                      () => handleSelect(subItem.formCtlNo, item.formCtlNo)
                    "
                    @update:form="emit('update:form', $event)"
                  />
                </template>
              </draggable>
            </template>
          </div>
        </div>

        <!-- 单个标签情况 -->
        <span
          v-else
          :ref="(el) => el && groupRefs.set(item.formCtlNo, el as HTMLElement)"
        >
          <FormTag
            :selected-item="item"
            :selected="props.selectedFormCtlNo === item.formCtlNo"
            :layout-type-code="layoutTypeCode"
            :form="form as FormDesign.FormControlInfo[]"
            @select="() => handleSelect(item.formCtlNo, item.formCtlNo)"
            @update:form="emit('update:form', $event)"
          />
        </span>
      </span>
    </template>
  </draggable>
</template>

<style scoped>
  .draggable-ghost,
  .draggable-chosen {
    @apply rounded !bg-sky-100 opacity-100 outline outline-1 outline-offset-[-1px] outline-blue-500;
  }

  .draggable-drag {
    @apply cursor-grabbing;
  }
</style>
