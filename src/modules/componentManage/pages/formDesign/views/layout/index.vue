<script setup lang="ts" name="employeeDetail">
  import { ref, Ref, computed, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import {
    queryFormByExample,
    queryFormDesignListByExample,
  } from '@/modules/componentManage/api/formDesign';
  import {
    LAYOUT_TYPE_CODE,
    ENABLED_FLAG,
    BIZ_ID_TYPE_CODE,
  } from '@/utils/constant';
  import LayoutContainer from '../../components/LayoutContainer.vue';
  import { generateUUID, downloadFile } from '@sun-toolkit/shared';
  import { exportDmlScriptByExample } from '@/modules/baseConfig/api/code';
  import { useGetDMLList } from '@/hooks/useGetDMLList.ts';
  import { Grid, List, ArrowDown, Plus } from '@element-sun/icons-vue';
  import {
    addFormDesign,
    saveFormDesignDetail,
  } from '@/modules/componentManage/api/formDesign';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { useAppConfigData, MAIN_APP_CONFIG } from 'sun-biz';

  const props = defineProps<{
    fromId?: string;
  }>();
  const { t } = useTranslation();
  //isCloudenv，true指云端，flase其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  export type TableRef =
    | {
        formRef: FormInstance;
        validateRow: (
          index: number,
          cb?: (isValid: boolean) => void,
        ) => Promise<boolean>;
        proTableRef?: {
          clearSelection: () => void;
        };
      }
    | undefined;
  const submitLoading = ref(false);
  const TABLE = 'table';
  const isVisualize = ref(false);
  const DEFAULT_OBJ: FormDesign.FormDesignInfo = {
    layoutTypeCode: LAYOUT_TYPE_CODE.ONE,
    enabledFlag: ENABLED_FLAG.YES,
    defaultFlag: ENABLED_FLAG.YES,
    formDesignName: t('formDesign.default.formDesignName', '默认方案'),
    formDesign2ndName: '',
    formDesignId: '',
    formDesignExtName: '',
    layoutTypeDesc: '',
  };
  const route = useRoute();
  const router = useRouter();
  const dmlList = useGetDMLList();
  const layoutContainerRef = ref<{
    saveDesign: () => Promise<FormDesign.ReqAddFormDesign>;
    saveContent: () => Promise<{
      formDesignDetailList: FormDesign.DesignDetailInfo[];
    }>;
    tableRef: Ref<TableRef, TableRef>;
  }>();
  const formName = ref('');
  const formId = computed(() => props.fromId ?? route.params.id) as Ref<
    string,
    string
  >;
  const schemeList = ref<FormDesign.SchemeInfo[]>([]);
  let activeName = ref('');
  const schemeInfo = computed(() =>
    schemeList.value.find(
      (item) =>
        (item.detail.formDesignId || item.detail?.identification) ===
        activeName.value,
    ),
  );
  onMounted(() => {
    queryForm();
    queryFormDesignList();
  });
  async function queryForm() {
    let [, result] = await queryFormByExample({
      formId: formId.value,
    });
    if (result?.success) {
      formName.value = result.data[0]?.formName;
    }
  }

  async function queryFormDesignList() {
    let [, result] = await queryFormDesignListByExample({
      formId: formId.value,
    });
    if (result?.success) {
      if (result.data.length) {
        schemeList.value = result.data.map((item: { formDesignId: string }) => {
          return {
            detail: {
              ...item,
              identification: item.formDesignId,
            },
            tableData: [],
          };
        });
        activeName.value = result.data[0].formDesignId || '';
      } else {
        let identification = generateUUID();
        schemeList.value = [
          {
            detail: {
              ...DEFAULT_OBJ,
              identification: identification,
            },
            tableData: [],
          },
        ];
        activeName.value = identification;
      }
    }
  }

  function changeSchemeList(
    detailOrTable:
      | FormDesign.SchemeInfoDetail
      | FormDesign.FormDesignDetailInfo[],
    type?: string,
  ) {
    schemeList.value = schemeList.value.map((item) => {
      if (item.detail.identification === activeName.value) {
        if (type === TABLE) {
          return {
            ...item,
            tableData: detailOrTable as FormDesign.FormDesignDetailInfo[],
          };
        } else {
          activeName.value =
            (detailOrTable as FormDesign.SchemeInfoDetail)?.formDesignId ||
            activeName.value;
          return {
            ...item,
            detail: {
              ...detailOrTable,
              identification: activeName.value,
            } as FormDesign.SchemeInfoDetail,
          };
        }
      }
      return item;
    });
  }

  function tabChange(vaule: string) {
    activeName.value = vaule;
  }

  // 返回主页
  const goBack = () => {
    router.push('/');
  };

  function addSecheme() {
    let identification = generateUUID();
    schemeList.value.unshift({
      detail: {
        ...DEFAULT_OBJ,
        identification: identification,
        formDesignName: t(
          'formDesign.new.default.formDesignName',
          '方案 {{content}}',
          {
            content: schemeList.value.length + 1,
          },
        ),
      },
      tableData: [],
    });
    activeName.value = identification;
  }

  function cancelClick() {
    schemeList.value = schemeList.value.filter(
      (item) => item.detail.identification !== activeName.value,
    );
    activeName.value = schemeList.value[0].detail.identification || '';
  }

  async function clickDropdown(item: { label: string; value: string }) {
    let [, result] = await exportDmlScriptByExample({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_FORM_DESIGN,
      bisIds: [schemeInfo?.value?.detail.formDesignId || ''],
      dataBaseTypeCode: item.value,
    });
    if (result?.success) {
      downloadFile(result?.data);
      // layoutContainerRef?.value?.tableRef?.value?.proTableRef?.clearSelection();
    }
  }

  async function saveDesign() {
    layoutContainerRef.value?.saveDesign().then(async (res) => {
      submitLoading.value = true;
      let [, result] = await addFormDesign(res as FormDesign.ReqAddFormDesign);
      submitLoading.value = false;
      if (result?.success) {
        changeSchemeList(
          {
            ...res,
            formDesignId: result?.data.formDesignId,
          },
          'detail',
        );
        ElMessage({
          type: 'success',
          message: t('global:save.success'),
        });
      }
    });
  }

  function changeSortData(data: FormDesign.FormControlInfo[]) {
    if (data.length) {
      schemeList.value = schemeList.value.map((item) => {
        if (
          (item.detail.formDesignId || item.detail?.identification) ===
          activeName.value
        ) {
          if (!data[0].belongGroupElementId) {
            return {
              ...item,
              tableData: data,
            };
          } else {
            let belongGroupElementId = data[0].belongGroupElementId;
            return {
              ...item,
              tableData: item.tableData.map((cur) => {
                return {
                  ...cur,
                  subFormControlList: (cur.formControlId ===
                  belongGroupElementId
                    ? data
                    : cur.subFormControlList || []
                  ).map((obj) => ({
                    ...obj,
                  })),
                };
              }),
            };
          }
        }
        return item;
      });
    }
  }

  function saveContent() {
    layoutContainerRef.value?.saveContent().then(async (res) => {
      submitLoading.value = true;
      let [, result] = await saveFormDesignDetail(
        res as FormDesign.SaveFormDesignDetailReq,
      );
      submitLoading.value = false;
      if (result?.success) {
        ElMessage({
          type: 'success',
          message: t('global:save.success'),
        });
      }
    });
  }

  /**
   * 切换可视化状态
   */
  function changeVisualize() {
    isVisualize.value = !isVisualize.value;
  }
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <div>
      <el-page-header @back="goBack">
        <template #content>
          <span class="text-base">
            {{ `${$t('global:edit', '编辑')}-${formName}` }}
          </span>
        </template>
      </el-page-header>
      <!-- <Title :title="$t('formDesign.layout', '表单设计')" class="my-3" /> -->
    </div>
    <div class="flex" v-if="schemeList.length">
      <div
        @click="addSecheme"
        class="new-scheme flex w-28 items-center text-center"
        style="
          height: 40px;
          line-height: 40px;
          border-bottom: 2px solid #e4e7ed;
        "
      >
        <el-icon class="mr-2" style="margin-top: -2px"><Plus /></el-icon>
        <a class="new-scheme" href="javaScript:;" style="font-size: 0.875rem">
          {{ $t('formDesign.newScheme', '新方案') }}
        </a>
      </div>
      <el-tabs
        @tab-change="tabChange"
        v-model="activeName"
        class="flex h-full flex-1 overflow-hidden"
      >
        <el-tab-pane
          v-for="item in schemeList"
          :key="item.detail.identification"
          :name="item.detail.identification"
          :label="item.detail.formDesignName"
          class="flex h-full flex-1"
        >
        </el-tab-pane>
      </el-tabs>
      <div
        class="new-scheme flex content-start items-center text-center"
        style="
          height: 40px;
          padding-left: 2px;
          line-height: 40px;
          border-bottom: 2px solid #e4e7ed;
        "
      >
        <span>
          <el-button
            class="mr-4 align-top"
            type="primary"
            @click="
              () => {
                if (!schemeInfo?.detail.formDesignId) {
                  saveDesign();
                } else {
                  saveContent();
                }
              }
            "
            :loading="submitLoading"
            :disabled="submitLoading"
          >
            {{ $t('global:save', '保存') }}
          </el-button>
          <el-button
            style="margin-left: 0"
            class="ml-0 mr-4 align-top"
            @click="cancelClick"
            v-if="!schemeInfo?.detail.formDesignId && schemeList.length > 1"
          >
            {{ $t('global:cancel', '取消') }}
          </el-button>
          <el-button
            style="margin-left: 0"
            @click="changeVisualize"
            class="ml-0 mr-4 align-top"
          >
            <el-icon v-if="isVisualize">
              <Grid />
            </el-icon>
            <el-icon v-else>
              <List />
            </el-icon>
          </el-button>
          <el-dropdown @click="clickDropdown">
            <el-button :disabled="!schemeInfo?.detail.formDesignId">
              DML<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template
              #dropdown
              v-if="!!dmlList.length && schemeInfo?.detail.formDesignId"
            >
              <el-dropdown-menu>
                <el-dropdown-item
                  @click="clickDropdown(item)"
                  :key="item.value"
                  v-for="item in dmlList"
                  >{{ item.label }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </span>
      </div>
    </div>

    <LayoutContainer
      :is-cloud-env="isCloudEnv"
      ref="layoutContainerRef"
      :is-visualize="isVisualize"
      :change-sort-data="changeSortData"
      :change-scheme-list="changeSchemeList"
      :scheme-info="schemeInfo"
      :form-id="formId"
    />
  </div>
</template>
<style scoped>
  /* 你的样式代码 */
  .new-scheme:hover {
    color: #2468da;
  }
</style>
