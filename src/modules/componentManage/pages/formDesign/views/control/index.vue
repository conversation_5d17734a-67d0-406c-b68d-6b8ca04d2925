<script setup lang="ts" name="employeeDetail">
  import { ref, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { useTableColumnsConfig } from '../../config/useControlTableColumns.tsx';
  import { exportDmlScriptByExample } from '@/modules/baseConfig/api/code';
  import {
    queryFormByExample,
    updateFormControlSortByIds,
  } from '@/modules/componentManage/api/formDesign';
  import { ElMessage } from 'element-sun';
  import { Search } from '@element-sun/icons-vue';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant.ts';
  import { downloadFile, generateUUID, debounce } from '@sun-toolkit/shared';
  import { useGetDMLList } from '@/hooks/useGetDMLList.ts';
  import { useTranslation } from 'i18next-vue';
  import { Title, ProTable, type AnyObject } from 'sun-biz';

  const { t } = useTranslation();

  interface FormControlInfo
    extends Partial<Omit<FormDesign.FormControlInfo, 'subFormControlList'>> {
    editable?: boolean;
    subFormControlList?: FormControlInfo[];
    oldParam?: FormControlInfo;
    identification?: string;
    formControlId?: string;
    sort?: number;
    formCtlNo?: string;
    formCtlName?: string;
    labelName?: string;
    controlTypeCode?: string;
    maxLength?: string;
    dataSearchBizIdTypeCode?: string;
    codeSystemNo?: string;
    defaultValue?: string;
    defaultValueName?: string;
    displayFlag?: number;
    mustInputFlag?: number;
    allowModifyFlag?: number;
    specialMark?: string;
    bindingFieldNo?: string;
  }

  const DEFAULT_OBJ: FormControlInfo = {
    editable: true,
    displayFlag: 1,
    mustInputFlag: 1,
    allowModifyFlag: 1,
  };
  const route = useRoute();
  const router = useRouter();
  const formName = ref('');
  const selections = ref<FormControlInfo[]>([]);
  const tableData = ref<FormControlInfo[]>([]);
  const loading = ref(false);
  const keyWord = ref<string>('');
  const dmlList = useGetDMLList();
  const refreshIndex = ref(0);
  const tableRef = ref();
  const expandedRowKeys = ref<string[]>([]); // 展开行
  onMounted(() => {
    fetchData();
  });

  let inputChange = debounce(handleEnter, 500);
  const controlColumns = useTableColumnsConfig(
    addControl,
    handleExpandChange,
    refreshTableSort,
    tableRef,
    tableData,
    DEFAULT_OBJ,
    route.params.id as string,
  );
  function selectChange(value: AnyObject[]) {
    selections.value = value as FormControlInfo[];
  }

  async function clickDropdown(item: { label: string; value: string }) {
    let [, result] = await exportDmlScriptByExample({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_FORM_CONTROL,
      bisIds: selections.value.map((item) => item?.formControlId || ''),
      dataBaseTypeCode: item.value,
    });
    if (result?.success) {
      downloadFile(result?.data);
      tableRef?.value?.proTableRef.clearSelection();
    }
  }

  /**
   * 数组过滤
   */
  function filterData(tableData: FormControlInfo[], keyWord: string) {
    tableData = tableData.filter((item) => {
      const filteredChildren = filterData(
        item.subFormControlList || [],
        keyWord,
      );
      return (
        (item.formCtlNo || '').includes(keyWord) ||
        (item.formCtlName || '').includes(keyWord) ||
        (item.labelName || '').includes(keyWord) ||
        filteredChildren.length
      );
    });
    return tableData;
  }

  async function fetchData() {
    loading.value = true;
    let [, result] = await queryFormByExample({
      formId: route.params.id as string,
    });
    loading.value = false;
    if (result?.success) {
      formName.value = result.data[0]?.formName;
      let data = (result.data[0]?.formControlList || []).map((item) => ({
        ...item,
      }));
      refreshIndex.value = refreshIndex.value + 1;
      tableData.value = refreshTableSort(filterData(data, keyWord.value));
    }
  }
  function handleEnter() {
    fetchData();
  }
  // 返回主页
  const goBack = () => {
    router.push('/');
  };

  function addControl(formControlId?: string) {
    if (formControlId === undefined) {
      tableData.value.push({
        ...DEFAULT_OBJ,
        identification: generateUUID(),
      });
      tableData.value = refreshTableSort(tableData.value);
    } else {
      let findIndex = tableData.value.findIndex(
        (item) => item.formControlId === formControlId,
      );
      tableData.value.splice(findIndex + 1, 0, {
        ...DEFAULT_OBJ,
        identification: generateUUID(),
      });
      tableData.value = refreshTableSort(tableData.value);
    }
  }

  function handleExpandChange(row: AnyObject, expanded: boolean) {
    if (expanded) {
      expandedRowKeys.value.push(row.formControlId);
    } else {
      expandedRowKeys.value = expandedRowKeys.value.filter(
        (item) => item !== row.formControlId,
      );
    }
  }

  function refreshTableSort(tableData: FormControlInfo[]) {
    tableData = tableData.map((item, index) => {
      if (item.subFormControlList) {
        item.subFormControlList = refreshTableSort(
          item.subFormControlList,
        ) as unknown as FormDesign.FormControlInfo[];
      }
      return {
        ...item,
        sort: index + 1,
      };
    });
    return tableData;
  }

  const handleSortEnd = async (data: FormControlInfo[]) => {
    let [, result] = await updateFormControlSortByIds({
      formControlSortList: data.map((item: FormControlInfo, index) => ({
        formControlId: item.formControlId || '',
        sort: index + 1,
      })),
    });
    if (result?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success'),
      });
      handleEnter();
    }
  };
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <div>
      <el-page-header @back="goBack">
        <template #content>
          <span class="text-base">
            {{ `${$t('global:edit', '编辑')}-${formName}` }}
          </span>
        </template>
      </el-page-header>
      <Title :title="$t('formDesign.control', '表单控件')" class="my-3" />
      <div class="mb-5 flex justify-between">
        <span>
          <el-input
            v-model="keyWord"
            @input="inputChange"
            @keydown.enter="handleEnter"
            class="mr-5 w-72"
            :placeholder="$t('global:placeholder.keyword')"
            :suffix-icon="Search"
          />
          <el-button @click="handleEnter">
            {{ $t('global:search') }}
          </el-button>
        </span>
        <span>
          <el-dropdown @click="clickDropdown">
            <el-button
              class="mr-5"
              type="primary"
              :disabled="!selections.length"
            >
              DML<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown v-if="selections.length && !!dmlList.length">
              <el-dropdown-menu>
                <el-dropdown-item
                  @click="clickDropdown(item)"
                  :key="item.value"
                  v-for="item in dmlList"
                  >{{ item.label }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button
            type="primary"
            @click="
              () => {
                addControl();
              }
            "
          >
            {{ $t('global:add') }}</el-button
          >
        </span>
      </div>
    </div>
    <div class="flex h-full flex-1 flex-col overflow-hidden">
      <pro-table
        :highlight-current-row="true"
        @selection-change="selectChange"
        row-class-name="cursor-pointer"
        :key="refreshIndex"
        :expand-row-keys="expandedRowKeys"
        :tree-props="{
          children: 'subFormControlList',
        }"
        :draggable="true"
        @drag-end="handleSortEnd"
        @expand-change="handleExpandChange"
        ref="tableRef"
        :editable="true"
        :columns="controlColumns"
        row-key="formControlId"
        :data="tableData"
        :loading="loading"
      />
    </div>
  </div>
</template>
