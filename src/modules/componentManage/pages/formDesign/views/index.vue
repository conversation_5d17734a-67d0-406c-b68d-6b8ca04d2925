<script setup lang="tsx">
  import { Title, ProTable, useAppConfigData, MAIN_APP_CONFIG } from 'sun-biz';
  import { onMounted, reactive, ref } from 'vue';
  import { Search } from '@element-sun/icons-vue';
  import CreateOrEditForm from '../components/CreateOrEditForm.vue';
  import { queryFormByExample } from '@/modules/componentManage/api/formDesign';
  import { useTableColumnsConfig } from '../config/useFormDesignTableColumns.tsx';
  import { exportDmlScriptByExample } from '@/modules/baseConfig/api/code';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant.ts';
  import { downloadFile, debounce } from '@sun-toolkit/shared';
  import { useGetDMLList } from '@/hooks/useGetDMLList.ts';
  //isCloudenv，true指云端，flase其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  type DialogData = {
    [key: string]: unknown;
  };
  const tableRef = ref();
  const keyWord = ref<string>('');
  const selections = ref<FormDesign.FormInfo[]>([]);
  const tableData = ref<FormDesign.FormInfo[]>([]);
  const loading = ref(false);
  const dmlList = useGetDMLList();
  const formData = reactive<Partial<FormDesign.FormInfo>>({});
  const createOrEditFormRef = ref();
  function onClose() {
    fetchData();
  }
  async function clickDropdown(item: { label: string; value: string }) {
    let [, result] = await exportDmlScriptByExample({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_FORM,
      bisIds: selections.value.map((item) => item.formId),
      dataBaseTypeCode: item.value,
    });
    if (result?.success) {
      downloadFile(result?.data);
      tableRef?.value?.proTableRef.clearSelection();
    }
  }

  async function fetchData() {
    loading.value = true;
    let [, result] = await queryFormByExample({
      keyWord: keyWord.value,
    });
    loading.value = false;
    if (result?.success) {
      tableData.value = result.data;
    }
  }

  onMounted(() => {
    fetchData();
  });

  function handleEnter() {
    fetchData();
  }

  function openFormDialog(data: DialogData) {
    Object.keys(data).forEach((key) => {
      (formData as DialogData)[key] = data[key];
    });
    createOrEditFormRef.value.dialogRef.open();
  }

  let inputChange = debounce(handleEnter, 500);

  function selectChange(value: FormDesign.FormInfo[]) {
    selections.value = value as FormDesign.FormInfo[];
  }
  const formDesignColumns = useTableColumnsConfig(openFormDialog, isCloudEnv);
</script>
<template>
  <div class="p-box flex h-full flex-1 flex-col">
    <Title :title="$t('formDesign.list', '表单列表')" class="mb-5"> </Title>
    <div class="mb-5 flex justify-between">
      <span>
        <el-input
          v-model="keyWord"
          @input="inputChange"
          @keydown.enter="handleEnter"
          class="mr-5 w-72"
          :placeholder="$t('global:placeholder.keyword')"
          :suffix-icon="Search"
        />
        <el-button @click="handleEnter">
          {{ $t('global:search') }}
        </el-button>
      </span>
      <span>
        <el-dropdown @click="clickDropdown">
          <el-button
            class="mr-5"
            type="primary"
            :disabled="!selections.length || !isCloudEnv"
          >
            DML<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template
            #dropdown
            v-if="selections.length && !!dmlList.length && isCloudEnv"
          >
            <el-dropdown-menu>
              <el-dropdown-item
                @click="clickDropdown(item)"
                :key="item.value"
                v-for="item in dmlList"
                >{{ item.label }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <el-button
          type="primary"
          :disabled="!isCloudEnv"
          @click="
            () => {
              openFormDialog({
                row: {},
                title: $t('formDesign.addTitle', '新增表单'),
              });
            }
          "
          >{{ $t('global:add') }}</el-button
        >
      </span>
    </div>
    <pro-table
      :highlight-current-row="true"
      @selection-change="selectChange"
      row-class-name="cursor-pointer"
      ref="tableRef"
      :columns="formDesignColumns"
      row-key="formId"
      :data="tableData"
      :loading="loading"
    />
    <CreateOrEditForm
      ref="createOrEditFormRef"
      v-bind="formData"
      @success="onClose"
    />
  </div>
</template>
