import {
  COMPONENT_MAP,
  CONTROL_TYPE_CODE,
  SWITCH_CODE,
} from '@/utils/constant';
import type { FormInstance } from 'element-sun';
import type { TreeNodeData } from 'element-sun';

const DICT_COMMON_TABLE = 'dictCommonTable';

// 通用类型定义
export type TableRef = {
  formRef: FormInstance;
  validateRow: (
    index: number,
    cb?: (isValid: boolean) => void,
  ) => Promise<boolean>;
  proTableRef?: {
    clearSelection: () => void;
  };
};

export type NodeData = TreeNodeData & {
  formCtlNo: string;
  group?: string;
  labelName?: string;
  parentFormCtlNo?: string;
};

// 工具函数
export function findField(
  form: FormDesign.FormControlInfo[],
  formCtlNo: string,
  group?: string,
): FormDesign.FormControlInfo | undefined {
  let field = form.find((f) => f.formCtlNo === formCtlNo);
  if (!field && group) {
    const groupObj = form.find((g) => g.labelName === group);
    field = groupObj?.subFormControlList?.find(
      (f) => f.formCtlNo === formCtlNo,
    );
  }
  return field;
}

export function updateFieldSort(
  form: FormDesign.FormControlInfo[],
  field: FormDesign.FormControlInfo,
  isSort: boolean,
): FormDesign.FormControlInfo[] {
  if (!isSort) return form;

  const updatedForm = [...form];

  // 如果是标签（有 belongGroupElementId）
  if (field.belongGroupElementId) {
    const groupIndex = updatedForm.findIndex(
      (g) => g.formControlId === field.belongGroupElementId,
    );
    if (groupIndex !== -1) {
      const updatedGroup = { ...updatedForm[groupIndex] };
      const updatedSubList = [...(updatedGroup.subFormControlList || [])];
      const index = updatedSubList.findIndex(
        (item) => item.formCtlNo === field.formCtlNo,
      );
      if (index !== -1) {
        // 移动元素
        const [movedItem] = updatedSubList.splice(index, 1);
        updatedSubList.splice(field.sort - 1, 0, movedItem);
        // 更新所有元素的 sort 值
        updatedSubList.forEach((item, idx) => {
          item.sort = idx + 1;
        });
        updatedGroup.subFormControlList = updatedSubList;
        updatedForm[groupIndex] = updatedGroup;
      }
    }
  } else {
    // 如果是分组（没有 belongGroupElementId）
    const index = updatedForm.findIndex(
      (item) => item.formCtlNo === field.formCtlNo,
    );
    if (index !== -1) {
      // 移动元素
      const [movedItem] = updatedForm.splice(index, 1);
      updatedForm.splice(field.sort - 1, 0, movedItem);
      // 更新所有元素的 sort 值
      updatedForm.forEach((item, idx) => {
        item.sort = idx + 1;
      });
    }
  }

  return updatedForm;
}

export function updateFieldValue(
  form: FormDesign.FormControlInfo[],
  field: FormDesign.FormControlInfo,
  selectedFormCtlNo: string,
): FormDesign.FormControlInfo[] {
  return form.map((group) => {
    if (group.formCtlNo === selectedFormCtlNo) {
      return { ...field };
    } else if (group.subFormControlList) {
      return {
        ...group,
        subFormControlList: group.subFormControlList.map((item) =>
          item.formCtlNo === selectedFormCtlNo ? { ...field } : item,
        ),
      };
    }
    return group;
  });
}

/**
 * 获取组件类型
 * @param item
 * @returns
 */
export function getComponentType(item: FormDesign.FormControlInfo) {
  if (item.controlTypeCode === CONTROL_TYPE_CODE.SELECT) {
    return DICT_COMMON_TABLE;
  }
  return COMPONENT_MAP[item.controlTypeCode as keyof typeof COMPONENT_MAP];
}

export function getPlaceholder(
  item: FormDesign.FormControlInfo,
  t: (
    arg0: string,
    arg1: { name: string | undefined; content: string | undefined },
  ) => string,
) {
  return (
    item.hintMsg ||
    t(
      `global:placeholder.${item.controlTypeCode === CONTROL_TYPE_CODE.INPUT ? 'input' : 'select'}.template`,
      {
        name: item.labelNameDisplay || item.labelName,
        content: item.labelNameDisplay || item.labelName,
      },
    )
  );
}

export function getExtraProps(item: FormDesign.FormControlInfo) {
  const initObj = {
    defaultValue: item.defaultValue,
    defaultValueName: item.defaultValueName,
  };

  let resultObj: { [key: string]: unknown } = {
    style: { width: '100%' },
    filterable: true,
    codeSystemNo: item.codeSystemNo,
    dataSearchBizIdTypeCode: item?.dataSearchBizIdTypeCode,
    ...initObj,
  };
  if (item.controlTypeCode === CONTROL_TYPE_CODE.DATEPICKER) {
    resultObj.type = 'date';
    resultObj.format = 'YYYY-MM-DD';
    resultObj.valueFormat = 'YYYY-MM-DD  HH:mm:ss';
    resultObj.defaultValue = undefined;
  }
  if (item.controlTypeCode === CONTROL_TYPE_CODE.SWITCH) {
    resultObj = {
      ...resultObj,
      'inline-prompt': true,
      'active-value': SWITCH_CODE.YES,
      'inactive-value': SWITCH_CODE.NO,
    };
  }
  return resultObj;
}

/**
 * 树形数据过滤
 * @param tableData 要过滤的数据数组
 * @param keyWord 搜索关键词
 * @param options 可选配置项
 */
/**
 * 树形数据过滤
 * @param tableData 要过滤的数据数组
 * @param keyWord 搜索关键词
 * @param options 可选配置项
 */
export function filterData(
  tableData: FormDesign.FormDesignDetailInfo[],
  keyWord: string,
  options?: {
    caseSensitive?: boolean;
    keepParent?: boolean;
  },
): FormDesign.FormDesignDetailInfo[] {
  if (!keyWord) return tableData;

  const { caseSensitive = false } = options || {};
  const searchKey = caseSensitive ? keyWord : keyWord.toLowerCase();

  return tableData.filter((item: FormDesign.FormDesignDetailInfo) => {
    // 检查当前节点是否匹配
    const isMatch =
      (item.formCtlNo || '').toLowerCase().includes(searchKey) ||
      (item.formCtlName || '').toLowerCase().includes(searchKey) ||
      (item.labelName || '').toLowerCase().includes(searchKey);

    // 如果有子节点，递归过滤子节点
    const filteredChildren: FormDesign.FormDesignDetailInfo[] = filterData(
      item.subFormControlList || [],
      keyWord,
      options,
    );

    // 如果当前节点匹配，保留所有子节点
    if (isMatch) {
      return true;
    }

    // 如果当前节点不匹配，但子节点有匹配项，只保留匹配的子节点
    if (filteredChildren.length > 0) {
      // 更新当前节点的子节点列表，只保留匹配的子节点
      item.subFormControlList = filteredChildren;
      return true;
    }

    // 如果当前节点和子节点都不匹配，不显示
    return false;
  });
}

/**
 * 整合数组
 * @param tableData
 * @returns
 */
export function setTableDefaultKey(
  tableData: FormDesign.FormDesignDetailInfo[],
  layoutTypeCode: string,
) {
  tableData = tableData.map((item, index) => {
    if (item.subFormControlList) {
      item.subFormControlList = setTableDefaultKey(
        item.subFormControlList,
        layoutTypeCode,
      ) as unknown as FormDesign.FormDesignDetailInfo[];
    }
    return {
      ...item,
      sort: index + 1,
      focusAutoDropDownFlag: item.focusAutoDropDownFlag ?? 0,
      cursorStayFlag: item.cursorStayFlag ?? 0,
      displayWidth: item.displayWidthRatio
        ? Math.round(item.displayWidthRatio * Number(layoutTypeCode))
        : 1,
      editable: true,
    };
  });
  return tableData;
}

/**
 * 对表单数据进行排序
 * @param formData 表单数据
 * @returns 排序后的表单数据
 */
export function sortFormData(
  formData: FormDesign.FormControlInfo[],
): FormDesign.FormControlInfo[] {
  if (!formData) return [];

  // 对分组进行排序
  const sortedData = [...formData].sort(
    (a, b) => (a.sort || 0) - (b.sort || 0),
  );

  // 对每个分组内的子项进行排序
  return sortedData.map((group) => ({
    ...group,
    subFormControlList: group.subFormControlList?.sort(
      (a, b) => (a.sort || 0) - (b.sort || 0),
    ),
  }));
}
