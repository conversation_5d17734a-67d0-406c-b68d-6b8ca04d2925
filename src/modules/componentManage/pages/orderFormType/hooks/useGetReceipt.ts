import { queryReceiptByExample } from '@/modules/baseConfig/api/receipt';
import { ref } from 'vue';

export function useGetReceipt() {
  const loading = ref(false);
  const receiptList = ref<Receipt.ReceiptReqItem[]>([]);

  const getReceiptList = async (keyWord?: string) => {
    loading.value = true;
    const [, res] = await queryReceiptByExample({
      keyWord,
    });
    loading.value = false;

    if (res?.success) {
      receiptList.value = res?.data ?? [];
    }
  };

  return {
    loading,
    receiptList,
    getReceiptList,
  };
}
