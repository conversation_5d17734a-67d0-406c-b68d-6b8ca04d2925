import { useColumnConfig, useEditableTable } from 'sun-biz';
import { ENABLED_FLAG } from '@sun-toolkit/enums';
import { Ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-sun';
import { useTranslation } from 'i18next-vue';
import {
  addOrderFormType,
  deleteOrderFormTypeById,
  updateOrderFormTypeById,
} from '../../../api/order.ts';
import { bizNoparams, ordertableConfig } from '../../../typings/orderFormType';

export function useTableColumns(
  tableRef: Ref,
  orderList: Ref,
  queryData: () => void,
  options: {
    receiptLoading: Ref<boolean>;
    receiptList: Ref<Receipt.ReceiptReqItem[]>;
    getReceiptList: (keyWord?: string) => Promise<void>;
    bizNoGenerateRuleDetailData: Ref;
    codingRules: (keyWord?: string) => void;
  },
) {
  const {
    receiptLoading,
    receiptList,
    getReceiptList,
    bizNoGenerateRuleDetailData,
    codingRules,
  } = options;
  const { t } = useTranslation();
  const { toggleEdit, cancelEdit, validateItem } = useEditableTable({
    tableRef,
    data: orderList,
    id: 'orderFormTypeId',
  });
  // 保存
  const handelsave = async (row: ordertableConfig) => {
    const verificationResults = await validateItem(row);
    if (verificationResults) {
      let res;
      if (row.orderFormTypeId) {
        // 编辑
        const [, editRes] = await updateOrderFormTypeById(row);
        res = editRes;
      } else {
        // 新增
        const [, addRes] = await addOrderFormType(row);
        res = addRes;
        // 新增成功后，后端返回新 id，更新本地数据
        if (res?.success && res.data?.orderFormTypeId) {
          row.orderFormTypeId = res.data.orderFormTypeId;
        }
      }

      if (res?.success) {
        ElMessage.success(t('global:save.success'));
        row.editable = false;
        row.saving = false;
        // 可选：刷新表格数据
      }
    } else {
      ElMessage.error(
        t(
          'bizLimitCondition.departmentManageTable.completeTheFormContent',
          '请填写对应必填项',
        ),
      );
    }
  };
  // 编辑
  const handleEnableSwitch = async (row: ordertableConfig) => {
    if (row.orderFormTypeId) {
      return new Promise<void>(() => {
        ElMessageBox.confirm(
          t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
            action:
              row.enabledFlag === ENABLED_FLAG.YES
                ? t('global:disabled')
                : t('global:enabled'),
            name: row.orderFormTypeName,
          }),
          t('global:tip'),
          {
            confirmButtonText: t('global:confirm'),
            cancelButtonText: t('global:cancel'),
            type: 'warning',
          },
        )
          .then(async () => {
            const [, res] = await updateOrderFormTypeById({
              ...row,
              enabledFlag:
                row.enabledFlag === ENABLED_FLAG.YES
                  ? ENABLED_FLAG.NO
                  : ENABLED_FLAG.YES,
            });
            if (res?.success) {
              row.enabledFlag =
                row.enabledFlag === ENABLED_FLAG.YES
                  ? ENABLED_FLAG.NO
                  : ENABLED_FLAG.YES;
              ElMessage.success(
                t(
                  row.enabledFlag === ENABLED_FLAG.YES
                    ? 'global:enabled.success'
                    : 'global:disabled.success',
                ),
              );
            }
          })
          .catch(() => {});
      });
    } else {
      row.enabledFlag =
        row.enabledFlag === ENABLED_FLAG.YES
          ? ENABLED_FLAG.NO
          : ENABLED_FLAG.YES;
    }
  };
  // 删除
  const del = async (row: ordertableConfig) => {
    try {
      await ElMessageBox.confirm(
        t(
          'global:delete.confirm.message',
          '您确定要删除医嘱单据【{{name}}】吗？',
          {
            name: row.orderFormTypeName,
          },
        ),
        t('global:delete.confirm.title', '删除确认'),
        {
          confirmButtonText: t('global:confirm', '确定'),
          cancelButtonText: t('global:cancel', '取消'),
          type: 'warning',
        },
      );
      const params = {
        orderFormTypeId: row.orderFormTypeId || '',
      };

      const [, res] = await deleteOrderFormTypeById(params);
      if (res?.success) {
        ElMessage.success(t('address.execute.success', '执行成功'));
        queryData();
      }
    } catch {
      console.log('输出错误');
    }
  };
  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        label: t('global.sequenceNumber', '顺序'),
        minWidth: 100,
        prop: 'indexNo',
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('The name of the document type', '单据类型名称'),
        prop: 'orderFormTypeName',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('The name of the document type', '单据类型名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (row: {
          orderFormTypeName: string;
          editable: boolean;
          orderFormTypeId: string;
        }) => {
          const isAdd = !row.orderFormTypeId;
          return row.editable ? (
            <>
              <el-input
                disabled={!isAdd}
                v-model={row.orderFormTypeName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('The name of the document type', '单据类型名称'),
                })}
              ></el-input>
            </>
          ) : (
            <> {row.orderFormTypeName}</>
          );
        },
      },

      {
        label: t('Secondary name', '辅助名称'),
        prop: 'orderFormType2ndName',
        minWidth: 120,
        editable: true,
        render: (row: { orderFormType2ndName: string; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-input
                v-model={row.orderFormType2ndName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('Secondary name', '辅助名称'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.orderFormType2ndName}</>
          );
        },
      },

      {
        label: t('Extension name', '扩展名称'),
        prop: 'orderFormTypeExtName',
        minWidth: 120,
        editable: true,
        render: (row: { orderFormTypeExtName: string; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-input
                v-model={row.orderFormTypeExtName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('Extension name', '扩展名称'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.orderFormTypeExtName}</>
          );
        },
      },

      {
        label: t('Encoding rules', '编码规则'),
        prop: 'bizNoGenerateRuleId',
        minWidth: 120,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('Encoding rules', '编码规则'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (row: ordertableConfig) => {
          return row.editable ? (
            <>
              <el-select
                v-model={row.bizNoGenerateRuleId}
                filterable
                remote
                remote-method={(keyWord?: string) => {
                  codingRules(keyWord);
                }}
                onChange={(val: string) => {
                  const matchedRule = (
                    bizNoGenerateRuleDetailData?.value || []
                  ).find(
                    (item: bizNoparams) => item?.bizNoGenerateRuleId === val,
                  );
                  row.bizNoGenerateRuleName = matchedRule?.ruleName ?? '';
                }}
                placeholder={t('global:placeholder.select.template', {
                  name: t('Encoding rules', '编码规则'),
                })}
              >
                {(bizNoGenerateRuleDetailData.value || [])?.map(
                  (item: bizNoparams) => (
                    <el-option
                      key={item.bizNoGenerateRuleId}
                      label={item.ruleName}
                      value={item.bizNoGenerateRuleId}
                    />
                  ),
                )}
              </el-select>
            </>
          ) : (
            <>{row.bizNoGenerateRuleName}</>
          );
        },
      },
      {
        label: t('receiptName', '对应单据'),
        prop: 'receiptName',
        minWidth: 120,
        editable: true,
        render: (row: ordertableConfig) => {
          return row.editable ? (
            <>
              <el-select
                v-model={row.receiptId}
                filterable
                loading={receiptLoading.value}
                remote-show-suffix
                remote-method={async (keyWord?: string) => {
                  await getReceiptList(keyWord);
                }}
                onChange={(val: string) => {
                  const receiptData = (receiptList?.value || []).find(
                    (item: Receipt.ReceiptReqItem) => item?.receiptId === val,
                  );
                  row.receiptName = receiptData?.receiptName ?? '';
                }}
                placeholder={t('global:placeholder.select.template', {
                  name: t('receiptName', '对应单据'),
                })}
              >
                {(receiptList.value || [])?.map(
                  (item: Receipt.ReceiptReqItem) => (
                    <el-option
                      key={item.receiptId}
                      label={item.receiptName}
                      value={item.receiptId}
                    />
                  ),
                )}
              </el-select>
            </>
          ) : (
            <>{row.receiptName}</>
          );
        },
      },
      {
        label: t('contactInfo.contactInfoTable.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: ordertableConfig) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },

      {
        label: t('global:operation'),
        width: 180,
        render: (row: ordertableConfig, index: number) => {
          if (!row.editable) {
            return (
              <>
                <el-button
                  type="primary"
                  onClick={() => {
                    toggleEdit(row);
                  }}
                  link
                >
                  {t('global:edit')}
                </el-button>

                <el-button
                  type="primary"
                  onClick={() => {
                    del(row);
                  }}
                  link
                >
                  {t('global:delete')}
                </el-button>
              </>
            );
          } else {
            return (
              <>
                <el-button
                  type="primary"
                  onClick={() => {
                    cancelEdit(row, index);
                  }}
                  link
                >
                  {t('global:cancel')}
                </el-button>
                <el-button
                  type="primary"
                  v-loading={row.saving}
                  onClick={() => {
                    handelsave(row);
                  }}
                  link
                >
                  {t('global:save')}
                </el-button>
              </>
            );
          }
        },
      },
    ],
  });
  return tableColumns;
}
