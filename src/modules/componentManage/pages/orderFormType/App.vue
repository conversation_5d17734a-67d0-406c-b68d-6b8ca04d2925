<script lang="ts" setup>
  import { onMounted, ref, computed } from 'vue';
  import { DmlButton, ProForm, ProTable, Title, useFormConfig } from 'sun-biz';
  import { ElMessage } from 'element-sun';
  import { BIZ_ID_TYPE_CODE, ENABLED_FLAG } from '@/utils/constant.ts';
  import { useTranslation } from 'i18next-vue';
  import { useGetReceipt } from './hooks/useGetReceipt.ts';
  const { t } = useTranslation();
  import { useTableColumns } from './config/usetableColumns.tsx';
  import { queryOrderFormTypeByExample } from '@/modules/componentManage/api/order.ts';
  import {
    bizNoparams,
    orderformParams,
    ordertableConfig,
  } from '../../typings/orderFormType';
  import { queryBizNoGenerateRuleListByExample } from '@/modules/baseConfig/api/bizNoGenerateRule.ts';

  const {
    loading: receiptLoading,
    receiptList,
    getReceiptList,
  } = useGetReceipt();

  const orderTableRef = ref();
  const queryMsgSendWayListParams = ref<orderformParams>({
    enabledFlag: ENABLED_FLAG.ALL,
  });
  const orderList = ref<ordertableConfig[]>([]);
  const allOrderList = ref<ordertableConfig[]>([]);
  const bizNoGenerateRuleDetailData = ref<bizNoparams[]>();
  const addNewSendWay = async () => {
    const isEditing = orderList.value.some((item) => !!item.editable);

    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    } else {
      orderList.value.push({
        orderFormTypeId: '',
        receiptId: '',
        receiptName: '',
        orderFormTypeName: '',
        orderFormType2ndName: '',
        orderFormTypeExtName: '',
        enabledFlag: ENABLED_FLAG.YES,
        editable: true,
      });
    }
  };

  const loading = ref(false);
  const searchConfig = useorderform();

  const fetchAllOrderList = async () => {
    const [, res] = await queryOrderFormTypeByExample({}); // 不传条件查全部
    if (res?.success) {
      allOrderList.value = (res.data as ordertableConfig[]) || [];
    }
  };
  const orderdata = computed(() => {
    return allOrderList.value.map((item) => {
      return item.orderFormTypeId;
    });
  });
  function useorderform() {
    const enabledFlagList = [
      { value: ENABLED_FLAG.ALL, label: '全部' },
      { value: ENABLED_FLAG.YES, label: '启用' },
      { value: ENABLED_FLAG.NO, label: '停用' },
    ];
    const data = useFormConfig({
      getData: (t) => [
        {
          label: t('global:enabledFlag', '启用标志'),
          name: 'enabledFlag',
          component: 'select',
          triggerModelChange: true,
          placeholder: t('global:placeholder.select.template', {
            name: t('global:enabledFlag', '启用标志'),
          }),
          extraProps: {
            options: enabledFlagList,
            clearable: true,
            className: 'w-80',
          },
        },
      ],
    });
    return data;
  }

  const queryData = async () => {
    const params = {
      enabledFlag: queryMsgSendWayListParams.value.enabledFlag,
    };
    if (params.enabledFlag == -1) {
      delete params.enabledFlag;
    }
    const [, res] = await queryOrderFormTypeByExample(params);
    if (res?.success) {
      orderList.value = (res?.data as ordertableConfig[]) || [];
    }
  };
  //请求编码规则
  const codingRules = async (keyWord?: string) => {
    const [, res] = await queryBizNoGenerateRuleListByExample({
      enabledFlag: ENABLED_FLAG.YES,
      keyWord: keyWord,
    });
    if (res?.success) {
      bizNoGenerateRuleDetailData.value = res?.data;
    }
  };

  const tableColumns = useTableColumns(orderTableRef, orderList, queryData, {
    receiptLoading,
    receiptList,
    getReceiptList,
    bizNoGenerateRuleDetailData,
    codingRules,
  });
  const queryDataList = (data: orderformParams) => {
    if (data) {
      queryMsgSendWayListParams.value = {
        ...queryMsgSendWayListParams.value,
        ...data,
      };
      queryData();
    }
  };
  onMounted(async () => {
    await queryData();
    await fetchAllOrderList();
    await getReceiptList();
    await codingRules();
  });
</script>
<template>
  <div className="p-box flex h-full flex-col">
    <Title :title="$t('TheTypeOfOrderDocument', '医嘱单据类型')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          ref="proForm"
          @submit.prevent
          v-model="queryMsgSendWayListParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="queryDataList"
        />
      </div>
      <div>
        <el-button class="mr-2" type="primary" @click="addNewSendWay">
          {{ $t('global:add', '新增') }}
        </el-button>
        <DmlButton
          :disabled="false"
          :biz-data="orderdata"
          :code="BIZ_ID_TYPE_CODE.DICT_ORDER_FORM_TYPE"
        />
      </div>
    </div>
    <ProTable
      ref="orderTableRef"
      :columns="tableColumns"
      :data="orderList"
      :editable="true"
      :loading="loading"
      row-key="orderFormTypeId"
    />
  </div>
</template>
