export interface orderformParams {
  enabledFlag?: number;
}

export interface ordertableConfig {
  orderFormTypeId?: string;
  orderFormTypeName: string;
  orderFormType2ndName: string;
  orderFormTypeExtName: string;
  bizNoGenerateRuleId?: string;
  bizNoGenerateRuleName?: string;
  enabledFlag: number; // 统一为 number
  receiptId: string;
  receiptName: string;
  editable: boolean;
  saving?: boolean;
}
export interface bizNoparams {
  bizNoObjectDesc: string;
  bizNoGenerateRuleId: string;
  bizNoGenerateRuleName: string;
  ruleName: string;
}

export interface OrderDivideRuleContentParams {
  /** 医嘱分方规则标识 */
  orderDivideRuleId: number | string;
  /** 分方规则内容值 */
  divideRuleContentValue: string;
  /** 启用标志 */
  enabledFlag: number;
}

export interface OrderDivideRuleContentResult {
  /** 分方规则内容标识 */
  divideRuleContentId: number | string;
}

export interface DivideRuleContent {
  /** 分方规则内容标识 */
  divideRuleContentId: number | string;
  /** 分方规则内容值 */
  divideRuleContentValue: string;
  /** 分方规则内容值描述 */
  divideRuleContentValueDesc: string;
  /** 启用标志 */
  enabledFlag: number;
}

export interface OrderDivideRule {
  /** 医嘱分方规则标识 */
  orderDivideRuleId: number | string;
  /** 医嘱分方规则编码 */
  orderDivideRuleNo: string;
  /** 医嘱分方规则名称 */
  orderDivideRuleName: string;
  /** 医嘱分方规则类型代码 */
  orderDivideRuleTypeCode: string;
  /** 医嘱分方规则类型代码描述 */
  orderDivideRuleTypeCodeDesc: string;
  /** 医嘱分方规则说明 */
  orderDivideRuleDesc: string;
  /** 启用标志 */
  enabledFlag: number;
  /** 分方规则内容列表 */
  divideRuleContentList: DivideRuleContent[];
}

export interface OrderDivideRuleListParams {
  /** 医嘱分方规则列表 */
  orderDivideRuleList: OrderDivideRule[];
}
