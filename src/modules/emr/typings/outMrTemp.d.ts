declare namespace OutMrTemp {
  interface QueryMrTempTreeByExample {
    keyWord?: string;
    encounterTypeCode?: string;
  }

  interface MrTempTreeList {
    mrTempTreeId: string;
    mrTempTreeName: string;
    mrTempTreeParentId: string;
    mrTempTreeFlag: number;
    sort: number;
    enabledFlag: number;
    encounterTypeCode: string;
    encounterTypeCodeDesc: string;
    mrTemp: ManageObjectMrTemp;
  }

  interface ManageObjectMrTemp {
    mrTempId: string;
    mrTempName: string;
    medicalRecordTypeId: string;
    medicalRecordTypeName: string;
    enabledFlag: number;
    ownerTypeCode: string;
    ownerTypeCodeDesc: string;
    ownerId: string;
    ownerName: string;
    mrTempShareTypeCode: string;
    mrTempShareTypeCodeDesc: string;
    mrTempDesc: string;
    mrTempDisplayName: string;
    diseaseName: string;
    hospitalId: string;
    mrtempCategory: string | string[];
    mrtempSourceCode: string;
    mrtempSourceCodeDesc: string;
    mrtempSourceId: string;
    mrTempSharingList: MrTempSharingList[];
  }

  interface MrTempSharingList {
    mrTempSharingId: string;
    bizId: string;
    bizName: string;
    bizTypeCode: string;
    bizTypeCodeDesc: string;
  }

  interface UpsertMrTempTreeParams {
    mrTempTreeName?: string;
    mrTempTreeParentId?: string;
    encounterTypeCode?: string;
    mrTempTreeId?: string;
    enabledFlag?: number;
  }

  interface UpsertMrTempParams {
    mrTempName?: string;
    medicalRecordTypeId?: string;
    mrTempTreeParentId?: string;
    encounterTypeCode?: string;
    enabledFlag?: number;
    mrTempId?: string;
    mrTempContent?: string;
    ownerTypeCode?: string;
    ownerId?: string;
    mrTempShareTypeCode?: string;
    mrTempDesc?: string;
    mrTempDisplayName?: string;
    diseaseName?: string;
    shareBizIds?: string[];
    mrtempCategory: string | string[];
    mrtempSourceCode?: string;
    mrtempSourceId?: string;
    mrTempTreeId?: string;
    mrTempSharingList?: MrTempSharingList[];
  }

  interface MrTempList {
    mrTempId: string;
    mrTempName: string;
    medicalRecordTypeId: string;
    medicalRecordTypeName: string;
    mrTempContent: MrTempScopeList[];
  }

  interface MrTempScopeList {
    mrTempScopeId: string;
    mrTempScopeCode: string;
    mrTempScopeCodeDesc: string;
  }
}
