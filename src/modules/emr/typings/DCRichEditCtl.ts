/** 通用类型定义 */

export interface DCRichEditCtl extends HTMLElement {
  [key: string]: unknown;
  DCExecuteCommand: (
    commandName: string,
    showUI: boolean,
    args: unknown,
  ) => void;
  Readonly: boolean;
  CurrentTableCell: () => unknown;
  cellGridlineDialog: (settings: unknown, eventSender: DCRichEditCtl) => void;
  cellDiagonalLineDialog: (cell: unknown, eventSender: DCRichEditCtl) => void;
  bordersShadingDialog: () => void;
  borderShadingcellDialog: () => void;
  IsPrintPreview: () => boolean;
  GetCommandStatus: (commandName: string) => {
    Supported: boolean;
    Enabled: boolean;
    Checked: boolean;
  };
}
