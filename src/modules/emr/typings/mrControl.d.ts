declare namespace MrControl {
  interface MrCtrlTreeItem {
    mrCtrlTreeId: string;
    mrCtrlTreeName: string;
    mrCtrlTreeParentId: string;
    mrCtrlFlag: number;
    mrControl?: MrControlInfo;
  }

  interface MrControlInfo {
    mrControlId: string;
    mrControlName: string;
    mrControlTypeCode: string;
    mrControlTypeCodeDesc: string;
    editableFlag: number;
    ownerTypeCode: string;
    ownerTypeCodeDesc: string;
    ownerId: string;
    enabledFlag: number;
    mrControlContent: string;
  }

  interface MrControlTreeUpsertParams {
    mrCtrlTreeName: string;
    mrCtrlTreeParentId?: string;
    mrCtrlTreeId?: string; // 编辑
  }

  interface MrControlUpsertParams {
    mrControlId?: string; // 编辑
    mrControlName: string;
    mrCtrlTreeParentId?: string;
    mrControlTypeCode: string;
    ownerTypeCode?: string;
    ownerId?: string;
    enabledFlag: number;
    mrControlContent?: string;
    editableFlag: number;
  }

  interface TreeDataItem extends MrCtrlTreeItem {
    label: string;
    value: string;
    children: (MrCtrlTreeItem & {
      label: string;
      value: string;
      children: TreeDataItem[];
    })[];
  }
}
