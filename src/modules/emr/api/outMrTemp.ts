import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10535-1]根据标识查询病历模板V1（50病历模板调用）
 * @param params
 * @returns
 */
export const queryMrTempByIdV1 = (params: {
  mrTempIds: string[];
  includeContentFlag: number;
}) => {
  return dictRequest<ManageTemplate.MrTempItem[]>(
    '/medicalRecordTemp/queryMrTempByIdV1',
    params,
  );
};

/**
 * [1-10525-1] 根据条件查询病历模板树
 * @param params
 * @returns
 */
export const queryMrTempTreeByExample = (
  params: OutMrTemp.QueryMrTempTreeByExample,
) => {
  return dictRequest<OutMrTemp.MrTempTreeList[]>(
    '/medicalRecordTemp/queryMrTempTreeByExample',
    params,
  );
};
/**
 * [1-10526-1] 新增病历模板树
 * @param params
 * @returns
 */
export const addMrTempTree = (params: OutMrTemp.UpsertMrTempTreeParams) => {
  return dictRequest<{ mrTempTreeId: string }>(
    '/medicalRecordTemp/addMrTempTree',
    params,
    {
      successMsg: translation('global:add.success'),
    },
  );
};
/**
 * [1-10528-1] 编辑病历模板树
 * @param params
 * @returns
 */
export const editMrTempTree = (params: OutMrTemp.UpsertMrTempTreeParams) => {
  return dictRequest('/medicalRecordTemp/editMrTempTree', params, {
    successMsg: translation('global:edit.success'),
  });
};

/**
 * [1-10530-1] 删除病历模板树
 * @param params
 * @returns
 */
export const deleteMrTempTree = (params: OutMrTemp.UpsertMrTempTreeParams) => {
  return dictRequest('/medicalRecordTemp/deleteMrTempTree', params, {
    successMsg: translation('global:delete.success'),
  });
};

/**
 * [1-10527-1] 新增病历模板
 * @param params
 * @returns
 */
export const addMrTemp = (params: OutMrTemp.UpsertMrTempParams) => {
  return dictRequest<{
    mrTempTreeId: string;
    mrTempId: string;
  }>('/medicalRecordTemp/addMrTemp', params, {
    successMsg: translation('global:add.success'),
  });
};

/**
 * [1-10529-1] 编辑病历模板
 * @param params
 * @returns
 */
export const editMrTemp = (params: OutMrTemp.UpsertMrTempParams) => {
  return dictRequest('/medicalRecordTemp/editMrTemp', params, {
    successMsg: translation('global:edit.success'),
  });
};

/**
 * [1-10532-1] 保存病历模板内容
 * @param params
 * @returns
 */
export const saveMrTempContent = (params: OutMrTemp.UpsertMrTempParams) => {
  return dictRequest('/medicalRecordTemp/saveMrTempContent', params, {
    successMsg: translation('global:save.success'),
  });
};
