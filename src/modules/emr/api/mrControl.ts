import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10543-1] 根据条件查询病历组件树
 * 是否分页	N
 * @param params
 * @returns
 */
export const queryMrControlTreeByExample = (params: { keyWord?: string }) => {
  return dictRequest<MrControl.MrCtrlTreeItem[]>(
    '/MrControl/queryMrControlTreeByExample',
    params,
  );
};

/**
 * [1-10544-1] 新增病历组件树
 * @param params
 * @returns
 */
export const addMrControlTree = (
  params: MrControl.MrControlTreeUpsertParams,
) => {
  return dictRequest<{ mrCtrlTreeId: string }>(
    '/MrControl/addMrControlTree',
    params,
    {
      successMsg: translation('global:add.success'),
    },
  );
};

/**
 * [1-10545-1] 编辑病历组件树
 * @param params
 * @returns
 */
export const editMrControlTree = (
  params: MrControl.MrControlTreeUpsertParams,
) => {
  return dictRequest<{ mrCtrlTreeId: string }>(
    '/MrControl/editMrControlTree',
    params,
    {
      successMsg: translation('global:edit.success'),
    },
  );
};

/**
 * [1-10546-1] 新增病历组件
 * @param params
 * @returns
 */
export const addMrControl = (params: MrControl.MrControlUpsertParams) => {
  return dictRequest<{ mrControlId: string }>(
    '/MrControl/addMrControl',
    params,
    {
      successMsg: translation('global:add.success'),
    },
  );
};

/**
 * [1-10547-1] 编辑病历组件
 * @param params
 * @returns
 */
export const editMrControl = (params: MrControl.MrControlUpsertParams) => {
  return dictRequest('/MrControl/editMrControl', params, {
    successMsg: translation('global:edit.success'),
  });
};

/**
 * [1-10548-1] 删除病历组件树
 * @param params
 * @returns
 */
export const deleteMrControlTree = (params: { mrCtrlTreeId: string }) => {
  return dictRequest('/MrControl/deleteMrControlTree', params, {
    successMsg: translation('global:delete.success'),
  });
};
