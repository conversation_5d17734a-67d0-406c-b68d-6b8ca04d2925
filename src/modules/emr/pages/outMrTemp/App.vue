<script lang="ts" setup>
  import { useTranslation } from 'i18next-vue';
  import {
    MAIN_APP_CONFIG,
    ProForm,
    queryParamListByNos,
    useAppConfigData,
    useFetchDataset,
  } from 'sun-biz';
  import { computed, onMounted, ref, watch } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import {
    deleteMrTempTree,
    queryMrTempByIdV1,
    queryMrTempTreeByExample,
    saveMrTempContent,
  } from '@/modules/emr/api/outMrTemp';
  import { commonSort } from '@/api/common';
  import {
    BIZ_ID_TYPE_CODE,
    ENABLED_FLAG,
    ENCOUNTER_TYPE_CODE_NAME,
    FLAG,
    MR_TEMP_SCOPE_CODE_NAME,
    MR_TEMP_SHARE_TYPE_CODE_NAME,
    MR_TEMP_SOURCE_CODE_NAME,
  } from '@/utils/constant';
  import { useTreeSearchFormConfig } from './config/useFormConfig';
  import EmrEditor from './components/EmrEditor.vue';
  import AddMrNodeDialog from './components/addMrNodeDialog.vue';
  import AddMrTempDialog from './components/addMrTempDialog.vue';
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import emptyImage from '@/modules/emr/pages/outMrTemp/assets/emptyImage.png';

  const isProd = process.env.NODE_ENV === 'production';

  const { t } = useTranslation();
  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);

  const dataSet = useFetchDataset([
    ENCOUNTER_TYPE_CODE_NAME,
    MR_TEMP_SCOPE_CODE_NAME,
    MR_TEMP_SHARE_TYPE_CODE_NAME,
    MR_TEMP_SOURCE_CODE_NAME,
  ]);

  const optionsMap = computed(() => {
    const map = {
      // 就诊类型（使用范围）
      encounterTypeList: (dataSet?.value?.[ENCOUNTER_TYPE_CODE_NAME] || []).map(
        (item) => ({
          value: item?.dataValueNo,
          label: item?.dataValueNameDisplay,
        }),
      ),
      // 病历模板应用范围
      mrTempScopeList: (dataSet?.value?.[MR_TEMP_SCOPE_CODE_NAME] || []).map(
        (item) => ({
          value: item?.dataValueNo,
          label: item?.dataValueNameDisplay,
        }),
      ),
      // 病历模板共享类型
      mrTempShareTypeList: (
        dataSet?.value?.[MR_TEMP_SHARE_TYPE_CODE_NAME] || []
      ).map((item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueNameDisplay,
      })),
      // 病历模板来源
      mrTempSourceList: (dataSet?.value?.[MR_TEMP_SOURCE_CODE_NAME] || []).map(
        (item) => ({
          value: item?.dataValueNo,
          label: item?.dataValueNameDisplay,
        }),
      ),
    };
    return map;
  });

  const addMrNodeDialogRef = ref<InstanceType<typeof AddMrNodeDialog> | null>(
    null,
  );
  const addMrTempDialogRef = ref<InstanceType<typeof AddMrTempDialog> | null>(
    null,
  );
  const emrEditorRef = ref<InstanceType<typeof EmrEditor> | null>(null);
  const searchParams = ref<OutMrTemp.QueryMrTempTreeByExample>({
    keyWord: '',
    encounterTypeCode: '1',
  });
  const mrTempTreeData = ref<OutMrTemp.MrTempTreeList[]>([]);
  const mrTempOriginalData = ref<OutMrTemp.MrTempTreeList[]>([]);
  const selectedNode = ref<OutMrTemp.MrTempTreeList | null>(null);
  const mrTempDetail = ref<ManageTemplate.MrTempItem | null>(null);
  const isEditMode = ref<boolean>(false); // 编辑模式状态
  const showEmr = ref<boolean>(true); // 编辑模式状态
  const searchConfig = useTreeSearchFormConfig();
  const handleModelChange = (data: OutMrTemp.QueryMrTempTreeByExample) => {
    searchParams.value = {
      ...searchParams.value,
      ...data,
    };
    queryMrTempTree();
  };
  const registerCode = ref<string>('');
  const splitValue = ref([25, 75]);
  const handleResize = (sizes: number[]) => {
    splitValue.value = sizes;
  };
  // 先根据 sort 字段排序，再构建树形结构
  const buildTreeStructure = (data: OutMrTemp.MrTempTreeList[]) => {
    // 先根据 sort 字段排序（升序，sort 越小越靠前）
    const sortedData = [...data].sort((a, b) => {
      // sort 可能为 undefined，默认排到最后
      const sortA =
        typeof a.sort === 'number' ? a.sort : Number.MAX_SAFE_INTEGER;
      const sortB =
        typeof b.sort === 'number' ? b.sort : Number.MAX_SAFE_INTEGER;
      return sortA - sortB;
    });

    // 构建所有节点的 Map
    const itemMap = new Map<
      string,
      OutMrTemp.MrTempTreeList & {
        children?: OutMrTemp.MrTempTreeList[];
        displayName?: string;
        isTemplate?: boolean;
      }
    >();

    // 初始化所有节点
    sortedData.forEach((item) => {
      if (!item.mrTempTreeId) return;
      // 标记模板节点
      const isTemplate = item.mrTempTreeFlag === FLAG.YES;
      const displayName = isTemplate
        ? `${item.mrTempTreeName} 【模板】`
        : item.mrTempTreeName;
      // 初始化 children 为空数组
      itemMap.set(item.mrTempTreeId, {
        ...item,
        children: [],
        displayName,
        isTemplate,
      });
    });

    // 构建树结构
    const rootItems: (OutMrTemp.MrTempTreeList & {
      children?: OutMrTemp.MrTempTreeList[];
      displayName?: string;
      isTemplate?: boolean;
    })[] = [];

    // 再次遍历排序后的数据，保证 children 顺序
    sortedData.forEach((item) => {
      if (!item.mrTempTreeId) return;
      const current = itemMap.get(item.mrTempTreeId);
      if (!current) return;
      if (
        item.mrTempTreeParentId === null ||
        item.mrTempTreeParentId === undefined
      ) {
        rootItems.push(current);
      } else {
        const parent = itemMap.get(item.mrTempTreeParentId);
        if (parent) {
          parent.children = parent.children || [];
          parent.children.push(current);
        }
      }
    });

    return rootItems;
  };

  // 获取接口数据时，先根据 sort 排序再构建树
  const queryMrTempTree = async () => {
    mrTempTreeData.value = [];
    mrTempOriginalData.value = [];
    const [, res] = await queryMrTempTreeByExample(searchParams.value);
    if (res?.success) {
      // 保存原始数据
      mrTempOriginalData.value = res.data;
      // 先根据 sort 排序再构建树形结构
      const treeStructure = buildTreeStructure(res.data);
      mrTempTreeData.value = treeStructure;
    }
  };
  // 树节点选择事件
  const handleNodeClick = async (data: OutMrTemp.MrTempTreeList) => {
    // if (data.mrTempTreeId === selectedNode.value?.mrTempTreeId) {
    //   selectedNode.value = null; // 取消选中
    //   mrTempDetail.value = null;
    //   isEditMode.value = false; // 重置编辑模式
    //   return;
    // }
    selectedNode.value = data;
    isEditMode.value = false; // 重置编辑模式
    showEmr.value = true; // 重置显示编辑器 以防万一  可以设置为 false，重新渲染编辑器，现在会比较慢，现在做法是空的赋值一个空 xml

    console.log(data, '点击');
    if (data.mrTempTreeFlag === FLAG.YES) {
      mrTempDetail.value = null; // 清除模板详情
      const params = {
        mrTempIds: [data.mrTemp?.mrTempId || ''],
        includeContentFlag: FLAG.YES,
      };
      const [, res] = await queryMrTempByIdV1(params);
      if (res?.success && res.data && res.data.length > 0) {
        mrTempDetail.value = res.data[0];
      }
      showEmr.value = true;
    } else {
      mrTempDetail.value = null; // 清除模板详情
    }
  };

  // 添加子节点
  const openAddMrNodeDialog = () => {
    if (addMrNodeDialogRef.value) {
      if (
        selectedNode.value &&
        selectedNode.value.mrTempTreeFlag === FLAG.YES
      ) {
        ElMessage.warning(t('只有节点可以新增节点！'));
        return;
      } else {
        addMrNodeDialogRef.value.openDialog('child');
      }
    }
  };
  // 添加同级节点
  const openAddSameMrNodeDialog = () => {
    if (!addMrNodeDialogRef.value) return;
    if (selectedNode.value && selectedNode.value.mrTempTreeFlag === FLAG.YES) {
      ElMessage.warning(t('只有节点可以新增节点！'));
      return;
    } else {
      addMrNodeDialogRef.value.openDialog('sibling');
    }
  };
  // 新增模板
  const openAddMrTemp = () => {
    if (!addMrTempDialogRef.value) return;
    if (selectedNode.value && selectedNode.value.mrTempTreeFlag === FLAG.YES) {
      ElMessage.warning(t('只有节点可以新增模板！'));
      return;
    } else {
      addMrTempDialogRef.value.open();
    }
  };
  // 编辑节点或模板
  const openEditDialog = (flag?: string) => {
    if (!selectedNode.value) {
      return;
    }
    console.log(selectedNode.value, 'selectedNode.value');
    if (selectedNode.value.mrTempTreeFlag === FLAG.NO) {
      // 编辑节点，使用 AddMrNodeDialog
      if (addMrNodeDialogRef.value) {
        addMrNodeDialogRef.value.openDialog('edit');
      }
    } else if (selectedNode.value.mrTempTreeFlag === FLAG.YES) {
      // 编辑模板，使用 AddMrTempDialog
      if (addMrTempDialogRef.value && mrTempDetail.value) {
        // 构造编辑模板的数据

        const editData: OutMrTemp.UpsertMrTempParams = {
          mrTempTreeId: selectedNode.value.mrTempTreeId,
          mrTempId: mrTempDetail.value.mrTempId,
          mrTempName: mrTempDetail.value.mrTempName,
          mrTempTreeParentId: selectedNode.value.mrTempTreeParentId,
          medicalRecordTypeId: mrTempDetail.value.medicalRecordTypeId,
          mrTempDesc: selectedNode.value.mrTemp.mrTempDesc,
          encounterTypeCode: selectedNode.value.encounterTypeCode,
          enabledFlag: ENABLED_FLAG.YES, // 默认启用
          ownerTypeCode: selectedNode.value.mrTemp.ownerTypeCode,
          ownerId: selectedNode.value.mrTemp.ownerId,
          mrTempShareTypeCode: selectedNode.value.mrTemp.mrTempShareTypeCode,
          mrTempDisplayName: selectedNode.value.mrTemp.mrTempDisplayName,
          diseaseName: selectedNode.value.mrTemp.diseaseName,
          shareBizIds: selectedNode.value.mrTemp.shareBizIds || [],
          mrTempSharingList: selectedNode.value.mrTemp.mrTempSharingList || [],
          mrtempCategory: selectedNode.value.mrTemp.mrtempCategory || '',
          mrtempSourceCode: selectedNode.value.mrTemp.mrtempSourceCode,
          mrtempSourceId: selectedNode.value.mrTemp.mrtempSourceId,
          mrTempContent: mrTempDetail.value.mrTempContent,
        };
        console.log('editData--', editData);
        addMrTempDialogRef.value.open(
          flag,
          editData, // 传递要编辑的数据
        );
      }
    }
  };
  // 删除模板或节点
  const onDeleteTempOrNode = async () => {
    ElMessageBox.confirm(
      t('outMrTemp.delete', '您确定要删除“{{name}}”吗', {
        name: `${selectedNode.value?.mrTempTreeName}`,
      }),
      t('global:tip', '提示'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        mrTempTreeId: selectedNode.value?.mrTempTreeId || '',
      };
      const [, res] = await deleteMrTempTree(params);
      if (res?.success) {
        selectedNode.value = null;
        queryMrTempTree();
      }
    });
  };
  // 打开设计
  const onHandleDesign = () => {
    if (!selectedNode.value) {
      ElMessage.warning(t('outMrTemp.selectNode', '请选择模板'));
      return;
    }

    if (selectedNode.value.mrTempTreeFlag !== FLAG.YES) {
      ElMessage.warning(
        t('outMrTemp.selectTemplate', '请选择模板节点进行设计'),
      );
      return;
    }

    // 开启编辑模式（即使 mrTempDetail 为空也可以编辑）
    isEditMode.value = true;
  };

  // 处理取消编辑
  const handleCancelEdit = () => {
    isEditMode.value = false;
  };

  // 从头部按钮触发保存
  const handleSaveFromHeader = async () => {
    if (
      emrEditorRef.value &&
      typeof emrEditorRef.value.handleSave === 'function'
    ) {
      const content = emrEditorRef.value.handleSave();
      const templateId = mrTempDetail.value?.mrTempId;
      const params = {
        mrTempId: templateId,
        mrTempContent: content,
      };

      const [, res] = await saveMrTempContent(params);
      if (res?.success) {
        isEditMode.value = false;
      }
    } else {
      ElMessage.error('编辑器未准备就绪，请稍后再试');
    }
  };
  // 处理新增成功后的回调
  const handleAddSuccess = async (newNodeId?: string) => {
    await queryMrTempTree();

    // 如果有新节点ID，自动选中该节点
    if (newNodeId && mrTempOriginalData.value.length > 0) {
      const newNode = mrTempOriginalData.value.find(
        (item) => item.mrTempTreeId === newNodeId,
      );
      if (newNode) {
        // 自动选中新创建的节点
        await handleNodeClick(newNode);
      } else {
        showEmr.value = false; // 如果没有找到新节点，隐藏编辑器
      }
    } else {
      showEmr.value = false; // 如果没有找到新节点，隐藏编辑器」
    }
  };
  // 允许拖拽的判断函数
  const allowDrop = (
    draggingNode: { data: OutMrTemp.MrTempTreeList },
    dropNode: { data: OutMrTemp.MrTempTreeList },
    type: 'prev' | 'inner' | 'next',
  ) => {
    // 只允许在同一层级内进行排序（prev 和 next）
    // 不允许拖拽到其他节点内部（inner）
    if (type === 'inner') {
      return false;
    }

    // 确保拖拽节点和目标节点在同一层级
    const draggingParentId = draggingNode.data.mrTempTreeParentId;
    const dropParentId = dropNode.data.mrTempTreeParentId;

    // 如果父级ID不同，说明不在同一层级，不允许拖拽
    if (draggingParentId !== dropParentId) {
      return false;
    }

    // 只允许在同一层级内进行排序
    return type === 'prev' || type === 'next';
  };

  // 处理拖拽排序完成后的逻辑
  const handleNodeDrop = async (
    draggingNode: unknown,
    dropNode: unknown,
    dropType: string,
  ) => {
    console.log(draggingNode, dropNode, dropType);
    // 直接用树形结构平铺后的顺序作为新的排序
    const flatList = generateSortedList(mrTempTreeData.value);

    // 直接用平铺后的顺序作为sort
    const bizIdList = flatList.map((item, index) => ({
      bizId: item.mrTempTreeId,
      sort: index + 1,
    }));

    const [, res] = await commonSort({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_MR_TEMP_TREE,
      bizIdList,
    });
    if (res?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success'),
      });
      await queryMrTempTree();
    }
  };

  // 递归遍历树形结构，生成平铺的列表，顺序即为树结构顺序
  const generateSortedList = (
    treeData: (OutMrTemp.MrTempTreeList & {
      children?: OutMrTemp.MrTempTreeList[];
      displayName?: string;
      isTemplate?: boolean;
    })[],
    parentId: string | undefined = undefined,
    level: number = 0,
  ): (OutMrTemp.MrTempTreeList & { sort?: number; level?: number })[] => {
    let result: (OutMrTemp.MrTempTreeList & {
      sort?: number;
      level?: number;
    })[] = [];

    treeData.forEach((node, index) => {
      // 当前节点的排序信息
      const sortedNode: OutMrTemp.MrTempTreeList & {
        sort?: number;
        level?: number;
      } = {
        mrTempTreeId: node.mrTempTreeId,
        mrTempTreeName: node.mrTempTreeName,
        mrTempTreeParentId: parentId ?? '',
        mrTempTreeFlag: node.mrTempTreeFlag,
        sort: index + 1,
        level: level,
        encounterTypeCode: node.encounterTypeCode,
        enabledFlag: node.enabledFlag,
        encounterTypeCodeDesc: node.encounterTypeCodeDesc,
        mrTemp: node.mrTemp,
      };

      result.push(sortedNode);

      // 如果有子节点，递归处理
      if (node.children && node.children.length > 0) {
        const childNodes = generateSortedList(
          node.children,
          node.mrTempTreeId,
          level + 1,
        );
        result = result.concat(childNodes);
      }
    });

    // 不再在这里console.log
    return result;
  };

  // 加载外部js
  function loadScript(src: string) {
    return new Promise<void>((resolve, reject) => {
      if (document.querySelector(`script[src="${src}"]`)) {
        return resolve();
      }
      const script = document.createElement('script');
      script.src = src;
      script.onload = () => {
        resolve();
      };
      script.onerror = (e) => {
        reject(e);
      };
      document.head.appendChild(script);
    });
  }

  const loadEmrEditor = async () => {
    // 1. 获取编辑器服务
    const list =
      (await queryParamListByNos({
        hospitalId: currentOrg?.orgId || '',
        paramNos: ['9007', '9010'],
      })) || [];
    let ServicePage = '';
    list.forEach((item) => {
      if (item?.data?.[0]?.paramNo === '9007') {
        ServicePage = item.data[0].paramSettingList[0].paramValue;
      }
      if (item?.data?.[1]?.paramNo === '9010') {
        registerCode.value = item.data[1].paramSettingList[0].paramValue;
        registerCode.value = item.data[1].paramSettingList[0].paramValue ?? '';
        if (!registerCode.value) {
          console.error('[EmrEditor] 编辑器注册码 未配置');
        }
      }
    });
    if (!ServicePage) {
      ElMessageBox.alert('请先配置参数9007', '提示', {
        confirmButtonText: '确定',
        callback: () => {},
      });
      return;
    }
    // 2. 加载jQuery和编辑器js
    const Jquery = `${isProd ? '/web-dict' : ''}/js/jquery-1.7.2.min.js`;
    await loadScript(Jquery);
    await loadScript(ServicePage);
    // 重新赋值 Jquery 到全局 window 对象
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    window.rawWindow.jQuery = window.jQuery;
  };

  onMounted(() => {
    queryMrTempTree();
    loadEmrEditor();
  });

  watch(
    () => selectedNode.value,
    (selectedNode) => {
      console.log(selectedNode, 'selectedNode');
    },
    { immediate: true },
  );
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div
      :class="isEditMode ? 'justify-between' : 'justify-start'"
      class="mt-3 flex"
    >
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          :data="searchConfig"
          :show-search-button="true"
          layout-mode="inline"
          @model-change="handleModelChange"
        />
      </div>
      <div class="ml-3 flex justify-end">
        <!-- 非编辑模式时显示的操作按钮 -->
        <template v-if="!isEditMode">
          <el-button
            v-permission="`XZZML`"
            :disabled="
              !mrTempTreeData.length ||
              selectedNode?.mrTempTreeFlag === FLAG.YES
            "
            type="primary"
            @click="openAddMrNodeDialog()"
          >
            {{ $t('outMrTemp.addChildNode', '添加子节点') }}
          </el-button>
          <el-button
            v-permission="`TJTJJD`"
            :disabled="selectedNode?.mrTempTreeFlag === FLAG.YES"
            type="primary"
            @click="openAddSameMrNodeDialog()"
          >
            {{ $t('outMrTemp.addSameNode', '添加同级节点') }}
          </el-button>
          <el-button
            v-permission="`XZMZBLMB`"
            :disabled="
              !selectedNode || selectedNode.mrTempTreeFlag === FLAG.YES
            "
            type="primary"
            @click="openAddMrTemp()"
          >
            {{ $t('outMrTemp.addMrTemp', '新增模板') }}
          </el-button>
          <el-button
            v-permission="`SJMZBLMB`"
            :disabled="
              !selectedNode || selectedNode.mrTempTreeFlag !== FLAG.YES
            "
            type="primary"
            @click="onHandleDesign()"
          >
            {{ $t('outMrTemp.design', '设计模板') }}
          </el-button>
          <el-button
            v-permission="`KLMZBLMB`"
            :disabled="
              !selectedNode || selectedNode.mrTempTreeFlag !== FLAG.YES
            "
            type="primary"
            @click="openEditDialog('copy')"
          >
            {{ $t('outMrTemp. copy', '克隆模板') }}
          </el-button>
          <el-button
            v-permission="`BJMZBLMBS`"
            :disabled="!selectedNode"
            type="primary"
            @click="openEditDialog('edit')"
          >
            {{ $t('global:edit') }}
          </el-button>
          <el-button
            v-permission="`SCMZBLMBS`"
            :disabled="!selectedNode"
            type="danger"
            @click="onDeleteTempOrNode()"
          >
            {{ $t('global:delete') }}
          </el-button>
        </template>

        <!-- 编辑模式时显示的保存和取消按钮 -->
        <template v-else>
          <div>
            <el-button type="primary" @click="handleSaveFromHeader()">
              {{ $t('global:save', '保存') }}
            </el-button>
            <el-button plain type="primary" @click="handleCancelEdit()">
              {{ $t('global:cancel', '取消') }}
            </el-button>
          </div>
        </template>
      </div>
    </div>
    <Splitpanes
      v-model="splitValue"
      class="default-theme p-box"
      style="height: calc(100vh - 170px)"
      @resize="handleResize"
    >
      <Pane
        :size="splitValue[0]"
        class="flex flex-col"
        style="background-color: #fff"
      >
        <el-tree
          ref="treeRef"
          :allow-drop="allowDrop"
          :data="mrTempTreeData"
          :expand-on-click-node="false"
          :props="{
            children: 'children',
            label: 'mrTempTreeName', // 保持使用原始名称，因为我们现在用自定义模板
          }"
          default-expand-all
          draggable
          highlight-current
          @node-click="handleNodeClick"
          @node-drop="handleNodeDrop"
        >
          <template #default="{ data }">
            <span class="tree-node-content">
              <span
                :class="data.isTemplate ? 'template-node' : 'folder-node'"
                class="node-icon mr-1"
              >
                {{ data.isTemplate ? '📄' : '📁' }}
              </span>
              <span>{{ data.mrTempTreeName }}</span>
              <el-tag
                v-if="data.isTemplate"
                class="ml-2"
                size="small"
                type="primary"
              >
                模板
              </el-tag>
            </span>
          </template>
        </el-tree>
      </Pane>
      <Pane
        :size="splitValue[1]"
        class="flex h-full flex-col justify-center"
        style="background-color: #fff"
      >
        <el-scrollbar class="pb-1 pr-1.5" style="height: 100%">
          <EmrEditor
            v-show="
              selectedNode &&
              selectedNode.mrTempTreeFlag === FLAG.YES &&
              showEmr
            "
            ref="emrEditorRef"
            :is-readonly="!isEditMode"
            :medical-record-content="
              (typeof mrTempDetail?.mrTempContent === 'string'
                ? mrTempDetail.mrTempContent
                : '') || ''
            "
            :register-code="registerCode"
            :template-id="mrTempDetail?.mrTempId || ''"
            :template-name="mrTempDetail?.mrTempName || ''"
            @cancel="handleCancelEdit"
            @save="handleSaveFromHeader"
          />
          <div
            v-show="
              !selectedNode ||
              selectedNode.mrTempTreeFlag !== FLAG.YES ||
              !showEmr
            "
            class="flex h-full items-center justify-center"
          >
            <div v-if="selectedNode">
              <el-empty
                :description="$t('outMrTemp.pleaseSelectMRT', '请选择病历模板')"
                :image="emptyImage"
                :image-size="400"
              />
            </div>
            <el-empty v-else description="暂无数据" />
          </div>
        </el-scrollbar>
      </Pane>
    </Splitpanes>
    <AddMrNodeDialog
      ref="addMrNodeDialogRef"
      :options-map="optionsMap"
      :original-data="mrTempOriginalData"
      :selected-node="selectedNode"
      @success="queryMrTempTree"
    />
    <AddMrTempDialog
      ref="addMrTempDialogRef"
      :options-map="optionsMap"
      :original-data="mrTempOriginalData"
      :selected-node="selectedNode"
      @success="handleAddSuccess"
    />
  </div>
</template>

<style scoped>
  .tree-node-content {
    display: flex;
    flex: 1;
    align-items: center;
  }

  .node-icon {
    margin-right: 8px;
    font-size: 16px;
  }

  .template-node {
    color: #409eff;
  }

  .folder-node {
    color: #67c23a;
  }
</style>
