/*
 * @Author: lxy <EMAIL>
 * @Date: 2024-12-23 09:35:10
 * @LastEditors: lxy <EMAIL>
 * @LastEditTime: 2024-12-30 15:19:09
 * @FilePath: \20241223_Vue3\language\zh.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export default {
  newFunction: '新功能',
  PureFrontFramework: '纯前端框架',
  documentation: '技术文档',
  onlineDemo: '在线演示',
  onlineTools: '在线工具',
  technicalSupport: '技术支持',
  downloadPurchase: '下载/购买',
  updateLog: '更新日志',
  templateDesignTool: '模板设计工具',
  temperatureSheetDesignTool: '体温单设计工具',
  mobileFormDesignTool: '移动表单设计工具',
  textEditor: '专业的结构化文本编辑器',
  features1: '纯前端、基于Wasm+Canvas、类word、案例丰富',
  features: '【创新体验】纯前端Wasm+Canvas，类Word，海量案例任您探索！',
  getStarted: '开始体验',
  quickIntegration: '快速集成',
  integrationService:
    'DCWriter提供一键启动的静态资源服务（支持JAVA或.net core），1分钟内完成基础集成',
  quickGuide: '点我进入“快速集成指南”',
  frontendMode: '纯前端运行',
  frontendCall: '支持静态资源纯前端调用，浏览器运行，无需安装任何插件依赖',
  browserCompatibility: '浏览器兼容说明',
  nativeJS: '原生JS开发',
  mainstreamCompatibility:
    '底层完全使用原生js开发，兼容主流开发平台，提供vue2、vue3、react等集成示例Demo',
  demoDownload: '点我进入“Demo下载”',
  strongProductivity: '产品力强',
  responseTeam: '强大的支撑团队，能够快速响应客户需求、市场需求等',
  customerConsultation: '点我进入“客户咨询”',
  powerfulAndStable: '功能强大稳定',
  builtInAPIs:
    'DCWriter5.0内置1000+丰富的功能api，满足各行各业对于结构化文书的需求，现已用于医疗电子病历、政府文书、工业报告等行业',
  functionList: '点我进入“功能列表”',
  richComponents: '结构化组件丰富',
  customComponents:
    '提供数十种结构化元素，提供丰富的结构化录入方式，并支持用户自定义结构化组件录入',
  structuredElementsDemo: '点我进入“功能演示-结构化元素”',
  printingFunction: '打印功能强大',
  printingModes:
    '提供完整的打印功能，支持复杂的混合合并打印模式，提供本地打印插件，实现静默打印等，跳出浏览器打印限制',
  printingDemo: '点我进入“功能演示-打印功能”',
  wordLikeOperation: '类word操作',
  operationConsistency:
    'DCWriter5.0所有基本文书操作与word保持一致，降低用户操作难度，所见即所得的流式排版是所有文书类报告的必备需求',
  performance: '性能优越',
  documentLoading:
    'DCWriter5.0能够实现百页文档内的秒开，能够实现50页内文档2s内打印，大文档加载快且编辑不卡顿',
  performanceValidationDemo: '点我进入“功能演示-性能验证”',
  longLifecycle: '生命周期持久',
  developmentHistory:
    'DCWriter从2012年发展至今以10余年历史，历经cs版、ie版、asp.net版、html5版、canvas版等，不断更新迭代，追求“软件及服务”理念',
  versionHistory: '点我进入“都昌编辑器历史版本介绍”',
  richExamples: '示例丰富',
  apiExamples:
    '提供DCWriter5.0各种常用的功能的api示例和组合调用示例，提供常见业务场景的推荐实践方案',
  demoProgram: '点我进入“演示程序”',
  dataFormatSupport: '支持多种数据格式',
  structuredData:
    '支持结构化xml或json格式，支持原格式加载word，支持导出pdf、html、txt、doc、rtf、ofd、长图片等',
  dataFormatDemo: '点我进入“演示程序-数据格式”',
  javaApiSupport:
    '支持java端对结构化的xml的操作api，支持批量的取值、赋值，以及java后端将xml格式转为pdf。',
  CrossIndustryApplications: '跨行业应用',
  CrossIndustryApplicationsdesc:
    '都昌结构化文本编辑器主要用于医疗电子病历，但同步支持更多跨行业应用，支持合同、政务、法律、投标标书制作、试卷等数十种跨行业文书应用',

  quickStart: '快速上手',
  GetLatest5: '获取DCWriter5.0的最新版本',
  ClickMeToCustomerCases: '点我进入“客户案例”',
  SatisfyXinchuang: '满足信创',
  SatisfyXinchuangdesc:
    'DCWriter5.0已与麒麟、统信等国产操作系统完成兼容互认，与国产数据库厂商达梦、人大金仓等完成适配认证',
  StrongProfessionalism: '专业性强',
  StrongProfessionalismDesc:
    '（DCWriter5.0满足电子病历的各种需求细节，符合卫生部《电子病历系统功能规范（试行）》中对病历编辑器的41个必需功能和推荐功能）',
  SatisfyRating: '满足评级',
  SatisfyRatingDesc:
    '基于DCWriter5.0开发的电子病历系统最高过电子病历评级8级（北京阜外医院），互联互通5乙等',
  OFDFormat: 'OFD格式',
  OFDFormatDesc:
    '支持导出ofd格式，并支持ofd格式的加载与保存，满足最新电子病历评级要求',
  ExperienceNow: '立即体验',
  JavaSideOperation: 'java端操作',
  care: '重症',
  careDesc:
    '全面支持特护单、重症监护单的模板设计和打印，支持更多医疗业务场景。',
  Partogram: '产程图',
  PartogramDesc: '全面支持产程图的模板设计和打印，支持更多医疗业务场景',
  NursingFormDesign: '护理表单设计',
  NursingFormDesignDesc:
    '提供全新护理记录单的表单设计和编辑功能，使其更便于编辑和查看，支持更多医疗业务场景。',
  SupportMobileDevices: '支持移动端',
  SupportMobileDevicesDesc:
    '提供移动端表单与都昌xml的格式自适应切换，实现一套模板两端使用，支持更多医疗业务场景。',

  RealDCWriter5uantity: 'DCWriter5.0接口数量实时统计',
  RealDCWriter5uantityVersion:
    'DCWriter5.0最新版本：2025年3月稳定版（DCWriter5.0.2503）',
  PublicFunction: '公开函数',
  PublicAttributes: '公开属性',
  PublicDocumentOptions: '公开文档选项',
  PublicEvent: '公开事件',
  Demo_En: '英文演示',
  ReturnHome: '首页',
  Demo_Complete: '完整演示',
  Demo_Temperature: '体温单演示',
  Demo_TemperatureA: '体温单演示A',
  Demo_TemperatureB: '体温单演示B',
  Demo_Partogram: '产程图演示',
  Demo_NewbornTemperature: '新生儿体温单演示',
  OldDemo: '旧版官网',
  UpdateLog: '更新日志',
  DcWriterSetting: {
    Cancel: '取消',
    Comfirm: '确定',
    InputFieldSetting: '输入域设置',
    CheckBoxOrRadioSetting: '单复选框设置',
    JsonDataShow: '获取结构化数据',
    AddData: '新增',
    Delete: '删除',
    CheckBoxOrRadioTableTitle: '明细项',
    CheckBoxOrRadioTableValueCol: '值',
    CheckBoxOrRadioTableTextCol: '文本',
    Radio: '单项框',
    CheckBox: '复选框',
    AboutInfo: '关于',
    Version: '版本',
    Releases: '产品ID',
    CompanyName: '公司名',
    CompanyNameContent: '南京都昌信息科技有限公司',
    Contact: '联系方式',
    ContactContent: '13382028281',
    Explain: '说明',
    ExplainContent:
      '本Demo演示仅包含部分功能演示，完整功能请根据上述方式联系我们获取完整版本和最新文档。',
    ProductName: '第五代WEB版DCWriter文本编辑器',
  },
  DcElementProps: {
    ID: '编号',
    Name: '名称',
    BackgroundText: '背景文字',
    ToolTip: '提示信息',
    StartBorderText: '左边框',
    EndBorderText: '右边框',
    ContentReadonly: '只读',
    Required: '必填',
    InnerEditStyle: '类型',
    SelectValueType: '下拉内容',
    Type: '类型',
    GroupName: '组名',
  },
  DcEditStyle: {
    Text: '纯文本输入域',
    DropdownList: '下拉列表',
    Date: '日期',
    DateTime: '日期时间类型',
    DateTimeWithoutSecond: '不含秒的日期时间类型',
    Time: '时间类型',
    Numeric: '数值类型',
  },
  DcMessageTips: {
    HasSameElement: '已存在相同ID的输入域，请修改ID后重试',
  },
  DcContextMenu: {
    Revoke: '撤销',
    Redo: '重做',
    Copy: '复制(Ctrl+C)',
    Paste: '粘贴(Ctrl+V)',
    Cut: '剪切(Ctrl+X)',
    CopyAsText: '纯文本复制',
    PasteAsText: '纯文本粘贴',
    Attributes: '属性',
    TableRowsAndColumns: '表格行列',
    DeleteRow: '删除表格行',
    DeleteColumn: '删除表格列',
    InsertRowsAbove: '在上面插入行',
    InsertRowsBottom: '在下面插入行',
    InsertRowsLeft: '在左侧插入列',
    InsertRowsRight: '在右侧插入列',
    MergeCell: '合并单元格',
    SplitCell: '拆分单元格',
  },
  OnlineDemo: {
    bright: '明亮',
    dark: '暗黑',
    eyeProtection: '护眼',
    openLocalFile: '打开本地文件',
    saveXml: '保存为XML文件',
    savePdf: '保存为PDF文件',
    paperSize: '页面大小',
    paperCm: '厘米',
    paperDiy: '自定义',
    confirm: '确认',
    cancel: '取消',

    paperWidth: '宽度',
    paperHeight: '高度',
    paperMargin: '页边距',
    leftMargin: '左',
    rightMargin: '右',
    topMargin: '上',
    bottomMargin: '下',
    paperDirection: '纸张方向',
    longitudinal: '纵向',
    horizontal: '横向',
    undo: '撤销',
    redo: '重做',
    formatBrush: '格式刷',
    clearFormat: '清除格式',
    font: '字体',
    fontSize: '字体大小',
    bold: '粗体',
    italic: '斜体',
    underline: '下划线',
    strikethrough: '中划线',
    superscript: '上标',
    subscript: '下标',
    fontColor: '文字颜色',
    backgroundColor: '文字背景颜色',
    header: '标题',
    bodyText: '正文',
    header1: '标题1',
    header2: '标题2',
    header3: '标题3',
    header4: '标题4',
    header5: '标题5',
    alignLeft: '左对齐',
    alignRight: '右对齐',
    alignCenter: '居中对齐',
    increaseIndent: '增加缩进',
    decreaseIndent: '减少缩进',
    lineSpacing: '行间距',
    numberedList: '有序列表',
    bulletedList: '无序列表',
    formMode: '表单模式',
    insert: '插入',
    inputfile: '文本域',
    datetimefield: '日期时间文本域',
    dropdownfield: '下拉文本域',
    numberfield: '数字文本域',
    checkboxgroup: '单复选框组',
    table: '表格',
    formula: '公式',
    specialcharacter: '特殊字符',
    image: '图片',
    video: '视频',
    noformmode: '非表单模式',
    normalformmode: '普通表单模式',
    strictformmode: '严格表单模式',

    getStructuredData: '获取结构化数据',
    print: '打印',
    find: '查找',
    about: '关于',
    tableOfContents: '目录',
    close: '关闭',
    outline: '大纲',
    continuousView: '连页视图',
    page: '页面',
    wordCount: '字数',
    zoomIn: '放大',
    zoomOut: '缩小',
    chooseScale: '选择比例',
    fullscreen: '全屏',
    exitFullscreen: '取消全屏',
    loadingText:
      '首次启动DCWriter时，系统会自动下载部分必要资源。下载完成后，这些资源会缓存至浏览器中。此后再次打开DCwriter，便能借助缓存的资源迅速进入程序界面。即使关闭浏览器再打开，也能够秒开编辑器。',
  },
  DcSubPage: {
    document: '文档',
    pageSetup: '页面设置',
    editAndFormat: '编辑与格式',
    view: '视图',
    userTrace: '用户痕迹',
    table: '表格',
    printAndPreview: '打印及预览',
    structuredElements: '结构化元素',
    developmentSupport: '开发支持',

    new: '新建',
    clear: '清空',
    load: '加载',
    save: '保存',
    downloadOrSaveAs: '下载或另存为',
    saveSelectedContent: '保存选择内容',
    openLocalFile: '打开本地文件',
    pdfPreview: 'PDF预览',
    refreshDocument: '刷新文档',
    insertDocument: '插入文档',
    documentStatus: '文档状态',
    documentReadOnly: '文档只读',
    headerFooterReadOnly: '页眉页脚只读',
    appendDocument: '追加文档',
    validate: '校验',
    settings: '设置',
    paperSettings: '纸张设置（包含页面设置对话框）',
    documentGridLines: '文档网格线（包含文档网格线对话框）',
    documentWatermark: '文档水印（包含文档水印对话框）',
    bindingLine: '装订线（包含装订线对话框）',
    ruler: '标尺',
    page: '页面',
    edit: '编辑',
    font: '字体（包含字体对话框）',
    paragraph: '段落（包含段落对话框）',
    readingView: '阅读视图',
    adminView: '管理员视图',
    contentProtectionView: '内容保护视图',
    formModeView: '表单模式视图',
    designModeView: '设计模式视图',
    displayModeView: '显示模式视图',
    zoomView: '缩放视图',
    previewModeView: '预览模式视图',
    cleanMode: '清洁模式',
    trackChangesMode: '留痕模式',
    clearChanges: '清除痕迹',
    submitChanges: '提交痕迹',
    userLogin: '用户登录',
    trackChangesSettings: '痕迹设置',
    trackChangesList: '留痕信息列表',
    insertTable: '插入表格（包含对话框）',
    deleteTable: '删除表格',
    deleteRow: '删除行',
    deleteColumn: '删除列',
    insertRow: '插入行',
    insertColumn: '插入列',
    mergeCells: '合并单元格',
    splitCells: '拆分单元格（包含对话框）',
    cellBackgroundNumber: '单元格背景编号',
    cellGridLines: '单元格网格线（包含对话框）',
    tableBorders: '表格边框（包含对话框）',
    cellBorders: '单元格边框（包含对话框）',
    cellDiagonalLines: '单元格斜分线（包含对话框）',
    cellAlignment: '单元格对齐方式',
    tableProperties: '表格属性（包含对话框）',
    tableRowProperties: '表格行属性（包含对话框）',
    cellProperties: '单元格属性（包含对话框）',
    print: '打印',
    continuePrinting: '续打',
    offsetContinuePrinting: '偏移续打',
    printPreview: '打印预览',
    inputField: '输入域(包含对话框)',
    radioButton: '单选框(包含对话框)',
    checkBox: '复选框(包含对话框)',
    barcode: '条形码(包含对话框)',
    qrCode: '二维码(包含对话框)',
    pageNumber: '页码(包含对话框)',
    labelText: '标签文本(包含对话框)',
    button: '按钮(包含对话框)',
    horizontalLine: '水平线(包含对话框)',
    image: '图片(包含对话框)',
    medicalExpression: '医学表达式(包含对话框)',
    comment: '批注',
    courseOfDisease: '病程',
    viewAboutInfo: '查看关于信息',
    contextMenu: '右键菜单',
    auxiliaryInput: '辅助录入',
    dragAndDrop: '拖拽',
    dropdownList: '下拉列表',
    register: '注册',
    mouseEvents: '鼠标事件',
    keyboardEvents: '键盘事件',
    documentEvents: '文档事件',
    otherEvents: '其他事件',

    newBlankDocument: '新建空白文档',
    clearContent: '清空正文',
    clearHeaderFooter: '清空页眉页脚',
    loadHeaderFooter: '加载页眉页脚（格式包含XML、Json）',
    loadContent: '加载正文（格式包含XML、Json）',
    loadFullDocument: '加载全文（格式包含XML、Json）',
    saveHeaderFooter: '保存页眉页脚（格式包含XML、Json）',
    saveContent: '保存正文（格式包含XML、Json）',
    saveFullDocument: '保存全文（格式包含XML、Json）',
    XML: 'XML',
    JSON: 'JSON',
    PDF: 'PDF',
    HTML: 'HTML',
    longImage: '长图片',
    RTF: 'RTF',
    WordDocument: 'Word文档',
    realTimePDFPreview: '实时预览PDF内容，不需要下载或导出',
    refreshDocumentDisplay: '对文档刷新显示',
    insertXMLDocument: '插入XML文档',
    insertJSONDocument: '插入JSON文档',
    insertText: '插入文本',
    insertSpecialCharacters: '插入特殊字符',
    currentModificationStatus: '当前修改状态',
    resetModificationStatus: '重置修改状态',
    appendDocumentAtEnd: '在文档最后追加文档（格式包含XML、Json）',
    documentValidation: '文档校验',
    getForbiddenKeywordsSettings: '获取违禁关键词设置',
    setForbiddenKeywords: '设置违禁关键词',
    annotatedDocumentValidation: '批注式文档校验',
    clearAnnotationValidationResults: '清除批注校验结果',
    showTableHiddenBordersInGray: '是否以灰色线条显示表格隐藏边框',
    showHeaderUnderline: '是否显示页眉下划线',
    showParagraphMarks: '是否显示文档的段落标记',
    showInputFieldStatusMarks: '是否显示输入域状态的标记',
    outputFormattedXML: '是否输出格式化的XML',
    paragraphSymbolAfterTableOrSection:
      '排版时段落符号紧跟在表格或文档节后面。',
    inputCursor: '输入光标',
    deletionPromptForUndeletableContent: '当删除无法删除的内容时的提示方式',
    selectPaper: '选择纸张',
    paperSize: '纸张尺寸',
    margins: '页边距',
    layoutDirection: '排版方向',
    gridLineStyle: '网格线线形',
    gridLineColor: '网格线颜色',
    linesPerPage: '每页行数',
    printGridLines: '是否打印网格线',
    textWatermark: '文字水印',
    imageWatermark: '图片水印',
    bindingLinePosition: '装订线位置',
    bindingLineDistance: '装订线距离',
    doubleSidedBinding: '是否双面装订',
    showRuler: '是否显示标尺',
    rulerBackgroundColor: '标尺背景色',
    pageBackgroundColor: '页面背景色',
    pageBorderColor: '页面边框颜色',
    copy: '复制',
    cut: '剪切',
    paste: '粘贴',
    plainTextCopy: '纯文本复制',
    plainTextPaste: '纯文本粘贴',
    formatPainter: '格式刷',
    undo: '撤销',
    redo: '重做',
    find: '查找（快捷键Ctrl+F）',
    replace: '替换（快捷键Ctrl+F）',
    fontSize: '字体大小',
    strikethrough: '删除线',
    bold: '粗体',
    italic: '斜体',
    underline: '下划线',
    foregroundColor: '前景色',
    backgroundColor: '背景色',
    superscript: '上标',
    subscript: '下标',
    getCurrentCursorFont: '获取当前光标字体，包含名称、大小',
    textBorder: '文字边框',
    defaultFont: '默认字体',
    clearFormatting: '清除格式',
    horizontalAlignment: '水平对齐',
    firstLineIndent: '首行缩进',
    hangingIndent: '悬挂缩进',
    orderedList: '有序列表',
    unorderedList: '无序列表',
    lineSpacing: '行间距',
    spacingBeforeParagraph: '段前距',
    spacingAfterParagraph: '段后距',
    firstLineIndentAmount: '首行缩进量',
    leftIndentAmount: '左侧缩进量',
    noRestrictionsExceptEditorControl:
      '除了受到编辑器控件的内容只读及表单模式的限制外，其他没有任何限制，比如授权只读控制、输入域只读控制、内容保护、文档签名保护等等统统无效。',
    protectContentFromDeletionAllowInput:
      '保护一段内容不允许删除，在中间可以输入',
    protectContentFromDeletionNoInput: '保护一段内容不允许删除，中间也不能输入',
    operateOnlyInInputFieldNoSelection:
      '只能在输入域内操作，其他区域不能操作，也不能选择',
    operateOnlyInInputFieldAllowSelection:
      '只能在输入域内操作，其他区域不能操作，允许选择',
    designModeDropdownListInvalid:
      '为方便制作模式，设计模式时，下拉列表设置无效、级联模式设计无效',
    singleColumnDisplay: '单栏显示',
    multiColumnDisplay: '多栏显示',
    horizontalSpreadDisplay: '水平铺开显示',
    zoomIn: '放大',
    zoomOut: '缩小',
    fitToScreen: '自适应',
    checkPreviewMode: '判断是否预览模式',
    cleanPreviewMode: '清洁预览模式',
    trackChangesPreviewMode: '留痕预览模式',
    previewCurrentPage: '预览当前页',
    closePreviewMode: '关闭预览模式',
    hideUserTraces: '不显示用户痕迹',
    leaveTracesAfterUserLogin: '在用户登录后，操作文档留下痕迹',
    clearAllTraces:
      '清除所有痕迹（把所有用户的操作记录去掉，逻辑删除的内容也显示出来）',
    clearCurrentUserTraces:
      '清除当前用户痕迹（把所有用户的操作记录去掉，逻辑删除的内容也显示出来）',
    removeAllUserTraces: '把所有用户的操作记录去掉，逻辑删除的内容彻底删除',
    userID: '用户编号',
    userName: '用户姓名',
    clientComputerName: '客户端电脑名称',
    userAuthorizationLevel: '用户授权等级',
    description: '说明',
    enableLogicalDeletion: '是否启用逻辑删除',
    enablePermissionControl: '是否启用权限控制',
    enablePhysicalDeletionOfOwnContent: '是否启用物理删除自己输入的内容',
    showLogicalDeletionContent: '是否显示逻辑删除内容',
    showAuthorizationMark: '是否显示授权标记',
    showPromptInformation: '是否显示提示信息',
    traceStyle: '痕迹样式（包含多个权限用户的样式）',
    rowCount: '行数',
    columnCount: '列数',
    deleteCurrentTable: '删除当前表格',
    deleteTableByNumber: '删除指定编号的表格',
    insertRowAbove: '在当前行上面插入行',
    insertRowBelow: '在当前行下面插入行',
    insertColumnLeft: '在当前列左边插入列',
    insertColumnRight: '在当前列右边插入列',
    mergeSelectedCells: '选择同行或同列的单元格合并',
    splitCellIntoRowsAndColumns: '把当前单元格拆分成制定的行和列',
    toggleCellBackgroundNumber: '为方便制作模板显示或隐藏单元格背景编号',
    gridLineWidth: '网格线宽度',
    gridLineSpacing: '网格线间隔',
    borderWidth: '边框宽度',
    borderTopBottomLeftRight: '上下左右边框',
    borderColorTopBottomLeftRight: '上下左右边框颜色',
    diagonalLineFromTopLeft: '左上角开始一条',
    diagonalLineFromTopLeftTwo: '左上角开始两条',
    diagonalLineFromTopRight: '右上角开始一条',
    diagonalLineFromTopRightTwo: '右上角开始两条',
    diagonalLineFromBottomLeft: '左下角开始一条',
    diagonalLineFromBottomLeftTwo: '左下角开始两条',
    diagonalLineFromBottomRight: '右下角开始一条',
    diagonalLineFromBottomRightTwo: '右下角开始两条',
    alignTopLeft: '顶端左对齐',
    alignTopCenter: '顶端居中对齐',
    alignTopRight: '顶端右对齐',
    alignMiddleLeft: '垂直居中水平左对齐',
    alignMiddleCenter: '垂直居中水平居中对齐',
    alignMiddleRight: '垂直居中水平右对齐',
    alignBottomLeft: '底端左对齐',
    alignBottomCenter: '底端居中对齐',
    alignBottomRight: '底端右对齐',
    tableNumber: '编号',
    customAttributes: '自定义属性',
    enableAuthorizationControl: '是否启用授权控制',
    allowUserToAdjustRowHeight: '允许用户调整行高',
    allowUserToAdjustColumnWidth: '允许用户调整列宽',
    allowUserToAddTableRows: '允许用户新增表格行',
    allowUserToDeleteTableRows: '允许用户删除表格行',
    allowUserToDeleteTable: '允许用户删除表格',
    compressTableRowSpacing: '是否压缩表格上下行间距',
    height: '高度',
    copyMode: '复制模式',
    contentReadOnly: '内容只读',
    allowUserToAdjustHeight: '是否允许用户调整高度',
    isTitleRow: '是否标题行',
    forcePageBreak: '强制分页',
    printBorder: '是否打印边框',
    dataSourceName: '数据源名称',
    bindingPath: '绑定路径',

    printAll: '打印全部',
    printCurrentPage: '打印当前页',
    printSpecifiedPage: '打印指定页',
    printSpecifiedRange: '打印指定范围页',
    printOddPages: '打印奇数页',
    printEvenPages: '打印偶数页',
    printAsPDF: '打印为PDF',
    printAsHtml: '打印为Html',

    elementContinuePrinting: '元素续打',
    positionContinuePrinting: '位置续打',
    cancelContinuePrinting: '取消续打',
    verticalOffset: '纵向偏移量',
    initialPageNumber: '初始页码',
    printHeaderFooterOnFirstPage: '首页是否打印页眉页脚',
    loadDocument: '加载文档',

    mergeLoadDocument: '合并加载文档',

    backgroundText: '背景文字',
    hintText: '提示文本',
    leftBorder: '左边框',
    hasBorder: '有边框',
    contentAlignment: '内容对齐方式',
    width: '宽度',
    highlight: '高亮度',
    focusShortcutKey: '焦点快捷键',

    unitText: '单位文本',
    isReadOnly: '是否只读',
    isDeletable: '是否允许被删除',
    isEditable: '是否允许直接编辑内容',
    maxInputLength: '允许输入最大字符串',
    encryptedDisplay: '加密显示',
    dataSourceBinding: '数据源绑定',
    textColor: '文本颜色',
    backgroundTextColor: '背景文字颜色',
    textInputMode: '文本输入方式',
    dropdownInputMode: '下拉列表输入方式',
    dateTimeInputMode: '日期时间格式输入方式',
    dateInputMode: '日期格式输入方式',
    numberInputMode: '数字类型输入方式',
    timeInputMode: '时间格式输入方式',
    dateInputModeWithoutSeconds: '日期格式（不含秒）输入方式',
    outputFormat: '输出格式',
    isRequired: '是否必填',
    errorMessage: '错误提示信息',
    forbiddenKeywords: '违禁关键词',
    allowedKeywords: '允许关键词',
    textValidation: '文本校验',
    numberValidation: '数值校验',
    dateTimeValidation: '日期时间校验',
    regexValidation: '正则表达式校验',
    expression: '表达式',

    getInputFieldContentByNumber: '通过编号获取输入域内容',
    setInputFieldValueByNumber: '通过编号给输入域赋值',
    getInputFieldByNumber: '通过编号获取输入域',
    setUnderline: '设置下划线',
    setGlobalInputFieldHighlight: '全局设置输入域高亮',
    setGlobalInputFieldBackgroundTextColor: '全局设置输入域背景文字颜色',
    setGlobalInputFieldTextColor: '全局设置输入域文本颜色',
    setGlobalInputFieldBackgroundColor: '全局设置输入域背景颜色',
    setGlobalInputFieldBorderColor: '全局设置输入域边框颜色',
    setGlobalInputFieldFocusBackgroundColor: '全局设置输入域聚焦时的背景颜色',
    setGlobalInputFieldHoverBackgroundColor:
      '全局设置输入域鼠标悬浮时的背景颜色',
    setGlobalInputFieldDataErrorBackgroundColor:
      '全局设置输入域数据异常时的背景颜色',
    setGlobalInputFieldDataErrorHighlightTextColor:
      '全局设置输入域数据异常时的高亮度文本颜色',
    setGlobalInputFieldPrintTextColor: '全局设置输入域打印时的文本颜色',
    setGlobalInputFieldHideBorderOnBlur: '全局设置输入域失去焦点时隐藏边框',
    setGlobalInputFieldIgnoreBorderOnPrint:
      '全局设置输入域打印时忽略边框，使其不占据位置',
    setGlobalInputFieldPrintBackgroundText:
      '全局设置输入域打印时是否打印背景文字',
    setGlobalInputFieldReadOnlyBorderColor: '全局设置输入域只读时的边框颜色',
    setGlobalInputFieldShowBorder: '全局设置输入域是否显示边框',
    setGlobalInputFieldShowStatusMark:
      '全局设置输入域是否显示状态标记（右下角）',
    setGlobalInputFieldStatusMarkColorOnContentChange:
      '全局设置输入域内容修改时状态标记颜色',
    setGlobalInputFieldDefaultStatusMarkColor: '全局设置输入域默认状态标记颜色',
    setGlobalInputFieldReadOnlyStatusMarkColor:
      '全局设置输入域只读状态标记颜色',
    setGlobalInputFieldValidationErrorStatusMarkColor:
      '全局设置输入域校验异常状态标记颜色',
    setGlobalInputFieldDialogOnlyEditStatusMarkColor:
      '全局设置输入域用户不能直接修改只能通过对话框修改的状态标记颜色',
    clearInputFieldContentOnCopy: '全局设置输入域复制时清空输入域内容',
    exportRTFIncludeBackgroundText: '全局设置输入域导出RTF时是否导出背景文字',
    displayStyle: '显示样式',

    checkboxPosition: '勾选框位置',
    text: '文本',
    number: '数值',

    isChecked: '是否选中',
    isEnabled: '是否可用',
    requiredValidation: '必选校验',
    flowLayout: '流式排版',
    highlightState: '高亮度状态',
    checkedCascadeObject: '勾选级联对象',
    uncheckedCascadeObject: '不勾选级联对象',
    uncheckedPrintSettings: '不勾选时打印设置',

    barcodeStyle: '条码样式',
    alignment: '对齐方式',
    showText: '是否显示文本',

    errorCorrectionLevel: '纠错能力',

    formatString: '格式化字符串',
    autoSize: '自动大小',
    wordWrap: '自动换行',

    linkMode: '连接模式',
    name: '名称',

    printAsText: '是否以文本方式打印',
    visibleOnPrint: '打印时是否可见',
    commandText: '命令文本',
    buttonImage: '按钮图片',
    pressedImage: '按下时图片',
    hoverImage: '悬浮时图片',
    script: '脚本',

    color: '颜色',
    line: '线条',
    insertImage: '插入图片',
    saveImageInDocument: '在文档中是否保存图片',
    imageEditing: '图片编辑（包含对话框）',
    menstrualHistory: '月经史',
    exophthalmos: '眼球突出度',
    strabismusSymbol: '斜视符号',
    fetalHeartRateChart: '胎心图',
    pupilChart: '瞳孔图',
    permanentTeethChart: '恒牙牙位图',
    deciduousTeethChart: '乳牙牙位图',
    upperTeethLesion: '病变上牙',
    lowerTeethLesion: '病变下牙',
    scoreFormula: '分数公式',
    rulerFormula: '标尺公式',
    insertOrEditComment: '插入或编辑批注',
    deleteComment: '删除批注',
    hideComment: '隐藏批注',
    insertCourseOfDisease: '插入病程',
    appendCourseOfDisease: '追加病程',
    getAllCoursesOfDisease: '获取所有病程',
    locateCourseOfDisease: '定位病程',
    deleteCourseOfDisease: '删除病程',
    getCurrentCourseOfDiseaseNumber: '获取当前病程编号',
    readOnly: '只读',
    crossPage: '跨页',
    saveCourseOfDiseaseContentByNumber: '保存指定编号病程内容（XML或json格式）',
    getCurrentElementType: '获取当前元素类型',
    customContextMenuStyle: '自定义右键菜单样式',
    customContextMenuFunction: '自定义右键菜单功能',
    customAuxiliaryInputDatabase: '自定义辅助录入数据库',
    dragTextFromOutsideToEditor: '从外部拖拽文本到编辑器',
    dragElementFromOutsideToEditor: '从外部拖拽元素到编辑器',
    dynamicDropdownList: '动态下拉列表',
    dynamicDropdownListAsync: '动态下拉列表（异步）',
    registrationCode: '注册码',
    registrationMarkPosition: '注册标记位置',

    ondocumentmousedown: '文档鼠标按下',
    ondocumentmouseup: '文档鼠标松开',
    ondocumentkeydown: '文档键盘按下',
    ondocumentclick: '文档点击',
    ondocumentdblclick: '文档双击',
    DocumentLoad: '文档加载',
    DocumentContentChanged: '文档内容改变',
    SelectionChanged: '选择改变',
    EventBeforeCopy: '复制前事件',
    EventBeforePaste: '粘贴前事件',
    EventBeforePrint: '打印前事件',
    EventBeforePrintPreview: '打印预览前事件',
    EventAfterPrintPreview: '打印预览后事件',
    EventElementGotFocus: '元素获得焦点事件',
    EventElementLostFocus: '元素失去焦点事件',
    EventAfterInsertSubDocuments: '插入子文档后事件',
    EventElementMouseClick: '元素鼠标点击事件',
    EventElementMouseDblClick: '元素鼠标双击事件',

    WelcometoNanjingDuchangInformationTechnologyCoLtd:
      '欢迎来到  南京都昌信息技术有限公司',
    NanjingDuchangInformationwasestablishedin2012: '南京都昌信息成立于2012年',
    electronicmedicalrecords:
      '是一家专业从事于医疗信息化基础核心软件研发的高新技术企业、电子病历功能应用水平分级评价标准的参与单位',
    informationizationindustry:
      '公司依托强大的底层核心技术跻身电子病历信息化行业前列',
    softwarecompaniesintheindustry:
      '目前拥有80多项自主知识产权、技术领先的软件产品，与300多家业内著名软件公司达成合作',
    Duchangsproducts:
      '都昌的产品已经覆盖全国800多家三甲医院、5000多家区县级医院、数万家基层医疗机构，广泛应用于电子病历、医疗影像、区域医疗、门急诊系统、护理系统、康复、美容、口腔、眼科等',
    achievinginterconnectivity:
      '实现了跨软件系统、跨医疗机构、跨软件厂商的数据共享，为实现互联互通打下了坚实的基础',
    AddressRoom: '地址:南京市雨花台区软件大道106号2号楼501-2室',

    introduce: '介绍',
    CompanyProfile: '公司介绍',
    IntroductionEditor: '电子病历编辑器介绍',
    Electronicmedicalrecordeditorisasoftwaremiddleware:
      '电子病历编辑器是一种软件中间件',
    manufacturersorhospitals: '它被厂商或医院进行二次开发成病历书写系统',
    medicalrecorddocuments: '允许医疗专业人员创建、编辑和存储结构化的病历文档',
    documentwritingscenarios:
      '电子病历编辑器是电子病历系统或者其他结构化文书书写场景的核心组件',
    recordsordocuments: '它提供了创建和编辑结构化病历或文书所需的各种功能',
    innovationrequirements: '它需要符合电子病历的最高评级要求、信创要求等',

    QuickIntegrationGuide: '快速集成指南',
    overview: '概述',
    serverdeployment: '服务器部署',
    Frontendintegration: '前端集成',
    Browsercompatibility: '浏览器兼容',
    Chromekernel: 'Chrome内核',
    Andabove: '及以上',
    Firefoxbrowserkernel: '火狐浏览器内核',
    Edgebrowserkernel: 'Edge浏览器内核',
    IEbrowserdoesnotsupport: 'IE浏览器不支持',
    WebViewkernel: 'WebView内核',
    Recommendcustomers: '推荐客户使用谷歌浏览器',
    Additionally:
      '另外：windows7推荐谷歌80-108，不推荐谷歌109，存在字体模糊问题，但可通过插件进行解决',

    usersmanual: '用户手册',
    CustomerConsultation: '客户咨询',
    TechnicalcontactinformationPhone: '技术联系方式：电话',
    BusinesscontactinformationPhone: '商务联系方式：电话',
    email: '邮箱',
    WeChattechnicalsupport: '微信技术支持：扫码进群',
    QQtechnicalsupport: 'QQ技术支持：',
    iframeAddress: 'https://ydydc.yuque.com/org-wiki-ydydc-te6liv/dzx9gm',
    FunctionList: '功能列表',
    IntroductiontotheHistoricalVersionsofDuchangEditor:
      '都昌编辑器历史版本介绍',
    DuchangCertificate: '都昌证书',
    CustomerCase: '客户案例',
    Patentcertificate: '专利证书',
    Softworks: '软著',
    truestart: '信创',
    Othercompatible: '其他兼容',
    Customerthankyouletter: '客户感谢信',

    historyOne:
      '1.0 桌面版结构化编辑器，支持winfrom、wpf开发，同时支持pb、delphi、vb、c++等以com接口形式开发，是都昌存量客户使用最多的一个版本。产品功能强大，性能优越，都昌编辑器2015年开始成为业内电子病历编辑器的首选。',
    historyTwo:
      '以activex插件形式运行在浏览器端，是cs版的com接口板，是早期基于ie开发web版客户的首选，其功能与cs版保持一致，但只能在ie浏览器或者ie内核的插件运行。',
    historyThree:
      '基于webform的网页版编辑器，支持基本的结构化编辑和打印等，不支持复杂的工具条操作，主要用于客户在网页端简单的查看和打印。',
    historyFour:
      'html5版是基于html5的前端编辑器，后端需要部署依赖.net core服务，基本功能都满足，支持结构化编辑、级联、留痕、负责打印等，但实时分页一直无法解决，后端依赖较重。',
    historyfive:
      'canvas版（dcwriter5.0）是都昌目前的核心版本，由于基于wasm-canvas，很大程度了避免了不同浏览器版本导致编辑器展示不一致的问题，且功能强大，涵盖了cs版所有功能，并提供了更多的数据格式支持和api，dcwriter5.0性能强大，能够实现百页文档的秒开。目前已替换了近一半的都昌编辑器客户，也是都昌全力开发与维护的版本。现已成功上线北部战区总医院、上海瑞金医院、上海市第一人民医院、武汉亚心医院、上海市精神卫生中心、中国医科大学附属第四医院、浙江大学医学院附属第二医院、内蒙古林业总医院、浙大四院生殖医学中心、 北京阜外医院、四川大学华西口腔医院、东莞市人民医院等数十家三级医院。',
    edition: '版',
    now: '今',
    Stopmaintenanceattheendoftheyear: '年底停止维护',
  },
};
