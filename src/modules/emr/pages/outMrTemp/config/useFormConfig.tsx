import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import { BIZ_ID_TYPE_CODE } from '@/utils/constant';

export function useTreeSearchFormConfig() {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'keyWord',
        label: t('global:keyword', '关键字'),
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-64',
        triggerModelChange: true,
        extraProps: {
          prefixIcon: 'Search',
        },
      },
    ],
  });
  return data;
}

export function useAddMrNodeUpsertConfig(
  superiorNodeOptions: Ref<{ label: string; value: string }[]>,
  encounterTypeList: Ref<{ value: string; label: string }[]>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'mrTempTreeParentId',
        label: t('outMrTemp.mrTempTreeParentId', '上级节点'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('outMrTemp.mrTempTreeParentId', '上级节点'),
        }),
        span: 2,
        extraProps: {
          options: superiorNodeOptions.value || [],
          filterable: true,
          disabled: true,
        },
      },
      {
        name: 'enabledFlag',
        component: 'switch',
        labelWidth: 40,
        extraProps: {
          'inline-prompt': true,
          'active-value': 1,
          'inactive-value': 0,
          'active-text': t('global:enabled', '启用'),
          'inactive-text': t('global:disabled', '禁用'),
        },
      },
      {
        name: 'mrTempTreeName',
        label: t('outMrTemp.mrTempTreeName', '节点名称'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('outMrTemp.mrTempTreeName', '节点名称'),
        }),
        isFullWidth: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('outMrTemp.mrTempTreeName', '节点名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {},
      },
      {
        name: 'encounterTypeCode',
        component: 'select',
        label: t('useTreeSearchFormConfig.encounterTypeCode', '使用范围'),
        placeholder: t('useTreeSearchFormConfig.encounterTypeCode', '使用范围'),
        isFullWidth: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('outMrTemp.encounterTypeCode', '使用范围'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          options: encounterTypeList.value || [],
          filterable: true,
        },
      },
    ],
  });
  return data;
}

const ownerTypeCodeList = [
  { label: '医院', value: BIZ_ID_TYPE_CODE.DICT_HOSPITAL },
  { label: '科室', value: BIZ_ID_TYPE_CODE.DICT_DEPARTMENT },
  { label: '个人', value: BIZ_ID_TYPE_CODE.DICT_USER },
];

const diseaseName = ['高血压', '糖尿病', '肾透析', '癌症放化疗'];
const mrtempCategoryOptions = ['病历质控', '互联互通'];

export function useAddMrTempConfig(
  internalOptionMap: Ref<{
    [key: string]: { label: string; value: string }[];
  }>,
  superiorNodeOptions: Ref<{ label: string; value: string }[]>,
  modeName: Ref<string>,
  isDisabledSelectOwnerId: Ref<boolean>,
  isDisabledSelectShareBizIds: Ref<boolean>,
  changeOwnerIdSelect: (flag?: boolean, value?: string) => void,
  changeMrTempShareTypeCode: (value?: string) => void,
  selectOwnerIdOptions: Ref<{ label: string; value: string }[]>,
  medicalRecordTypeList: Ref<{ label: string; value: string }[]>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'mrTempTreeParentId',
        label: t('outMrTemp.mrTempTreeParentId', '上级节点'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('outMrTemp.mrTempTreeParentId', '上级节点'),
        }),
        span: 2,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('outMrTemp.mrTempTreeParentId', '上级节点'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        class: 'w-64',
        extraProps: {
          disabled: modeName.value === 'edit',
          options: superiorNodeOptions.value || [],
          filterable: true,
          clearable: false,
        },
      },
      {
        name: 'mrTempName',
        label: t('outMrTemp.mrTempName', '模板名称'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('outMrTemp.mrTempName', '模板名称'),
        }),
        span: 2,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('outMrTemp.mrTempName', '模板名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        name: 'medicalRecordTypeId',
        label: t('outMrTemp.medicalRecordTypeId', '病历类型'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('outMrTemp.medicalRecordTypeId', '病历类型'),
        }),
        span: 2,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('outMrTemp.medicalRecordTypeId', '病历类型'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        class: 'w-64',
        extraProps: {
          options: medicalRecordTypeList.value || [],
          filterable: true,
        },
      },
      {
        name: 'encounterTypeCode',
        label: t('outMrTemp.encounterTypeCode', '使用范围'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('outMrTemp.encounterTypeCode', '使用范围'),
        }),
        span: 2,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('outMrTemp.encounterTypeCode', '使用范围'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        class: 'w-64',
        extraProps: {
          disabled: modeName.value === 'edit',
          options: internalOptionMap.value?.encounterTypeList || [],
          filterable: true,
        },
      },
      {
        name: 'ownerTypeCode',
        label: t('outMrTemp.ownerId', '所有者'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('outMrTemp.ownerTypeCode', '所有者类型'),
        }),
        span: 1,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('outMrTemp.ownerTypeCode', '所有者类型'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        class: 'w-64',
        extraProps: {
          options: ownerTypeCodeList,
          filterable: true,
          clearable: false,
          onChange: (value?: string) => {
            // 根据选择的所有者类型，动态更新所有者ID的选项
            if (value === BIZ_ID_TYPE_CODE.DICT_HOSPITAL) {
              changeOwnerIdSelect(true);
            } else {
              changeOwnerIdSelect(false, value);
            }
          },
        },
      },
      {
        name: 'ownerId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('outMrTemp.ownerId', '所有者'),
        }),
        span: 2,
        class: 'w-80',
        extraProps: {
          disabled: isDisabledSelectOwnerId.value,
          options: selectOwnerIdOptions.value || [],
          filterable: true,
        },
      },
      {
        name: 'enabledFlag',
        component: 'switch',
        labelWidth: 40,
        span: 1,
        extraProps: {
          'inline-prompt': true,
          'active-value': 1,
          'inactive-value': 0,
          'active-text': t('global:enabled', '启用'),
          'inactive-text': t('global:disabled', '禁用'),
        },
      },
      {
        name: 'mrTempShareTypeCode',
        label: t('outMrTemp.mrTempShareTypeCode', '共享类型'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('outMrTemp.mrTempShareTypeCode', '共享类型'),
        }),
        span: 1,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('outMrTemp.mrTempShareTypeCode', '共享类型'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        class: 'w-64',
        extraProps: {
          onChange: (value?: string) => {
            changeMrTempShareTypeCode(value);
          },
          options: internalOptionMap.value?.mrTempShareTypeList || [],
          filterable: true,
        },
      },
      {
        name: 'shareBizIds',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('outMrTemp.shareBizIds', '共享业务标识'),
        }),
        span: 3,
        class: 'w-80',
        extraProps: {
          disabled: isDisabledSelectShareBizIds.value,
          options: selectOwnerIdOptions.value || [],
          filterable: true,
          multiple: true,
        },
      },
      {
        name: 'mrTempDisplayName',
        label: t('outMrTemp.mrTempName', '显示名称'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('outMrTemp.mrTempDisplayName', '显示名称'),
        }),
        span: 2,
      },
      {
        name: 'diseaseName',
        label: t('outMrTemp.diseaseName', '适应病种'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('outMrTemp.diseaseName', '适应病种'),
        }),
        span: 2,
        class: 'w-80',
        extraProps: {
          options: diseaseName.map((item) => ({
            label: item,
            value: item,
          })),
          filterable: true,
        },
      },
      {
        name: 'mrtempSourceCode',
        label: t('outMrTemp.mrtempSourceCode', '模板来源'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('outMrTemp.mrtempSourceCode', '模板来源'),
        }),
        rules: [
          {
            required: modeName.value === 'copy',
            message: t('global:placeholder.select.template', {
              name: t('outMrTemp.mrtempSourceCode', '模板来源'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        span: 2,
        class: 'w-80',
        extraProps: {
          disabled: modeName.value === 'edit',
          options: internalOptionMap.value?.mrTempSourceList || [],
          filterable: true,
        },
      },
      {
        name: 'mrtempCategory',
        label: t('outMrTemp.mrtempCategory', '模板分类'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('outMrTemp.mrtempCategory', '模板分类'),
        }),
        span: 2,
        class: 'w-80',
        extraProps: {
          options: mrtempCategoryOptions.map((item) => ({
            label: item,
            value: item,
          })),
          multiple: true,
          filterable: true,
        },
      },
      {
        name: 'mrTempDesc',
        isFullWidth: true,
        label: t('outMrTemp.mrTempDesc', '模板描述'),
        component: 'input',
        type: 'textarea',
        placeholder: t('global:placeholder.input.template', {
          content: t('outMrTemp.mrTempDesc', '模板描述'),
        }),
      },
    ],
  });
  return data;
}
