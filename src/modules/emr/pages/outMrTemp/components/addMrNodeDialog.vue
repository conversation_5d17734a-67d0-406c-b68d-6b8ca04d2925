<script lang="ts" name="addMrNodeDialog" setup>
  import { ProDialog, ProForm } from 'sun-biz';
  import { type FormInstance } from 'element-sun';
  import { computed, nextTick, ref, watch } from 'vue';
  import { ENABLED_FLAG, FLAG } from '@/utils/constant';
  import { useAddMrNodeUpsertConfig } from '../config/useFormConfig';
  import { addMrTempTree, editMrTempTree } from '@/modules/emr/api/outMrTemp';

  const props = defineProps<{
    originalData?: OutMrTemp.MrTempTreeList[];
    selectedNode?: OutMrTemp.MrTempTreeList | null;
    optionsMap?: {
      [key: string]: { label: string; value: string }[];
    };
  }>();

  const formRef = ref<{
    ref: FormInstance;
    model: OutMrTemp.UpsertMrTempTreeParams;
  }>();

  const dialogRef = ref();
  const dialogForm = ref<OutMrTemp.UpsertMrTempTreeParams>({
    mrTempTreeName: '',
    mrTempTreeParentId: '',
    encounterTypeCode: '',
    enabledFlag: ENABLED_FLAG.YES,
  });
  const modeName = ref('add'); // add, edit

  // 内部状态管理
  const internalOperationType = ref<'child' | 'sibling'>('child');
  const internalSelectedNode = ref<OutMrTemp.MrTempTreeList | null>();
  const internalOriginalData = ref<OutMrTemp.MrTempTreeList[]>([]);
  const encounterTypeCodeOptions = ref<{ value: string; label: string }[]>([]);

  const emits = defineEmits<{ success: [] }>();

  // 计算节点选项（只显示mrTempTreeFlag为0的节点供选择）
  const superiorNodeOptions = computed(() => {
    if (!internalOriginalData.value?.length) {
      return [];
    }
    // 只显示 mrTempTreeFlag === 0 的节点供用户选择
    const options = internalOriginalData.value
      .filter((item) => item.mrTempTreeFlag === FLAG.NO)
      ?.map((item) => ({
        label: item.mrTempTreeName || '',
        value: item.mrTempTreeId || '',
      }));
    return options;
  });

  watch(
    () => props,
    () => {
      encounterTypeCodeOptions.value =
        props.optionsMap?.encounterTypeList || [];
      internalOriginalData.value = props.originalData || [];
      internalSelectedNode.value = props.selectedNode;
    },
    {
      deep: true,
      immediate: true,
    },
  );

  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params = {
            ...dialogForm.value,
            ...formRef?.value?.model,
          };
          let res;
          if (modeName.value === 'edit') {
            // 编辑模式，调用编辑API
            [, res] = await editMrTempTree(params);
          } else {
            // 新增模式，调用新增API
            [, res] = await addMrTempTree(params);
          }
          if (res?.success) {
            dialogRef.value?.close();
            emits('success');
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error(res?.errorMessage || '保存失败')]);
            return;
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };

  const handleClose = () => {
    dialogRef.value.close();
  };

  const openDialog = (operationType: 'child' | 'sibling' | 'edit') => {
    // 更新内部状态
    if (operationType === 'edit') {
      internalOperationType.value = 'child'; // 编辑时不需要特殊的操作类型逻辑
    } else {
      internalOperationType.value = operationType;
    }
    dialogRef.value?.open();

    // 重置或设置表单数据
    let mrTempTreeParentId = '';
    if (
      !internalSelectedNode.value?.mrTempTreeId ||
      !internalOperationType.value
    ) {
      mrTempTreeParentId = '';
    } else if (internalOperationType.value === 'child') {
      // 添加子节点时，父节点为选中节点
      mrTempTreeParentId = internalSelectedNode.value?.mrTempTreeId || '';
    } else if (internalOperationType.value === 'sibling') {
      // 添加同级节点时，父节点为选中节点的父节点
      mrTempTreeParentId = internalSelectedNode.value?.mrTempTreeParentId || '';
    }
    if (operationType === 'edit') {
      // 编辑模式，填充现有数据
      dialogForm.value = {
        ...dialogForm.value,
        ...internalSelectedNode.value,
        mrTempTreeParentId,
      };
      modeName.value = 'edit';
    } else {
      // 新增模式，重置表单
      dialogForm.value = {
        mrTempTreeParentId,
        mrTempTreeName: '',
        encounterTypeCode: '',
        enabledFlag: ENABLED_FLAG.YES,
      };
      modeName.value = 'add';
    }
    nextTick(() => {
      formRef.value?.ref.clearValidate();
    });
  };

  // 清空数据
  const handleReset = async () => {
    formRef.value?.ref.resetFields();
    formRef.value?.ref.clearValidate();
  };

  // 动态生成表单配置
  const formConfig = useAddMrNodeUpsertConfig(
    superiorNodeOptions,
    encounterTypeCodeOptions,
  );

  defineExpose({ dialogRef, openDialog });
</script>

<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="onConfirm"
    :include-footer="false"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          done();
          handleReset();
        });
      }
    "
    :title="`${$t(`global:${modeName}`)}${internalOperationType === 'child' ? $t('outMrTemp.child', '子') : $t('outMrTemp.sibling', '同级')}${$t('outMrTemp.mrTempNode', '节点')}`"
    class="w-2/5"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="dialogForm"
      :column="3"
      :data="formConfig"
      class="w-full"
    />
    <div class="flex items-center justify-center">
      <el-button class="mt-4" type="primary" @click="onConfirm">
        {{ $t('global:save') }}
      </el-button>
      <el-button class="ml-2 mt-4" @click="handleClose"
        >{{ $t('global:cancel') }}
      </el-button>
    </div>
  </ProDialog>
</template>
