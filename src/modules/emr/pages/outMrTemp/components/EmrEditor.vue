<script lang="ts" setup>
  import { onBeforeUnmount, onMounted, ref, watch } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { ElMessage } from 'element-sun';
  import { Handle_EventShowContextMenu } from './contextMenu.ts';
  import { type ShowContextMenuArgs } from './contextMenu';
  import { type DCRichEditCtl } from '../../../typings/DCRichEditCtl.ts';

  const { t } = useTranslation();
  // 类型声明

  // 编辑器控件类型声明
  interface EmrEditorDocument {
    New?: () => void;
    ReadOnly?: boolean;
  }

  interface EmrEditorControl {
    CreateNew?: () => void;
    NewDocument?: () => void;
    CreateDocument?: () => void;
    Document?: EmrEditorDocument;
    LoadDocumentFromString?: (
      content: string,
      type: string,
      extra: string,
    ) => void;
    LoadFromString?: (content: string) => void;
    SetDocumentContent?: (content: string) => void;
    SetControlReadonly?: (readonly: boolean) => void;
    SetReadonly?: (readonly: boolean) => void;
    ReadOnly?: boolean;
    Dispose?: () => void;
    SetToolBarVisibility?: (visible?: boolean) => void;
    SaveDocumentToString?: (options: Record<string, string>) => string;
    UserLoginByParameter?: (
      userId?: string,
      userName?: string,
      mode?: number,
    ) => void;
    InComplexViewMode?: (visible?: boolean) => boolean; // 修改返回类型为 boolean
    EventShowContextMenu?: (
      eventSender: DCRichEditCtl,
      args: ShowContextMenuArgs,
    ) => void;
    PrintDocument?: () => void;
    DCExecuteCommand?: (command: string, a: boolean, b: null) => void;
    CleanViewMode?: () => void;
    DocumentSelection?: (mode: string) => string;
  }

  declare global {
    interface Window {
      CreateWriterControlForWASM:
        | ((container: HTMLElement) => void)
        | undefined;
    }
  }

  const emrEditor = ref(null);
  const ctl = ref<EmrEditorControl | null>(null); // 编辑器控件对象
  let timer = ref<number | null>(null); // 定时器
  const loading = ref<boolean>(true); // 页面是否加载

  const props = defineProps({
    medicalRecordContent: {
      type: String,
      default: '',
    },
    isReadonly: {
      type: Boolean,
      default: false,
    },
    templateId: {
      type: String,
      default: '',
    },
    registerCode: {
      type: String,
      default: '',
    },
    templateName: {
      type: String,
      default: '',
    },
    save: {
      type: Function,
      default: () => {},
    },
  });

  // 初始化编辑器控件
  const init = () => {
    if (!window.CreateWriterControlForWASM) {
      console.error('[EmrEditor] window.CreateWriterControlForWASM 不存在');
      return;
    }
    if (timer.value !== null) {
      window.clearInterval(timer.value);
      timer.value = null;
    }
    if (!emrEditor.value) {
      console.error('[EmrEditor] emrEditor容器未渲染');
      return;
    }
    ctl.value = emrEditor.value;
    loading.value = false;
    try {
      window.CreateWriterControlForWASM(emrEditor.value);
      // 内容和只读状态
      setTimeout(() => {
        if (props.medicalRecordContent) {
          addXML(props.medicalRecordContent);
        } else {
          createNewDocument();
        }
        setReadonly(props.isReadonly);
      }, 200);
      if (ctl.value) {
        ctl.value.EventShowContextMenu = (eventSender, args) => {
          Handle_EventShowContextMenu(eventSender, args, { getImpValues });
        };
      }
    } catch (e) {
      ElMessage.error(
        t('EmrEditor.ControlCreateFailed', '病历编辑器加载失败，请刷新页面'),
      );
      console.error('[EmrEditor] 控件创建失败', e);
    }
  };

  // 创建新文档
  function createNewDocument() {
    if (!ctl.value) {
      console.warn('[EmrEditor] createNewDocument: ctl.value 不存在');
      return;
    }
    try {
      if (typeof ctl.value.CreateNew === 'function') {
        ctl.value.CreateNew();
      } else if (typeof ctl.value.NewDocument === 'function') {
        ctl.value.NewDocument();
      } else if (typeof ctl.value.CreateDocument === 'function') {
        ctl.value.CreateDocument();
      } else if (
        ctl.value.Document &&
        typeof ctl.value.Document.New === 'function'
      ) {
        ctl.value.Document.New();
      } else {
        console.warn('[EmrEditor] 未找到创建新文档的方法');
      }
    } catch (e) {
      console.warn('[EmrEditor] 创建新文档失败', e);
    }
  }

  // 加载xml内容
  function addXML(content: string) {
    if (!ctl.value) {
      console.warn('[EmrEditor] addXML: ctl.value 不存在');
      return;
    }
    // 重新渲染编辑器，现在会比较慢，现在做法是空的赋值一个空 xml
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const xmlContent =
      '<XTextDocument xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" EditorVersionString="*******">\n   <EnableValueValidate>true</EnableValueValidate>\n   <XElements Count="5">\n      <Element xsi:type="XTextHeader">\n         <AcceptTab>true</AcceptTab>\n         <EnableValueValidate>true</EnableValueValidate>\n         <XElements Count="1">\n            <Element xsi:type="XParagraphFlag" StyleIndex="0">\n               <AutoCreate>true</AutoCreate>\n            </Element>\n         </XElements>\n      </Element>\n      <Element xsi:type="XTextBody">\n         <AcceptTab>true</AcceptTab>\n         <EnableValueValidate>true</EnableValueValidate>\n         <XElements Count="1" />\n      </Element>\n      <Element xsi:type="XTextFooter">\n         <AcceptTab>true</AcceptTab>\n         <EnableValueValidate>true</EnableValueValidate>\n         <XElements Count="1" />\n      </Element>\n      <Element xsi:type="XTextHeaderForFirstPage">\n         <AcceptTab>true</AcceptTab>\n         <EnableValueValidate>true</EnableValueValidate>\n         <XElements Count="1">\n            <Element xsi:type="XParagraphFlag" StyleIndex="0">\n               <AutoCreate>true</AutoCreate>\n            </Element>\n         </XElements>\n      </Element>\n      <Element xsi:type="XTextFooterForFirstPage">\n         <AcceptTab>true</AcceptTab>\n         <EnableValueValidate>true</EnableValueValidate>\n         <XElements Count="1" />\n      </Element>\n   </XElements>\n   <FileFormat>XML</FileFormat>\n   <ContentStyles>\n      <Default xsi:type="DocumentContentStyle">\n         <FontName>宋体</FontName>\n         <FontSize>12</FontSize>\n      </Default>\n      <Styles>\n         <Style Index="0">\n            <FontName>宋体</FontName>\n            <FontSize>12</FontSize>\n            <Align>Center</Align>\n         </Style>\n      </Styles>\n   </ContentStyles>\n   <Info>\n      <CreationTime>2025-07-13T00:54:31+08:00</CreationTime>\n      <LastModifiedTime>2025-07-15T19:53:48+08:00</LastModifiedTime>\n      <LastPrintTime>1980-01-01T00:00:00+08:00</LastPrintTime>\n      <Operator>DCSoft.Writer Version:*******</Operator>\n      <NumOfPage>1</NumOfPage>\n   </Info>\n   <LocalConfig />\n   <PageSettings />\n</XTextDocument>';

    let newContent = content || xmlContent || '';
    try {
      if (typeof ctl.value.LoadDocumentFromString === 'function') {
        ctl.value.LoadDocumentFromString(newContent, 'xml', '');
      } else if (typeof ctl.value.LoadFromString === 'function') {
        ctl.value.LoadFromString(newContent);
      } else if (typeof ctl.value.SetDocumentContent === 'function') {
        ctl.value.SetDocumentContent(newContent);
      } else {
        console.warn('[EmrEditor] 未找到加载内容的方法');
      }
      setReadonly(props.isReadonly);
    } catch (e) {
      console.warn('[EmrEditor] 加载内容失败', e);
    }
  }

  // 设置只读
  function setReadonly(isReadonly: boolean) {
    if (!ctl.value) {
      console.warn('[EmrEditor] setReadonly: ctl.value 不存在');
      return;
    }
    try {
      if (typeof ctl.value.SetControlReadonly === 'function') {
        ctl.value.SetControlReadonly(isReadonly);
        console.log(
          '[EmrEditor] 使用 SetControlReadonly 设置只读:',
          isReadonly,
        );
      } else if (typeof ctl.value.SetReadonly === 'function') {
        ctl.value.SetReadonly(isReadonly);
      } else if ('ReadOnly' in ctl.value) {
        ctl.value.ReadOnly = isReadonly;
      } else if (ctl.value.Document && 'ReadOnly' in ctl.value.Document) {
        ctl.value.Document.ReadOnly = isReadonly;
      } else {
        console.warn('[EmrEditor] 未找到设置只读的方法');
      }
    } catch (e) {
      console.warn('[EmrEditor] 设置只读失败', e);
    }
  }

  // 销毁控件
  function disposeEmr() {
    if (ctl.value && typeof ctl.value.Dispose === 'function') {
      try {
        ctl.value.Dispose();
        ctl.value = null;
      } catch (e) {
        console.warn('[EmrEditor] 销毁控件失败', e);
      }
    }
  }

  // 监听内容变化
  watch(
    () => props.medicalRecordContent,
    (val: string) => {
      if (ctl.value) {
        console.log('[EmrEditor] watch medicalRecordContent:', val.length);
        addXML(val);
      }
    },
  );

  // 监听只读变化
  watch(
    () => props.isReadonly,
    (isReadonly: boolean) => {
      if (ctl.value) {
        setTimeout(() => setReadonly(isReadonly), 100);
        ctl.value?.SetToolBarVisibility?.(!isReadonly);
      }
    },
    { immediate: true },
  );

  onMounted(async () => {
    try {
      loading.value = false;
      timer.value = window.setInterval(() => {
        if (window.CreateWriterControlForWASM && emrEditor.value) {
          init();
        }
      }, 1500);
    } catch (error) {
      console.error('[EmrEditor] 脚本加载失败:', error);
    }
  });

  onBeforeUnmount(() => {
    if (timer.value !== null) {
      window.clearInterval(timer.value);
      loading.value = false;
    }
    disposeEmr();
  });

  function handleSave() {
    console.log('[EmrEditor] handleSave called');
    if (ctl.value) {
      try {
        let options = {
          FileFormat: 'xml',
          commitusertrace: 'true',
          outputformatxml: 'true',
        };
        return ctl.value.SaveDocumentToString(options);
      } catch (e) {
        console.error('[EmrEditor] 保存内容失败', e);
      }
    } else {
      console.warn('[EmrEditor] ctl.value 不存在，无法保存内容');
    }
  }

  function getImpValues() {
    console.log('getImpValues called');
    // 在这里实现导入数据源的逻辑
  }

  defineExpose({
    handleSave,
  });
</script>

<template>
  <div class="flex h-full flex-col items-center justify-between">
    <!-- <div class="flex w-full justify-center">
      模板名称：{{ props.templateName }}
    </div> -->
    <div
      id="my-writer-control"
      ref="emrEditor"
      v-loading="loading"
      :RegisterCode="registerCode"
      autoCreateControl="false"
      dctype="WriterControlForWASM"
      loading-text="编辑器正在加载中..."
    >
      正在加载...
    </div>
  </div>
</template>

<style lang="scss" scoped>
  #my-writer-control {
    display: block !important;
  }
</style>
