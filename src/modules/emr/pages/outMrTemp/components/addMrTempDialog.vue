<script lang="ts" name="addMrTempDialog" setup>
  import { type FormInstance } from 'element-sun';
  import { computed, nextTick, onMounted, ref, watch } from 'vue';
  import {
    MAIN_APP_CONFIG,
    ProDialog,
    ProForm,
    useAppConfigData,
  } from 'sun-biz';
  import {
    BIZ_ID_TYPE_CODE,
    ENABLED_FLAG,
    FLAG,
    MR_TEMP_SHARE_TYPE_CODE,
    MR_TEMP_SOURCE_CODE,
    ORG_TYPE_CODE,
  } from '@/utils/constant';
  import { useAddMrTempConfig } from '../config/useFormConfig';
  import { addMrTemp, editMrTemp } from '@/modules/emr/api/outMrTemp';
  import {
    queryBizUnitListByExample,
    queryMedicalRecordTypeByExampleV1,
  } from '@/modules/baseConfig/api/medicalTypeDict';
  import { queryUserList } from '@modules/system/api/user';

  type UpsertMrTempParams = OutMrTemp.UpsertMrTempParams & {
    mrtempCategory?: string | string[];
    shareBizIds?: string[];
  };

  const props = defineProps<{
    originalData?: OutMrTemp.MrTempTreeList[];
    selectedNode?: OutMrTemp.MrTempTreeList | null;
    optionsMap?: {
      [key: string]: { label: string; value: string }[];
    };
  }>();

  const formRef = ref<{
    ref: FormInstance;
    model: UpsertMrTempParams;
  }>();
  const dialogRef = ref();
  const medicalRecordTypeList = ref<{ label: string; value: string }[]>([]);
  const selectOwnerIdOptions = ref<{ label: string; value: string }[]>([]);
  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);
  const bizUnitList = ref<MedicalTypeDict.OrganizationList[]>([]);
  const userList = ref<User.Item[]>([]);
  const internalSelectedNode = ref<OutMrTemp.MrTempTreeList>();
  const internalOriginalData = ref<OutMrTemp.MrTempTreeList[]>([]);
  const internalOptionMap = ref<{
    [key: string]: { label: string; value: string }[];
  }>({});

  const dialogForm = ref<UpsertMrTempParams>({
    mrTempName: '',
    mrTempTreeParentId: '',
    medicalRecordTypeId: '',
    encounterTypeCode: '',
    enabledFlag: ENABLED_FLAG.YES,
    ownerTypeCode: BIZ_ID_TYPE_CODE.DICT_HOSPITAL,
    ownerId: '',
    mrTempShareTypeCode: MR_TEMP_SHARE_TYPE_CODE.NOT_SHARED,
    mrTempDesc: '',
    mrTempDisplayName: '',
    diseaseName: '',
    shareBizIds: [],
    mrtempCategory: '',
    mrtempSourceCode: MR_TEMP_SOURCE_CODE.NORMAL,
    mrtempSourceId: '',
    mrTempContent: '',
  });

  // 是否禁用选择ownerId
  const isDisabledSelectOwnerId = ref(true);
  const isDisabledSelectShareBizIds = ref(true);
  const modeName = ref('add');

  const emits = defineEmits<{ success: [newNodeId?: string] }>();

  // 计算节点选项（只显示mrTempTreeFlag为0的节点供选择）
  const superiorNodeOptions = computed(() => {
    if (!internalOriginalData.value?.length) {
      return [];
    }
    // 只显示 mrTempTreeFlag === 0 的节点供用户选择
    const options = internalOriginalData.value
      .filter((item) => item.mrTempTreeFlag === FLAG.NO)
      ?.map((item) => ({
        label: item.mrTempTreeName || '',
        value: item.mrTempTreeId || '',
      }));
    return options;
  });

  watch(
    () => props,
    () => {
      internalOptionMap.value = {
        ...internalOptionMap.value,
        ...props.optionsMap,
      };
      internalOriginalData.value = props.originalData || [];
      internalSelectedNode.value = props.selectedNode || undefined;
    },
    {
      deep: true,
      immediate: true,
    },
  );

  // 查询病历类型
  const queryMedicalRecordTypeList = async () => {
    const [, res] = await queryMedicalRecordTypeByExampleV1({});
    if (res?.success) {
      medicalRecordTypeList.value =
        res.data.map((item) => ({
          label: item.medicalRecordTypeName,
          value: item.medicalRecordTypeId,
        })) || [];
    }
  };

  const queryBizUnitList = async () => {
    const [, res] = await queryBizUnitListByExample({
      pageSize: 500,
      pageNumber: 1,
      orgTypeCodes: [ORG_TYPE_CODE.DEPARTMENT],
      hospitalId: currentOrg?.orgId || '',
      enabledFlag: ENABLED_FLAG.YES,
    });
    if (res?.success) {
      bizUnitList.value = res.data || [];
    }
  };

  const fetchUserList = async (data?: { keyWord: string }) => {
    let defaultParams = {
      pageNumber: 1,
      pageSize: 400,
      enabledFlag: 1,
      hospitalId: currentOrg?.orgId || '',
      keyWord: '',
    };
    if (data?.keyWord) {
      defaultParams = {
        ...defaultParams,
        keyWord: data.keyWord || '',
      };
    }
    const [, res] = await queryUserList(defaultParams);
    if (res?.success) {
      userList.value = res.data || [];
    }
  };
  const changeOwnerIdSelect = (flag?: boolean, type?: string) => {
    // 根据传入的 flag 值动态设置是否禁用选择 ownerId

    if (type === BIZ_ID_TYPE_CODE.DICT_DEPARTMENT) {
      selectOwnerIdOptions.value = bizUnitList.value.map((item) => ({
        label: item.orgNameDisplay,
        value: item.orgId,
      }));
    }
    if (type === BIZ_ID_TYPE_CODE.DICT_USER) {
      selectOwnerIdOptions.value = userList.value.map((item) => ({
        label: item.userName,
        value: item.userId,
      }));
    }
    nextTick(() => {
      isDisabledSelectOwnerId.value = !!flag;
      dialogForm.value.ownerId = '';
    });
  };

  const changeMrTempShareTypeCode = (code?: string) => {
    // 根据传入的 code 值动态设置是否禁用选择 shareBizIds

    if (code === '3') {
      isDisabledSelectShareBizIds.value = false;
      selectOwnerIdOptions.value = bizUnitList.value.map((item) => ({
        label: item.orgNameDisplay,
        value: item.orgId,
      }));
    } else {
      isDisabledSelectShareBizIds.value = true;
    }
    nextTick(() => {
      dialogForm.value.shareBizIds = [];
    });
  };

  const formConfig = useAddMrTempConfig(
    internalOptionMap,
    superiorNodeOptions,
    modeName,
    isDisabledSelectOwnerId,
    isDisabledSelectShareBizIds,
    changeOwnerIdSelect,
    changeMrTempShareTypeCode,
    selectOwnerIdOptions,
    medicalRecordTypeList,
  );

  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params: OutMrTemp.UpsertMrTempParams = {
            ...dialogForm.value,
            ...formRef?.value?.model,
          };
          if (dialogForm.value.mrtempCategory?.length) {
            // 确保 mrtempCategory 不为空
            params.mrtempCategory = (
              dialogForm.value.mrtempCategory as string[]
            ).join(',');
          } else {
            params.mrtempCategory = '';
          }
          let res;
          if (modeName.value === 'edit') {
            console.log(params.shareBizIds, 'shareBizIds');
            // 编辑模式，先对比shareBizIds和mrTempSharingList，赋值给mrTempSharingList
            if (params.shareBizIds && Array.isArray(params.shareBizIds)) {
              // 以 shareBizIds 为主，生成 mrTempSharingList，编辑时优先保留原有信息，查不到则传空
              const oldMrTempSharingList: OutMrTemp.MrTempSharingList[] =
                Array.isArray(params.mrTempSharingList)
                  ? params.mrTempSharingList
                  : [];
              const newMrTempSharingList: OutMrTemp.MrTempSharingList[] = [];
              params.shareBizIds.forEach((bizId: string) => {
                console.log(bizId, 'bizId');
                // 在原有的 mrTempSharingList 里查找是否有对应 bizId 的项
                const found = oldMrTempSharingList.find(
                  (item: OutMrTemp.MrTempSharingList) => item.bizId === bizId,
                );
                if (found) {
                  // 如果找到了，保留原有信息
                  newMrTempSharingList.push({
                    mrTempSharingId: found.mrTempSharingId || '',
                    bizId: found.bizId || bizId,
                    bizName: found.bizName || '',
                    bizTypeCode: found.bizTypeCode || '',
                    bizTypeCodeDesc: found.bizTypeCodeDesc || '',
                  });
                } else {
                  // 没找到就传空，bizId 赋值，其他字段置空
                  newMrTempSharingList.push({
                    mrTempSharingId: '',
                    bizId: bizId,
                    bizName: '',
                    bizTypeCode: '',
                    bizTypeCodeDesc: '',
                  });
                }
              });
              params.mrTempSharingList = newMrTempSharingList;
            }
            [, res] = await editMrTemp(params);
          } else {
            // 新增模式，调用新增API
            [, res] = await addMrTemp(params);
          }

          if (res?.success) {
            dialogRef.value?.close();
            // 传递新创建的节点ID（如果是新增模式且有返回ID）
            const newNodeId =
              modeName.value === 'add' &&
              (
                res.data as {
                  mrTempTreeId: string;
                  mrTempId: string;
                }
              )?.mrTempTreeId
                ? (
                    res.data as {
                      mrTempTreeId: string;
                      mrTempId: string;
                    }
                  )?.mrTempTreeId
                : params.mrTempTreeId;
            emits('success', newNodeId);
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error(res?.errorMessage || '保存失败')]);
            return;
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };

  const handleClose = () => {
    dialogRef.value.close();
  };

  const openDialog = (openMode?: string, editData?: UpsertMrTempParams) => {
    // 重置或设置表单数据
    if (editData) {
      // 设置分类
      if (editData.mrtempCategory?.length) {
        // 确保 mrtempCategory 不为空
        editData.mrtempCategory = editData?.mrtempCategory?.split(',');
      } else {
        editData.mrtempCategory = [];
      }
      // 设置共享
      if (editData.mrTempSharingList?.length) {
        editData.shareBizIds = editData.mrTempSharingList.map(
          (item: OutMrTemp.MrTempSharingList) => item.bizId,
        );
      } else {
        editData.shareBizIds = [];
      }

      if (openMode === 'edit') {
        // 编辑模式，填充现有数据
        dialogForm.value = { ...editData };
        modeName.value = 'edit';
      } else {
        // 克隆模式
        // 编辑模式，填充现有数据
        dialogForm.value = { ...editData };
        dialogForm.value.mrtempSourceId = editData.mrTempId;
        dialogForm.value.mrtempSourceCode = MR_TEMP_SOURCE_CODE.COPY;
        dialogForm.value.mrTempId = '';
        dialogForm.value.mrTempName = `${editData.mrTempName}-克隆`;
        dialogForm.value.mrTempDisplayName = `${editData.mrTempDisplayName}-克隆`;
        modeName.value = 'copy';
      }
    } else {
      // 新增模式，重置表单
      dialogForm.value = {
        mrTempName: '',
        mrTempTreeParentId: internalSelectedNode.value?.mrTempTreeId || '',
        medicalRecordTypeId: '',
        encounterTypeCode: '',
        enabledFlag: ENABLED_FLAG.YES,
        ownerTypeCode: BIZ_ID_TYPE_CODE.DICT_HOSPITAL,
        ownerId: '',
        mrTempShareTypeCode: MR_TEMP_SHARE_TYPE_CODE.NOT_SHARED,
        mrTempDesc: '',
        mrTempDisplayName: '',
        diseaseName: '',
        shareBizIds: [],
        mrtempCategory: '',
        mrtempSourceCode: MR_TEMP_SOURCE_CODE.NORMAL,
        mrtempSourceId: '',
        mrTempContent: '',
      };
      modeName.value = 'add';
      isDisabledSelectOwnerId.value = true;
      selectOwnerIdOptions.value = [];
    }
    if (modeName.value !== 'add') {
      // 设置初始值
      if (dialogForm.value.ownerTypeCode === BIZ_ID_TYPE_CODE.DICT_DEPARTMENT) {
        isDisabledSelectOwnerId.value = false;
        selectOwnerIdOptions.value = bizUnitList.value.map((item) => ({
          label: item.orgNameDisplay,
          value: item.orgId,
        }));
      } else if (
        dialogForm.value.ownerTypeCode === BIZ_ID_TYPE_CODE.DICT_USER
      ) {
        isDisabledSelectOwnerId.value = false;
        selectOwnerIdOptions.value = userList.value.map((item) => ({
          label: item.userName,
          value: item.userId,
        }));
      } else if (
        dialogForm.value.ownerTypeCode === BIZ_ID_TYPE_CODE.DICT_HOSPITAL
      ) {
        isDisabledSelectOwnerId.value = true;
      }
      if (
        dialogForm.value.mrTempShareTypeCode ===
        MR_TEMP_SHARE_TYPE_CODE.SPECIFIED_DEPARTMENT_SHARED
      ) {
        isDisabledSelectShareBizIds.value = false;
        selectOwnerIdOptions.value = bizUnitList.value.map((item) => ({
          label: item.orgNameDisplay,
          value: item.orgId,
        }));
      }
    }

    dialogRef.value.open();
    nextTick(() => {
      formRef.value?.ref.clearValidate();
    });
  };

  // 清空数据
  const handleReset = async () => {
    dialogForm.value = {
      mrTempName: '',
      mrTempTreeParentId: '',
      medicalRecordTypeId: '',
      encounterTypeCode: '',
      enabledFlag: ENABLED_FLAG.YES,
      ownerTypeCode: BIZ_ID_TYPE_CODE.DICT_HOSPITAL,
      ownerId: '',
      mrTempShareTypeCode: MR_TEMP_SHARE_TYPE_CODE.NOT_SHARED,
      mrTempDesc: '',
      mrTempDisplayName: '',
      diseaseName: '',
      shareBizIds: [],
      mrtempCategory: '',
      mrtempSourceCode: MR_TEMP_SOURCE_CODE.NORMAL,
      mrtempSourceId: '',
      mrTempContent: '',
    };
    formRef.value?.ref.resetFields();
    formRef.value?.ref.clearValidate();
  };

  onMounted(() => {
    queryMedicalRecordTypeList();
    queryBizUnitList();
    fetchUserList();
  });

  defineExpose({ dialogRef, open: openDialog });
</script>

<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          done();
          handleReset();
        });
      }
    "
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="onConfirm"
    :include-footer="false"
    :title="`${$t(`global:${modeName}`)}${$t('outMrTemp.mrTemp', '病历模板')}`"
    class="w-3/5"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="dialogForm"
      :column="4"
      :data="formConfig"
    />
    <div class="flex items-center justify-center">
      <el-button class="mt-4" type="primary" @click="onConfirm">
        {{ $t('global:save') }}
      </el-button>

      <el-button class="ml-2 mt-4" @click="handleClose"
        >{{ $t('global:cancel') }}
      </el-button>
    </div>
  </ProDialog>
</template>
