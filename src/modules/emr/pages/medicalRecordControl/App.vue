<script lang="ts" name="medicalRecordControl" setup>
  import { useTranslation } from 'i18next-vue';
  import {
    MAIN_APP_CONFIG,
    useAppConfigData,
    queryParamListByNos,
  } from 'sun-biz';
  import { onMounted, ref, nextTick } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import {
    queryMrControlTreeByExample,
    deleteMrControlTree,
    editMrControl,
  } from '@/modules/emr/api/mrControl';
  import { FLAG } from '@/utils/constant';
  import EmrEditor from '@/modules/emr/pages/outMrTemp/components/EmrEditor.vue';
  import ControlUpsertDialog from './components/ControlUpsertDialog.vue';
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import emptyImage from '@/modules/emr/pages/outMrTemp/assets/emptyImage.png';

  const { t } = useTranslation();
  const { isCloudEnv, currentOrg } = useAppConfigData([
    MAIN_APP_CONFIG.IS_CLOUD_ENV,
    MAIN_APP_CONFIG.CURRENT_ORG,
  ]);
  const emrEditorRef = ref();
  const controlUpsertDialogRef = ref();
  const dialogFormMode = ref(''); // addChildNode 添加子节点, addPeerNode 添加同级节点, addMrControl 新增组件 designMrControl 设计组件 edit 编辑

  const loading = ref(false);
  const mrControlTreeData = ref<MrControl.MrCtrlTreeItem[]>([]);
  const mrControlTreeOriginalData = ref<MrControl.MrCtrlTreeItem[]>([]);
  const selectedNode = ref<MrControl.MrCtrlTreeItem>();
  const mrControlDetail = ref<MrControl.MrControlInfo>(); // 病历组件详情
  const isDesignMode = ref<boolean>(false); // 设计模式状态
  const showEmr = ref<boolean>(true); // 编辑模式状态
  const keyWord = ref(''); // 检索关键字
  const splitValue = ref([25, 75]);
  const handleResize = (sizes: number[]) => {
    splitValue.value = sizes;
  };
  const registerCode = ref<string>('');

  const getTreeData = (treeDataList?: MrControl.MrCtrlTreeItem[]) => {
    const data = treeDataList;
    if (!data || data.length === 0) {
      return [];
    }
    const map = {} as { [key: string]: MrControl.TreeDataItem };
    const treeData: MrControl.TreeDataItem[] = [];
    data.forEach((item: MrControl.MrCtrlTreeItem) => {
      const node = {
        ...item,
        label: item.mrCtrlTreeName,
        value: item.mrCtrlTreeId,
        children: [],
      };
      map[item.mrCtrlTreeId] = node;
    });
    data.forEach((item) => {
      const node = map[item.mrCtrlTreeId];
      if (item.mrCtrlTreeParentId) {
        const parentNode = map[item.mrCtrlTreeParentId];
        if (parentNode) {
          parentNode.children.push(node);
        } else {
          treeData.push(node);
        }
      } else {
        treeData.push(node);
      }
    });
    return treeData;
  };

  const queryMrControlTree = async () => {
    selectedNode.value = undefined;
    mrControlDetail.value = undefined;
    loading.value = true;
    const [, res] = await queryMrControlTreeByExample({
      keyWord: keyWord.value,
    });
    loading.value = false;
    if (res?.success) {
      if (!keyWord.value) {
        mrControlTreeOriginalData.value = res.data || [];
      }
      mrControlTreeData.value = getTreeData(res.data);
    }
  };

  // 树节点选择事件
  const handleNodeClick = async (data: MrControl.MrCtrlTreeItem) => {
    selectedNode.value = data;
    isDesignMode.value = false; // 重置编辑模式
    showEmr.value = false;
    if (data.mrCtrlFlag === FLAG.YES) {
      mrControlDetail.value = data.mrControl;
      showEmr.value = true;
    } else {
      mrControlDetail.value = undefined; // 清除组件详情
    }
  };

  const handelUpsertOperation = (mode: string) => {
    dialogFormMode.value = mode;
    nextTick(() => {
      switch (mode) {
        case 'addChildNode':
        case 'addPeerNode':
        case 'addMrControl':
        case 'edit':
          controlUpsertDialogRef.value.open();
          break;
        case 'designMrControl':
          // 开启编辑模式（即使 mrControlDetail 为空也可以编辑）
          isDesignMode.value = true;
          break;
        default:
          break;
      }
    });
  };

  // 删除组件或节点
  const onDeleteClick = async () => {
    ElMessageBox.confirm(
      t('outMrTemp.delete', '您确定要删除“{{name}}”吗', {
        name: `${selectedNode.value?.mrCtrlTreeName}`,
      }),
      t('global:tip', '提示'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        mrCtrlTreeId: selectedNode.value?.mrCtrlTreeId || '',
      };
      const [, res] = await deleteMrControlTree(params);
      if (res?.success) {
        queryMrControlTree();
      }
    });
  };

  // 处理取消编辑
  const handleCancelDesign = () => {
    isDesignMode.value = false;
  };

  // 从头部按钮触发保存
  const handleSaveDesign = async () => {
    if (
      emrEditorRef.value &&
      typeof emrEditorRef.value.handleSave === 'function' &&
      mrControlDetail.value
    ) {
      const content = emrEditorRef.value.handleSave();
      const params = {
        ...mrControlDetail.value,
        mrControlContent: content,
      };

      const [, res] = await editMrControl(params);
      if (res?.success) {
        isDesignMode.value = false;
      }
    } else {
      ElMessage.error('编辑器未准备就绪，请稍后再试');
    }
  };

  // 加载外部js
  function loadScript(src: string) {
    return new Promise<void>((resolve, reject) => {
      if (document.querySelector(`script[src="${src}"]`)) {
        return resolve();
      }
      const script = document.createElement('script');
      script.src = src;
      script.onload = () => {
        resolve();
      };
      script.onerror = (e) => {
        reject(e);
      };
      document.head.appendChild(script);
    });
  }

  const loadEmrEditor = async () => {
    // 1. 获取编辑器服务
    const list =
      (await queryParamListByNos({
        hospitalId: currentOrg?.orgId || '',
        paramNos: ['9007', '9010'],
      })) || [];
    let ServicePage = '';
    list.forEach((item) => {
      if (item?.data?.[0]?.paramNo === '9007') {
        ServicePage = item.data[0].paramSettingList[0].paramValue;
      }
      if (item?.data?.[1]?.paramNo === '9010') {
        registerCode.value = item.data[1].paramSettingList[0].paramValue;
        registerCode.value = item.data[1].paramSettingList[0].paramValue ?? '';
        if (!registerCode.value) {
          console.error('[EmrEditor] 编辑器注册码 未配置');
        }
      }
    });
    if (!ServicePage) {
      ElMessageBox.alert('请先配置参数9007', '提示', {
        confirmButtonText: '确定',
        callback: () => {},
      });
      return;
    }
    // 2. 加载jQuery和编辑器js
    const Jquery =
      'https://cdn.bootcdn.net/ajax/libs/jquery/1.7.2/jquery.min.js';
    await loadScript(Jquery);
    await loadScript(ServicePage);
    // 重新赋值 Jquery 到全局 window 对象
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    window.rawWindow.jQuery = window.jQuery;
  };

  onMounted(() => {
    queryMrControlTree();
    loadEmrEditor();
  });
</script>
<template>
  <div class="p-box h-full">
    <Splitpanes
      v-model="splitValue"
      class="default-theme p-box"
      @resize="handleResize"
    >
      <Pane
        :size="splitValue[0]"
        class="flex flex-col"
        style="background-color: #fff"
      >
        <el-input
          class="mb-2"
          v-model="keyWord"
          :placeholder="$t('global:placeholder.keyword')"
          prefix-icon="Search"
          @keydown="
            (e: KeyboardEvent) => {
              if (e.key === 'Enter') {
                queryMrControlTree();
              }
            }
          "
        >
          <template #append>
            <el-button
              :loading="loading"
              type="primary"
              @click="() => queryMrControlTree()"
            >
              {{ $t('global:query') }}
            </el-button>
          </template>
        </el-input>
        <el-tree
          ref="treeRef"
          :data="mrControlTreeData"
          default-expand-all
          highlight-current
          :expand-on-click-node="false"
          :props="{
            label: 'label',
            children: 'children',
          }"
          @node-click="handleNodeClick"
        >
          <template #default="{ data }">
            <div>
              <span class="mr-1">
                {{ data.mrCtrlFlag ? '📄' : '📁' }}
              </span>
              <span>{{ data.mrCtrlTreeName }}</span>
              <el-tag
                v-if="data.mrCtrlFlag"
                class="ml-2"
                size="small"
                type="primary"
              >
                {{ $t('medicalRecordControl.mrControl', '组件') }}
              </el-tag>
            </div>
          </template>
        </el-tree>
      </Pane>
      <Pane
        :size="splitValue[1]"
        class="flex h-full flex-col justify-center"
        style="background-color: #fff"
      >
        <div v-if="!isDesignMode" class="mb-3">
          <el-button
            :disabled="
              !mrControlTreeData.length ||
              !selectedNode ||
              selectedNode?.mrCtrlFlag === FLAG.YES
            "
            type="primary"
            @click="handelUpsertOperation('addChildNode')"
            >{{
              $t('medicalRecordControl.buttons.addChildNode', '添加子节点')
            }}</el-button
          >
          <el-button
            :disabled="selectedNode?.mrCtrlFlag === FLAG.YES"
            type="primary"
            @click="handelUpsertOperation('addPeerNode')"
            >{{
              $t('medicalRecordControl.buttons.addPeerNode', '添加同级节点')
            }}</el-button
          >
          <el-button
            :disabled="!selectedNode || selectedNode.mrCtrlFlag === FLAG.YES"
            type="primary"
            @click="handelUpsertOperation('addMrControl')"
          >
            {{ $t('medicalRecordControl.buttons.addMrControl', '新增组件') }}
          </el-button>
          <el-button
            :disabled="
              !selectedNode ||
              selectedNode.mrCtrlFlag !== FLAG.YES ||
              (selectedNode.mrCtrlFlag === FLAG.YES &&
                mrControlDetail?.editableFlag === FLAG.NO &&
                !isCloudEnv)
            "
            type="primary"
            @click="handelUpsertOperation('designMrControl')"
          >
            {{ $t('medicalRecordControl.buttons.designMrControl', '设计组件') }}
          </el-button>
          <el-button
            :disabled="
              !selectedNode ||
              (selectedNode.mrCtrlFlag === FLAG.YES &&
                mrControlDetail?.editableFlag === FLAG.NO &&
                !isCloudEnv)
            "
            type="primary"
            @click="handelUpsertOperation('edit')"
          >
            {{ $t('global:edit') }}
          </el-button>
          <el-button
            :disabled="
              !selectedNode ||
              (selectedNode.mrCtrlFlag === FLAG.YES &&
                mrControlDetail?.editableFlag === FLAG.NO &&
                !isCloudEnv)
            "
            type="danger"
            @click="onDeleteClick()"
          >
            {{ $t('global:delete') }}
          </el-button>
        </div>
        <div v-else class="mb-3 flex justify-end">
          <el-button type="primary" @click="handleSaveDesign()">
            {{ $t('global:save', '保存') }}
          </el-button>
          <el-button plain type="primary" @click="handleCancelDesign()">
            {{ $t('global:cancel', '取消') }}
          </el-button>
        </div>
        <el-scrollbar class="h-full pb-1 pr-1.5">
          <EmrEditor
            v-show="
              selectedNode && selectedNode.mrCtrlFlag === FLAG.YES && showEmr
            "
            ref="emrEditorRef"
            :is-readonly="!isDesignMode"
            :medical-record-content="
              (typeof mrControlDetail?.mrControlContent === 'string'
                ? mrControlDetail.mrControlContent
                : '') || ''
            "
            :register-code="registerCode"
            :template-id="mrControlDetail?.mrControlId || ''"
            :template-name="mrControlDetail?.mrControlName || ''"
            @cancel="handleCancelDesign"
            @save="handleSaveDesign"
          />
          <div
            v-show="
              !selectedNode || selectedNode.mrCtrlFlag !== FLAG.YES || !showEmr
            "
            class="flex h-full items-center justify-center"
          >
            <div v-if="selectedNode">
              <el-empty
                :image="emptyImage"
                :image-size="400"
                :description="$t('outMrTemp.pleaseSelectMRT', '请选择病历组件')"
              />
            </div>
            <el-empty v-else description="暂无数据" />
          </div>
        </el-scrollbar>
      </Pane>
    </Splitpanes>
    <ControlUpsertDialog
      ref="controlUpsertDialogRef"
      :mode="dialogFormMode"
      :selected-node="selectedNode"
      :original-data="mrControlTreeOriginalData"
      @success="queryMrControlTree"
    />
  </div>
</template>
