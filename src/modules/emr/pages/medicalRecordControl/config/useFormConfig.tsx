import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import {
  BIZ_ID_TYPE_CODE,
  ENABLED_FLAG,
  FLAG,
  MR_CONTROL_TYPE_CODE_NAME,
} from '@/utils/constant';

const ownerTypeCodeList = [
  // { label: '医院', value: BIZ_ID_TYPE_CODE.DICT_HOSPITAL },
  { label: '科室', value: BIZ_ID_TYPE_CODE.DICT_DEPARTMENT },
  { label: '个人', value: BIZ_ID_TYPE_CODE.DICT_USER },
];

export function useControlFormUpsertConfig(
  formData: Ref<
    | MrControl.MrControlTreeUpsertParams
    | MrControl.MrControlUpsertParams
    | undefined
  >,
  isMrCtrlTreeEdit: Ref<boolean>,
  superiorNodeOptions: Ref<{ label: string; value: string }[]>,
  ownerIdSelectOptions: Ref<{ label: string; value: string }[]>,
  handleOwnerTypeCodeChange: (value?: string) => void,
) {
  return useFormConfig({
    dataSetCodes: [MR_CONTROL_TYPE_CODE_NAME],
    getData: (t, dataSet) =>
      isMrCtrlTreeEdit.value
        ? [
            {
              name: 'mrCtrlTreeParentId',
              label: t('medicalRecordControl.mrCtrlTreeParentId', '上级节点'),
              component: 'select',
              placeholder: t('global:placeholder.select.template', {
                name: t('medicalRecordControl.mrCtrlTreeParentId', '上级节点'),
              }),
              isFullWidth: true,
              extraProps: {
                options: superiorNodeOptions.value || [],
                filterable: true,
                disabled: true,
              },
            },
            {
              name: 'mrCtrlTreeName',
              label: t('medicalRecordControl.mrCtrlTreeName', '节点名称'),
              component: 'input',
              placeholder: t('global:placeholder.input.template', {
                content: t('medicalRecordControl.mrCtrlTreeName', '节点名称'),
              }),
              isFullWidth: true,
              rules: [
                {
                  required: true,
                  message: t('global:placeholder.input.template', {
                    content: t(
                      'medicalRecordControl.mrCtrlTreeName',
                      '节点名称',
                    ),
                  }),
                  trigger: ['change', 'blur'],
                },
              ],
              extraProps: {},
            },
          ]
        : [
            {
              name: 'mrCtrlTreeParentId',
              label: t('medicalRecordControl.mrCtrlTreeParentId', '上级节点'),
              component: 'select',
              placeholder: t('global:placeholder.select.template', {
                name: t('medicalRecordControl.mrCtrlTreeParentId', '上级节点'),
              }),
              span: 2,
              extraProps: {
                options: superiorNodeOptions.value || [],
                filterable: true,
                disabled: true,
              },
            },
            {
              name: 'mrControlName',
              label: t('medicalRecordControl.mrControlName', '组件名称'),
              component: 'input',
              placeholder: t('global:placeholder.input.template', {
                content: t('medicalRecordControl.mrControlName', '组件名称'),
              }),
              span: 2,
              rules: [
                {
                  required: true,
                  message: t('global:placeholder.input.template', {
                    content: t(
                      'medicalRecordControl.mrControlName',
                      '组件名称',
                    ),
                  }),
                  trigger: ['change', 'blur'],
                },
              ],
              extraProps: {},
            },
            {
              name: 'ownerTypeCode',
              label: t('medicalRecordControl.ownerId', '所有者'),
              component: 'select',
              placeholder: t('global:placeholder.select.template', {
                name: t('medicalRecordControl.ownerTypeCode', '所有者类型'),
              }),
              span: 2,
              rules: [
                {
                  required: true,
                  message: t('global:placeholder.select.template', {
                    name: t('medicalRecordControl.ownerTypeCode', '所有者类型'),
                  }),
                  trigger: ['change', 'blur'],
                },
              ],
              class: 'w-64',
              extraProps: {
                options: ownerTypeCodeList,
                filterable: true,
                clearable: false,
                onChange: (value?: string) => {
                  handleOwnerTypeCodeChange(value);
                },
              },
            },
            {
              name: 'ownerId',
              component: 'select',
              placeholder: t('global:placeholder.select.template', {
                name: t('medicalRecordControl.ownerId', '所有者'),
              }),
              span: 2,
              class: 'w-80',
              extraProps: {
                // disabled: !!(
                //   formData.value &&
                //   (formData.value as MrControl.MrControlUpsertParams)
                //     .ownerTypeCode === BIZ_ID_TYPE_CODE.DICT_HOSPITAL
                // ),
                options: ownerIdSelectOptions.value || [],
                filterable: true,
              },
            },
            {
              label: t(
                'medicalRecordControl.mrControlTypeCode',
                '病历组件类型',
              ),
              name: 'mrControlTypeCode',
              component: 'select',
              placeholder: t('global:placeholder.select.template', {
                name: t(
                  'addressBook.searchFrom.contactsCategoryCode',
                  '病历组件类型',
                ),
              }),
              span: 2,
              rules: [
                {
                  required: true,
                  message: t('global:placeholder.select.template', {
                    name: t(
                      'addressBook.searchFrom.contactsCategoryCode',
                      '病历组件类型',
                    ),
                  }),
                  trigger: ['change', 'blur'],
                },
              ],
              extraProps: {
                options: dataSet?.value
                  ? dataSet.value[MR_CONTROL_TYPE_CODE_NAME]
                  : [],
              },
            },
            {
              label: t('medicalRecordControl.editableFlag', '允许编辑'),
              name: 'editableFlag',
              component: 'switch',
              extraProps: {
                'inline-prompt': true,
                'active-value': FLAG.YES,
                'inactive-value': FLAG.NO,
                'active-text': t('global:enabled', '启用'),
                'inactive-text': t('global:disabled', '禁用'),
              },
            },
            {
              label: t('medicalRecordControl.enabledFlag', '启用标志'),
              name: 'enabledFlag',
              component: 'switch',
              extraProps: {
                'inline-prompt': true,
                'active-value': ENABLED_FLAG.YES,
                'inactive-value': ENABLED_FLAG.NO,
                'active-text': t('global:enabled', '启用'),
                'inactive-text': t('global:disabled', '禁用'),
              },
            },
          ],
  });
}
