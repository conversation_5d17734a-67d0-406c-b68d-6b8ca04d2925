<script lang="ts" name="ControlUpsertDialog" setup>
  import { useTranslation } from 'i18next-vue';
  import {
    ProDialog,
    ProForm,
    useAppConfigData,
    MAIN_APP_CONFIG,
  } from 'sun-biz';
  import { type FormInstance } from 'element-sun';
  import { computed, nextTick, ref, watch, onMounted } from 'vue';
  import { ENABLED_FLAG, FLAG, BIZ_ID_TYPE_CODE } from '@/utils/constant';
  import { useControlFormUpsertConfig } from '../config/useFormConfig';
  import {
    addMrControlTree,
    editMrControlTree,
    addMrControl,
    editMrControl,
  } from '@/modules/emr/api/mrControl';
  import { queryUserList } from '@/modules/system/api/user';
  import { queryDepartmentListByExample } from '@/modules/system/api/org';

  const props = defineProps<{
    mode?: string;
    originalData?: MrControl.MrCtrlTreeItem[];
    selectedNode?: MrControl.MrCtrlTreeItem;
  }>();

  const formRef = ref<{
    ref: FormInstance;
    model:
      | MrControl.MrControlTreeUpsertParams
      | MrControl.MrControlUpsertParams;
  }>();

  const { t } = useTranslation();
  const dialogRef = ref();
  const formData = ref<
    MrControl.MrControlTreeUpsertParams | MrControl.MrControlUpsertParams
  >();
  const dialogMode = ref(); // addChildNode 添加子节点, addPeerNode 添加同级节点, addMrControl 新增组件  edit 编辑

  const internalSelectedNode = ref<MrControl.MrCtrlTreeItem>();
  const internalOriginalData = ref<MrControl.MrCtrlTreeItem[]>([]);

  const userList = ref<{ label: string; value: string }[]>([]);
  const departmentList = ref<{ label: string; value: string }[]>([]);
  const ownerIdSelectOptions = ref<{ label: string; value: string }[]>([]);

  const emits = defineEmits<{ success: [] }>();

  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);

  const isMrCtrlTreeEdit = computed(
    () =>
      ['addChildNode', 'addPeerNode'].includes(dialogMode.value) ||
      (dialogMode.value === 'edit' &&
        internalSelectedNode?.value?.mrCtrlFlag === FLAG.NO),
  );

  // 计算节点选项（只显示 mrCtrlFlag 为 0 的节点供选择）
  const superiorNodeOptions = computed(() => {
    if (!internalOriginalData.value?.length) {
      return [];
    }
    // 只显示 mrCtrlFlag === 0 的节点供用户选择
    const options = internalOriginalData.value
      .filter((item) => item.mrCtrlFlag === FLAG.NO)
      ?.map((item) => ({
        label: item.mrCtrlTreeName || '',
        value: item.mrCtrlTreeId || '',
      }));
    return options;
  });

  const dialogTitle = computed(() => {
    let title = '';
    switch (dialogMode.value) {
      case 'addChildNode':
        title = t(
          'medicalRecordControl.controlUpsertDialog.dialogTitle.addChildNode',
          '添加子节点',
        );
        break;
      case 'addPeerNode':
        title = t(
          'medicalRecordControl.controlUpsertDialog.dialogTitle.addPeerNode',
          '添加同级节点',
        );
        break;
      case 'addMrControl':
        title = t(
          'medicalRecordControl.controlUpsertDialog.dialogTitle.addMrControl',
          '新增组件',
        );
        break;
      case 'edit':
        title = t(
          'medicalRecordControl.controlUpsertDialog.dialogTitle.edit',
          '编辑',
        );
        break;
    }
    return title;
  });

  watch(
    () => props,
    () => {
      dialogMode.value = props.mode;
      internalOriginalData.value = props.originalData || [];
      internalSelectedNode.value = props.selectedNode;
    },
    {
      deep: true,
      immediate: true,
    },
  );

  const queryDepartmentList = async () => {
    const [, res] = await queryDepartmentListByExample({
      hospitalId: currentOrg?.orgId || '',
      enabledFlag: ENABLED_FLAG.YES,
    });
    if (res?.success) {
      departmentList.value = (res.data || []).map((item) => ({
        label: item.orgNameDisplay || '',
        value: item.orgId || '',
      }));
    }
  };

  const fetchUserList = async (keyWord?: string) => {
    const params = {
      pageNumber: 1,
      pageSize: 400,
      enabledFlag: ENABLED_FLAG.YES,
      hospitalId: currentOrg?.orgId || '',
      keyWord: keyWord,
    };
    const [, res] = await queryUserList(params);
    if (res?.success) {
      userList.value = (res.data || []).map((item) => ({
        label: item.userName,
        value: item.userId,
      }));
    }
  };

  const onConfirm = async () => {
    const isValid = await formRef?.value?.ref.validate();
    if (!isValid) {
      return;
    }
    let params = {
      ...formData.value,
      ...formRef?.value?.model,
    };
    let res;
    switch (dialogMode.value) {
      case 'addChildNode':
        [, res] = await addMrControlTree(
          params as MrControl.MrControlTreeUpsertParams,
        );
        break;
      case 'addPeerNode':
        [, res] = await addMrControlTree(
          params as MrControl.MrControlTreeUpsertParams,
        );
        break;
      case 'addMrControl':
        [, res] = await addMrControl(params as MrControl.MrControlUpsertParams);
        break;
      case 'edit': {
        if (isMrCtrlTreeEdit.value) {
          params = {
            ...params,
            mrCtrlTreeId: internalSelectedNode.value!.mrCtrlTreeId,
          } as MrControl.MrControlTreeUpsertParams;
          [, res] = await editMrControlTree(
            params as MrControl.MrControlTreeUpsertParams,
          );
        } else {
          params = {
            ...params,
            mrControlId: internalSelectedNode.value?.mrControl?.mrControlId,
          } as MrControl.MrControlUpsertParams;
          [, res] = await editMrControl(
            params as MrControl.MrControlUpsertParams,
          );
        }
        break;
      }
    }
    if (res?.success) {
      dialogRef.value?.close();
      emits('success');
    }
  };

  const handleClose = () => {
    dialogRef.value.close();
  };

  const openDialog = () => {
    switch (dialogMode.value) {
      case 'addChildNode':
        formData.value = {
          mrCtrlTreeName: '',
          mrCtrlTreeParentId:
            internalSelectedNode.value?.mrCtrlFlag === FLAG.NO
              ? internalSelectedNode.value.mrCtrlTreeId
              : '',
        };
        break;
      case 'addPeerNode':
        formData.value = {
          mrCtrlTreeName: '',
          mrCtrlTreeParentId:
            internalSelectedNode.value?.mrCtrlFlag === FLAG.NO
              ? internalSelectedNode.value.mrCtrlTreeParentId
              : '',
        };
        break;
      case 'addMrControl':
        formData.value = {
          mrControlName: '',
          mrCtrlTreeParentId:
            internalSelectedNode.value?.mrCtrlFlag === FLAG.NO
              ? internalSelectedNode.value.mrCtrlTreeId
              : '',
          mrControlTypeCode: '',
          ownerTypeCode: BIZ_ID_TYPE_CODE.DICT_DEPARTMENT,
          ownerId: '',
          enabledFlag: ENABLED_FLAG.YES,
          mrControlContent: '',
          editableFlag: FLAG.YES,
        };
        ownerIdSelectOptions.value = departmentList.value;
        break;
      case 'edit': {
        if (internalSelectedNode.value?.mrCtrlFlag === FLAG.NO) {
          formData.value = {
            mrCtrlTreeId: internalSelectedNode.value.mrCtrlTreeId,
            mrCtrlTreeName: internalSelectedNode.value.mrCtrlTreeName,
            mrCtrlTreeParentId: internalSelectedNode.value.mrCtrlTreeParentId,
          };
        } else if (internalSelectedNode.value?.mrCtrlFlag === FLAG.YES) {
          formData.value = {
            mrCtrlTreeParentId: internalSelectedNode.value.mrCtrlTreeParentId,
            mrControlId: internalSelectedNode.value?.mrControl?.mrControlId,
            mrControlName:
              internalSelectedNode.value?.mrControl?.mrControlName || '',
            mrControlTypeCode:
              internalSelectedNode.value?.mrControl?.mrControlTypeCode || '',
            ownerTypeCode: internalSelectedNode.value?.mrControl?.ownerTypeCode,
            ownerId: internalSelectedNode.value?.mrControl?.ownerId,
            enabledFlag:
              internalSelectedNode.value?.mrControl?.enabledFlag ||
              ENABLED_FLAG.NO,
            mrControlContent:
              internalSelectedNode.value?.mrControl?.mrControlContent,
            editableFlag:
              internalSelectedNode.value?.mrControl?.editableFlag || FLAG.NO,
          };
          switch (formData.value.ownerTypeCode) {
            case BIZ_ID_TYPE_CODE.DICT_DEPARTMENT: {
              ownerIdSelectOptions.value = departmentList.value;
              break;
            }
            case BIZ_ID_TYPE_CODE.DICT_USER: {
              ownerIdSelectOptions.value = userList.value;
              break;
            }
            case BIZ_ID_TYPE_CODE.DICT_HOSPITAL: {
              ownerIdSelectOptions.value = [];
              break;
            }
          }
        }
        break;
      }
    }
    dialogRef.value?.open();
    // console.log('formData===', formData.value);
    nextTick(() => {
      formRef.value?.ref.clearValidate();
    });
  };

  // 清空数据
  const handleReset = async () => {
    formRef.value?.ref.resetFields();
    formRef.value?.ref.clearValidate();
  };

  const handleOwnerTypeCodeChange = (value?: string) => {
    switch (value) {
      case BIZ_ID_TYPE_CODE.DICT_DEPARTMENT: {
        ownerIdSelectOptions.value = departmentList.value;
        break;
      }
      case BIZ_ID_TYPE_CODE.DICT_USER: {
        ownerIdSelectOptions.value = userList.value;
        break;
      }
      case BIZ_ID_TYPE_CODE.DICT_HOSPITAL: {
        ownerIdSelectOptions.value = [];
        break;
      }
    }
    nextTick(() => {
      if (formData.value) {
        (formData.value as MrControl.MrControlUpsertParams).ownerId = undefined;
      }
    });
  };

  const formConfig = useControlFormUpsertConfig(
    formData,
    isMrCtrlTreeEdit,
    superiorNodeOptions,
    ownerIdSelectOptions,
    handleOwnerTypeCodeChange,
  );

  onMounted(() => {
    fetchUserList();
    queryDepartmentList();
  });
  defineExpose({ dialogRef, open: openDialog });
</script>

<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="() => {}"
    :include-footer="false"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          done();
          handleReset();
        });
      }
    "
    :title="dialogTitle"
    class="w-2/5"
    destroy-on-close
  >
    <ProForm
      ref="formRef"
      v-model="formData"
      :data="formConfig"
      class="w-full"
    />
    <div class="mt-4 flex items-center justify-center">
      <el-button type="primary" @click="onConfirm">
        {{ $t('global:save') }}
      </el-button>
      <el-button @click="handleClose">{{ $t('global:cancel') }} </el-button>
    </div>
  </ProDialog>
</template>
