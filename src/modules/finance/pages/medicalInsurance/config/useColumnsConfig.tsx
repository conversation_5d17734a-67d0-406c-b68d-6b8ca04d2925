import { Ref } from 'vue';
import { FLAG } from '@/utils/constant';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';
import { saveReimburseType } from '@/modules/finance/api/reimburseType';
import { InterfaceReqItem, InterfaceReqParams } from '@/api/types';
// 来源table的tsx
export function useMedInsuranceConfig(
  operationFn: (
    row: MedInsurance.MedInsuranceReqItem,
    disabledStatus: boolean,
  ) => Promise<void>,
  handleEnableSwitch: (row: MedInsurance.MedInsuranceReqItem) => Promise<void>,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        prop: 'selection',
        type: 'selection',
      },
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        minWidth: 100,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('global:code'),
        prop: 'medInsuranceNo',
        minWidth: 150,
      },
      {
        label: t('global:name'),
        prop: 'medInsuranceName',
        minWidth: 150,
      },
      {
        label: t('global:secondName'),
        prop: 'medInsurance2ndName',
        minWidth: 150,
      },
      {
        label: t('global:thirdName'),
        prop: 'medInsuranceExtName',
        minWidth: 150,
      },
      {
        label: t('global:spellNo'),
        prop: 'spellNo',
        minWidth: 150,
      },
      {
        label: t('global:wbNo'),
        prop: 'wbNo',
        minWidth: 150,
      },
      {
        label: t('global:createTime'),
        prop: 'createdAt',
        minWidth: 170,
      },
      {
        label: t('global:lastModifiedTime'),
        prop: 'modifiedAt',
        minWidth: 170,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 150,
        render: (row: MedInsurance.MedInsuranceReqItem) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              onChange={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('medInsurance.reimburseType', '报销类型'),
        prop: 'reimburseTypeList',
        minWidth: 170,
        render: (row: MedInsurance.MedInsuranceReqItem) => {
          return (
            <div class="flex flex-wrap items-center gap-2">
              {row.reimburseTypeList?.map((item) => (
                <el-tag type="primary">{item.reimburseTypeName}</el-tag>
              ))}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 150,
        fixed: 'right',
        render: (row: MedInsurance.MedInsuranceReqItem) => {
          return (
            <div class="flex justify-around" key="editable">
              <el-button
                type="primary"
                link={true}
                onClick={() => operationFn(row, false)}
              >
                {t('global:edit')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => operationFn(row, true)}
              >
                {t('global:view')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
}

export function useReimburseTypeConfig(options: {
  data: Ref<ReimburseType.ReimburseTypeReqItem[]>;
  tableRef: Ref<TableRef>;
  searchModel: Ref<MedInsurance.MedInsuranceReqQuery>;
  interfaceList: Ref<InterfaceReqItem[]>;
  handleEnableSwitch: (
    row: ReimburseType.ReimburseTypeReqItem,
  ) => Promise<void>;
  getInterfaceList: (params: InterfaceReqParams) => Promise<void>;
  queryMedInsuranceData: (
    params?:
      | MedInsurance.MedInsuranceReqQuery
      | ReimburseType.ReimburseTypeReqQuery,
  ) => Promise<void>;
}) {
  const {
    data,
    tableRef,
    searchModel,
    interfaceList,
    getInterfaceList,
    handleEnableSwitch,
    queryMedInsuranceData,
  } = options;
  const { toggleEdit, cancelEdit, addItem } = useEditableTable({
    tableRef,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: data as any,
    id: 'reimburseTypeId',
  });
  const tableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        minWidth: 100,
        render: (row: ReimburseType.ReimburseTypeReqItem, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t('global:name'),
        prop: 'reimburseTypeName',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:name'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: ReimburseType.ReimburseTypeReqItem & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.reimburseTypeName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('global:name'),
                })}
              />
            );
          } else {
            return <>{row.reimburseTypeName}</>;
          }
        },
      },
      {
        label: t('global:secondName'),
        prop: 'reimburseTypeName',
        minWidth: 150,
        editable: true,
        render: (
          row: ReimburseType.ReimburseTypeReqItem & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.reimburseType2ndName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('global:secondName'),
                })}
              />
            );
          } else {
            return <>{row.reimburseType2ndName}</>;
          }
        },
      },
      {
        label: t('global:thirdName'),
        prop: 'reimburseTypeName',
        minWidth: 150,
        editable: true,
        render: (
          row: ReimburseType.ReimburseTypeReqItem & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.reimburseTypeExtName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('global:thirdName'),
                })}
              />
            );
          } else {
            return <>{row.reimburseTypeExtName}</>;
          }
        },
      },
      {
        label: t('reimburseType.interfaceId', '对应接口'),
        prop: 'interfaceId',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('reimburseType.interfaceId', '对应接口'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: ReimburseType.ReimburseTypeReqItem & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-select
                v-model={row.interfaceId}
                remote={true}
                filterable={true}
                remote-method={async (keyWord: string) => {
                  await getInterfaceList({
                    keyWord,
                    interfaceTypeCode: '1',
                    hospitalId: searchModel.value.hospitalId as string,
                  });
                }}
                placeholder={t('global:placeholder.select.template', {
                  name: t('reimburseType.interfaceId', '对应接口'),
                })}
                onChange={(val: string) => {
                  const obj = interfaceList.value?.find(
                    (item) => item.interfaceId === val,
                  );
                  row.interfaceName = obj?.interfaceName ?? '';
                }}
              >
                {interfaceList.value?.map((item) => (
                  <el-option
                    key={item.interfaceId}
                    label={item.interfaceName}
                    value={item.interfaceId}
                  />
                ))}
              </el-select>
            );
          } else {
            return <>{row.interfaceName}</>;
          }
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 150,
        render: (
          row: ReimburseType.ReimburseTypeReqItem & {
            editable: boolean;
          },
        ) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              onChange={() => {
                if (row?.editable) {
                  if (row.enabledFlag === FLAG.YES) {
                    row.enabledFlag = FLAG.NO;
                  } else {
                    row.enabledFlag = FLAG.YES;
                  }
                } else {
                  handleEnableSwitch(row);
                }
              }}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 100,
        render: (
          row: ReimburseType.ReimburseTypeReqItem & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return (
            <>
              {row.editable ? (
                <div class={'flex justify-around'} key="editable">
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => cancelEdit(row, $index)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={async () => {
                      const isValid = await tableRef.value?.validateRow($index);
                      if (!isValid) return;
                      const [, res] = await saveReimburseType({
                        ...row,
                        hospitalId: searchModel.value.hospitalId as string,
                        sort: $index,
                      });
                      if (res?.success) {
                        row.editable = false;
                        await queryMedInsuranceData();
                      }
                    }}
                  >
                    {t('global:save')}
                  </el-button>
                </div>
              ) : (
                <div class={'flex justify-center'}>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => toggleEdit(row)}
                  >
                    {t('global:edit')}
                  </el-button>
                </div>
              )}
            </>
          );
        },
      },
    ],
  });
  return {
    tableConfig,
    addItem,
  };
}
