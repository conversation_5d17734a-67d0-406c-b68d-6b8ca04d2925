<script setup lang="ts" name="searchBar">
  import { ref } from 'vue';
  import { TABS_TYPE } from '../constant.ts';
  import { DmlButton, ProForm } from 'sun-biz';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant';

  const { activeName, searchModel, searchConfig, draggableFlag, bizData } =
    defineProps<{
      activeName: string;
      searchModel:
        | MedInsurance.MedInsuranceReqQuery
        | ReimburseType.ReimburseTypeReqQuery;
      draggableFlag: boolean;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      searchConfig: any;
      bizData: string[];
    }>();

  const emit = defineEmits(['modelChange', 'success', 'menuConfig', 'addFn']);

  const formModel = ref<
    MedInsurance.MedInsuranceReqQuery | ReimburseType.ReimburseTypeReqQuery
  >(searchModel);

  const handleModelChange = (
    data:
      | MedInsurance.MedInsuranceReqQuery
      | ReimburseType.ReimburseTypeReqQuery,
  ) => {
    emit('modelChange', data);
  };

  const dmlSuccess = () => {
    emit('success');
  };

  const handleMenuConfig = () => {
    emit('menuConfig');
  };

  const handleAddFn = () => {
    emit('addFn');
  };
</script>
<template>
  <div class="flex justify-between">
    <ProForm
      :class="draggableFlag ? 'mb-2' : 'mb-4'"
      layout-mode="inline"
      v-model="formModel"
      :data="searchConfig"
      :show-search-button="true"
      @model-change="handleModelChange"
    />
    <div>
      <DmlButton
        v-if="activeName === TABS_TYPE.MED_INSURANCE"
        :code="BIZ_ID_TYPE_CODE.DICT_MED_INSURANCE"
        :biz-data="bizData"
        class="mr-4"
        @success="dmlSuccess"
      />
      <el-button
        type="primary"
        @click="handleMenuConfig"
        v-if="activeName === TABS_TYPE.MED_INSURANCE"
      >
        {{ $t('menu.config', '按菜单配置') }}
      </el-button>
      <el-button type="primary" @click="handleAddFn">{{
        $t('global:add')
      }}</el-button>
    </div>
  </div>
</template>
