<script setup lang="ts" name="menuConfig">
  import { ref, computed, nextTick } from 'vue';
  import { FLAG } from '@/utils/constant';
  import { generateUUID } from '@sun-toolkit/shared';
  import { useTranslation } from 'i18next-vue';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { Title, ProDialog, useAppConfigData, MAIN_APP_CONFIG } from 'sun-biz';
  import {
    queryMenuXMedInsuranceByExample,
    saveMenuXMedInsurance,
  } from '@/modules/finance/api/medicalInsurance';
  import { useMenuList } from '@/modules/finance/pages/payWay/hooks/useMenuList';
  import { queryMedInsuranceByExample } from '@/modules/finance/api/medicalInsurance';
  import menuCard from './menuCard.vue';

  const { t } = useTranslation();
  const { menuFilterList, getMenuList } = useMenuList();
  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);

  const dialogRef = ref();
  const scrollRef = ref();
  const menuMedRef = ref();

  const loading = ref(false);
  const medList = ref<MedInsurance.MedInsuranceReqItem[]>([]);
  const menuMedList = ref<MedInsurance.MenuXMedInsuranceReqItems[]>([]);

  const orgId = computed(() => currentOrg?.orgId);

  /** 打开弹窗 */
  const openDialog = async () => {
    nextTick(() => {
      dialogRef.value.open();
    });
    await queryMenuMedList();
    await getMenuList();
    await getMedList();
  };

  /** 查询根据菜单配置的医保费别 */
  const queryMenuMedList = async () => {
    loading.value = true;

    const [, res] = await queryMenuXMedInsuranceByExample({});
    loading.value = false;

    if (res?.success) {
      menuMedList.value = res?.data ?? [];
    }
  };

  /** 获取医保费别的列表 */
  const getMedList = async () => {
    const [, res] = await queryMedInsuranceByExample({
      enabledFlag: FLAG.YES,
      hospitalId: orgId.value as string,
    });
    if (res?.success) {
      medList.value = res?.data ?? [];
    }
  };

  /** 新增菜单配置 */
  const addMenuConfig = async () => {
    (
      menuMedList.value as (MedInsurance.MenuXMedInsuranceReqItems & {
        editable: boolean;
        key: string;
      })[]
    ).push({
      key: generateUUID(),
      menuId: '',
      menuName: '',
      editable: true,
      dictMedInsuranceList: [],
    });

    // 等待 DOM 更新
    nextTick(() => {
      // 获取滚动容器并设置滚动到底部
      if (scrollRef.value) {
        const wrapEl = scrollRef.value.wrapRef;
        if (wrapEl) {
          // 使用原生的scrollTo方法，支持平滑滚动
          wrapEl.scrollTo({
            top: wrapEl.scrollHeight,
            behavior: 'smooth',
          });
        }
      }
    });
  };

  /** 获取当前的点击的项 */
  const currentItemFn = async (
    item: MedInsurance.MenuXMedInsuranceReqItems,
  ) => {
    const obj = menuFilterList.value.find((im) => im.menuId === item.menuId);

    const objIndex = !(item as unknown as { editable: boolean })?.editable
      ? menuMedList.value.findIndex((im) => im.menuId === item.menuId)
      : menuMedList.value.findIndex(
          (im) =>
            (im as unknown as { key: string }).key ===
            (item as unknown as { key: string }).key,
        );

    return { obj, objIndex };
  };

  /** 移除当前选中那个card */
  const removeCard = async (item: MedInsurance.MenuXMedInsuranceReqItems) => {
    const { obj, objIndex } = await currentItemFn(item);

    if (objIndex === -1) return;

    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action: t('global:remove'),
        name: obj?.menuNameDisplay ?? '',
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(() => {
      menuMedList.value.splice(objIndex, 1);
    });
  };

  /** 过滤菜单、不重复*/
  const getAvailableMenuList = (
    currentItem: MedInsurance.MenuXMedInsuranceReqItems,
  ) => {
    return menuFilterList.value.filter(
      (menu) =>
        // 如果是当前项自身的menuId，则保留
        menu.menuId === currentItem.menuId ||
        // 如果不是其他项的menuId，则保留
        !menuMedList.value.some(
          (item) =>
            item.menuId === menu.menuId && item.menuId !== currentItem.menuId,
        ),
    );
  };

  /** 移除对应的菜单下的医保费别 */
  const removeCardMed = async (
    item: MedInsurance.MenuXMedInsuranceReqItems,
    index: number,
  ) => {
    const obj = (item.dictMedInsuranceList ?? [])[index];

    const medObj = (item.dictMedInsuranceList ?? []).find(
      (item) => item.medInsuranceId === obj.medInsuranceId,
    );

    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action: t('global:remove'),
        name: medObj?.medInsuranceName ?? '',
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(() => {
      const indexInParent = menuMedList.value.findIndex(
        (i) => i.menuId === item.menuId,
      );
      if (indexInParent !== -1) {
        // 创建新对象避免引用问题
        const updatedItem = {
          ...item,
          dictMedInsuranceList:
            item.dictMedInsuranceList?.filter((_, i) => i !== index) ?? [],
        };

        // 替换整个数组
        menuMedList.value = [
          ...menuMedList.value.slice(0, indexInParent),
          updatedItem,
          ...menuMedList.value.slice(indexInParent + 1),
        ];
      }
    });
  };

  /** 新增费别 */
  const addDictMed = async (item: MedInsurance.MenuXMedInsuranceReqItems) => {
    const { objIndex } = await currentItemFn(item);

    item.dictMedInsuranceList?.push({
      key: generateUUID(),
    } as unknown as MedInsurance.DictMedInsuranceReqItems);

    nextTick(() => {
      // 获取滚动容器并设置滚动到底部
      const scrollbarRef = menuMedRef.value[objIndex]?.medScrollCardRef;

      if (scrollbarRef) {
        try {
          const wrapEl = scrollbarRef.wrapRef;
          if (wrapEl) {
            // 使用原生的scrollTo方法，支持平滑滚动
            wrapEl.scrollTo({
              top: wrapEl.scrollHeight,
              behavior: 'smooth',
            });
          }
        } catch (error) {
          console.error('滚动设置失败:', error);
        }
      }
    });
  };

  /** 保存按菜单配置的医保费别 */
  const handleSubmit = async () => {
    // 确保menuMedRef.value是数组
    if (!Array.isArray(menuMedRef.value)) {
      console.warn('menuMedRef不是数组');
      return false;
    }

    // 执行所有表单的校验
    await Promise.all(
      (menuMedRef.value ?? []).map((ref) => ref?.formRef?.validate()),
    );

    // 检查是否有菜单的医保费别列表为空
    const hasEmptyMedList = (menuMedList.value ?? []).some(
      (item) => !item.dictMedInsuranceList?.length,
    );

    const hasEmptyMedInsurance = (menuMedList.value ?? []).some((item) =>
      (item.dictMedInsuranceList ?? []).some((im) => !im.medInsuranceId),
    );

    if (hasEmptyMedList) {
      ElMessage.error(
        t('medInsurance.empty.tips', '当前存在未添加医保费别的菜单，请先添加'),
      );
      return;
    }

    if (hasEmptyMedInsurance) {
      ElMessage.error(
        t('medInsurance.valid.tips', '当前存在未保存的医保费别，请先保存'),
      );
      return;
    }

    const params = menuMedList.value.map((item) => {
      return {
        menuId: item.menuId,
        dictMedInsuranceList:
          (item.dictMedInsuranceList ?? []).map((im, ix) => {
            return {
              menuXMedInsuranceId: im?.menuXMedInsuranceId ?? undefined,
              medInsuranceId: im?.medInsuranceId as string,
              sort: ix + 1,
            };
          }) ?? [],
      };
    });

    return await saveMenuXMedInsurance({ menuXMedInsuranceList: params });
  };

  const successFn = async () => {
    ElMessage.success(t('global:edit.success'));
  };

  defineExpose({
    open: openDialog,
  });
</script>

<template>
  <ProDialog
    class="w-3/5"
    ref="dialogRef"
    :title="$t('menu.config', '按菜单配置')"
    :confirm-fn="handleSubmit"
    @success="successFn"
  >
    <div class="flex size-full flex-col" v-loading="loading">
      <div class="flex w-full items-center justify-between">
        <Title :title="$t('select.menu', '菜单选择')" class="text-lg" />
        <el-button type="primary" @click="addMenuConfig">
          {{ $t('global:add') }}
        </el-button>
      </div>
      <el-scrollbar
        ref="scrollRef"
        height="53vh"
        class="w-full pr-2.5 pt-3"
        v-if="menuMedList?.length > 0"
      >
        <div class="grid grid-cols-3 gap-4">
          <menuCard
            ref="menuMedRef"
            v-for="item in menuMedList"
            :key="(item as any).key || item.menuId"
            :card-item="item"
            :menu-list="getAvailableMenuList(item)"
            :med-list="medList"
            @remove="removeCard"
            @remove-med="removeCardMed"
            @add-dict-med="addDictMed"
          />
        </div>
      </el-scrollbar>
      <div class="flex h-full items-center justify-center pt-3" v-else>
        <el-empty :description="$t('global:noData')"></el-empty>
      </div>
    </div>
  </ProDialog>
</template>
