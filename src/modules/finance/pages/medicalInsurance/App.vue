<script setup lang="ts" name="medicalInsurance">
  import { TABS_TYPE } from './constant.ts';
  import { useTranslation } from 'i18next-vue';
  import { ref, computed, Ref } from 'vue';
  import { useSearchFormConfig } from './config/useSearchConfigData.ts';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import {
    Title,
    // ProForm,
    // DmlButton,
    ProTable,
  } from 'sun-biz';
  import { ENABLED_FLAG, BIZ_ID_TYPE_CODE } from '@/utils/constant';
  import {
    useMedInsuranceConfig,
    useReimburseTypeConfig,
  } from './config/useColumnsConfig.tsx';
  import {
    saveReimburseType,
    queryReimburseTypeListByExample,
  } from '@/modules/finance/api/reimburseType';
  import {
    saveMedInsurance,
    queryMedInsuranceByExample,
    updateMedInsuranceSortByIds,
  } from '@/modules/finance/api/medicalInsurance';
  import { useGetInterface } from '@/hooks/useGetInterface';
  import { commonSort } from '@/api/common.ts';
  import dialogComponent from './components/dialogComponent.vue';
  import menuConfigDialog from './components/menuConfigDialog.vue';
  import searchBar from './components/searchBar.vue';

  const { t } = useTranslation();
  const { interfaceList, getInterfaceList } = useGetInterface();

  // 查询医保费别条件
  const searchModel = ref<
    MedInsurance.MedInsuranceReqQuery | ReimburseType.ReimburseTypeReqQuery
  >({
    keyWord: undefined,
    enabledFlag: ENABLED_FLAG.ALL,
    hospitalId: '',
  });
  const rowValue = ref<MedInsurance.MedInsuranceReqItem>(); //当前编辑行
  const rowDisabledStatus = ref<boolean>(false); //当前行编辑还是查看

  const loading = ref(false); //加载状态
  const tableData = ref<
    MedInsurance.MedInsuranceReqItem[] | ReimburseType.ReimburseTypeReqItem[]
  >([]); //当前table数据

  const activeName = ref<TABS_TYPE>(TABS_TYPE.MED_INSURANCE);
  const selectTableData = ref<MedInsurance.MedInsuranceReqItem[]>([]); //费别选中项

  const tableRef = ref(); // 表格Ref
  const reimburseTypeRef = ref(); // 表格Ref
  const dialogRef = ref(); // 弹窗组件Ref
  const menuConfigRef = ref(); // 菜单配置Ref

  // 标题
  const dialogTitle = computed(() => {
    return rowValue.value?.medInsuranceId
      ? t('medInsurance.manage.title.edit', '编辑医保费别')
      : t('medInsurance.manage.title.add', '新增医保费别');
  });
  // DML
  const bizData = computed(() => {
    const list = selectTableData.value.map((im) => {
      return im.medInsuranceId;
    });
    return list ?? [];
  });
  // 是否支持拖拽
  const draggableFlag = computed(() => {
    const isEdit = tableData.value.find(
      (item) => (item as { editable?: boolean }).editable,
    );
    if (
      isEdit ||
      searchModel.value.keyWord ||
      searchModel.value.enabledFlag !== ENABLED_FLAG.ALL
    ) {
      return false;
    }
    return true;
  });

  /** model-change变化 */
  const modelChange = async (
    data:
      | MedInsurance.MedInsuranceReqQuery
      | ReimburseType.ReimburseTypeReqQuery,
  ) => {
    searchModel.value = {
      ...searchModel.value,
      ...data,
    };

    await queryMedInsuranceData();
  };

  // 表格数据列表
  const queryMedInsuranceData = async () => {
    loading.value = true;

    const paramsVal = {
      ...searchModel.value,
      enabledFlag:
        searchModel.value?.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchModel.value.enabledFlag,
    };

    const [, res] =
      activeName.value === TABS_TYPE.MED_INSURANCE
        ? await queryMedInsuranceByExample(paramsVal)
        : await queryReimburseTypeListByExample(paramsVal);
    loading.value = false;
    if (res?.success) {
      tableData.value = res.data ?? [];
    }
  };

  //拖拽排序
  const handleSortEnd = async (list: MedInsurance.MedInsuranceReqItem[]) => {
    if (activeName.value === TABS_TYPE.MED_INSURANCE) {
      const arr: MedInsurance.UpdateMedInsuranceSortReqQuery[] = [];
      list?.forEach(async (item, index) => {
        arr.push({
          medInsuranceId: item.medInsuranceId,
          sort: index + 1,
        });
      });
      await updateMedInsuranceSortByIds({ dictMedInsuranceList: arr });
    } else if (activeName.value === TABS_TYPE.REIMBURSE_TYPE) {
      const arr: { bizId: string; sort: number }[] = [];
      list?.forEach(async (item, index) => {
        arr.push({
          bizId: (item as unknown as ReimburseType.ReimburseTypeReqItem)
            .reimburseTypeId,
          sort: index,
        });
      });
      const params = {
        bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_REIMBURSE_TYPE,
        bizIdList: arr,
      };
      const [, res] = await commonSort(params);
      if (res?.success) {
        ElMessage.success(t('global:modify.sort.success'));
      }
    }
    await queryMedInsuranceData();
  };

  /** 启用状态切换 */
  const handleEnableSwitch = async (
    row: MedInsurance.MedInsuranceReqItem | ReimburseType.ReimburseTypeReqItem,
  ) => {
    const params = {
      ...row,
      enabledFlag: (row.enabledFlag === ENABLED_FLAG.YES
        ? ENABLED_FLAG.NO
        : ENABLED_FLAG.YES) as number,
      hospitalId: searchModel.value.hospitalId,
      reimburseTypeIds:
        activeName.value === TABS_TYPE.MED_INSURANCE
          ? (
              (row as MedInsurance.MedInsuranceReqItem).reimburseTypeList ?? []
            ).map((item) => item.reimburseTypeId)
          : undefined,
    };
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name:
          activeName.value === TABS_TYPE.MED_INSURANCE
            ? (row as MedInsurance.MedInsuranceReqItem).medInsuranceNameDisplay
            : (row as ReimburseType.ReimburseTypeReqItem)
                .reimburseTypeNameDisplay,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] =
        activeName.value === TABS_TYPE.MED_INSURANCE
          ? await saveMedInsurance(
              params as MedInsurance.SaveMedInsuranceReqQuery,
            )
          : await saveReimburseType(
              params as ReimburseType.SaveReimburseTypeReqQuery,
            );
      if (res?.success) {
        row.enabledFlag =
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES;
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.NO
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        await queryMedInsuranceData();
      }
    });
  };

  const operationFn = async (
    row: MedInsurance.MedInsuranceReqItem | undefined,
    disabledStatus: boolean = false,
  ) => {
    rowValue.value = row;
    rowDisabledStatus.value = disabledStatus;
    dialogRef.value.open();
  };

  // 切换tab
  const changeTab = async (tab: TABS_TYPE) => {
    searchModel.value = {
      ...searchModel.value,
      keyWord: undefined,
      enabledFlag: ENABLED_FLAG.ALL,
    };
    activeName.value = tab as TABS_TYPE;
    tableData.value = [];
    await queryMedInsuranceData();
    if (tab === TABS_TYPE.REIMBURSE_TYPE) {
      await getInterfaceList({
        interfaceTypeCode: '1',
        hospitalId: searchModel.value.hospitalId as string,
      });
    }
  };

  // 新增
  const addFn = async () => {
    if (activeName.value === TABS_TYPE.MED_INSURANCE) {
      await operationFn(undefined, false);
    } else {
      addItem({
        editable: true,
        enabledFlag: ENABLED_FLAG.YES,
      });
    }
  };

  /** 关键字查询 */
  const queryData = async (keyWord?: string) => {
    searchModel.value = {
      ...searchModel.value,
      keyWord: keyWord,
    };

    await queryMedInsuranceData();
  };

  /** 按菜单配置 */
  const menuConfig = async () => {
    menuConfigRef.value.open();
  };

  // 检索条件配置数据
  const searchConfig = useSearchFormConfig({
    queryData,
  });

  const medInsuranceColumns = useMedInsuranceConfig(
    operationFn,
    handleEnableSwitch,
  );
  const { tableConfig, addItem } = useReimburseTypeConfig({
    searchModel: searchModel,
    interfaceList: interfaceList,
    getInterfaceList: getInterfaceList,
    handleEnableSwitch: handleEnableSwitch,
    tableRef: reimburseTypeRef,
    data: tableData as Ref<ReimburseType.ReimburseTypeReqItem[]>,
    queryMedInsuranceData,
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title
      :title="
        activeName === TABS_TYPE.MED_INSURANCE
          ? $t('medInsurance.list', '医保费别列表')
          : $t('reimburseType.list', '报销类型列表')
      "
      class="mb-2"
    />
    <el-tabs v-model="activeName" @tab-change="changeTab">
      <el-tab-pane
        :key="TABS_TYPE.MED_INSURANCE"
        :label="$t('medInsurance', '医保费别')"
        :name="TABS_TYPE.MED_INSURANCE"
      >
        <search-bar
          :active-name="activeName"
          :search-model="searchModel"
          :draggable-flag="draggableFlag"
          :search-config="searchConfig"
          :biz-data="bizData"
          @model-change="modelChange"
          @success="
            () => {
              tableRef?.proTableRef.clearSelection();
            }
          "
          @menu-config="menuConfig"
          @add-fn="addFn"
        />
        <ProTable
          ref="tableRef"
          :data="tableData"
          :loading="loading"
          :draggable="draggableFlag"
          @drag-end="handleSortEnd"
          :editable="false"
          :columns="medInsuranceColumns"
          :row-key="'medInsuranceId'"
          @selection-change="
            (val: MedInsurance.MedInsuranceReqItem[]) => {
              selectTableData = val;
            }
          "
        />
      </el-tab-pane>
      <el-tab-pane
        :key="TABS_TYPE.REIMBURSE_TYPE"
        :label="$t('reimburseType', '报销类型')"
        :name="TABS_TYPE.REIMBURSE_TYPE"
      >
        <search-bar
          :active-name="activeName"
          :search-model="searchModel"
          :draggable-flag="draggableFlag"
          :search-config="searchConfig"
          :biz-data="bizData"
          @model-change="modelChange"
          @menu-config="menuConfig"
          @add-fn="addFn"
        />
        <ProTable
          ref="reimburseTypeRef"
          :data="tableData"
          :loading="loading"
          :draggable="draggableFlag"
          @drag-end="handleSortEnd"
          :editable="true"
          :columns="tableConfig"
          :row-key="'reimburseTypeId'"
          @selection-change="
            (val: MedInsurance.MedInsuranceReqItem[]) => {
              selectTableData = val;
            }
          "
        />
      </el-tab-pane>
    </el-tabs>
    <!-- 检索项 -->
    <!-- <div class="flex justify-between">
      <ProForm
        :class="draggableFlag ? 'mb-2' : 'mb-4'"
        layout-mode="inline"
        v-model="searchModel"
        :data="searchConfig"
        :show-search-button="true"
        @model-change="modelChange"
      />
      <div>
        <DmlButton
          v-if="activeName === TABS_TYPE.MED_INSURANCE"
          :code="BIZ_ID_TYPE_CODE.DICT_MED_INSURANCE"
          :biz-data="bizData"
          class="mr-4"
          @success="
            () => {
              tableRef?.proTableRef.clearSelection();
            }
          "
        />
        <el-button type="primary" @click="menuConfig">
          {{ $t('menu.config', '按菜单配置') }}
        </el-button>
        <el-button type="primary" @click="addFn">{{
          $t('global:add')
        }}</el-button>
      </div>
    </div> -->

    <!-- 医保费别列表 -->
    <!-- <ProTable
      ref="tableRef"
      :key="activeName"
      :data="tableData"
      :loading="loading"
      :draggable="draggableFlag"
      @drag-end="handleSortEnd"
      :editable="activeName === TABS_TYPE.MED_INSURANCE ? false : true"
      :columns="
        activeName === TABS_TYPE.MED_INSURANCE
          ? medInsuranceColumns
          : tableConfig
      "
      :row-key="
        activeName === TABS_TYPE.MED_INSURANCE
          ? 'medInsuranceId'
          : 'reimburseTypeId'
      "
      @selection-change="
        (val: MedInsurance.MedInsuranceReqItem[]) => {
          selectTableData = val;
        }
      "
    /> -->
    <dialogComponent
      ref="dialogRef"
      :row-value="rowValue"
      :dialog-title="dialogTitle"
      :hospital-id="searchModel.hospitalId as string"
      :disabled-status="rowDisabledStatus"
      @success="queryMedInsuranceData"
    />

    <menuConfigDialog ref="menuConfigRef" />
  </div>
</template>
