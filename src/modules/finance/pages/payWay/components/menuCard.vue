<script setup lang="ts" name="menuCard">
  import { FLAG } from '@/utils/constant';
  import { Title } from 'sun-biz';
  import { ref, reactive } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { filterSelectData } from '@sun-toolkit/shared';
  import { type FormRules, type FormInstance } from 'element-sun';

  interface MenuPayWayReqItem extends PayWay.MenuPayWayReqItem {
    editable: boolean;
  }
  interface RuleForm {
    menuId: string;
    defaultMultiPayFlag: number;
    maxDisplayCount: number;
  }

  const props = defineProps<{
    cardItem: PayWay.MenuPayWayReqItem;
    cascaderProps: {
      label: string;
      value: string;
      children: string;
    };
    menuFilterList: Menu.MenuInfo[];
    payWayList: PayWay.PayWayReqItem[];
  }>();

  const emit = defineEmits(['remove', 'removeMenuPayWay', 'addMenuPayWay']);
  const { t } = useTranslation();

  const formRef = ref<FormInstance>();
  const cardItem = ref(props.cardItem);
  const filteredMenuList = ref(props.menuFilterList);

  const countValidator = (
    rule: unknown,
    value: string,
    callback: (error?: Error | undefined) => void,
  ) => {
    const regex = /^[0-9]+$/;
    const flag = regex.test(String(value));
    if (!flag) {
      callback(new Error(t('maxDisplayCount.thanZero', '最大展示需要为整数')));
    } else {
      callback();
    }
  };

  const rules = reactive<FormRules<RuleForm>>({
    menuId: [
      {
        required: true,
        message: t('global:placeholder.select.template', {
          name: t('sysXMenu.menuName', '菜单'),
        }),
        trigger: ['blur', 'change'],
      },
    ],
    defaultMultiPayFlag: [
      {
        required: true,
        message: t('global:placeholder.select.template', {
          name: t('default.style', '默认样式'),
        }),
        trigger: ['blur', 'change'],
      },
    ],
    maxDisplayCount: [
      {
        required: true,
        validator: countValidator,
        trigger: ['blur', 'change'],
      },
    ],
  });

  const filterMethod = (val: string) => {
    filteredMenuList.value = props.menuFilterList.filter((item) =>
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      String((item as any).menuGroupName)
        .toLowerCase()
        .includes(val.toLowerCase()),
    );
  };
  defineExpose({
    formRef,
  });
</script>

<template>
  <el-form
    ref="formRef"
    :model="cardItem"
    :rules="rules"
    @keydown.enter="(e: Event) => e.preventDefault()"
  >
    <el-card>
      <template #default>
        <div class="mb-2 flex items-center justify-between">
          <Title>
            <template #title>
              <el-form-item
                class="mb-0"
                v-if="(cardItem as unknown as MenuPayWayReqItem).editable"
                label-width="0px"
                prop="menuId"
              >
                <el-select
                  class="w-60"
                  v-model="cardItem.menuId"
                  :placeholder="$t('global:placeholder.select')"
                  :clearable="true"
                  :filterable="true"
                  :filter-method="filterMethod"
                >
                  <el-option
                    v-for="(item, index) in filteredMenuList"
                    :key="`${item.menuId}-${index}`"
                    :label="(item as any).menuGroupName"
                    :value="item.menuId"
                  ></el-option>
                </el-select>
              </el-form-item>
              <div class="h-8 text-lg leading-8" v-else>
                {{ cardItem.menuNameDisplay }}
              </div>
            </template>
          </Title>
          <el-icon @click="emit('remove', cardItem)" class="cursor-pointer">
            <Close />
          </el-icon>
        </div>
        <div class="flex">
          <el-form-item
            class="flex-1"
            :label="$t('default.style', '默认样式')"
            prop="defaultMultiPayFlag"
          >
            <el-radio-group
              v-model="cardItem.defaultMultiPayFlag"
              class="flex flex-1"
            >
              <el-radio :value="0" class="mr-2">{{
                $t('single.payWay', '单支付')
              }}</el-radio>
              <el-radio :value="1">{{
                $t('single.payWay', '多支付')
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            prop="maxDisplayCount"
            v-if="cardItem.defaultMultiPayFlag"
            :label="$t('menuPayWay.maxDisplayCount', '最大展示')"
          >
            <el-input-number
              class="w-32"
              value-on-clear="min"
              :clearable="false"
              :min="0"
              v-model="cardItem.maxDisplayCount"
              :validate-event="true"
              :placeholder="$t('global:placeholder.input')"
            />
          </el-form-item>
        </div>
        <div class="h-80">
          <el-scrollbar max-height="18.2rem">
            <div class="flex flex-col gap-2">
              <div
                class="line-item flex items-center justify-between p-2.5"
                v-for="(
                  item, index
                ) in cardItem.menuPayWayDtList as unknown as PayWay.MenuPayWayDtListItem[]"
                :key="item.payWayId ?? (item as any).key"
                @mouseover="item.defaultHoverFlag = true"
                @mouseleave="item.defaultHoverFlag = false"
              >
                <div v-if="item.editable">
                  <!-- @blur="() => (item.editable = false)" -->
                  <el-form-item
                    class="mb-0"
                    :rules="[
                      {
                        required: true,
                        message: $t('global:placeholder.select.template', {
                          name: $t('payWay.payWayName', '支付方式'),
                        }),
                        trigger: ['blur', 'change'],
                      },
                    ]"
                  >
                    <el-select
                      @change="
                        (val: string) => {
                          item.payWayName =
                            (props.payWayList.find((im) => im.payWayId === val)
                              ?.payWayNameDisplay as string) || '';
                          item.enabledFlag =
                            (props.payWayList.find((im) => im.payWayId === val)
                              ?.enabledFlag as number) || FLAG.YES;
                          item.editable = false;
                        }
                      "
                      class="w-36"
                      v-model="item.payWayId"
                      :placeholder="$t('global:placeholder.select')"
                      :filterable="true"
                    >
                      <el-option
                        v-for="im in filterSelectData(
                          props.payWayList,
                          cardItem.menuPayWayDtList.filter(
                            (imx) => imx.payWayId !== item.payWayId,
                          ),
                          'payWayId',
                        )"
                        :key="im.payWayId"
                        :value="im.payWayId"
                        :label="im.payWayNameDisplay"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div v-else class="cursor-pointer">
                  {{ item.payWayName }}
                </div>

                <div class="flex items-center gap-2">
                  <div
                    class="flex h-8 cursor-pointer items-center justify-center"
                    :style="{
                      color:
                        item.defaultFlag === FLAG.YES &&
                        item.defaultHoverFlag === true
                          ? 'var(--el-color-error)'
                          : 'var(--el-color-primary)',
                    }"
                    @click="
                      () => {
                        if (item.defaultFlag === FLAG.YES) {
                          item.defaultFlag = FLAG.NO;
                        } else {
                          cardItem.menuPayWayDtList.map((im) => {
                            im.defaultFlag = FLAG.NO;
                            return im;
                          });
                          item.defaultFlag = FLAG.YES;
                        }
                      }
                    "
                  >
                    {{
                      item.defaultFlag === FLAG.YES
                        ? item.defaultHoverFlag === true
                          ? t('payWay.cancelDefault', '取消默认')
                          : t('payWay.default', '默认')
                        : item.defaultHoverFlag === true
                          ? t('payWay.setDefault', '设为默认')
                          : ''
                    }}
                  </div>
                  <el-icon
                    class="cursor-pointer"
                    @click="
                      emit('removeMenuPayWay', {
                        index: index,
                        cardItem: cardItem,
                      })
                    "
                  >
                    <Close />
                  </el-icon>
                </div>
              </div>
            </div>
          </el-scrollbar>
          <el-button
            class="mt-2 w-full"
            @click="() => emit('addMenuPayWay', cardItem)"
          >
            {{ $t('global:add') }}
          </el-button>
        </div>
      </template>
    </el-card>
  </el-form>
</template>

<style lang="scss" scoped>
  /* stylelint-disable-next-line */
  :deep(.el-card__body) {
    padding: 10px 16px;
  }

  .line-item {
    background: var(--el-color-primary-light-9);
    border-radius: 4px;
  }

  :deep(.el-scrollbar) {
    height: auto;
  }
</style>
