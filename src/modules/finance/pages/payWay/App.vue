<script setup lang="ts" name="payWay">
  import { useGetPayWay } from './hooks/useGetPayWay.ts';
  import { useTranslation } from 'i18next-vue';
  import { useSearchFormConfig } from './config/useSearchFormConfig.ts';
  import { useTableColumnsConfig } from './config/useTableColumnsConfig.tsx';
  import { BIZ_ID_TYPE_CODE, FLAG } from '@/utils/constant.ts';
  import { ElMessageBox, ElMessage } from 'element-sun';
  import { ref, onMounted, computed } from 'vue';
  import { Title, ProForm, ProTable, DmlButton } from 'sun-biz';
  import {
    updatePayWaySortByIds,
    updatePayWayEnabledFlagById,
  } from '@/modules/finance/api/payWay.ts';
  import MenuDialog from './components/menuDialog.vue';
  import OperationDialog from './components/operationDialog.vue';

  export type searchFormType = {
    enabledFlag?: FLAG;
    keyWord?: string | undefined;
  };

  const searchFormModel = ref<searchFormType>({
    enabledFlag: FLAG.ALL,
    keyWord: undefined,
  }); // 检索条件数据
  const draggableFlag = ref(true); // 是否开启拖拽排序
  const selectTableData = ref<PayWay.PayWayReqItem[]>([]);
  const tableData = ref<PayWay.PayWayReqItem[]>([]);
  const rowValue = ref<PayWay.PayWayReqItem>();

  const dialogRef = ref();
  const menuDialogRef = ref();
  const tableColumnsRef = ref();

  // 标题
  const dialogTitle = computed(() => {
    return rowValue.value?.payWayId
      ? t('payWay.edit', '编辑支付方式')
      : t('payWay.add', '新增支付方式');
  });

  const bizData = computed(() => {
    const list = selectTableData.value.map((im) => {
      return im.payWayId;
    });
    return list ?? [];
  });

  const { t } = useTranslation();
  const { loading, payWayList, getPayWayList, result } = useGetPayWay();

  // 选中行设置
  const selectionChange = (val: PayWay.PayWayReqItem[]) => {
    selectTableData.value = val;
  };

  // 查询支付方式
  const queryPayWayList = async (
    params: searchFormType = {
      keyWord: undefined,
      enabledFlag: FLAG.ALL,
    },
  ) => {
    searchFormModel.value = {
      ...searchFormModel.value,
      ...params,
    };
    await getPayWayList(searchFormModel.value);
    if (
      (
        result.value as unknown as {
          success: boolean;
        }
      )?.success
    ) {
      tableData.value = payWayList.value;
      draggableFlag.value =
        searchFormModel.value.keyWord ||
        searchFormModel.value.enabledFlag !== FLAG.ALL
          ? false
          : true;
    }
  };

  // 排序
  const handleSortEnd = async (data: PayWay.PayWayReqItem[]) => {
    tableData.value = data.map((item, index) => {
      item.sort = index + 1;
      return item;
    });
    await updatePayWaySortByIds({
      payWaySortList:
        tableData.value as unknown as PayWay.UpdatePayWaySortReqParams,
    } as unknown as PayWay.UpdatePayWaySortReqParams);
    await queryPayWayList();
  };

  // 停启用切换
  const handleEnableSwitch = async (row: PayWay.PayWayReqItem) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.payWayNameDisplay,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        payWayId: row.payWayId,
        enabledFlag: row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES,
      };
      loading.value = true;
      const [, res] = await updatePayWayEnabledFlagById(params);
      loading.value = false;
      if (res?.success) {
        row.enabledFlag = row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES;
        ElMessage.success(
          t(
            row.enabledFlag === FLAG.YES
              ? 'global:enabled.success'
              : 'global:disabled.success',
          ),
        );
      }
    });
  };

  // 打开弹窗
  const operationFn = async (row: PayWay.PayWayReqItem) => {
    rowValue.value = row;
    dialogRef.value.open();
  };
  // 打开按菜单配置弹窗
  const menuOperation = async () => {
    menuDialogRef.value.open();
  };

  // 检索条件配置数据
  const searchConfig = useSearchFormConfig({
    queryPayWayList: queryPayWayList,
  });
  // 表格配置数据
  const { tableColumns } = useTableColumnsConfig({
    handleEnableSwitch: handleEnableSwitch,
    operationFn: operationFn,
  });

  onMounted(async () => {
    await queryPayWayList();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title class="mb-2" :title="$t('payWay.list', '支付方式列表')" />
    <div class="flex justify-between">
      <ProForm
        ref="searchFormRef"
        layout-mode="inline"
        v-model="searchFormModel"
        :data="searchConfig"
        :show-search-button="true"
        @model-change="queryPayWayList"
      />
      <div class="mb-3">
        <DmlButton
          :code="BIZ_ID_TYPE_CODE.DICT_PAY_WAY"
          :biz-data="bizData"
          @success="
            () => {
              tableColumnsRef?.proTableRef.clearSelection();
            }
          "
        />
        <el-button class="ml-3" type="primary" @click="menuOperation">
          {{ $t('operation.menu.edit', '按菜单配置') }}
        </el-button>
        <el-button type="primary" @click="operationFn">
          {{ $t('global:add') }}
        </el-button>
      </div>
    </div>
    <ProTable
      :class="draggableFlag ? '' : 'mt-1'"
      :draggable="draggableFlag"
      :data="tableData"
      :loading="loading"
      :columns="tableColumns"
      row-key="payWayId"
      ref="tableColumnsRef"
      @drag-end="handleSortEnd"
      @selection-change="selectionChange"
    />
    <OperationDialog
      ref="dialogRef"
      :row-value="rowValue as PayWay.PayWayReqItem"
      :dialog-title="dialogTitle"
      @success="queryPayWayList"
    />
    <MenuDialog
      ref="menuDialogRef"
      :dialog-title="$t('operation.menu.edit', '按菜单配置')"
      @success="
        () => {
          ElMessage.success($t('global:save.success'));
          queryPayWayList();
        }
      "
    />
  </div>
</template>
