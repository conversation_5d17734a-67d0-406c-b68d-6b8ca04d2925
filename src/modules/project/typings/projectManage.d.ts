declare namespace ProjectManage {
  interface QueryParams {
    ownerOrgId?: string;
    keyWord?: string;
    projectStatusCodes?: string[];
    devGroupCode?: string;
    pageNumber?: number;
    pageSize?: number;
  }

  interface ProjectInfo {
    projectId: string;
    projectName: string;
    projectName2nd: string;
    projectNameExt: string;
    wbNo: string;
    spellNo: string;
    ownerOrgId: string;
    ownerOrgName: string;
    projectStatusCode: string | PROJECT_STATUS_CODE;
    projectStatusCodeDesc: string;
    beginDate: string;
    endDate: string;
    projectManagerUserId: string;
    projectManagerUserName: string;
    teamerCount: number;
    createdUserId: string;
    createdUserName: string;
    createdAt: string;
    modifiedUserId: string;
    modifiedUserName: string;
    modifiedAt: string;
    freeServicePeriod: number;
    freeServiceEndDate: string;
    onlineDatePlan: string;
    onlineDate: string;
    closeDate: string;
    storySourceCode: string;
    storySourceCodeDesc: string;
    devGroupCode: string;
    devGroupCodeDesc: string;
    needTestFlag?: boolean;
  }

  interface ProjectUpsertParams {
    projectId?: string;
    projectName?: string;
    projectName2nd?: string;
    projectNameExt?: string;
    wbNo?: string;
    spellNo?: string;
    ownerOrgId?: string;
    projectStatusCode?: string | PROJECT_STATUS_CODE;
    beginDate?: string;
    endDate?: string;
    freeServicePeriod?: number;
    freeServiceEndDate?: string;
    onlineDatePlan?: string;
    onlineDate?: string;
    closeDate?: string;
    storySourceCode?: string;
    devGroupCode?: string;
  }

  interface ProjectTeamQueryParams {
    projectId?: string;
    teamerTypeCode?: string[];
    ownOrgId?: string;
  }

  interface ProjectTeamInfo {
    proTeamerId: string;
    userId: string;
    userName: string;
    userNo: string;
    deptId: string;
    deptName: string;
    teamerTypeCode: string;
    teamerTypeCodeDesc: string;
  }

  interface WorkItemXDescQueryParams {
    workItemTypeCode?: string;
  }

  interface WorkItemXDescItem {
    workItemXDescId: string;
    workItemTypeCode: string;
    workItemTypeCodeDesc: string;
    workItemDescTypeCode: string;
    workItemDescTypeCodeDesc: string;
    sort: number;
  }

  interface WorkItemXDescUpsertItem {
    workItemXDescId?: string;
    workItemTypeCode: string;
    workItemDescTypeCode: string;
    deleteFlag: number;
  }
}
