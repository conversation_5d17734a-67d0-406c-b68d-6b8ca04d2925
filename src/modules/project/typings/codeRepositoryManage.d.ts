declare namespace CodeRepositoryManage {
  interface QueryParams {
    keyWord?: string;
    enabledFlag?: number;
    codeRepositoryIds?: string[];
    sysId?: string;
    codeRepositoryTypeCode?: string;
  }

  interface CodeRepositoryInfo {
    codeRepositoryId: string;
    codeRepositoryName: string;
    codeRepositoryDesc: string;
    codeRepositoryAddr: string;
    codeRepositoryTypeCode: string;
    codeRepositoryTypeCodeDesc: string;
    sort: number;
    enabledFlag: number;
    createdUserId: string;
    createdUserName: string;
    createdAt: string;
    modifiedUserId: string;
    modifiedUserName: string;
    modifiedAt: string;
    loginUserId: string;
    loginUserName: string;
    codeBranchList: {
      codeRepositoryBranchId: string;
      codeBranchId: string;
      codeBranchName: string;
    }[];
    appPath?: string;
    backupPath?: string;
    port?: number | string;
    servicePrefix?: string;
    editable?: boolean;
    codeRepositoryNo?: string;
  }

  interface UpsertParams {
    apiId?: string;
    codeRepositoryId?: string;
    codeRepositoryName?: string;
    codeRepositoryDesc?: string;
    codeRepositoryAddr?: string;
    codeRepositoryTypeCode?: string;
    loginUserId?: string;
    loginUserName?: string;
    enabledFlag?: number;
    sort?: number;
    appPath?: string;
    backupPath?: string;
    mode?: string;
    port?: number | string;
    servicePrefix?: string;
    codeRepositoryNo?: string;
  }

  interface codeBranchSearch {
    keyWord?: string;
    hospitalId?: string;
  }

  interface CreateBranchParams {
    codeRepostitoryIds: string[];
    codeBranchIds: string[];
  }

  interface CodeRepositorySetting {
    codeRepositoryId: string;
    codeRepoXAppConfigList: CodeRepoXAppConfigList[];
  }

  interface CodeRepoXAppConfigList {
    codeRepoXAppConfigId?: string;
    appConfigItemId?: string;
    appConfigName?: string;
    editable?: boolean;
  }
}
