import { Ref } from 'vue';
import { SelectOptions } from '@/typings/common';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';

type tableRowData = ProjectManage.WorkItemXDescItem & { editable: boolean };

export function useWorkItemXDescTableConfig(
  tableRef: Ref<TableRef>,
  data: Ref<tableRowData[]>,
  workItemTypeCodeList: Ref<SelectOptions[]>,
  wIDescTypeCodeList: Ref<SelectOptions[]>,
  handleDelete: (data: tableRowData, index: number) => void,
) {
  const { toggleEdit, cancelEdit, addItem, delItem } = useEditableTable({
    tableRef,
    data,
    id: 'workItemXDescId',
  });

  const onItemConfirm = (data: tableRowData) => {
    toggleEdit(data);
  };

  const workItemXDescTableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('workItemDescSetting.table.workItemTypeCode', '工作项类型'),
        prop: 'workItemTypeCode',
        minWidth: 220,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'workItemDescSetting.table.workItemTypeCode',
                '工作项类型',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: tableRowData) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.workItemTypeCode}
                  clearable={false}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'workItemDescSetting.table.workItemTypeCode',
                      '工作项类型',
                    ),
                  })}
                  onChange={(val: string) => {
                    const item = workItemTypeCodeList.value.find(
                      (codeItem) => codeItem.value === val,
                    );
                    row.workItemTypeCodeDesc = item?.label as string;
                  }}
                >
                  {workItemTypeCodeList.value?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.workItemTypeCodeDesc}</>
              )}
            </div>
          );
        },
      },
      {
        label: t(
          'workItemDescSetting.table.workItemDescTypeCode',
          '工作项描述类型',
        ),
        prop: 'workItemDescTypeCode',
        minWidth: 220,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'workItemDescSetting.table.workItemDescTypeCode',
                '工作项描述类型',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: tableRowData) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.workItemDescTypeCode}
                  clearable={false}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'workItemDescSetting.table.workItemDescTypeCode',
                      '工作项描述类型',
                    ),
                  })}
                  onChange={(val: string) => {
                    const item = wIDescTypeCodeList.value.find(
                      (codeItem) => codeItem.value === val,
                    );
                    row.workItemDescTypeCodeDesc = item?.label as string;
                  }}
                >
                  {wIDescTypeCodeList.value?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.workItemDescTypeCodeDesc}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 200,
        render: (row: tableRowData, $index: number) => {
          return row.editable ? (
            <div class={'flex justify-around'} key="editable">
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => onItemConfirm(row)}
              >
                {t('global:confirm')}
              </el-button>
            </div>
          ) : (
            <el-button
              type="danger"
              link={true}
              onClick={() => handleDelete(row, $index)}
            >
              {t('global:delete')}
            </el-button>
          );
        },
      },
    ],
  });
  return {
    workItemXDescTableConfig,
    addItem,
    delItem,
  };
}
