import { type AnyObject, useColumnConfig } from 'sun-biz';

export function useProjectColumnConfig(
  openDialog: (data: AnyObject) => void,
  openTeamListDialog: (data: AnyObject) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('projectManage.table.ownerOrgName', '所属医院'),
        prop: 'ownerOrgName',
        minWidth: 150,
      },
      {
        label: t('projectManage.table.projectName', '项目名称'),
        prop: 'projectName',
        minWidth: 190,
      },
      {
        label: t('projectManage.table.projectStatusCodeDesc', '项目状态'),
        prop: 'projectStatusCodeDesc',
        minWidth: 120,
      },
      {
        label: t('projectManage.table.storySourceCodeDesc', '需求来源'),
        prop: 'storySourceCodeDesc',
        minWidth: 120,
      },
      {
        label: t('projectManage.table.storySourceCodeDesc', '需要测试'),
        prop: 'needTestFlag',
        minWidth: 120,
        render: (row: ProjectManage.ProjectInfo) => {
          return row.needTestFlag === null
            ? '--'
            : Number(row.needTestFlag) === 1
              ? '是'
              : '否';
        },
      },
      {
        label: t('projectManage.table.devGroupCodeDesc', '开发组别'),
        prop: 'devGroupCodeDesc',
        minWidth: 120,
      },
      {
        label: t('projectManage.table.beginDate', '开始日期'),
        prop: 'beginDate',
        minWidth: 170,
      },
      {
        label: t('projectManage.table.endDate', '结束日期'),
        prop: 'endDate',
        minWidth: 170,
      },
      {
        label: t('projectManage.table.projectManagerUserName', '项目经理'),
        prop: 'projectManagerUserName',
        minWidth: 150,
      },
      {
        label: t('projectManage.table.teamerCount', '项目人数'),
        prop: 'teamerCount',
        minWidth: 150,
      },
      {
        label: t('global:wbNo'),
        prop: 'wbNo',
        minWidth: 150,
      },
      {
        label: t('global:spellNo'),
        prop: 'spellNo',
        minWidth: 150,
      },
      {
        label: t('projectManage.table.createdUserName', '创建人'),
        prop: 'createdUserName',
        minWidth: 150,
      },
      {
        label: t('global:createTime'),
        prop: 'createdAt',
        minWidth: 170,
      },
      {
        label: t('projectManage.table.modifiedUserName', '最后修改人员'),
        prop: 'modifiedUserName',
        minWidth: 150,
      },
      {
        label: t('global:lastModifiedTime'),
        prop: 'modifiedAt',
        minWidth: 170,
      },
      {
        label: t('projectManage.table.freeServicePeriod', '免费维保(月)'),
        prop: 'freeServicePeriod',
        minWidth: 120,
      },
      {
        label: t('projectManage.table.freeServiceEndDate', '免费维保截止日期'),
        prop: 'freeServiceEndDate',
        minWidth: 170,
      },
      {
        label: t('projectManage.table.onlineDatePlan', '计划上线日期'),
        prop: 'onlineDatePlan',
        minWidth: 170,
      },
      {
        label: t('projectManage.table.onlineDate', '上线日期'),
        prop: 'onlineDate',
        minWidth: 170,
      },
      {
        label: t('projectManage.table.closeDate', '终验日期'),
        prop: 'closeDate',
        minWidth: 170,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 150,
        fixed: 'right',
        render: (row: ProjectManage.ProjectInfo) => {
          return (
            <div class="flex justify-around">
              <el-button
                onClick={() =>
                  openDialog({
                    row: { ...row },
                    title: t('projectManage.modalTitle', '项目维护'),
                  })
                }
                link={true}
                type="primary"
              >
                {t('global:edit')}
              </el-button>
              <el-button
                onClick={() =>
                  openDialog({
                    row: { ...row, disabled: true },
                    title: t('projectManage.modalTitle', '项目维护'),
                  })
                }
                link={true}
                type="primary"
              >
                {t('global:view')}
              </el-button>

              <el-button
                onClick={() =>
                  openTeamListDialog({
                    row: { ...row },
                  })
                }
                link={true}
                type="primary"
              >
                {t('projectManage.team.title', '团队')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
}
