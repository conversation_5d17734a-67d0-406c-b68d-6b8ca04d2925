import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import { SelectOptions } from '@/typings/common.ts';
import HospitalSelect from '@/modules/project/components/HospitalSelect/index.vue';
import { ENABLED_FLAG } from '@/utils/constant.ts';

type ROW = {
  projectId?: string;
  beginDate?: string;
  closeDate?: string;
  endDate?: string;
  freeServiceEndDate?: string;
  freeServicePeriod?: number;
  onlineDate?: string;
  onlineDatePlan?: string;
  ownerOrgId?: string;
  projectName?: string;
  projectName2nd?: string;
  projectNameExt?: string;
  projectStatusCode?: string;
  spellNo?: string;
  wbNo?: string;
  disabled?: boolean;
  isAdd?: boolean | undefined;
  needTestFlag?: boolean;
};

export function useProjectConfig(
  disabled: Ref<boolean, boolean>,
  projectStatusCodeList: Ref<SelectOptions[]>,
  devGroupCodeList: Ref<SelectOptions[]>,
  storySourceCodeList: Ref<SelectOptions[]>,
  onSetFormValueByFormKey: (setFormKey: string, formValueKey: string) => void,
  formModel: Ref<ROW>,
) {
  const projectDialogForm = useFormConfig({
    getData: (t) => [
      {
        name: 'projectName',
        label: t('projectManage.table.projectName', '项目名称'),
        component: 'input',
        autoConvertSpellNoAndWbNo: true,
        isFullWidth: true,
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('projectManage.table.projectName', '项目名称'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('projectManage.table.projectName', '项目名称'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'ownerOrgId',
        // component: 'hospitalSelect',
        render: () => <HospitalSelect useAllHospitalList={true} />,
        extraProps: {
          clearable: false,
          disabled: disabled.value,
        },
        span: 2,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('person.belongHospital', '所属医院'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'spellNo',
        label: t('global:spellNo'),
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:spellNo'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        component: 'input',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('global:wbNo'),
            }),
        extraProps: {
          disabled: disabled.value,
        },
      },
      {
        name: 'projectStatusCode',
        label: t('projectManage.table.projectStatusCode', '项目状态'),
        component: 'select',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.select.template', {
              name: t('projectManage.table.projectStatusCode', '项目状态'),
            }),
        extraProps: {
          options: projectStatusCodeList.value,
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('projectManage.table.projectStatusCode', '项目状态'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'storySourceCode',
        label: t('projectManage.table.storySourceCode', '需求来源'),
        component: 'select',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.select.template', {
              name: t('projectManage.table.storySourceCode', '需求来源'),
            }),
        extraProps: {
          options: storySourceCodeList.value,
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('projectManage.table.storySourceCode', '需求来源'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'needTestFlag',
        label: t('projectManage.table.needTestFlag', '需要测试'),
        minWidth: 100,
        render: () => {
          return (
            <el-switch
              modelValue={formModel.value.needTestFlag}
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              active-text={t('global:yes')}
              inactive-text={t('global:no')}
            />
          );
        },
      },
      {
        name: 'devGroupCode',
        label: t('projectManage.table.devGroupCode', '开发组别'),
        component: 'select',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.select.template', {
              name: t('projectManage.table.devGroupCode', '开发组别'),
            }),
        extraProps: {
          options: devGroupCodeList.value,
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('projectManage.table.devGroupCode', '开发组别'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'beginDate',
        label: t('projectManage.table.beginDate', '开始日期'),
        component: 'datePicker',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('projectManage.table.beginDate', '开始日期'),
            }),
        extraProps: {
          type: 'date',
          format: 'YYYY-MM-DD',
          'value-format': 'YYYY-MM-DD',
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('projectManage.table.beginDate', '开始日期'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'endDate',
        label: t('projectManage.table.endDate', '结束日期'),
        component: 'datePicker',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('projectManage.table.endDate', '结束日期'),
            }),
        extraProps: {
          type: 'date',
          format: 'YYYY-MM-DD',
          'value-format': 'YYYY-MM-DD',
          disabled: disabled.value,
        },
      },
      {
        name: 'onlineDatePlan',
        label: () => {
          return (
            <>
              {disabled.value ? (
                <>{t('projectManage.table.onlineDatePlan', '计划上线日期')}</>
              ) : (
                <el-dropdown
                  v-slots={{
                    default: () => (
                      <span class="leading-8">
                        {t(
                          'projectManage.table.onlineDatePlan',
                          '计划上线日期',
                        )}
                      </span>
                    ),
                    dropdown: () => (
                      <el-dropdown-menu>
                        <el-dropdown-item
                          onClick={() =>
                            onSetFormValueByFormKey(
                              'onlineDatePlan',
                              'beginDate',
                            )
                          }
                        >
                          {t(
                            'projectManage.form.asBeginDate',
                            '同计划开始时间',
                          )}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    ),
                  }}
                ></el-dropdown>
              )}
            </>
          );
        },
        component: 'datePicker',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('projectManage.table.onlineDatePlan', '计划上线日期'),
            }),
        extraProps: {
          type: 'date',
          format: 'YYYY-MM-DD',
          'value-format': 'YYYY-MM-DD',
          disabled: disabled.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('projectManage.table.onlineDatePlan', '计划上线日期'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'freeServicePeriod',
        label: t('projectManage.table.freeServicePeriod', '免费维保(月)'),
        component: 'input-number',
        placeholder: '',
        extraProps: {
          disabled: disabled.value,
          min: 0,
          'controls-position': 'right',
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'projectManage.table.freeServicePeriod',
                '免费维保(月)',
              ),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'freeServiceEndDate',
        label: t('projectManage.table.freeServiceEndDate', '免费维保截止日期'),
        component: 'datePicker',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t(
                'projectManage.table.freeServiceEndDate',
                '免费维保截止日期',
              ),
            }),
        extraProps: {
          type: 'date',
          format: 'YYYY-MM-DD',
          'value-format': 'YYYY-MM-DD',
          disabled: disabled.value,
        },
      },
      {
        name: 'onlineDate',
        label: () => {
          return (
            <>
              {disabled.value ? (
                <>{t('projectManage.table.onlineDate', '上线日期')}</>
              ) : (
                <el-dropdown
                  v-slots={{
                    default: () => (
                      <span class="leading-8">
                        {t('projectManage.table.onlineDate', '上线日期')}
                      </span>
                    ),
                    dropdown: () => (
                      <el-dropdown-menu>
                        <el-dropdown-item
                          onClick={() =>
                            onSetFormValueByFormKey(
                              'onlineDate',
                              'onlineDatePlan',
                            )
                          }
                        >
                          {t(
                            'projectManage.form.asOnlineDatePlan',
                            '同计划上线时间',
                          )}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    ),
                  }}
                ></el-dropdown>
              )}
            </>
          );
        },
        component: 'datePicker',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('projectManage.table.onlineDate', '上线日期'),
            }),
        extraProps: {
          type: 'date',
          format: 'YYYY-MM-DD',
          'value-format': 'YYYY-MM-DD',
          disabled: disabled.value,
        },
      },
      {
        name: 'closeDate',
        label: t('projectManage.table.closeDate', '终验日期'),
        component: 'datePicker',
        placeholder: disabled.value
          ? ''
          : t('global:placeholder.input.template', {
              content: t('projectManage.table.closeDate', '终验日期'),
            }),
        extraProps: {
          type: 'date',
          format: 'YYYY-MM-DD',
          'value-format': 'YYYY-MM-DD',
          disabled: disabled.value,
        },
      },
    ],
  });
  return {
    projectDialogForm,
  };
}
