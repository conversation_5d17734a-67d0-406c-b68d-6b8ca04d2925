import { useFormConfig } from 'sun-biz';
import { Ref } from 'vue';

export function CustomFormConfig(
  customSelectFormParams: Ref<CodeRepositoryManageAPI.UpsertApiCategory>,
  inputFocusRef: Ref<HTMLElement | undefined>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('customSelect.apiCategoryName', 'API类别'),
        name: 'apiCategoryName',
        placeholder: t('global:placeholder.input.template', {
          content: t('customSelect.apiCategoryName', 'API类别'),
        }),
        className: 'w-96 mt-4',
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('customSelect.apiCategoryName', 'API类别'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: () => {
          return (
            <el-input
              type="text"
              ref={inputFocusRef}
              placeholder={t('global:placeholder.input.template', {
                content: t('apiFrom.methodName', '类名'),
              })}
            />
          );
        },
      },
      {
        label: t('customSelect.apiCategoryName', '类名'),
        name: 'className',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('customSelect.className', '类名'),
        }),
        className: 'w-96 mt-4',
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('customSelect.className', '类名'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          onBlur: () => {
            const className = customSelectFormParams.value.className;
            const classNamePostMapping =
              customSelectFormParams.value.classNamePostMapping;
            if (className && !classNamePostMapping) {
              customSelectFormParams.value.classNamePostMapping = className;
            }
          },
        },
      },
      {
        label: t('customSelect.classNamePostMapping', '类名注解'),
        name: 'classNamePostMapping',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('customSelect.classNamePostMapping', '类名注解'),
        }),
        className: 'w-96 mt-4',
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('customSelect.classNamePostMapping', '类名注解'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
    ],
  });
  return data;
}
