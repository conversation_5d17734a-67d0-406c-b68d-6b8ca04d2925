<script lang="ts" name="customSelect" setup>
  import { nextTick, onMounted, ref, watch } from 'vue';
  import { useRoute } from 'vue-router';
  import { Delete, Plus, WarnTriangleFilled } from '@element-sun/icons-vue';
  import { useTranslation } from 'i18next-vue';
  import {
    addApiCategory,
    deleteApiCategory,
    editApiCategory,
    queryApiCategoryByExample,
  } from '@/modules/project/api/codeRepositoryManageAPI.ts';
  import { CustomFormConfig } from '../config/customFormConfig.tsx';
  import {
    MAIN_APP_CONFIG,
    ProDialog,
    ProForm,
    useAppConfigData,
  } from 'sun-biz';
  import { ElMessage, ElMessageBox, FormInstance } from 'element-sun';
  import { ENABLED_FLAG } from '@/utils/constant.ts';
  //isCloudEnv，true指云端，false其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);

  const customSelectRef = ref<HTMLSelectElement | null>(null);
  const { t } = useTranslation();
  const emits = defineEmits<{
    (
      event: 'success',
      apiCategoryId: CodeRepositoryManageAPI.UpsertApiCategory,
    ): void;
  }>();
  const route = useRoute();
  const { rowValue } = defineProps<{
    rowValue: CodeRepositoryManageAPI.UpsertApi;
  }>();
  const selectedValue = ref(''); // 初始化为空字符串
  const customSelectDialogRef = ref();
  const customSelectFormRef = ref<{
    ref: FormInstance;
  }>();
  const inputFocusRef = ref<HTMLInputElement | null>(null);

  const options = ref();
  const routeParams = route.params;
  let customSelectFormParams = ref<CodeRepositoryManageAPI.UpsertApiCategory>({
    apiCategoryName: '',
    className: '',
    codeRepositoryId: '',
    apiCategoryId: '',
    classNamePostMapping: '',
    enabledFlag: ENABLED_FLAG.YES,
  });

  // 监听 rowValue 的变化
  watch(
    () => rowValue.apiCategoryId,
    () => {
      selectedValue.value = rowValue.value.apiCategoryId || '';
    },
    {
      deep: true,
      immediate: true,
    },
  );

  // 查询 API 分类列表
  const queryApiCategoryList = async (
    data?: CodeRepositoryManageAPI.QueryApiCategoryByExample,
  ) => {
    let searchParams = {
      keyWord: '',
      enabledFlag: undefined,
      pageNumber: -1,
      deletedFlag: 0,
      codeRepositoryId: routeParams.codeRepositoryId,
    };
    if (data) {
      searchParams = {
        ...searchParams,
        ...data,
      };
    }
    const params = {
      ...searchParams,
    };
    try {
      const [, res] = await queryApiCategoryByExample(params);
      if (res?.success) {
        options.value = res.data || [];
      }
    } catch (error) {
      console.error('查询API分类列表失败', error);
      ElMessage.error('查询API分类列表失败，请稍后重试');
    }
  };

  // 组件挂载时查询 API 分类列表
  onMounted(() => {
    queryApiCategoryList();
  });

  // 处理添加选项的点击事件
  const handleAddOption = () => {
    customSelectFormParams.value = {
      apiCategoryName: '',
      className: '',
      codeRepositoryId: '',
      apiCategoryId: '',
      classNamePostMapping: '',
      enabledFlag: ENABLED_FLAG.YES,
    };
    customSelectDialogRef.value.open();
    // 等待下一个 tick，确保表单已经渲染完成
    nextTick(() => {
      customSelectFormRef.value?.ref?.clearValidate();
      customSelectFormRef.value?.ref?.resetFields();
    });
  };
  const handleClick = (e: MouseEvent) => {
    e.stopPropagation();
  };
  const switchChange = async (
    value: string,
    option: CodeRepositoryManageAPI.UpsertApiCategory,
  ) => {
    const [, res] = await editApiCategory({
      ...option,
    });
    if (res?.success) {
      ElMessage.success('操作成功');
    }
  };
  const handleDelete = async (
    e: MouseEvent,
    option: CodeRepositoryManageAPI.UpsertApiCategory,
  ) => {
    e.stopPropagation();
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action: t('global:delete'),
        name: option.apiCategoryName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        apiCategoryId: option.apiCategoryId,
      };
      const [, res] = await deleteApiCategory(params);
      if (res?.success) {
        ElMessage.success('操作成功');
        queryApiCategoryList();
      }
    });
  };
  const handleOpened = () => {
    // 打开对话框时，聚焦到第一个输入框
    nextTick(() => {
      if (inputFocusRef.value) {
        inputFocusRef.value.focus();
      }
    });
  };

  // 保存 API 分类
  const saveApiCategory = async () => {
    // 校验表单
    const valid = await customSelectFormRef.value?.ref?.validate();
    if (!valid) {
      return;
    }
    const routeParams = route.params;
    const params = {
      ...customSelectFormParams.value,
      ...routeParams,
      enabledFlag:
        customSelectFormParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : customSelectFormParams.value.enabledFlag,
    };
    try {
      const [, res] = await addApiCategory(params);
      if (res?.success) {
        await queryApiCategoryList();
        selectedValue.value = res?.data?.apiCategoryId;
        customSelectDialogRef.value.close();
        // 触发 success 事件
        const apiCategoryItem = options.value.find(
          (item: CodeRepositoryManageAPI.UpsertApiCategory) =>
            item.apiCategoryId === selectedValue.value,
        );
        emits('success', apiCategoryItem);
      } else {
        console.error('保存API分类失败', res?.message);
      }
    } catch (error) {
      console.error('保存API分类失败', error);
    }
  };
  const handleChange = () => {
    const apiCategoryItem = options.value.find(
      (item: CodeRepositoryManageAPI.UpsertApiCategory) =>
        item.apiCategoryId === selectedValue.value,
    );
    // 触发 success 事件
    customSelectRef.value?.blur();
    emits('success', apiCategoryItem);
  };

  // 表单配置
  const customFormConfig = CustomFormConfig(
    customSelectFormParams,
    inputFocusRef,
  );
</script>

<template>
  <div>
    <el-select
      ref="customSelectRef"
      v-model="selectedValue"
      :teleported="false"
      filterable
      placeholder="请选择"
      popper-class="custom-select-popper"
      style="width: 22rem"
      @change="handleChange"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <span class="flex-1 text-center text-base">类别名称</span>
          <el-icon
            v-if="isCloudEnv"
            class="cursor-pointer text-lg text-blue-500"
            @click="handleAddOption"
          >
            <Plus />
          </el-icon>
        </div>
      </template>
      <el-option
        v-for="(option, index) in options"
        :key="index"
        :disabled="option.enabledFlag === 0"
        :label="option.apiCategoryName"
        :value="option.apiCategoryId"
        style="display: block; width: 22rem"
      >
        <div class="flex items-center">
          <div style="width: 18rem">
            {{ option.apiCategoryName }}
            <el-tooltip
              :content="t('apiPage.deleteFlag', '请注意，该API已删除！')"
              effect="dark"
              placement="top"
            >
              <el-icon
                v-show="option.deleteFlag"
                class="ml-2"
                style="color: #f59e0b; cursor: pointer"
              >
                <WarnTriangleFilled />
              </el-icon>
            </el-tooltip>
          </div>
          <div class="flex items-center" style="width: 3.5rem">
            <el-switch
              v-model="option.enabledFlag"
              :active-text="t('global:enabled')"
              :active-value="ENABLED_FLAG.YES"
              :inactive-text="t('global:disabled')"
              :inactive-value="ENABLED_FLAG.NO"
              :inline-prompt="true"
              class="mr-2"
              @change="(value: string) => switchChange(value, option)"
              @click.stop.prevent="handleClick"
            ></el-switch>
            <el-icon>
              <Delete
                v-if="isCloudEnv"
                class="cursor-pointer text-red-500"
                @click.stop.prevent="
                  (e) => {
                    handleDelete(e, option);
                  }
                "
              />
            </el-icon>
          </div>
        </div>
      </el-option>
    </el-select>
    <ProDialog
      ref="customSelectDialogRef"
      :align-center="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :include-footer="false"
      :title="$t('customSelect.addApiCategory', '新增API类别')"
      :width="450"
      :z-index="99999"
      append-to="body"
      destroy-on-close
      @opened="handleOpened"
    >
      <ProForm
        ref="customSelectFormRef"
        v-model="customSelectFormParams"
        :column="1"
        :data="customFormConfig"
      />
      <div class="mt-4 text-center">
        <el-button type="primary" @click="saveApiCategory">
          保存&引用
        </el-button>
      </div>
    </ProDialog>
  </div>
</template>
