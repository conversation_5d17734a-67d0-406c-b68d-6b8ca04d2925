<script lang="ts" name="apiPage" setup>
  import { computed, onMounted, ref } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { BIZ_ID_TYPE_CODE, ENABLED_FLAG } from '@/utils/constant.ts';
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    useAppConfigData,
  } from 'sun-biz';
  import { useAPISearchFormConfig } from '@/modules/project/pages/codeRepositoryManage/apiPage/config/useFormConfig';
  import {
    useAPIDesignShowTableConfig,
    useAPITableConfig,
  } from '@/modules/project/pages/codeRepositoryManage/apiPage/config/useTableConfig.tsx';
  import AddApiDialog from '@/modules/project/pages/codeRepositoryManage/apiPage/components/addApiDialog.vue';
  import {
    deleteApi,
    queryApiByExample,
  } from '@/modules/project/api/codeRepositoryManageAPI.ts';
  import { queryCodeRepositoryByExample } from '@modules/project/api/codeRepositoryManage';
  import { unlockApi } from '@/modules/project/api/codeRepositoryManageAPIDesign';
  import { ElMessageBox } from 'element-sun';
  import { useRoute, useRouter } from 'vue-router';
  import { queryApiXParaByApiId } from '@/modules/project/api/codeRepositoryManageAPIDesign.ts';
  import { buildXTree } from '@/utils/buildTree.ts';
  import { DEFAULT_PAGE_SIZE } from '@sun-toolkit/enums';

  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';

  declare module 'splitpanes' {
    export interface SplitpanesProps {
      horizontal?: boolean;
      class?: string;
    }

    export interface PaneProps {
      size?: number;
    }
  }

  const router = useRouter();
  const route = useRoute();
  const { codeRepositoryId } = route.params;
  const { codeRepositoryName, codeRepositoryDesc } = route.query;
  console.log(route.query);
  const { t } = useTranslation();
  //isCloudEnv，true指云端，false其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const searchParams = ref<CodeRepositoryManageAPI.QueryParams>({
    codeRepositoryId: codeRepositoryId as string,
    keyWord: '',
    deletedFlag: 0,
    apiIds: [],
    enabledFlag: undefined,
  });
  const pageInfo = ref({
    pageNumber: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    total: 0,
  });
  const ApiUpsertDialogRef = ref();
  const apiUpsertDialogMode = ref('');
  const apiUpsertParams = ref<CodeRepositoryManageAPI.UpsertApi>({});
  const apiTableRef = ref();

  const loading = ref(false);
  const designLoading = ref(false);
  const inPutParaFlag = ref(0);
  const apiList = ref<CodeRepositoryManageAPI.ApiList[]>([]);
  const selections = ref<CodeRepositoryManageAPI.ApiList[]>([]);
  const apiDesignListTableRef = ref();
  const sourceData = ref<CodeRepositoryManageAPIDesign.ApiRecordParaList[]>([]);
  const apiDesignList = ref<CodeRepositoryManageAPIDesign.ApiRecordParaList[]>(
    [],
  );
  const treeData1 = ref<CodeRepositoryManageAPIDesign.ApiRecordParaList[]>([]);
  const treeData2 = ref<CodeRepositoryManageAPIDesign.ApiRecordParaList[]>([]);
  const cellData = ref<CodeRepositoryManageAPIDesign.ApiRecordParaList>({});

  const splitValue = ref([30, 70]);

  const bizData = computed(() => {
    return selections.value.map((item) => {
      return item.apiId || '';
    });
  });

  const queryApiList = async (data?: CodeRepositoryManageAPI.QueryParams) => {
    loading.value = true;
    if (data) {
      // 如果传入的数据包含分页参数，则使用传入的；否则保持当前分页状态
      const newPageNumber =
        data.pageNumber !== undefined
          ? data.pageNumber
          : pageInfo.value.pageNumber;
      const newPageSize =
        data.pageSize !== undefined ? data.pageSize : pageInfo.value.pageSize;

      searchParams.value = {
        ...searchParams.value,
        ...data,
        pageNumber: newPageNumber,
        pageSize: newPageSize,
      };

      // 同步更新 pageInfo
      pageInfo.value.pageNumber = newPageNumber;
      pageInfo.value.pageSize = newPageSize;
    }

    console.log('当前分页信息:', {
      pageNumber: pageInfo.value.pageNumber,
      pageSize: pageInfo.value.pageSize,
      searchParams: searchParams.value,
    });

    const params = {
      ...searchParams.value,
    };
    const [, res] = await queryApiByExample(params);
    loading.value = false;
    if (res?.success) {
      apiList.value = res.data || [];
      if (res?.total) {
        pageInfo.value.total = res?.total;
      }
    }
  };
  const deleteApiAction = (data?: CodeRepositoryManageAPI.UpsertApi) => {
    if (!data) return;
    console.log(data, 'deleteApiAction');
    ElMessageBox.confirm(
      t('api.delete.ask.title', '您确定要删除"{{name}}"吗', {
        name: `${data.apiName}`,
      }),
      t('global:tip', '提示'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        const params = {
          apiId: data?.apiId,
        };
        const [, res] = await deleteApi(params);
        if (res?.success) {
          apiTableRef?.value.proTableRef.clearSelection();
          selections.value = [];
          // 删除后重新查询，保持当前分页状态
          queryApiList({
            pageNumber: pageInfo.value.pageNumber,
            pageSize: pageInfo.value.pageSize,
          });
        }
      })
      .catch(() => {});
  };

  const onOpenApiDialog = async (
    mode: string,
    data?: CodeRepositoryManageAPI.UpsertApi,
  ) => {
    if (!codeRepositoryId) return;
    apiUpsertDialogMode.value = mode;

    let codeRepositoryInfo: {
      codeRepositoryNo?: string;
      apiSubNoMax?: string;
    } = {};
    const [, res] = await queryCodeRepositoryByExample({
      codeRepositoryIds: [codeRepositoryId as string],
    });
    if (res?.success) {
      codeRepositoryInfo = res.data[0] || {};
    }
    if (mode === 'add') {
      apiUpsertParams.value = {
        enabledFlag: ENABLED_FLAG.YES,
        queryFlag: ENABLED_FLAG.YES,
        apiNo: `${codeRepositoryInfo.codeRepositoryNo || ''}-${Number(codeRepositoryInfo.apiSubNoMax || 0) <= 0 ? 10001 : Number(codeRepositoryInfo.apiSubNoMax || 10000) + 1}-1`,
      };
    } else if (mode === 'edit' && data) {
      const {
        apiId,
        apiName,
        apiNo,
        interfaceId,
        queryFlag,
        className,
        methodName,
        codeRepositoryId,
        apiCategoryId,
        enabledFlag,
        classNamePostMapping,
      } = data;
      apiUpsertParams.value = {
        apiId,
        apiName,
        apiNo,
        interfaceId,
        queryFlag,
        className,
        methodName,
        codeRepositoryId,
        apiCategoryId,
        enabledFlag,
        classNamePostMapping,
      };
    }
    ApiUpsertDialogRef.value.dialogRef.open();
  };
  const handleInPutParaFlagChange = () => {
    apiDesignList.value =
      inPutParaFlag.value === 0 ? treeData1.value : treeData2.value;
  };

  const queryApiXPara = async (id?: string) => {
    designLoading.value = true;
    const params: CodeRepositoryManageAPIDesign.upsertAPI = {
      apiId: id,
    };
    let [, res] = await queryApiXParaByApiId(params);
    if (res?.success) {
      sourceData.value = res.data || [];
      if (res.data?.length > 0) {
        buildTreeDataFromApi(res.data);
      } else {
        treeData1.value = [];
        treeData2.value = [];
        apiDesignList.value = treeData1.value;
      }
    }
    designLoading.value = false;
  };

  function isObjectEmpty(obj: object): boolean {
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        return false;
      }
    }
    return true;
  }

  // 构造树形数据
  const buildTreeDataFromApi = (
    list?: CodeRepositoryManageAPIDesign.ApiRecordParaList[],
  ) => {
    console.log(list, 'list');
    const tree1 = list?.filter((item) => item.inParaFlag === 0) || [];
    const tree2 = list?.filter((item) => item.inParaFlag === 1) || [];
    treeData1.value = isObjectEmpty(buildXTree(tree1)) ? [] : buildXTree(tree1);
    treeData2.value = isObjectEmpty(buildXTree(tree2)) ? [] : buildXTree(tree2);
    apiDesignList.value =
      inPutParaFlag.value === 0 ? treeData1.value : treeData2.value;
  };
  const handleCellClick = (row: CodeRepositoryManageAPI.ApiList) => {
    cellData.value =
      row as unknown as CodeRepositoryManageAPIDesign.ApiRecordParaList;
    queryApiXPara(row.apiId);
  };
  const handleSelectChange = (
    value: CodeRepositoryManageAPI.ApiList[] = [],
  ) => {
    selections.value = value;
  };
  const handleDesignApi = (row: CodeRepositoryManageAPI.ApiList) => {
    router.push({
      name: 'apiDesign',
      params: { apiId: row.apiId },
      query: {
        apiName: row.apiName,
        codeRepositoryId: row.codeRepositoryId,
        codeRepositoryName: codeRepositoryName,
        codeRepositoryDesc: codeRepositoryDesc,
      },
    });
  };
  const unlock = (row?: CodeRepositoryManageAPI.ApiList) => {
    ElMessageBox.confirm(
      t(
        'switch.ask.title',
        '您确定要 {{action}} "{{name}}" 吗，{{action}}后编辑记录将全部清空！',
        {
          action: t('apiDesign.cancel.design', '解锁'),
          name: row.apiName,
        },
      ),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await unlockApi({ apiId: row.apiId });
      if (res?.success) {
        queryApiList();
      }
    });
  };

  const searchConfig = useAPISearchFormConfig(queryApiList);
  const apiTableColumnsConfig = useAPITableConfig(
    isCloudEnv,
    onOpenApiDialog,
    deleteApiAction,
    handleDesignApi,
    unlock,
  );
  const { tableColumns } = useAPIDesignShowTableConfig();

  // 搜索处理函数
  const handleSearch = (data: unknown) => {
    // 搜索时重置到第一页
    queryApiList({ ...data, pageNumber: 1 });
  };

  //获取分页的方法
  const currentPageChange = (pageNumber: number) => {
    pageInfo.value.pageNumber = pageNumber;
    // 直接调用查询，传入新的页码
    queryApiList({ pageNumber });
  };
  //当前多少条数据发生改变
  const sizePageChange = (num: number) => {
    pageInfo.value.pageSize = num;
    // 切换每页条数时，重置到第一页
    queryApiList({ pageSize: num, pageNumber: 1 });
  };
  onMounted(() => {
    // 初始化时传入分页信息
    queryApiList({
      pageNumber: pageInfo.value.pageNumber,
      pageSize: pageInfo.value.pageSize,
    });
  });
  // 返回主页
  const goBack = () => {
    router.push('/');
  };

  const handleResize = (sizes: number[]) => {
    splitValue.value = sizes;
  };
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <el-page-header @back="goBack">
      <template #content>
        <span class="text-base"
          >{{ codeRepositoryName }}({{ codeRepositoryDesc }})</span
        >
      </template>
    </el-page-header>
    <div class="mt-3 flex justify-start">
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          :data="searchConfig"
          :show-search-button="true"
          layout-mode="inline"
          @model-change="handleSearch"
        />
      </div>
      <div class="ml-3">
        <el-button
          :disabled="!isCloudEnv"
          type="primary"
          @click="onOpenApiDialog('add')"
        >
          {{ $t('global:add') }}
        </el-button>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_API"
          :disabled="false"
          class="mx-3"
          title="DML"
          @success="
            () => {
              apiTableRef?.proTableRef.clearSelection();
              selections = [];
            }
          "
        />
      </div>
    </div>
    <Splitpanes
      v-model="splitValue"
      class="default-theme p-box"
      style="height: calc(100vh - 170px)"
      @resize="handleResize"
    >
      <Pane
        :size="splitValue[0]"
        class="flex flex-col"
        style="background-color: #fff"
      >
        <ProTable
          ref="apiTableRef"
          :columns="apiTableColumnsConfig"
          :data="apiList"
          :highlight-current-row="true"
          :loading="loading"
          :page-info="pageInfo"
          :pagination="true"
          min-height="100%"
          row-key="apiId"
          @cell-click="handleCellClick"
          @selection-change="handleSelectChange"
          @current-page-change="currentPageChange"
          @size-page-change="sizePageChange"
        />
      </Pane>
      <Pane
        :size="splitValue[1]"
        class="flex h-full flex-col"
        style="background-color: #fff"
      >
        <div>
          <el-radio-group
            v-model="inPutParaFlag"
            class="mb-1"
            size="small"
            @change="handleInPutParaFlagChange"
          >
            <el-radio-button :value="0" label="入参" />
            <el-radio-button :value="1" label="出参" />
          </el-radio-group>
        </div>
        <ProTable
          ref="apiDesignListTableRef"
          :columns="tableColumns"
          :data="apiDesignList"
          :editable="false"
          :highlight-current-row="true"
          :loading="designLoading"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          row-class-name="cursor-pointer"
          row-key="apiXParaId"
        />
      </Pane>
    </Splitpanes>
    <AddApiDialog
      ref="ApiUpsertDialogRef"
      :data="apiUpsertParams"
      :mode="apiUpsertDialogMode"
      @success="queryApiList"
    />
  </div>
</template>

<style lang="scss" scoped>
  .context-menu-trigger {
    position: fixed;
    width: 0;
    height: 0;
    opacity: 0;
  }

  :deep(.el-dropdown-menu__item) {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 8px 16px;

    .el-icon {
      margin-right: 4px;
    }
  }
</style>
