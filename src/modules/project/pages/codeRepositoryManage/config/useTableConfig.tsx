import {
  MAIN_APP_CONFIG,
  TableRef,
  useAppConfigData,
  useColumnConfig,
  useEditableTable,
} from 'sun-biz';
import { Ref } from 'vue';

import { ENABLED_FLAG } from '@/utils/constant';
import { Plus } from '@element-sun/icons-vue';

export function useCodeRepositoryTableConfig(
  isCloudEnv: boolean | undefined,
  onOpenCodeRepositoryDialog: (
    mode: string,
    data?: CodeRepositoryManage.CodeRepositoryInfo,
  ) => void,
  onOpenCodeBranchDialog: (
    mode: string,
    data?: CodeRepositoryManage.CodeRepositoryInfo,
  ) => void,
  goToApiPage: (data?: CodeRepositoryManage.CodeRepositoryInfo) => void,
  handleEnableSwitch: (data: CodeRepositoryManage.CodeRepositoryInfo) => void,
  goToSettingPage: (data?: CodeRepositoryManage.CodeRepositoryInfo) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t('global.sequence', '顺序'),
        minWidth: 60,
        prop: 'indexNo',
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t(
          'codeRepositoryManage.codeRepositoryManageTable.codeRepositoryName',
          '仓库名称',
        ),
        prop: 'codeRepositoryName',
        minWidth: 150,
      },
      {
        label: t(
          'codeRepositoryManage.codeRepositoryManageTable.codeRepositoryNo',
          '仓库编号',
        ),
        prop: 'codeRepositoryNo',
        minWidth: 150,
      },
      {
        label: t(
          'codeRepositoryManage.codeRepositoryManageTable.codeRepositoryNo',
          'API最大子编号',
        ),
        prop: 'apiSubNoMax',
        minWidth: 150,
      },
      {
        label: t(
          'codeRepositoryManage.codeRepositoryManageTable.servicePrefix',
          '服务前缀',
        ),
        prop: 'servicePrefix',
        minWidth: 150,
      },
      {
        label: t(
          'codeRepositoryManage.codeRepositoryManageTable.codeRepositoryDesc',
          '仓库描述',
        ),
        prop: 'codeRepositoryDesc',
        minWidth: 190,
      },
      {
        label: t(
          'codeRepositoryManage.codeRepositoryManageTable.codeRepositoryTypeCodeDesc',
          '仓库类型',
        ),
        prop: 'codeRepositoryTypeCodeDesc',
        minWidth: 100,
      },
      {
        label: t(
          'codeRepositoryManage.codeRepositoryManageTable.codeRepositoryAddr',
          '仓库地址',
        ),
        prop: 'codeRepositoryAddr',
        minWidth: 230,
      },
      {
        label: t('codeRepositoryManage.codeRepositoryManageTable.port', '端口'),
        prop: 'port',
        minWidth: 150,
      },
      {
        label: t(
          'codeRepositoryManage.codeRepositoryManageTable.appPath',
          '应用程序路径',
        ),
        prop: 'appPath',
        minWidth: 150,
      },
      {
        label: t(
          'codeRepositoryManage.codeRepositoryManageTable.backupPath',
          '备份路径',
        ),
        prop: 'backupPath',
        minWidth: 150,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: CodeRepositoryManage.CodeRepositoryInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t(
          'codeRepositoryManage.codeRepositoryManageTable.loginUserId',
          '登录用户标识',
        ),
        prop: 'loginUserId',
        minWidth: 180,
      },
      {
        label: t(
          'codeRepositoryManage.codeRepositoryManageTable.loginUserName',
          '登录用户名称',
        ),
        prop: 'loginUserName',
        minWidth: 150,
      },
      {
        label: t('global:creator', '创建人'),
        prop: 'createdUserName',
        minWidth: 150,
      },
      {
        label: t('global:createTime'),
        prop: 'createdAt',
        minWidth: 170,
      },
      {
        label: t(
          'codeRepositoryManage.codeRepositoryManageTable.modifiedUserName',
          '最后修改人',
        ),
        prop: 'modifiedUserName',
        minWidth: 150,
      },
      {
        label: t('global:lastModifiedTime'),
        prop: 'modifiedAt',
        minWidth: 170,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 180,
        fixed: 'right',
        render: (row: CodeRepositoryManage.CodeRepositoryInfo) => {
          const userInfo = useAppConfigData(MAIN_APP_CONFIG.USER_INFO);
          return (
            <>
              <div class={'flex justify-center'}>
                <el-button
                  type="primary"
                  link={true}
                  disabled={!isCloudEnv && row.loginUserId !== userInfo?.userId}
                  onClick={() => onOpenCodeRepositoryDialog('edit', row)}
                >
                  {t('global:edit')}
                </el-button>
                <el-button
                  type="primary"
                  link={true}
                  disabled={!isCloudEnv}
                  onClick={() => onOpenCodeBranchDialog('preview', row)}
                >
                  {t('global:branch', '分支')}
                </el-button>
                {Number(row.codeRepositoryTypeCode) === 2 && (
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => goToApiPage(row)}
                  >
                    {t('global:api', 'API')}
                  </el-button>
                )}
                <el-button
                  type="primary"
                  link={true}
                  onClick={() => goToSettingPage(row)}
                >
                  {t('global:settingPage', '配置')}
                </el-button>
              </div>
            </>
          );
        },
      },
    ],
  });
}

export function useCodeRepositorySettingTableConfig(
  id: string,
  codeRepositorySettingTableRef: Ref<TableRef>,
  tableData: Ref<CodeRepositoryManage.CodeRepositoryInfo[]>,
  appConfigSettingList: Ref<AppConfigSetting.AppConfigList[]>,
  insertRow: () => void,
  handleSave: (
    row: CodeRepositoryManage.CodeRepoXAppConfigList,
  ) => Promise<void>,
  editRow: (row: CodeRepositoryManage.CodeRepoXAppConfigList) => void,
  deleteItem: (
    row: CodeRepositoryManage.CodeRepoXAppConfigList,
    $index: number,
  ) => Promise<void>,
  isCloudEnv: boolean | undefined,
) {
  const { toggleEdit, cancelEdit } = useEditableTable({
    id,
    tableRef: codeRepositorySettingTableRef,
    data: tableData,
  });
  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t(
          'codeRepositoryManage.codeRepositoryManageTable.appConfigItemId',
          '软件配置项',
        ),
        prop: 'appConfigItemId',
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'codeRepositoryManage.codeRepositoryManageTable.appConfigItemIdRequired',
                '软件配置项不能为空',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        editable: true,
        render: (row: CodeRepositoryManage.CodeRepoXAppConfigList) => {
          if (row.editable) {
            return (
              <el-select
                v-model={row.appConfigItemId}
                placeholder={t(
                  'codeRepositoryManage.codeRepositoryManageTable.appConfigItemId',
                  '软件配置项',
                )}
              >
                {appConfigSettingList.value.map(
                  (item: AppConfigSetting.AppConfigList) => (
                    <el-option
                      key={item.appConfigItemId}
                      label={item.appConfigItemName}
                      value={item.appConfigItemId}
                    />
                  ),
                )}
              </el-select>
            );
          } else {
            return (
              <span class={'flex items-center justify-center'}>
                {row.appConfigName}
              </span>
            );
          }
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        renderHeader: () => {
          return (
            <span class={'flex items-center justify-center'}>
              {t(
                'codeRepositoryManage.codeRepositoryManageTable.operation',
                '操作',
              )}
              <el-button
                class={'ml-2 cursor-pointer text-blue-500'}
                type={'text'}
                disabled={!isCloudEnv}
                icon={Plus}
                onClick={() => {
                  insertRow();
                }}
              ></el-button>
            </span>
          );
        },
        width: 150,
        fixed: 'right',
        render: (
          row: CodeRepositoryManage.CodeRepoXAppConfigList,
          $index: number,
        ) => {
          if (row.editable) {
            return (
              <div class={'flex justify-center'} key="editable">
                <el-button
                  type="danger"
                  link={true}
                  onClick={() => cancelEdit(row, $index)}
                >
                  {t('global:cancel')}
                </el-button>
                <el-button
                  type="primary"
                  link={true}
                  onClick={() => handleSave(row)}
                >
                  {t('global:save')}
                </el-button>
              </div>
            );
          } else {
            return (
              <>
                <div class={'flex justify-center'}>
                  <el-button
                    type="primary"
                    disabled={!isCloudEnv}
                    link={true}
                    onClick={() => editRow(row)}
                  >
                    {t('global:edit')}
                  </el-button>
                  <el-button
                    type="danger"
                    disabled={!isCloudEnv}
                    link={true}
                    onClick={() => deleteItem(row, $index)}
                  >
                    {t('global:delete')}
                  </el-button>
                </div>
              </>
            );
          }
        },
      },
    ],
  });
  return { tableColumns, toggleEdit };
}
