<script lang="ts" name="codeRepositoryManage" setup>
  import { computed, onMounted, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { dispatchPreferenceChange } from '@sun-toolkit/micro-app';
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    type PreferenceData,
    ProForm,
    ProTable,
    Title,
    useAppConfigData,
  } from 'sun-biz';
  import {
    BIZ_ID_TYPE_CODE,
    ENABLED_FLAG,
    REPOSITORY_CONDITION_PREFER_CODE,
  } from '@/utils/constant';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { commonSort } from '@/api/common';
  import { useTranslation } from 'i18next-vue';
  import { useCodeRepositoryTableConfig } from './config/useTableConfig.tsx';
  import { useCodeRepositorySearchFormConfig } from './config/useFormConfig.ts';
  import {
    editCodeRepository,
    queryCodeRepositoryByExample,
  } from '@modules/project/api/codeRepositoryManage';
  import { querySystemListByExample } from '@modules/system/api/menu';
  import CodeRepositoryUpsertDialog from '@/modules/project/pages/codeRepositoryManage/components/CodeRepositoryUpsertDialog.vue';
  import CodeBranchDialog from '@/modules/project/pages/codeRepositoryManage/components/CodeBranchDialog.vue';

  console.log(
    REPOSITORY_CONDITION_PREFER_CODE,
    'REPOSITORY_CONDITION_PREFER_CODE',
  );

  const router = useRouter();

  const { t } = useTranslation();
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const searchParams = ref<CodeRepositoryManage.QueryParams>({
    keyWord: '',
    enabledFlag: ENABLED_FLAG.ALL,
    codeRepositoryIds: [],
    codeRepositoryTypeCode: '',
    sysId: '',
  });
  const loading = ref(false);
  const codeRepositoryList = ref<CodeRepositoryManage.CodeRepositoryInfo[]>([]);
  const selections = ref<CodeRepositoryManage.CodeRepositoryInfo[]>([]);
  const codeRepositoryUpsertParams = ref<CodeRepositoryManage.UpsertParams>({});
  const codeRepositoryUpsertDialogRef = ref();
  const codeRepositoryUpsertDialogMode = ref('');
  const codeRepositoryTableRef = ref();
  const codeBranchDialogRef = ref();
  const codeBranchDialogModeMode = ref('');
  const prviewCodeBranchData = ref([]);
  const sysSelections = ref<Menu.SystemInfo[]>([]);
  const bizData = computed(() => {
    return selections.value.map((item) => {
      return item.codeRepositoryId || '';
    });
  });
  const querySystemList = async () => {
    const [, res] = await querySystemListByExample({
      enabledFlag: ENABLED_FLAG.YES,
    });
    if (res?.success) {
      sysSelections.value = res.data.map((item: Menu.SystemInfo) => {
        return {
          label: item.sysName,
          value: item.sysId,
        };
      });
    }
  };
  // 获取对应的偏好对象
  const { preferenceList } = useAppConfigData([
    MAIN_APP_CONFIG.PREFERENCE_LIST,
  ]);

  // 用户查询偏好
  const codeRepositoryManageCondition = computed<PreferenceData | undefined>(
    () =>
      preferenceList?.find(
        (item) => item.preferTypeCode === REPOSITORY_CONDITION_PREFER_CODE,
      ),
  );
  watch(
    codeRepositoryManageCondition,
    (newVal) => {
      if (newVal?.preferSetting) {
        const preferSetting = JSON.parse(newVal.preferSetting);
        searchParams.value = {
          ...searchParams.value,
          ...preferSetting.searchParams,
        };
      }
    },
    { deep: true, immediate: true },
  );
  // 保存偏好
  const saveDispatchPreferenceChange = (
    params?: CodeRepositoryManage.QueryParams,
  ) => {
    const opt = {
      searchParams: {
        keyWord: params?.keyWord,
        enabledFlag: params?.enabledFlag,
        codeRepositoryIds: params?.codeRepositoryIds,
        sysId: params?.sysId,
        codeRepositoryTypeCode: params?.codeRepositoryTypeCode,
      },
    };
    dispatchPreferenceChange({
      preferTypeCode: REPOSITORY_CONDITION_PREFER_CODE,
      preferSetting: JSON.stringify(opt),
    });
  };

  async function queryCodeRepositoryData(
    data?: CodeRepositoryManage.QueryParams,
  ) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    };
    const [, res] = await queryCodeRepositoryByExample(params);
    loading.value = false;
    if (res?.success) {
      saveDispatchPreferenceChange(searchParams.value);
      codeRepositoryList.value = (res.data || []).sort(
        (
          a: CodeRepositoryManage.CodeRepositoryInfo,
          b: CodeRepositoryManage.CodeRepositoryInfo,
        ) => {
          return Number(a?.sort) - Number(b?.sort);
        },
      );
    }
  }

  const handleModelChange = (data: CodeRepositoryManage.QueryParams) => {
    console.log(data, 'handleModelChange');
    searchParams.value = {
      ...searchParams.value,
      ...data,
    };
    queryCodeRepositoryData();
  };

  const onOpenCodeRepositoryDialog = (
    mode: string,
    data?: CodeRepositoryManage.CodeRepositoryInfo,
  ) => {
    codeRepositoryUpsertDialogMode.value = mode;
    if (mode === 'add') {
      codeRepositoryUpsertParams.value = {
        servicePrefix: '',
        enabledFlag: ENABLED_FLAG.YES,
      };
    } else if (mode === 'edit' && data) {
      const {
        codeRepositoryId,
        codeRepositoryName,
        codeRepositoryDesc,
        codeRepositoryAddr,
        codeRepositoryTypeCode,
        enabledFlag,
        loginUserId,
        loginUserName,
        sort,
        appPath,
        backupPath,
        port,
        servicePrefix,
        codeRepositoryNo,
      } = data;
      codeRepositoryUpsertParams.value = {
        codeRepositoryId,
        codeRepositoryName,
        codeRepositoryDesc,
        codeRepositoryAddr,
        codeRepositoryTypeCode,
        enabledFlag,
        loginUserId,
        loginUserName,
        sort,
        appPath,
        backupPath,
        port,
        servicePrefix,
        codeRepositoryNo,
      };
    }
    codeRepositoryUpsertDialogRef.value.dialogRef.open();
  };

  const handleSelectChange = (
    value: CodeRepositoryManage.CodeRepositoryInfo[],
  ) => {
    selections.value = value;
  };

  /** 拖拽排序 */
  const handleSortEnd = async (
    list: CodeRepositoryManage.CodeRepositoryInfo[],
  ) => {
    const bizIdList = (list || []).map((item, index) => ({
      bizId: item.codeRepositoryId,
      sort: index + 1,
    }));
    const [, res] = await commonSort({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_CODE_REPOSITORY,
      bizIdList,
    });
    if (res?.success) {
      ElMessage({
        type: 'success',
        message: t('global:modify.sort.success'),
      });
      await queryCodeRepositoryData();
    }
  };
  const onOpenCodeBranchDialog = (
    mode: string,
    data?: CodeRepositoryManage.CodeRepositoryInfo,
  ) => {
    if (mode === 'add' && !selections.value.length)
      return ElMessage.warning(
        t('codeBranch.selectCodeRepository', '请先选择要创建分支的代码仓库'),
      );
    codeBranchDialogModeMode.value = mode;
    codeBranchDialogRef.value.dialogRef.open();
    if (mode !== 'add' && data) {
      codeBranchDialogRef.value.previewData = data;
      const branchListTemp = codeBranchDialogRef.value.branchListTemp;
      codeBranchDialogRef.value.tableData = data.codeBranchList.map((item) => {
        return {
          ...item,
          hospitalList:
            branchListTemp.find(
              (branchItem: CodeBranchManage.CodeBranchInfo) =>
                branchItem.codeBranchId === item.codeBranchId,
            )?.hospitalList || [],
        };
      });
    } else {
      codeBranchDialogRef.value.queryCodeBranchData();
    }
  };
  const goToApiPage = (data?: CodeRepositoryManage.CodeRepositoryInfo) => {
    if (!data) return;
    const params = {
      codeRepositoryId: data.codeRepositoryId,
    };
    const query = {
      codeRepositoryName: data.codeRepositoryName,
      codeRepositoryDesc: data.codeRepositoryDesc,
    };
    router.push({
      name: 'apiPage',
      params,
      query,
    });
  };
  const goToSettingPage = (data?: CodeRepositoryManage.CodeRepositoryInfo) => {
    if (!data) return;
    const query = {
      codeRepositoryId: data.codeRepositoryId,
      codeRepositoryName: data.codeRepositoryName,
    };
    router.push({
      name: 'settingPage',
      query,
    });
  };

  async function handleEnableSwitch(
    row: CodeRepositoryManage.CodeRepositoryInfo,
  ) {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.codeRepositoryName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const {
        codeRepositoryId,
        codeRepositoryName,
        codeRepositoryDesc,
        codeRepositoryAddr,
        codeRepositoryTypeCode,
        sort,
        appPath,
        backupPath,
        port,
        servicePrefix,
        codeRepositoryNo,
      } = row;
      const params = {
        codeRepositoryId,
        codeRepositoryName,
        codeRepositoryDesc,
        codeRepositoryAddr,
        codeRepositoryTypeCode,
        sort,
        appPath,
        backupPath,
        port,
        servicePrefix,
        codeRepositoryNo,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await editCodeRepository(params);
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        queryCodeRepositoryData();
      }
    });
  }

  queryCodeRepositoryData();
  const searchConfig = useCodeRepositorySearchFormConfig(
    queryCodeRepositoryData,
    sysSelections,
  );
  const tableColumnsConfig = useCodeRepositoryTableConfig(
    isCloudEnv,
    onOpenCodeRepositoryDialog,
    onOpenCodeBranchDialog,
    goToApiPage,
    handleEnableSwitch,
    goToSettingPage,
  );
  onMounted(() => {
    querySystemList();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('codeRepositoryManage.list.title', '代码仓库列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          :data="searchConfig"
          :show-search-button="true"
          layout-mode="inline"
          @model-change="handleModelChange"
        />
      </div>
      <div>
        <el-button
          :disabled="!isCloudEnv"
          type="primary"
          @click="onOpenCodeRepositoryDialog('add')"
        >
          {{ $t('global:add') }}
        </el-button>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_CODE_REPOSITORY"
          class="mx-3"
          @success="
            () => {
              codeRepositoryTableRef?.proTableRef.clearSelection();
              selections = [];
            }
          "
        />
        <el-button type="primary" @click="onOpenCodeBranchDialog('add')">
          {{ $t('codeBranch.add', '创建分支') }}
        </el-button>
      </div>
    </div>
    <ProTable
      ref="codeRepositoryTableRef"
      :columns="tableColumnsConfig"
      :data="codeRepositoryList"
      :loading="loading"
      draggable
      row-key="codeRepositoryId"
      @drag-end="handleSortEnd"
      @selection-change="handleSelectChange"
    />
  </div>
  <CodeRepositoryUpsertDialog
    ref="codeRepositoryUpsertDialogRef"
    :data="codeRepositoryUpsertParams"
    :mode="codeRepositoryUpsertDialogMode"
    @success="queryCodeRepositoryData"
  />
  <CodeBranchDialog
    ref="codeBranchDialogRef"
    :mode="codeBranchDialogModeMode"
    :prview-code-branch-data="prviewCodeBranchData"
    :selections="selections"
    @success="queryCodeRepositoryData"
  />
</template>
