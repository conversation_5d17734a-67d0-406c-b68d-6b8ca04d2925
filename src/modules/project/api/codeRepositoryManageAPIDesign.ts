import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10324-1]锁定 API
 * @param params
 * @returns
 */
export const designApi = (params: CodeRepositoryManageAPIDesign.upsertAPI) => {
  return dictRequest<CodeRepositoryManageAPIDesign.AddApiPara>(
    '/apimanage/designApi',
    params,
  );
};
/**
 * [1-10324-1]锁定 API
 * @param params
 * @returns
 */
export const queryApiXParaByApiId = (
  params: CodeRepositoryManageAPIDesign.upsertAPI,
) => {
  return dictRequest<CodeRepositoryManageAPIDesign.AddApiPara>(
    '/apimanage/queryApiXParaByApiId',
    params,
  );
};
/**
 * [1-10325-1]解锁API
 * @param params
 * @returns
 */
export const unlockApi = (params: CodeRepositoryManageAPIDesign.upsertAPI) => {
  return dictRequest<CodeRepositoryManageAPIDesign.ApiXParaList>(
    '/apimanage/unlockApi',
    params,
    {
      successMsg: translation('apiPage.unlock.success', '解锁成功'),
    },
  );
};
/**
 * [1-10326-1]根据API标识查询API参数
 * @param params
 * @returns
 */
export const queryApiParaByApiId = (
  params: CodeRepositoryManageAPIDesign.upsertAPI,
) => {
  return dictRequest<CodeRepositoryManageAPIDesign.ApiXParaList[]>(
    '/apimanage/queryApiParaByApiId',
    params,
  );
};
/**
 * [1-10327-1]新增API参数
 * @param params
 * @returns
 */
export const addApiPara = (
  params?: CodeRepositoryManageAPIDesign.AddApiPara,
) => {
  return dictRequest<CodeRepositoryManageAPIDesign.AddApiPara>(
    '/apimanage/addApiPara',
    params,
    {
      successMsg: translation('global:add.success'),
    },
  );
};

/**
 * [1-10328-1]编辑API参数
 * @param params
 * @returns
 */
export const editApiPara = (
  params?: CodeRepositoryManageAPIDesign.EditApiPara,
) => {
  return dictRequest<CodeRepositoryManageAPIDesign.ApiRecordParaList[]>(
    '/apimanage/editApiPara',
    params,
  );
};
/**
 * [1-10329-1]根据编辑记录标识查询API编辑记录信息
 * @param params
 * @returns
 */
export const queryApiRecordInfoByApiRecordId = (
  params: CodeRepositoryManageAPIDesign.ApiRecordId,
) => {
  return dictRequest<CodeRepositoryManageAPIDesign.ApiRecordParaList>(
    '/apimanage/queryApiRecordInfoByApiRecordId',
    params,
  );
};
/**
 * [1-10330-1]删除API参数
 * @param params
 * @returns
 */
export const deleteApiPara = (
  params: CodeRepositoryManageAPIDesign.DeleteApiPara,
) => {
  return dictRequest<CodeRepositoryManageAPIDesign.ApiRecordParaList>(
    '/apimanage/deleteApiPara',
    params,
    {
      successMsg: translation('global:delete.success'),
    },
  );
};
/**
 * [1-10331-1]提交API变更
 * @param params
 * @returns
 */
export const commitApiPara = (
  params: CodeRepositoryManageAPIDesign.ApiRecordId,
) => {
  return dictRequest<CodeRepositoryManageAPIDesign.ApiRecordParaList>(
    '/apimanage/commitApiPara',
    params,
    {
      successMsg: translation('apiDesign.commitApiPara', '提交成功'),
    },
  );
};
/**
 * [1-10357-1] 根据条件查询API参数
 * @param params
 * @returns
 */
export const queryApiParaByExample = (
  params: CodeRepositoryManageAPIDesign.QueryApiParaByExample,
) => {
  return dictRequest<CodeRepositoryManageAPIDesign.ApiParaList>(
    '/apimanage/queryApiParaByExample',
    params,
  );
};
