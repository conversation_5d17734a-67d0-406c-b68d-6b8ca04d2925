import { useFormConfig } from 'sun-biz';
import {
  BIZ_SCENE_CODE_NAME,
  CONTROL_MODE_TYPE_CODE,
  FLAG,
} from '@/utils/constant';
import { Ref } from 'vue';
export function useRestrictionRulesFormConfig(
  disabled: Ref,
  queryBusinessRestrictions: () => void,
) {
  return useFormConfig({
    dataSetCodes: [BIZ_SCENE_CODE_NAME, CONTROL_MODE_TYPE_CODE],
    getData: (t, dataSet) => {
      return [
        {
          name: 'ruleName',
          component: 'input',
          triggerModelChange: true,
          rules: [
            {
              required: true,
              message: t('global:placeholder.input.template', {
                content: t('bizLimitCondition.ruleName', '规则名称'),
              }),
              trigger: ['change', 'blur'],
            },
          ],
          extraProps: {
            readonly: disabled.value,
          },
          label: t('bizLimitCondition.ruleName', '规则名称'),
          placeholder: t('global:placeholder.input.template', {
            content: t('bizLimitCondition.ruleName', '规则名称'),
          }),
        },
        {
          label: t('bizLimitCondition.bizSceneCode', '业务场景'),
          name: 'bizSceneCode',
          triggerModelChange: true,
          component: 'select',
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('bizLimitCondition.bizSceneCode', '业务场景'),
              }),
              trigger: ['change', 'blur'],
            },
          ],
          extraProps: {
            onBlur: () => {
              queryBusinessRestrictions();
            },
            disabled: disabled.value,
            options: dataSet?.value ? dataSet.value[BIZ_SCENE_CODE_NAME] : [],
            className: 'w-40',
            props: {
              label: 'dataValueNameDisplay',
              value: 'dataValueNo',
            },
          },
        },
        {
          label: t('bizLimitCondition.controlModeTypeCode', '控制方式'),
          name: 'controlModeTypeCode',
          triggerModelChange: true,
          component: 'select',
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('bizLimitCondition.controlModeTypeCode', '控制方式'),
              }),
              trigger: ['change', 'blur'],
            },
          ],
          extraProps: {
            disabled: disabled.value,
            options: dataSet?.value
              ? dataSet.value[CONTROL_MODE_TYPE_CODE]
              : [],
            className: 'w-40',
            props: {
              label: 'dataValueNameDisplay',
              value: 'dataValueNo',
            },
          },
        },
        {
          name: 'enabledFlag',
          label: t('system.msChargeSetting.effectiveDate', '启用标志'),
          component: 'switch',
          extraProps: {
            disabled: disabled.value,
            'inline-prompt': true,
            'active-value': FLAG.YES,
            'inactive-value': FLAG.NO,
            'active-text': t('global:enabled'),
            'inactive-text': t('global:disabled'),
          },
        },
        {
          name: 'tipsMessage',
          type: 'textarea',
          component: 'input',
          triggerModelChange: true,
          isFullWidth: true,
          rules: [
            {
              required: true,
              message: t('global:placeholder.input.template', {
                content: t('bizLimitCondition.tipsMessage', '提示信息'),
              }),
              trigger: ['change', 'blur'],
            },
          ],
          extraProps: {
            readonly: disabled.value,
          },
          label: t('bizLimitCondition.tipsMessage', '提示信息'),
          placeholder: t('global:placeholder.input.template', {
            content: t('bizLimitCondition.tipsMessage', '提示信息'),
          }),
        },
        {
          name: 'ruleDesc',
          type: 'textarea',
          component: 'input',
          triggerModelChange: true,
          isFullWidth: true,
          extraProps: {
            readonly: disabled.value,
          },
          label: t('bizLimitCondition.ruleDesc', '规则描述'),
          placeholder: t('global:placeholder.input.template', {
            content: t('bizLimitCondition.ruleDesc', '规则描述'),
          }),
        },
      ];
    },
  });
}
