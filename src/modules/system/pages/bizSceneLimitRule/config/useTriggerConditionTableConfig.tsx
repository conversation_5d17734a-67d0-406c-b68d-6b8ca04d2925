import { useColumnConfig, useEditableTable } from 'sun-biz';
import { nextTick, Ref, ref } from 'vue';
import ValueTypeSelection from '../components/ValueTypeSelection.vue';
import { VALUE_TYPE_ENUM } from '@/utils/constant';
import { ElMessage } from 'element-sun';
import { useLimitedConditionSet } from '../store/org';
import { cloneDeep, compact } from 'es-toolkit';
import { FLAG_STR } from '@/utils/constant.ts';

const LimitedConditionSet = useLimitedConditionSet();
const clickConfirm = ref(false);
//请求数据

interface PopoverInstance {
  bizSceneLimitRDetailList: { value: string; endValue: string }[];

  wipeData: () => void;
  retrieveDataList: (bizSceneLimitConditionId: string) => void;
  nullData: () => void;
  addProject: () => void;
}

const popoverRefs = ref<Record<number, PopoverInstance | undefined>>({});
const setPopoverRef = (el: PopoverInstance | null, id: number) => {
  if (el) {
    popoverRefs.value[id] = el;
  }
};
export function useTableColumnsTableConfig(
  tableRef: Ref,
  bizSceneLimitRSettingList: Ref,
  conditionList: Ref,
  disabled: Ref,
  menuId?: string,
  orgId?: string,
) {
  const { toggleEdit, validateItem, addItem, delItem, cancelEdit } =
    useEditableTable({
      tableRef,
      data: bizSceneLimitRSettingList,
      id: 'bizSceneLimitConditionId',
    });
  const getDataValueTypeCode = async (
    bizSceneLimitConditionId: string,
    bizSceneLimitRDetailList: { value: string; endValue: string }[],
    row: DizLimitCondition.DizSceneLimitRSettingList,
  ) => {
    if (!bizSceneLimitConditionId || !bizSceneLimitRDetailList.length) return;
    const data = await LimitedConditionSet.setRetrieveDataList(
      bizSceneLimitConditionId,
      menuId,
      orgId,
    );

    const filterContent = bizSceneLimitRDetailList.map((item) => {
      return data?.valuesList.find(
        (items) => items[data.primaryKey] === item.value,
      )?.[data.displayKey];
    });
    row.collectionOfValueTypes = compact(filterContent).join('|');
  };
  // const assignment = () => {
  //   let results = false;
  //   bizSceneLimitRSettingList.value = bizSceneLimitRSettingList.value.map(
  //     (item: DizLimitCondition.DizSceneLimitRSettingList, index: number) => {
  //       popoverRefs.value[index]?.nullData();
  //       if (item.editable) {
  //         results = true;
  //       }
  //       if (!popoverRefs.value[index]?.bizSceneLimitRDetailList.length) {
  //         popoverRefs.value[index]?.addProject();
  //       }
  //       return {
  //         ...item,
  //         bizSceneLimitRDetailList: cloneDeep(
  //           popoverRefs.value[index]?.bizSceneLimitRDetailList ||
  //             item.bizSceneLimitRDetailList ||
  //             '',
  //         ),
  //       };
  //     },
  //   );
  //   return results;
  // };
  const tableColumnsConfig = useColumnConfig({
    getData: (t) => {
      const data: DizLimitCondition.CheckReactive[] = [
        {
          label: t('global.sequenceNumber', '序号'),
          minWidth: 60,
          prop: 'indexNo',
          render: (row: object, $index: number) => <>{$index + 1}</>,
        },
        {
          label: t('bizLimitCondition.bizSceneLimitConditionDesc', '条件'),
          prop: 'bizSceneLimitConditionDesc',
          minWidth: 160,
          editable: true,
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('bizLimitCondition.bizSceneLimitConditionDesc', '条件'),
              }),
              trigger: ['change', 'blur'],
            },
          ],
          render: (
            row: DizLimitCondition.DizSceneLimitRSettingList,
            index: number,
          ) => {
            return (
              <>
                {row.editable ? (
                  <>
                    <el-select
                      filterable
                      v-model={row.bizSceneLimitCondition}
                      value-key="bizSceneLimitConditionCode"
                      placeholder={t('global:placeholder.select.template', {
                        name: t(
                          'bizLimitCondition.bizSceneLimitConditionDesc',
                          '条件',
                        ),
                      })}
                      onChange={(
                        item: DizLimitCondition.RestrictiveConditionsList,
                      ) => {
                        popoverRefs.value[index]?.wipeData();
                        row.valueTypeCode = item.valueTypeCode || '';
                        row.conditionSymbolList = item.conditionSymbolList;
                        row.bizSceneLimitConditionCode =
                          item.bizSceneLimitConditionCode || '';
                        row.bizSceneLimitConditionId =
                          item.bizSceneLimitConditionId || '';
                        row.bizSceneLimitConditionDesc =
                          item.bizSceneLimitConditionDesc;
                        if (
                          item.valueTypeCode === VALUE_TYPE_ENUM.CODE_SYSTEM
                        ) {
                          popoverRefs.value[index]?.retrieveDataList(
                            item.bizSceneLimitConditionId || '',
                          );
                        }
                      }}
                    >
                      {conditionList.value.map(
                        (item: DizLimitCondition.RestrictiveConditionsList) => {
                          return (
                            <el-option
                              key={item.bizSceneLimitConditionCode}
                              label={item.bizSceneLimitConditionDesc}
                              value={item}
                              disabled={bizSceneLimitRSettingList.value.some(
                                (
                                  sunb: DizLimitCondition.DizSceneLimitRSettingList,
                                ) =>
                                  sunb.bizSceneLimitConditionCode ===
                                  item.bizSceneLimitConditionCode,
                              )}
                            ></el-option>
                          );
                        },
                      )}
                    </el-select>
                  </>
                ) : (
                  <>{row.bizSceneLimitConditionDesc}</>
                )}
              </>
            );
          },
        },
        {
          label: t('bizLimitCondition.symbolTypeDesc', '符号'),
          prop: 'symbolTypeDesc',
          minWidth: 160,
          editable: true,
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('bizLimitCondition.symbolTypeDesc', '符号'),
              }),
              trigger: ['change', 'blur'],
            },
          ],
          render: (row: DizLimitCondition.DizSceneLimitRSettingList) => {
            return (
              <>
                {row.editable ? (
                  <>
                    <el-select
                      v-model={row.symbolTypeCode}
                      filterable={true}
                      value-key="conditionSymbolId"
                      onFocus={() => {
                        if (!row.bizSceneLimitConditionCode) {
                          ElMessage.error(
                            t('global:placeholder.select.template', {
                              name: t(
                                'bizLimitCondition.bizSceneLimitConditionDesc',
                                '条件',
                              ),
                            }),
                          );
                        }
                        validateItem(row);
                      }}
                      placeholder={t('global:placeholder.select.template', {
                        name: t(
                          'bizLimitCondition.bizSceneLimitConditionDesc',
                          '符号',
                        ),
                      })}
                    >
                      {(row.conditionSymbolList || []).map(
                        (item: DizLimitCondition.ConditionSymbolList) => {
                          return (
                            <el-option
                              key={item.symbolTypeCode}
                              label={item.symbolTypeDesc}
                              value={item.symbolTypeCode}
                              onClick={() => {
                                row.symbolTypeDesc = item.symbolTypeDesc || '';
                              }}
                            ></el-option>
                          );
                        },
                      )}
                    </el-select>
                  </>
                ) : (
                  <>{row.symbolTypeDesc}</>
                )}
              </>
            );
          },
        },
        {
          label: t('bizLimitCondition.bizSceneLimitRDetailList', '设置值'),
          prop: 'bizSceneLimitRDetailList',
          minWidth: 400,
          editable: true,
          render: (
            row: DizLimitCondition.DizSceneLimitRSettingList,
            index: number,
          ) => {
            return (
              <>
                {row.editable ? (
                  <>
                    <el-form-item
                      class={'!w-full'}
                      prop={`tableData.${index}.bizSceneLimitRDetailList`}
                      // class={`${!row.bizSceneLimitRDetailList?.length && clickConfirm.value ? 'is-error !mb-0' : '!mb-0'}`}
                      rules={[
                        {
                          required: true,
                          validator: (
                            rule: string,
                            value: { value: string; endValue: string }[],
                            callback: (err?: Error) => void,
                          ) => {
                            if (!row.bizSceneLimitRDetailList?.length) {
                              callback(
                                new Error(
                                  t(
                                    'bizLimitCondition.bizSceneLimitRDetailList',
                                    '请填写设置值',
                                  ),
                                ),
                              );
                            } else {
                              if (row.symbolTypeCode === 'Range') {
                                let results = false;
                                [...value].forEach((item) => {
                                  row.bizSceneLimitRDetailList = cloneDeep(
                                    popoverRefs.value[index]
                                      ?.bizSceneLimitRDetailList || [],
                                  );
                                  if (
                                    item.endValue === undefined ||
                                    item.endValue === '' ||
                                    item.value === undefined ||
                                    item.value === ''
                                  ) {
                                    results = true;
                                  }
                                });
                                if (results) {
                                  callback(
                                    new Error(
                                      t(
                                        'bizLimitCondition.bizSceneLimitRDetailList',
                                        '请填写设置值',
                                      ),
                                    ),
                                  );
                                } else {
                                  callback();
                                }
                              } else {
                                let results = false;
                                [...value].forEach((item) => {
                                  row.bizSceneLimitRDetailList = cloneDeep(
                                    popoverRefs.value[index]
                                      ?.bizSceneLimitRDetailList || [],
                                  );
                                  if (
                                    item.value === undefined ||
                                    item.value === ''
                                  ) {
                                    results = true;
                                  }
                                });
                                if (results) {
                                  callback(
                                    new Error(
                                      t(
                                        'bizLimitCondition.bizSceneLimitRDetailList',
                                        '请填写设置值',
                                      ),
                                    ),
                                  );
                                } else {
                                  callback();
                                }
                              }
                            }
                          },
                          trigger: ['change', 'blur'],
                        },
                      ]}
                    >
                      <ValueTypeSelection
                        ref={(el: unknown) =>
                          setPopoverRef(el as PopoverInstance | null, index)
                        }
                        list={row.bizSceneLimitRDetailList}
                        symbolTypeCode={row.symbolTypeCode || ''}
                        type={row.valueTypeCode}
                        bizSceneLimitConditionId={row.bizSceneLimitConditionId}
                      ></ValueTypeSelection>
                    </el-form-item>
                  </>
                ) : (
                  <>
                    {row.valueTypeCode === VALUE_TYPE_ENUM.CODE_SYSTEM ? (
                      <>
                        {row.collectionOfValueTypes ||
                          row.bizSceneLimitRDetailList
                            ?.map((item) => {
                              return item.endValue
                                ? item.value + '-' + item.endValue
                                : item.value;
                            })
                            .join('|')}
                      </>
                    ) : (
                      <>
                        {row.valueTypeCode === VALUE_TYPE_ENUM.SWITCH ? (
                          <>
                            <el-switch
                              v-model={row.bizSceneLimitRDetailList[0].value}
                              inline-prompt
                              disabled
                              active-value={FLAG_STR.YES}
                              inactive-value={FLAG_STR.NO}
                              active-text={t('global:enabled')}
                              inactive-text={t('global:disabled')}
                            ></el-switch>
                          </>
                        ) : (
                          <>
                            {row.bizSceneLimitRDetailList
                              ?.map((item) => {
                                return item.endValue
                                  ? item.value + '-' + item.endValue
                                  : item.value;
                              })
                              .join('|')}
                          </>
                        )}
                      </>
                    )}
                  </>
                )}
              </>
            );
          },
        },
      ];
      if (!disabled.value) {
        data.push({
          label: t('bizLimitCondition.departmentManageTable.orgDesc', '操作'),
          prop: 'orgDesc',
          minWidth: 140,
          fixed: 'right',
          render: (
            row: DizLimitCondition.DizSceneLimitRSettingList,
            index: number,
          ) => {
            return row.editable ? (
              <>
                <el-button
                  link
                  type="danger"
                  onClick={() => {
                    popoverRefs.value[index]?.nullData();
                    row.bizSceneLimitRDetailList = cloneDeep(
                      popoverRefs.value[index]?.bizSceneLimitRDetailList || [],
                    );
                    if (
                      !row.bizSceneLimitConditionDesc &&
                      !row.symbolTypeDesc &&
                      !row.bizSceneLimitRDetailList.length
                    ) {
                      delItem(index);
                      return;
                    }
                    if (!row?.bizSceneLimitRDetailList?.length) {
                      clickConfirm.value = true;
                      popoverRefs.value[index]?.addProject();
                      return;
                    }
                    clickConfirm.value = false;
                    if (row.valueTypeCode === VALUE_TYPE_ENUM.CODE_SYSTEM) {
                      getDataValueTypeCode(
                        row.bizSceneLimitConditionId,
                        row.bizSceneLimitRDetailList,
                        row,
                      );
                    }
                    cancelEdit(row, index, false);
                  }}
                >
                  {t('global:cancel')}
                </el-button>
                <el-button
                  link
                  type="primary"
                  onClick={() => {
                    popoverRefs.value[index]?.nullData();
                    row.bizSceneLimitRDetailList = cloneDeep(
                      popoverRefs.value[index]?.bizSceneLimitRDetailList || [],
                    );
                    if (!row?.bizSceneLimitRDetailList?.length) {
                      clickConfirm.value = true;
                      popoverRefs.value[index]?.addProject();
                      return;
                    }
                    clickConfirm.value = false;
                    if (row.valueTypeCode === VALUE_TYPE_ENUM.CODE_SYSTEM) {
                      getDataValueTypeCode(
                        row.bizSceneLimitConditionId,
                        row.bizSceneLimitRDetailList,
                        row,
                      );
                    }
                    validateItem(row).then(() => {
                      tableRef?.value?.formRef
                        ?.validateField(
                          `tableData.${index}.bizSceneLimitRDetailList`,
                        )
                        .then(() => {
                          toggleEdit(row);
                        })
                        .catch((err: string) => {
                          console.log(err);
                        });
                    });
                  }}
                >
                  {t('global:confirm')}
                </el-button>
              </>
            ) : (
              <>
                <el-button
                  link
                  type="primary"
                  onClick={() => {
                    if (row.editable) {
                      popoverRefs.value[index]?.nullData();
                      row.bizSceneLimitRDetailList = cloneDeep(
                        popoverRefs.value[index]?.bizSceneLimitRDetailList ||
                          [],
                      );
                      nextTick(() => {
                        row.editable = false;
                      });
                    } else {
                      toggleEdit(row);
                    }
                  }}
                >
                  {t('global:edit')}
                </el-button>
                <el-button link type="danger" onClick={() => delItem(index)}>
                  {t('global:delete')}
                </el-button>
              </>
            );
          },
        });
      }
      return data;
    },
  });
  return {
    tableColumnsConfig,
    addItem,
    getDataValueTypeCode,
  };
}
