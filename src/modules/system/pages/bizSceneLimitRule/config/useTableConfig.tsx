import { useColumnConfig } from 'sun-biz';
import { FLAG } from '@/utils/constant';

export function useBusinessScenarioRulesTableConfig(
  editRule: (row: DizLimitCondition.RuleList, type: string) => void,
  enableBusiness: (row: DizLimitCondition.RuleList) => void,
) {
  return useColumnConfig({
    getData: (t) => {
      return [
        {
          label: t('global.sequenceNumber', '序号'),
          minWidth: 100,
          prop: 'indexNo',
          render: (row: object, $index: number) => <>{$index + 1}</>,
        },
        {
          label: t('bizLimitCondition.ruleName', '规则名称'),
          prop: 'ruleName',
          minWidth: 160,
        },
        {
          label: t('bizLimitCondition.ruleDesc', '规则描述'),
          prop: 'ruleDesc',
          minWidth: 160,
        },
        {
          label: t('bizLimitCondition.controlModeTypeDesc', '控制方式'),
          prop: 'controlModeTypeDesc',
          minWidth: 160,
        },
        {
          label: t('bizLimitCondition.tipsMessage', '提示信息'),
          prop: 'tipsMessage',
          minWidth: 160,
        },
        {
          label: t('bizLimitCondition.enabledFlag', '启用标志'),
          prop: 'enabledFlag',
          minWidth: 160,
          render: (row: DizLimitCondition.RuleList) => {
            return (
              <>
                <el-switch
                  v-model={row.enabledFlag}
                  inline-prompt
                  active-value={FLAG.YES}
                  inactive-value={FLAG.NO}
                  onClick={() => {
                    enableBusiness(row);
                  }}
                  active-text={t('global:enabled')}
                  inactive-text={t('global:disabled')}
                ></el-switch>
              </>
            );
          },
        },
        {
          label: t('bizLimitCondition.departmentManageTable.orgDesc', '操作'),
          prop: 'orgDesc',
          minWidth: 140,
          fixed: 'right',
          render: (row: DizLimitCondition.RuleList) => {
            return (
              <>
                <el-button
                  link
                  type="primary"
                  onClick={() => editRule(row, 'edit')}
                >
                  {t('global:edit')}
                </el-button>
                <el-button
                  link
                  type="primary"
                  onClick={() => editRule(row, 'details')}
                >
                  {t('bizLimitCondition.details', '详情')}
                </el-button>
              </>
            );
          },
        },
      ];
    },
  });
}
