import { useFormConfig } from 'sun-biz';
import { BIZ_SCENE_CODE_NAME } from '@/utils/constant.ts';
export function useQueryCriteriaFormConfig() {
  return useFormConfig({
    dataSetCodes: [BIZ_SCENE_CODE_NAME],
    getData: (t, dataSet) => {
      return [
        {
          label: t('bizLimitCondition.bizSceneCode', '业务场景'),
          name: 'bizSceneCode',
          triggerModelChange: true,
          component: 'select',
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('bizLimitCondition.bizSceneCode', '业务场景'),
              }),
              trigger: ['change', 'blur'],
            },
          ],
          extraProps: {
            options: dataSet?.value ? dataSet.value[BIZ_SCENE_CODE_NAME] : [],
            className: 'w-40',
            clearable: false,
            props: {
              label: 'dataValueNameDisplay',
              value: 'dataValueNo',
            },
          },
        },
      ];
    },
  });
}
