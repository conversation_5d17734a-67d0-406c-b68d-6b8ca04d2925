<script setup lang="ts">
  import { VALUE_TYPE_ENUM } from '@/utils/constant';
  import { onMounted, ref } from 'vue';
  import { useAppConfigData, MAIN_APP_CONFIG } from 'sun-biz';
  import { generateUUID } from '@sun-toolkit/shared';
  import { useLimitedConditionSet } from '@/modules/system/pages/bizSceneLimitRule/store/org.ts';
  import { FLAG_STR } from '@/utils/constant.ts';
  interface Props {
    type?: string;
    symbolTypeCode: string; //"Range"
    list: { value: string; endValue: string }[];
    bizSceneLimitConditionId?: string;
  }
  const LimitedConditionSet = useLimitedConditionSet();
  const {
    type = '',
    list = [],
    bizSceneLimitConditionId,
    symbolTypeCode,
  } = defineProps<Props>();
  const primaryKey = ref('');
  const displayKey = ref('');
  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);
  const { menuId } = useAppConfigData([
    MAIN_APP_CONFIG.MENU_ID,
    MAIN_APP_CONFIG.CURRENT_ORG,
  ]);

  const valueRangeDataList = ref<string[]>([]);
  const bizSceneLimitRDetailList = ref<
    {
      id?: string;
      value: string;
      endValue?: string;
    }[]
  >([
    {
      id: generateUUID(),
      value: '',
      endValue: '',
    },
  ]);
  const valuesList = ref<{ [key: string]: string }[]>([]);
  //查询可检索数据
  const retrieveDataList = async (bizSceneLimitConditionId: string) => {
    if (!bizSceneLimitConditionId) return;
    const data = await LimitedConditionSet.setRetrieveDataList(
      bizSceneLimitConditionId,
      menuId,
      currentOrg?.orgId || '',
    );
    primaryKey.value = data.primaryKey;
    displayKey.value = data.displayKey;
    valuesList.value = data.valuesList;
  };

  const addProject = () => {
    bizSceneLimitRDetailList.value.push({
      id: generateUUID(),
      value: '',
      endValue: '',
    });
  };
  //清空数据
  const wipeData = () => {
    bizSceneLimitRDetailList.value = [
      {
        id: generateUUID(),
        value: '',
        endValue: '',
      },
    ];
    valuesList.value = [];
    valueRangeDataList.value = [];
  };
  //点击移除
  const clickToRemove = (index: number) => {
    bizSceneLimitRDetailList.value.splice(index, 1);
  };
  // 清除空数据
  const nullData = () => {
    bizSceneLimitRDetailList.value = bizSceneLimitRDetailList.value.filter(
      (item) => {
        return item.value !== undefined || item.value !== '' || item.endValue;
      },
    );
    //清洗第二个值如果不是区间进行清洗
    if (symbolTypeCode !== 'Range') {
      bizSceneLimitRDetailList.value = bizSceneLimitRDetailList.value.map(
        (item) => {
          return {
            value: item.value,
          };
        },
      );
    } else {
      bizSceneLimitRDetailList.value = bizSceneLimitRDetailList.value.map(
        (item) => {
          return {
            value: item.value,
            endValue: item.endValue,
          };
        },
      );
    }
  };
  //选中数据
  const changeSelect = () => {
    bizSceneLimitRDetailList.value = valueRangeDataList.value.map((item) => {
      return {
        value: item,
      };
    });
  };
  onMounted(() => {
    if (bizSceneLimitConditionId && VALUE_TYPE_ENUM.CODE_SYSTEM === type) {
      retrieveDataList(bizSceneLimitConditionId);
    }
  });
  /**
   * 使用 dayjs 判断字符串是否为有效时间格式
   * @param {string} str - 律师待检查的字符串
   * @returns {boolean} - 如果是有效时间返回 true，否则返回 false
   */
  /**
   * 判断字符串是否为时间格式
   * @param {string} str - 待检查的字符串
   * @returns {boolean} - 如果是时间格式返回 true，否则返回 false
   */
  function isTimeString(str: string) {
    const timeRegex =
      /^(\d{4})[-/]?(\d{2})[-/]?(\d{2})([\sT](\d{2}):(\d{2})(:(\d{2}))?)?$/;
    return timeRegex.test(str);
  }

  const initFun = () => {
    if (list.length) {
      if (VALUE_TYPE_ENUM.CODE_SYSTEM === type) {
        valueRangeDataList.value = list.map((item: { value: string }) => {
          return item.value;
        });
        changeSelect();
      } else {
        if (VALUE_TYPE_ENUM.TIME_PICKER === type) {
          bizSceneLimitRDetailList.value = list.map((item) => {
            return {
              value: isTimeString(item.value) ? item.value : '',
              endValue: isTimeString(item.endValue) ? item.endValue : '',
              id: generateUUID(),
            };
          });
        } else {
          bizSceneLimitRDetailList.value = list.map((item) => {
            return {
              ...item,
              id: generateUUID(),
            };
          });
        }
      }
    }
  };
  initFun();
  defineExpose({
    retrieveDataList,
    bizSceneLimitRDetailList,
    wipeData,
    nullData,
    addProject,
  });
</script>
<template>
  <div class="flex w-full flex-wrap justify-start">
    <div v-if="VALUE_TYPE_ENUM.CODE_SYSTEM === type" class="w-full">
      <el-select
        class="!w-full"
        v-model="valueRangeDataList"
        :placeholder="
          $t('global:placeholder.select.template', {
            name: $t('bizLimitCondition.data', '数据'),
          })
        "
        multiple
        clearable
        filterable
        @change="changeSelect"
        @clear="changeSelect"
      >
        <el-option
          v-for="items in valuesList"
          :key="items[primaryKey]"
          :label="items[displayKey]"
          :value="items[primaryKey]"
        >
        </el-option>
      </el-select>
    </div>
    <template v-else>
      <div
        v-for="(item, index) in bizSceneLimitRDetailList"
        :key="item.id"
        class="mb-1 mr-2 w-full"
      >
        <div v-if="VALUE_TYPE_ENUM.NUMBER === type" class="w-full">
          <div v-if="symbolTypeCode === 'Range'" class="relative w-full">
            <el-row>
              <el-col :span="10">
                <el-input
                  v-model="item.value"
                  :placeholder="
                    $t('global:placeholder.input.template', {
                      content: $t(
                        'storageRack.table.startTheContent',
                        '开始数字',
                      ),
                    })
                  "
                  type="number"
                  class="!w-full"
                ></el-input
              ></el-col>
              <el-col :span="1">-</el-col>
              <el-col
                :span="bizSceneLimitRDetailList.length - 1 === index ? 7 : 10"
                ><el-input
                  v-model="item.endValue"
                  :placeholder="
                    $t('global:placeholder.input.template', {
                      content: $t('storageRack.table.endContent', '结束数字'),
                    })
                  "
                  type="number"
                  class="!w-full"
                ></el-input
              ></el-col>
              <el-col :span="3">
                <el-button
                  class="ml-3 border"
                  @click="clickToRemove(index)"
                  icon="Minus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
              <el-col
                :span="3"
                v-if="bizSceneLimitRDetailList.length - 1 === index"
              >
                <el-button
                  class="ml-3 border"
                  @click="addProject"
                  icon="Plus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
            </el-row>
          </div>
          <div v-else class="relative w-full">
            <el-row>
              <el-col
                :span="bizSceneLimitRDetailList.length - 1 === index ? 18 : 21"
              >
                <el-input
                  v-model="item.value"
                  :placeholder="
                    $t('global:placeholder.input.template', {
                      content: $t('storageRack.table.data', '数字'),
                    })
                  "
                  type="number"
                  class="!w-full"
                ></el-input>
              </el-col>
              <el-col :span="3">
                <el-button
                  class="ml-3 border"
                  @click="clickToRemove(index)"
                  icon="Minus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
              <el-col
                :span="3"
                v-if="bizSceneLimitRDetailList.length - 1 === index"
              >
                <el-button
                  class="ml-3 border"
                  @click="addProject"
                  icon="Plus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
            </el-row>
          </div>
        </div>
        <div v-else-if="VALUE_TYPE_ENUM.STRING === type" class="w-full">
          <div v-if="symbolTypeCode === 'Range'" class="relative w-full">
            <el-row>
              <el-col :span="10">
                <el-input
                  v-model="item.value"
                  :placeholder="
                    $t('global:placeholder.input.template', {
                      content: $t(
                        'storageRack.table.startTheContent',
                        '开始内容',
                      ),
                    })
                  "
                  type="text"
                  class="!w-full"
                ></el-input
              ></el-col>
              <el-col :span="1">-</el-col>
              <el-col
                :span="bizSceneLimitRDetailList.length - 1 === index ? 7 : 10"
                ><el-input
                  v-model="item.endValue"
                  :placeholder="
                    $t('global:placeholder.input.template', {
                      content: $t('storageRack.table.endContent', '结束内容'),
                    })
                  "
                  type="text"
                  class="!w-full"
                ></el-input
              ></el-col>
              <el-col :span="3">
                <el-button
                  class="ml-3 border"
                  @click="clickToRemove(index)"
                  icon="Minus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
              <el-col
                :span="3"
                v-if="bizSceneLimitRDetailList.length - 1 === index"
              >
                <el-button
                  class="ml-3 border"
                  @click="addProject"
                  icon="Plus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
            </el-row>
          </div>
          <div v-else class="relative">
            <el-row>
              <el-col
                :span="bizSceneLimitRDetailList.length - 1 === index ? 18 : 21"
              >
                <el-input
                  v-model="item.value"
                  :placeholder="
                    $t('global:placeholder.input.template', {
                      content: $t('storageRack.table.data', '内容'),
                    })
                  "
                  type="text"
                  class="w-full"
                ></el-input
              ></el-col>
              <el-col :span="3">
                <el-button
                  class="ml-3 border"
                  @click="clickToRemove(index)"
                  icon="Minus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
              <el-col
                :span="3"
                v-if="bizSceneLimitRDetailList.length - 1 === index"
              >
                <el-button
                  class="ml-3 border"
                  @click="addProject"
                  icon="Plus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
            </el-row>
          </div>
        </div>
        <div v-else-if="VALUE_TYPE_ENUM.DATE_PICKER === type" class="w-full">
          <div v-if="symbolTypeCode === 'Range'" class="relative w-full">
            <el-row>
              <el-col :span="10">
                <el-date-picker
                  v-model="item.value"
                  type="date"
                  class="!w-full"
                  :placeholder="
                    $t('global:placeholder.select.template', {
                      name: $t('bizLimitCondition.startDate', '开始日期'),
                    })
                  "
                  value-format="YYYY-MM-DD"
                >
                </el-date-picker
              ></el-col>
              <el-col :span="1">-</el-col>
              <el-col
                :span="bizSceneLimitRDetailList.length - 1 === index ? 7 : 10"
                ><el-date-picker
                  v-model="item.endValue"
                  type="date"
                  class="!w-full"
                  :placeholder="
                    $t('global:placeholder.select.template', {
                      name: $t('bizLimitCondition.endDate', '结束日期'),
                    })
                  "
                  value-format="YYYY-MM-DD"
                >
                </el-date-picker
              ></el-col>
              <el-col :span="3">
                <el-button
                  class="ml-3 border"
                  @click="clickToRemove(index)"
                  icon="Minus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
              <el-col
                :span="3"
                v-if="bizSceneLimitRDetailList.length - 1 === index"
              >
                <el-button
                  class="ml-3 border"
                  @click="addProject"
                  icon="Plus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
            </el-row>
          </div>
          <div v-else class="relative w-full">
            <el-row>
              <el-col
                :span="bizSceneLimitRDetailList.length - 1 === index ? 18 : 21"
              >
                <el-date-picker
                  v-model="item.value"
                  type="date"
                  class="!w-full"
                  :placeholder="
                    $t('global:placeholder.select.template', {
                      name: $t('bizLimitCondition.date', '日期'),
                    })
                  "
                  value-format="YYYY-MM-DD"
                >
                </el-date-picker
              ></el-col>
              <el-col :span="3">
                <el-button
                  class="ml-3 border"
                  @click="clickToRemove(index)"
                  icon="Minus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
              <el-col
                :span="3"
                v-if="bizSceneLimitRDetailList.length - 1 === index"
              >
                <el-button
                  class="ml-3 border"
                  @click="addProject"
                  icon="Plus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
            </el-row>
          </div>
        </div>
        <div v-else-if="VALUE_TYPE_ENUM.TIME_PICKER === type" class="w-full">
          <div v-if="symbolTypeCode === 'Range'" class="relative w-full">
            <el-row>
              <el-col :span="10"
                ><el-time-picker
                  class="!w-full"
                  v-model="item.value"
                  :placeholder="
                    $t('global:placeholder.select.template', {
                      name: $t('bizLimitCondition.startTime', '开始时间'),
                    })
                  "
                  value-format="HH:mm:ss"
                >
                </el-time-picker
              ></el-col>
              <el-col :span="1">-</el-col>
              <el-col
                :span="bizSceneLimitRDetailList.length - 1 === index ? 7 : 10"
                ><el-time-picker
                  class="!w-full"
                  v-model="item.endValue"
                  :placeholder="
                    $t('global:placeholder.select.template', {
                      name: $t('bizLimitCondition.endTime', '结束时间'),
                    })
                  "
                  value-format="HH:mm:ss"
                >
                </el-time-picker
              ></el-col>
              <el-col :span="3">
                <el-button
                  class="ml-3 border"
                  @click="clickToRemove(index)"
                  icon="Minus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
              <el-col
                :span="3"
                v-if="bizSceneLimitRDetailList.length - 1 === index"
              >
                <el-button
                  class="ml-3 border"
                  @click="addProject"
                  icon="Plus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
            </el-row>
          </div>
          <div v-else class="relative w-full">
            <el-row>
              <el-col
                :span="bizSceneLimitRDetailList.length - 1 === index ? 18 : 21"
              >
                <el-time-picker
                  v-model="item.value"
                  :placeholder="
                    $t('global:placeholder.select.template', {
                      name: $t('bizLimitCondition.time', '时间'),
                    })
                  "
                  value-format="HH:mm:ss"
                  class="!w-full"
                >
                </el-time-picker
              ></el-col>
              <el-col :span="3">
                <el-button
                  class="ml-3 border"
                  @click="clickToRemove(index)"
                  icon="Minus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
              <el-col
                :span="3"
                v-if="bizSceneLimitRDetailList.length - 1 === index"
              >
                <el-button
                  class="ml-3 border"
                  @click="addProject"
                  icon="Plus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
            </el-row>
          </div>
        </div>
        <div
          v-else-if="VALUE_TYPE_ENUM.DATE_TIME_PICKER === type"
          class="w-full"
        >
          <div v-if="symbolTypeCode === 'Range'" class="relative w-full">
            <el-row>
              <el-col :span="10"
                ><el-date-picker
                  v-model="item.value"
                  type="datetime"
                  class="!w-full"
                  :placeholder="
                    $t('global:placeholder.select.template', {
                      name: $t(
                        'bizLimitCondition.startDateAndDate',
                        '开始时间日期',
                      ),
                    })
                  "
                  value-format="YYYY-MM-DD HH:mm:ss"
                >
                </el-date-picker
              ></el-col>
              <el-col :span="1">-</el-col>
              <el-col
                :span="bizSceneLimitRDetailList.length - 1 === index ? 7 : 10"
              >
                <el-date-picker
                  v-model="item.endValue"
                  type="datetime"
                  class="!w-full"
                  :placeholder="
                    $t('global:placeholder.select.template', {
                      name: $t(
                        'bizLimitCondition.endDateAndDate',
                        '结束时间日期',
                      ),
                    })
                  "
                  value-format="YYYY-MM-DD HH:mm:ss"
                >
                </el-date-picker
              ></el-col>
              <el-col :span="3">
                <el-button
                  class="ml-3 border"
                  @click="clickToRemove(index)"
                  icon="Minus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
              <el-col
                :span="3"
                v-if="bizSceneLimitRDetailList.length - 1 === index"
              >
                <el-button
                  class="ml-3 border"
                  @click="addProject"
                  icon="Plus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
            </el-row>
          </div>
          <div v-else class="relative w-full">
            <el-row>
              <el-col
                :span="bizSceneLimitRDetailList.length - 1 === index ? 18 : 21"
              >
                <el-date-picker
                  v-model="item.value"
                  type="datetime"
                  class="!w-full"
                  :placeholder="
                    $t('global:placeholder.select.template', {
                      name: $t('bizLimitCondition.dateAndDate', '时间日期'),
                    })
                  "
                  value-format="YYYY-MM-DD HH:mm:ss"
                >
                </el-date-picker
              ></el-col>
              <el-col :span="3">
                <el-button
                  class="ml-3 border"
                  @click="clickToRemove(index)"
                  icon="Minus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
              <el-col
                :span="3"
                v-if="bizSceneLimitRDetailList.length - 1 === index"
              >
                <el-button
                  class="ml-3 border"
                  @click="addProject"
                  icon="Plus"
                  type="primary"
                  plain
                ></el-button
              ></el-col>
            </el-row>
          </div>
        </div>
        <div v-else-if="VALUE_TYPE_ENUM.SWITCH === type">
          <el-switch
            v-model="item.value"
            inline-prompt
            :active-value="FLAG_STR.YES"
            :inactive-value="FLAG_STR.NO"
            :active-text="$t('global:enabled')"
            :inactive-text="$t('global:disabled')"
          ></el-switch>
        </div>
      </div>
    </template>
  </div>
</template>
