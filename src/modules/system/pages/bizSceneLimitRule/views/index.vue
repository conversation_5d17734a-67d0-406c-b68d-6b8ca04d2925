<script setup lang="ts">
  import { Title, ProForm, ProTable } from 'sun-biz';
  import { onUnmounted, ref } from 'vue';
  import { useQueryCriteriaFormConfig } from '../config/useFormConfig';
  import { useBusinessScenarioRulesTableConfig } from '../config/useTableConfig';
  import { useRouter } from 'vue-router';
  import {
    queryBizSceneLimitRuleByExample,
    updateEnableFlagByBizSceneLimitRuleId,
  } from '@/modules/system/api/bizScene.ts';
  import { useBizSceneCode } from '../store/org.ts';
  import { ElMessage } from 'element-sun';
  import { FLAG } from '@/utils/constant.ts';
  import { useTranslation } from 'i18next-vue';
  const { t } = useTranslation();
  const setBiz = useBizSceneCode();
  const router = useRouter();
  const proFormRef = ref();
  const searchParams = ref<{ bizSceneCode: string }>({
    bizSceneCode: '',
  });
  const ruleList = ref<DizLimitCondition.RuleList[]>([]);
  const loading = ref(false);
  const searchConfig = useQueryCriteriaFormConfig();

  //查询业务代码规则
  const businessRuleQuery = async () => {
    loading.value = true;
    proFormRef.value?.ref.validate();
    if (!searchParams.value?.bizSceneCode) {
      loading.value = false;
      return;
    }
    const [, res] = await queryBizSceneLimitRuleByExample({
      ...searchParams.value,
    });
    if (res?.data) {
      ruleList.value = res?.data || [];
    }
    loading.value = false;
  };
  const modelChange = (data: { bizSceneCode: string }) => {
    searchParams.value = {
      ...searchParams.value,
      ...(data ?? {}),
    };
    businessRuleQuery();
  };
  //点击查询
  const onQuery = () => {
    businessRuleQuery();
  };
  //点击新增
  const addClick = () => {
    if (searchParams.value?.bizSceneCode) {
      router.push(`/details/add/${searchParams.value.bizSceneCode}`);
    } else {
      router.push('/details/add');
    }
  };
  //点击编辑规则
  const editRule = (row: DizLimitCondition.RuleList, type: string) => {
    router.push(
      `/details/${type}/${searchParams.value.bizSceneCode}/${row.bizSceneLimitRuleId}`,
    );
  };
  //启用停用业务
  const enableBusiness = async (row: DizLimitCondition.RuleList) => {
    loading.value = true;
    const [, res] = await updateEnableFlagByBizSceneLimitRuleId({
      bizSceneLimitRuleId: row.bizSceneLimitRuleId,
      enabledFlag: row.enabledFlag,
    });
    if (res?.success) {
      ElMessage.success(
        row.enabledFlag === FLAG.YES
          ? t('bizLimitCondition.enable.success', '启用成功')
          : t('bizLimitCondition.deactivated.success', '停用成功'),
      );
      businessRuleQuery();
    } else {
      loading.value = false;
    }
  };
  const tableColumnsConfig = useBusinessScenarioRulesTableConfig(
    editRule,
    enableBusiness,
  );
  const initFun = () => {
    if (setBiz.bizSceneCode) {
      searchParams.value.bizSceneCode = setBiz.bizSceneCode;
      businessRuleQuery();
    }
  };
  initFun();
  onUnmounted(() => {
    setBiz.delBizSceneCode();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title
      :title="$t('bizLimitCondition.list.title', '业务场景限定规则')"
      class="mb-3"
    />
    <div class="mb-3 flex justify-between">
      <ProForm
        ref="proFormRef"
        v-model="searchParams"
        layout-mode="inline"
        :data="searchConfig"
        @model-change="modelChange"
      ></ProForm>
      <div>
        <el-button type="primary" class="!mr-3" @click="onQuery">{{
          $t('global:query')
        }}</el-button>
        <el-button type="primary" @click="addClick">{{
          $t('global:add')
        }}</el-button>
      </div>
    </div>
    <ProTable
      :loading="loading"
      :data="ruleList"
      :columns="tableColumnsConfig"
    ></ProTable>
  </div>
</template>
