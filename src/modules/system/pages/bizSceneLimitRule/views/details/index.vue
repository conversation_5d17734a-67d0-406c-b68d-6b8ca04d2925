<script setup lang="ts">
  import {
    ProForm,
    Title,
    ProTable,
    useAppConfigData,
    MAIN_APP_CONFIG,
  } from 'sun-biz';
  import { useRestrictionRulesFormConfig } from '../../config/useModifyRulesFormConfig.tsx';
  import { useTableColumnsTableConfig } from '../../config/useTriggerConditionTableConfig.tsx';
  import { nextTick, ref } from 'vue';
  import { FLAG, VALUE_TYPE_ENUM } from '@/utils/constant.ts';
  import {
    queryBizSceneXLimitConditionByExample,
    addBizSceneLimitRule,
    updateBizSceneLimitRuleById,
    queryBizSceneLimitRuleByExample,
  } from '@/modules/system/api/bizScene.ts';
  import { ElMessage } from 'element-sun';
  import { useRouter, useRoute } from 'vue-router';
  import { useTranslation } from 'i18next-vue';
  import { useBizSceneCode } from '../../store/org.ts';

  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);
  const { menuId } = useAppConfigData([
    MAIN_APP_CONFIG.MENU_ID,
    MAIN_APP_CONFIG.CURRENT_ORG,
  ]);
  const setBiz = useBizSceneCode();
  const router = useRouter();
  const route = useRoute();
  const { t } = useTranslation();
  const goBack = () => {
    if (searchParams.value?.bizSceneCode) {
      setBiz.setBizSceneCode(searchParams.value?.bizSceneCode);
    }
    router.push('/');
  };

  const dialogType = ref(route.params.type);
  const dialogRef = ref();
  const tableRef = ref();
  const proFormRef = ref();
  const loading = ref(false);
  const conditionList = ref<DizLimitCondition.RestrictiveConditionsList[]>([]);
  const bizSceneLimitRSettingList = ref<
    DizLimitCondition.DizSceneLimitRSettingList[]
  >([]);
  const disabled = ref(route.params.type === 'details');
  const searchParams = ref<DizLimitCondition.RuleList>({
    bizSceneCode: route.params.bizSceneCode as string,
    enabledFlag: FLAG.YES,
  });
  const queryBusinessRestrictions = async (keyWord: string = '') => {
    if (!searchParams.value?.bizSceneCode) return;
    const [, res] = await queryBizSceneXLimitConditionByExample({
      bizSceneCodes: [searchParams.value?.bizSceneCode || ''],
      keyWord,
    });
    if (res?.success) {
      const [data] = res?.data || [];
      if (!data) return;
      conditionList.value = data.bizSceneXConditionList;

      bizSceneLimitRSettingList.value = bizSceneLimitRSettingList.value.map(
        (item) => {
          const values = data.bizSceneXConditionList.filter((sun) => {
            return (
              item.bizSceneLimitConditionCode === sun.bizSceneLimitConditionCode
            );
          });
          return {
            ...item,
            conditionSymbolList: values[0]?.conditionSymbolList,
            bizSceneLimitCondition: {
              bizSceneLimitConditionCode: item.bizSceneLimitConditionCode,
              bizSceneLimitConditionDesc: item.bizSceneLimitConditionDesc,
              bizSceneLimitConditionId: item.bizSceneLimitConditionId,
            },
          };
        },
      );
    }
  };

  const businessRuleQuery = async () => {
    loading.value = true;
    proFormRef.value?.ref.validate();
    if (!searchParams.value?.bizSceneCode) {
      loading.value = false;
      return;
    }
    if (dialogType.value === 'add') {
      await queryBusinessRestrictions();
      loading.value = false;
      return;
    }
    const [, res] = await queryBizSceneLimitRuleByExample({
      bizSceneCode: searchParams.value?.bizSceneCode,
    });
    if (res?.data) {
      //   ruleList.value = res?.data || [];
      const [ruleList] = (res?.data || []).filter((item) => {
        return route.params.bizSceneLimitRuleId === item.bizSceneLimitRuleId;
      });
      searchParams.value = ruleList;
      bizSceneLimitRSettingList.value = (
        ruleList?.bizSceneLimitRSettingList || []
      )?.map((item) => {
        return {
          ...item,
          editable: false,
        };
      });
      await queryBusinessRestrictions();
      //此处代码是为了对触发条件中的设置值进行回显（和后端已确认前端自己遍历值域保存的设置值）
      bizSceneLimitRSettingList.value.forEach((item) => {
        if (item.valueTypeCode === VALUE_TYPE_ENUM.CODE_SYSTEM)
          //如果是值域类型
          getDataValueTypeCode(
            item.bizSceneLimitConditionId,
            item.bizSceneLimitRDetailList,
            item,
          );
      });
    }
    loading.value = false;
  };
  const initFun = () => {
    businessRuleQuery();
  };
  initFun();
  const searchConfig = useRestrictionRulesFormConfig(
    disabled,
    queryBusinessRestrictions,
  );
  const addProject = () => {
    nextTick(() => {
      const row = tableRef.value?.proTableRef?.$el?.querySelector(
        `.el-table__body-wrapper tbody tr:nth-child(${bizSceneLimitRSettingList.value.length - 1})`,
      );
      row?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });
  };
  const { tableColumnsConfig, addItem, getDataValueTypeCode } =
    useTableColumnsTableConfig(
      tableRef,
      bizSceneLimitRSettingList,
      conditionList,
      disabled,
      menuId,
      currentOrg?.orgId,
    );

  //新增数据
  const addNewData = async () => {
    proFormRef?.value?.ref.validate();
    if (!searchParams.value?.bizSceneCode) {
      ElMessage.error(
        t('global:placeholder.select.template', {
          name: t('bizLimitCondition.businessScenario', '业务场景'),
        }),
      );
    } else {
      await queryBusinessRestrictions();
      addItem({ editable: true });
    }
    addProject();
  };
  //保存数据
  const submit = () => {
    const results = bizSceneLimitRSettingList.value.some(
      (item) => item.editable,
    );
    if (results) {
      ElMessage.error(
        t(
          'bizLimitCondition.PleaseAddTriggeringConditions',
          '您有【未确认】的触发条件，请确认所有触发条件',
        ),
      );
      return;
    }
    loading.value = true;
    proFormRef?.value?.ref.validate(async (valid: boolean) => {
      if (!bizSceneLimitRSettingList.value.length) {
        ElMessage.error(
          t(
            'bizLimitCondition.PleaseAddTriggeringConditions',
            '请添加触发条件',
          ),
        );
        loading.value = false;
        return;
      }
      console.log(
        'bizSceneLimitRSettingList.value',
        bizSceneLimitRSettingList.value,
      );
      // return;
      if (valid) {
        if (dialogType.value === 'add') {
          const [, res] = await addBizSceneLimitRule({
            ...searchParams.value,
            bizSceneLimitRSettingList: bizSceneLimitRSettingList.value,
          });
          if (res?.success) {
            ElMessage.success(
              t('bizLimitCondition.addedSuccessfully', '添加成功'),
            );
            goBack();
          }
        } else if (dialogType.value === 'edit') {
          const [, res] = await updateBizSceneLimitRuleById({
            ...searchParams.value,
            bizSceneLimitRSettingList: bizSceneLimitRSettingList.value,
          });
          if (res?.success) {
            ElMessage.success(
              t('bizLimitCondition.modifiedSuccessfully', '修改成功'),
            );
            goBack();
          }
        }

        loading.value = false;
      } else {
        loading.value = false;
        ElMessage.error(
          t(
            'bizLimitCondition.pleaseFillInTheCorrectInformation',
            '请填写正确内容',
          ),
        );
      }
    });
  };
  defineExpose({ dialogRef });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <el-page-header @back="goBack" class="mb-1">
      <template #content>
        <span class="text-base"
          >{{
            $t(
              'medicineAdjust.priceAdjustmentSheet',
              `${dialogType === 'add' ? '新增' : '编辑'}-业务场景限定规则`,
            )
          }}
        </span>
      </template>
    </el-page-header>
    <Title
      :title="$t('bizLimitCondition.restrictionRules', '限制规则')"
      class="mb-3"
    />
    <ProForm
      ref="proFormRef"
      v-model="searchParams"
      :data="searchConfig"
      :column="4"
    ></ProForm>
    <Title
      :title="$t('bizLimitCondition.restrictionRules', '触发条件')"
      class="mb-3"
    />
    <div class="mb-3 flex h-full min-h-0 flex-1 flex-col overflow-hidden">
      <div class="overflow-y-auto">
        <ProTable
          ref="tableRef"
          :data="bizSceneLimitRSettingList"
          :columns="tableColumnsConfig"
          :editable="true"
          :loading="loading"
        >
        </ProTable>
      </div>
      <el-button
        :disabled="disabled"
        class="w-full rounded-none border-t-0"
        icon="Plus"
        @click="addNewData"
      >
        {{ $t('global:add') }}
      </el-button>
    </div>
    <div v-if="!disabled" class="text-right">
      <el-button plain @click="goBack">{{ $t('global:cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{
        $t('global:confirm')
      }}</el-button>
    </div>
  </div>
</template>
