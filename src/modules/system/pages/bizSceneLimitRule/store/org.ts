import { defineStore } from 'pinia';
import { queryDictDataListByConditionId } from '@/modules/system/api/bizScene.ts';

export const useBizSceneCode = defineStore('bizSceneCode', {
  // 推荐使用 完整类型推断的箭头函数
  state: () => {
    return {
      // 所有这些属性都将自动推断其类型
      bizSceneCode: '',
    };
  },
  actions: {
    setBizSceneCode(val: string) {
      this.bizSceneCode = val;
    },
    delBizSceneCode() {
      this.bizSceneCode = '';
    },
  },
});

export const useLimitedConditionSet = defineStore('limitedConditionSet', {
  state: (): {
    conditionalSet: {
      [key: string]: {
        primaryKey: string;
        displayKey: string;
        valuesList: { [key: string]: string }[];
      };
    };
  } => {
    return {
      conditionalSet: {},
    };
  },
  actions: {
    async setRetrieveDataList(
      bizSceneLimitConditionId: string,
      menuId?: string,
      orgId?: string,
    ) {
      if (this.conditionalSet[bizSceneLimitConditionId]) {
        return this.conditionalSet[bizSceneLimitConditionId];
      }
      const [, res] = await queryDictDataListByConditionId({
        hospitalId: orgId || '',
        menuId: menuId as string,
        bizSceneLimitConditionId: bizSceneLimitConditionId,
      });

      if (res?.success) {
        this.conditionalSet[bizSceneLimitConditionId] = {
          primaryKey: res?.data.primaryKey,
          displayKey: res?.data.displayKey,
          valuesList: res?.data.data.data || [],
        };
      }
      return this.conditionalSet[bizSceneLimitConditionId];
    },
  },
});
