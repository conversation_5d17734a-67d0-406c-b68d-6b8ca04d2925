<script setup lang="ts">
  import { ref } from 'vue';

  import { Title, DmlButton, useAppConfigData, MAIN_APP_CONFIG } from 'sun-biz';
  import { DICT_BIZ_SCENE_LIMIT_CONDITION_NAME } from '@/utils/constant.ts';
  import BusinessScenarioLimitations from '../components/BusinessScenarioLimitations.vue';
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);

  const businessRef = ref();
  const limitersRef = ref();
  const bizData = ref();
  const searchCondition = ref('');
  //切换
  const initFun = () => {};
  initFun();
  //添加限制
  const addRestrictions = () => {
    businessRef.value.addValue();
  };
  //选项发生变化
  const selectionChange = (
    val: DizLimitCondition.RestrictiveConditionsList[],
  ) => {
    bizData.value = val.map((item) => item.bizSceneLimitConditionId);
  };
  //查询数据
  const onQueryData = (value: string) => {
    limitersRef.value?.queryBusinessRestrictions(value);
    businessRef.value?.queryBusinessRestrictions(value);
  };
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title
      :title="$t('bizLimitCondition.list.title', '业务场景限定条件')"
      class="mb-3"
    />
    <div class="my-3 flex justify-between">
      <div>
        <el-input
          :placeholder="
            $t('bizLimitCondition.pleaseEnterTheContent', '请输入内容')
          "
          v-model="searchCondition"
          @change="onQueryData"
          :clearable="true"
          @clear="onQueryData"
          class="w-80"
        >
          <template #suffix>
            <el-icon @click.stop class="text-white"><search /></el-icon>
          </template>
        </el-input>
        <el-button
          type="primary"
          plain
          class="ml-3"
          @click="onQueryData(searchCondition)"
          >{{ $t('global:query') }}</el-button
        >
      </div>
      <div>
        <el-button
          type="primary"
          @click="addRestrictions"
          class="!mr-3"
          :disabled="!isCloudEnv"
          >{{ $t('global:add') }}</el-button
        >
        <DmlButton
          :biz-data="bizData"
          :code="DICT_BIZ_SCENE_LIMIT_CONDITION_NAME"
        ></DmlButton>
      </div>
    </div>

    <BusinessScenarioLimitations
      @selection="selectionChange"
      ref="businessRef"
      :is-cloud-env="isCloudEnv"
    ></BusinessScenarioLimitations>
  </div>
</template>
