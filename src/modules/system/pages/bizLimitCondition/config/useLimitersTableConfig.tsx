import { useColumnConfig, useEditableTable } from 'sun-biz';
import {
  BIZ_SCENE_LIMIT_CONDITION_CODE,
  VALUE_TYPE_CODE,
  DATA_SEARCH_BIZ_ID_TYPE_CODE_NAME,
  SYMBOL_TYPE_CODE,
  VALUE_TYPE_ENUM,
  DATA_SEARCH_BIZ_ID_TYPE_CODE,
  BIZ_SCENE_CODE_NAME,
} from '@/utils/constant.ts';
import { ref, Ref } from 'vue';
import { ElMessage } from 'element-sun';
import { cloneDeep } from 'es-toolkit';
const listOfAvailableSymbols = ref<
  {
    symbolTypeCode: string;
    symbolTypeDesc: string;
  }[]
>([]);
//过滤时间并拼接
const splicingData = (
  getData: { bizSceneCode: string }[],
  dataContent:
    | { dataValueNo: string; dataValueNameDisplay: string }[]
    | undefined, // 允许传入 undefined
) => {
  return getData
    .map((item) => {
      if (!dataContent || dataContent.length === 0) return undefined; // 处理空数组或 undefined

      const foundItem = dataContent.find(
        (items) => item.bizSceneCode === items.dataValueNo,
      );
      return foundItem ? foundItem.dataValueNameDisplay : undefined; // 显式检查 find 结果
    })
    .join(',');
};

export function useContentOfLimitingConditionsTableConfig(
  isCloudEnv: boolean,
  tableRef: Ref,
  codeSystemNoList: Ref<{ codeSystemNo: string; codeSystemName: string }[]>,
  tableData: Ref<DizLimitCondition.RestrictiveConditionsList[]>,
  tableDataCope: Ref<DizLimitCondition.RestrictiveConditionsList[]>,
  preservationSystem: (index: number) => void,
  deleteEncodingSystem: (id: string, type: boolean, name?: string) => void,
  type: boolean,
  limitedList: Ref<DizLimitCondition.RestrictiveConditionsList[]>,
  limitedListFun: (key: string) => void,
  addNewRestrictions: (id: string) => void,
  queryCodingSystem: (key: string) => void,
) {
  const { toggleEdit, validateItem } = useEditableTable({
    tableRef,
    data: tableData,
    id: 'bizSceneLimitConditionId',
  });
  return useColumnConfig({
    dataSetCodes: [
      BIZ_SCENE_LIMIT_CONDITION_CODE,
      VALUE_TYPE_CODE,
      DATA_SEARCH_BIZ_ID_TYPE_CODE_NAME,
      SYMBOL_TYPE_CODE,
      BIZ_SCENE_CODE_NAME,
    ],
    getData: (t, dataSet) => {
      //处理默认数据
      if (dataSet?.value && dataSet.value[SYMBOL_TYPE_CODE]) {
        listOfAvailableSymbols.value = dataSet.value[SYMBOL_TYPE_CODE].map(
          (item) => {
            return {
              symbolTypeCode: item.dataValueNo,
              symbolTypeDesc: item.dataValueNameDisplay,
            };
          },
        );
      }
      return [
        {
          prop: 'indexNo',
          editable: false,
          type: 'selection',
        },
        {
          label: t('global.sequenceNumber', '序号'),
          minWidth: 60,
          prop: 'indexNo',
          render: (row: object, $index: number) => <>{$index + 1}</>,
        },
        {
          label: t('bizLimitCondition.bizSceneLimitConditionDesc', '条件名称'),
          prop: 'bizSceneLimitConditionDesc',
          minWidth: 130,
          editable: true,
          required: true,
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('bizLimitCondition.bizSceneLimitConditionDesc', '条件'),
              }),
              trigger: ['change', 'blur'],
            },
          ],
          render: (
            row: DizLimitCondition.RestrictiveConditionsList,
            index: number,
          ) => {
            return type && row.edit ? (
              <>
                <el-form-item
                  class="!mb-[0]"
                  prop={`tableData.${index}.bizScene`}
                  rules={[
                    {
                      required: true,
                      validator: async (
                        rule: string,
                        value: string,
                        callback: (arg0?: string | undefined) => void,
                      ) => {
                        if (row.bizSceneLimitConditionDesc) {
                          callback();
                        } else {
                          callback(
                            t('global:placeholder.select.template', {
                              name: t(
                                'bizLimitCondition.bizSceneLimitConditionDesc',
                                '条件',
                              ),
                            }),
                          );
                        }
                      },
                      trigger: ['change', 'blur'],
                    },
                  ]}
                >
                  <el-select
                    v-model={row.bizSceneLimitConditionDesc}
                    filterable
                    remote
                    remote-method={limitedListFun}
                    placeholder={t('global:placeholder.select.template', {
                      name: t(
                        'bizLimitCondition.bizSceneLimitConditionDesc',
                        '条件',
                      ),
                    })}
                    value-key="bizSceneLimitConditionId"
                    onChange={(
                      val: DizLimitCondition.RestrictiveConditionsList,
                    ) => {
                      tableData.value[index] = { ...val, edit: true };
                    }}
                  >
                    {limitedList.value.map(
                      (item: DizLimitCondition.RestrictiveConditionsList) => {
                        return (
                          <el-option
                            key={item.bizSceneLimitConditionCode}
                            label={item.bizSceneLimitConditionDesc}
                            value={item}
                            disabled={tableData.value.some(
                              (sunb) =>
                                sunb.bizSceneLimitConditionId ===
                                item.bizSceneLimitConditionId,
                            )}
                          ></el-option>
                        );
                      },
                    )}
                  </el-select>
                </el-form-item>
              </>
            ) : (
              <>
                {row.editable ? (
                  <el-select
                    v-model={row.bizSceneLimitConditionCode}
                    placeholder={t('global:placeholder.select.template', {
                      name: t(
                        'bizLimitCondition.bizSceneLimitConditionDesc',
                        '条件',
                      ),
                    })}
                  >
                    {(dataSet?.value
                      ? dataSet.value[BIZ_SCENE_LIMIT_CONDITION_CODE]
                      : []
                    ).map(
                      (item: {
                        dataValueNo: string;
                        dataValueNameDisplay: string;
                      }) => {
                        return (
                          <el-option
                            key={item.dataValueNo}
                            label={item.dataValueNameDisplay}
                            value={item.dataValueNo}
                            onClick={() => {
                              const res = tableData.value.some(
                                (sunb) =>
                                  sunb.bizSceneLimitConditionCode ===
                                  item.dataValueNo,
                              );
                              if (res) {
                                row.bizSceneLimitConditionDesc =
                                  item.dataValueNameDisplay;
                              }
                            }}
                            disabled={tableData.value.some(
                              (sunb) =>
                                sunb.bizSceneLimitConditionCode ===
                                item.dataValueNo,
                            )}
                          ></el-option>
                        );
                      },
                    )}
                  </el-select>
                ) : (
                  <>{row.bizSceneLimitConditionDesc}</>
                )}
              </>
            );
          },
        },
        {
          label: t('bizLimitCondition.valueTypeDesc', '值类型'),
          prop: 'valueTypeDesc',
          minWidth: 130,
          editable: true,
          required: true,
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t(
                  'bizLimitCondition.bizSceneLimitConditionDesc',
                  '值类型',
                ),
              }),
              trigger: ['change', 'blur'],
            },
          ],
          render: (row: DizLimitCondition.RestrictiveConditionsList) => {
            return row.editable ? (
              <el-select
                v-model={row.valueTypeCode}
                placeholder={t('global:placeholder.select.template', {
                  name: t(
                    'bizLimitCondition.bizSceneLimitConditionDesc',
                    '值类型',
                  ),
                })}
                onChange={() => {
                  if (row.valueTypeCode !== VALUE_TYPE_ENUM.CODE_SYSTEM) {
                    row.dataSearchBizIdTypeDesc = '';
                    row.dataSearchBizIdTypeCode = '';
                    row.codeSystemNo = '';
                  }
                }}
              >
                {(dataSet?.value ? dataSet.value[VALUE_TYPE_CODE] : []).map(
                  (item: {
                    dataValueNo: string;
                    dataValueNameDisplay: string;
                  }) => {
                    return (
                      <el-option
                        key={item.dataValueNo}
                        label={item.dataValueNameDisplay}
                        value={item.dataValueNo}
                        onClick={() => {
                          row.valueTypeDesc = item.dataValueNameDisplay;
                        }}
                      ></el-option>
                    );
                  },
                )}
              </el-select>
            ) : (
              <>{row.valueTypeDesc}</>
            );
          },
        },
        {
          label: t(
            'bizLimitCondition.dataSearchBizIdTypeDesc',
            '数据检索业务标识类型',
          ),
          prop: 'dataSearchBizIdTypeDesc',
          minWidth: 150,
          render: (
            row: DizLimitCondition.RestrictiveConditionsList,
            index: number,
          ) => {
            return row.editable ? (
              <el-form-item
                class="!mb-[0]"
                prop={`tableData.${index}.dataSearchBizIdTypeDesc`}
                rules={[
                  {
                    required: row.valueTypeCode === VALUE_TYPE_ENUM.CODE_SYSTEM,
                    validator: async (
                      rule: string,
                      value: string,
                      callback: (arg0?: string | undefined) => void,
                    ) => {
                      if (row.valueTypeCode === VALUE_TYPE_ENUM.CODE_SYSTEM) {
                        if (row.dataSearchBizIdTypeDesc) {
                          callback();
                        } else {
                          callback(
                            t('global:placeholder.select.template', {
                              name: t(
                                'bizLimitCondition.bizSceneLimitConditionDesc',
                                '数据检索业务标识类型',
                              ),
                            }),
                          );
                        }
                      } else {
                        callback();
                      }
                    },
                    trigger: ['change', 'blur'],
                  },
                ]}
              >
                <el-select
                  filterable
                  clearable
                  v-model={row.dataSearchBizIdTypeCode}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'bizLimitCondition.bizSceneLimitConditionDesc',
                      '数据检索业务标识类型',
                    ),
                  })}
                  disabled={row.valueTypeCode !== VALUE_TYPE_ENUM.CODE_SYSTEM}
                  onChange={() => {
                    if (
                      row.dataSearchBizIdTypeCode !==
                      DATA_SEARCH_BIZ_ID_TYPE_CODE.DICT_DATA_SETS
                    ) {
                      row.codeSystemNo = '';
                    }
                  }}
                >
                  {(dataSet?.value
                    ? dataSet.value[DATA_SEARCH_BIZ_ID_TYPE_CODE_NAME]
                    : []
                  ).map(
                    (item: {
                      dataValueNo: string;
                      dataValueNameDisplay: string;
                    }) => {
                      return (
                        <el-option
                          key={item.dataValueNo}
                          label={item.dataValueNameDisplay}
                          value={item.dataValueNo}
                          onClick={() => {
                            row.dataSearchBizIdTypeDesc =
                              item.dataValueNameDisplay;
                          }}
                        ></el-option>
                      );
                    },
                  )}
                </el-select>
              </el-form-item>
            ) : (
              <>{row.dataSearchBizIdTypeDesc || '--'}</>
            );
          },
        },
        {
          label: t('bizLimitCondition.codeSystemNo', '编码体系NO'),
          prop: 'codeSystemNo',
          minWidth: 130,
          editable: false,
          render: (
            row: DizLimitCondition.RestrictiveConditionsList,
            index: number,
          ) => {
            return row.editable ? (
              <el-form-item
                class="!mb-[0]"
                prop={`tableData.${index}.codeSystemNo`}
                rules={[
                  {
                    required:
                      row.dataSearchBizIdTypeCode ===
                      VALUE_TYPE_ENUM.CODE_SYSTEM,
                    validator: async (
                      rule: string,
                      value: string,
                      callback: (arg0?: string | undefined) => void,
                    ) => {
                      if (
                        row.dataSearchBizIdTypeCode ===
                        DATA_SEARCH_BIZ_ID_TYPE_CODE.DICT_DATA_SETS
                      ) {
                        if (row.codeSystemNo) {
                          callback();
                        } else {
                          callback(
                            t('global:placeholder.select.template', {
                              name: t(
                                'bizLimitCondition.codeSystemNo',
                                '编码体系NO',
                              ),
                            }),
                          );
                        }
                      } else {
                        callback();
                      }
                    },
                    trigger: ['change', 'blur'],
                  },
                ]}
              >
                <el-select
                  clearable
                  v-model={row.codeSystemNo}
                  filterable
                  remote
                  remoteShowSuffix
                  remote-method={(val: string) => {
                    queryCodingSystem(val);
                  }}
                  placeholder={t('global:placeholder.select.template', {
                    name: t('bizLimitCondition.codeSystemNo', '编码体系NO'),
                  })}
                  disabled={
                    row.dataSearchBizIdTypeCode !==
                    DATA_SEARCH_BIZ_ID_TYPE_CODE.DICT_DATA_SETS
                  }
                >
                  {(codeSystemNoList.value.length
                    ? codeSystemNoList.value
                    : [
                        {
                          codeSystemNo: row.codeSystemNo || '',
                          codeSystemName: row.codeSystemName || '',
                        },
                      ]
                  ).map(
                    (item: {
                      codeSystemNo: string;
                      codeSystemName: string;
                    }) => {
                      return (
                        <el-option
                          key={item.codeSystemNo}
                          label={item.codeSystemName}
                          value={item.codeSystemNo}
                        ></el-option>
                      );
                    },
                  )}
                </el-select>
              </el-form-item>
            ) : (
              <>{row.codeSystemName || '--'}</>
            );
          },
        },
        {
          label: t('bizLimitCondition.bizSceneXConditionList', '可用业务场景'),
          prop: 'bizSceneXConditionList',
          minWidth: 130,
          editable: false,
          render: (row: DizLimitCondition.RestrictiveConditionsList) => {
            return row.editable ? (
              <el-form-item class="!mb-[0]">
                <el-select
                  multiple
                  collapse-tags
                  v-model={row.bizSceneXConditionSelectList}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'bizLimitCondition.bizSceneXConditionList',
                      '可用业务场景',
                    ),
                  })}
                >
                  {(dataSet?.value
                    ? dataSet.value[BIZ_SCENE_CODE_NAME]
                    : []
                  ).map(
                    (item: {
                      dataValueNo: string;
                      dataValueNameDisplay: string;
                    }) => {
                      return (
                        <el-option
                          key={item.dataValueNo}
                          label={item.dataValueNameDisplay}
                          value={item.dataValueNo}
                          onClick={() => {
                            row.dataSearchBizIdTypeDesc =
                              item.dataValueNameDisplay;
                          }}
                        ></el-option>
                      );
                    },
                  )}
                </el-select>
              </el-form-item>
            ) : (
              <>
                {row?.bizSceneXConditionList &&
                  splicingData(
                    row.bizSceneXConditionList,
                    dataSet?.value ? dataSet.value[BIZ_SCENE_CODE_NAME] : [],
                  )}
              </>
            );
          },
        },
        {
          label: t('bizLimitCondition.conditionSymbolList', '可用符号'),
          prop: 'conditionSymbolList',
          minWidth: 130,
          editable: true,
          required: true,
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t(
                  'bizLimitCondition.bizSceneLimitConditionDesc',
                  '可用符号',
                ),
              }),
              trigger: ['change', 'blur'],
            },
          ],
          render: (row: DizLimitCondition.RestrictiveConditionsList) => {
            return row.editable ? (
              <el-select
                multiple
                collapse-tags
                v-model={row.conditionSymbolList}
                placeholder={t('global:placeholder.select.template', {
                  name: t(
                    'bizLimitCondition.bizSceneLimitConditionDesc',
                    '可用符号',
                  ),
                })}
                value-key="symbolTypeCode"
              >
                {listOfAvailableSymbols.value.map(
                  (item: {
                    symbolTypeCode: string;
                    symbolTypeDesc: string;
                  }) => {
                    return (
                      <el-option
                        key={item.symbolTypeCode}
                        label={item.symbolTypeDesc}
                        value={item}
                      ></el-option>
                    );
                  },
                )}
              </el-select>
            ) : (
              <>
                {(row?.conditionSymbolList || [])
                  .map((item: DizLimitCondition.ConditionSymbolList) => {
                    return item.symbolTypeDesc;
                  })
                  .join(',')}
              </>
            );
          },
        },
        {
          label: t('bizLimitCondition.departmentManageTable.orgDesc', '操作'),
          prop: 'orgDesc',
          minWidth: 140,
          render: (
            row: DizLimitCondition.RestrictiveConditionsList,
            index: number,
          ) => {
            return row.editable || row.edit ? (
              <div key="editable">
                <el-button
                  link
                  type="danger"
                  disabled={!isCloudEnv}
                  onClick={() => {
                    if (!row.bizSceneLimitConditionId) {
                      tableData.value.splice(index, 1);
                    } else {
                      tableData.value[index] = cloneDeep(
                        tableDataCope.value[index],
                      );
                    }
                  }}
                >
                  {t('global:cancel')}
                </el-button>
                <el-button
                  link
                  type="primary"
                  disabled={!isCloudEnv}
                  onClick={() => {
                    if (type) {
                      tableRef?.value?.formRef
                        ?.validateField(`tableData.${index}.bizScene`)
                        .then(() => {
                          addNewRestrictions(
                            row.bizSceneLimitConditionId || '',
                          );
                        })
                        .catch(() => {
                          ElMessage.error(
                            t('global:placeholder.select.template', {
                              name: t(
                                'bizLimitCondition.availableConditions',
                                '可用条件',
                              ),
                            }),
                          );
                        });
                      return;
                    }
                    Promise.all(
                      [
                        `tableData.${index}.dataSearchBizIdTypeDesc`,
                        `tableData.${index}.codeSystemNo`,
                      ].map((item) =>
                        tableRef?.value?.formRef?.validateField(item),
                      ),
                    )
                      .then(async () => {
                        const verificationResults = await validateItem(row);
                        if (verificationResults) {
                          preservationSystem(index);
                        } else {
                          if (!row?.bizSceneLimitConditionDesc) {
                            ElMessage.error(
                              t('global:placeholder.select.template', {
                                name: t(
                                  'bizLimitCondition.bizSceneLimitConditionDesc',
                                  '条件',
                                ),
                              }),
                            );
                          } else if (!row?.valueTypeCode) {
                            ElMessage.error(
                              t('global:placeholder.select.template', {
                                name: t(
                                  'bizLimitCondition.bizSceneLimitConditionDesc',
                                  '值类型',
                                ),
                              }),
                            );
                          } else if (
                            !row?.conditionSymbolList ||
                            !row?.conditionSymbolList.length
                          ) {
                            ElMessage.error(
                              t('global:placeholder.select.template', {
                                name: t(
                                  'bizLimitCondition.bizSceneLimitConditionDesc',
                                  '可用符号',
                                ),
                              }),
                            );
                          }
                        }
                      })
                      .catch((err) => {
                        Object.values(err).map((item) => {
                          ((item as []) || []).forEach((cur) => {
                            if ((cur as { message: string })?.message) {
                              ElMessage.error(
                                (cur as { message: string })?.message,
                              );
                            }
                          });
                        });
                      });
                  }}
                >
                  {t('global:save')}
                </el-button>
              </div>
            ) : (
              <>
                {!type && (
                  <el-button
                    link
                    type="primary"
                    disabled={!isCloudEnv}
                    onClick={() => toggleEdit(row)}
                  >
                    {t('global:edit')}
                  </el-button>
                )}

                <el-button
                  link
                  type="danger"
                  disabled={!isCloudEnv}
                  onClick={() => {
                    if (row.bizSceneLimitConditionId) {
                      deleteEncodingSystem(
                        type
                          ? row.bizSceneXConditionId || ''
                          : row.bizSceneLimitConditionId,
                        type,
                        row?.bizSceneLimitConditionDesc,
                      );
                    } else {
                      tableData.value.splice(index, 1);
                    }
                  }}
                >
                  {t('global:delete')}
                </el-button>
              </>
            );
          },
        },
      ];
    },
  });
}
