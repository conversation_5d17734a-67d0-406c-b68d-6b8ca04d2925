import { useColumnConfig } from 'sun-biz';
export function useBusinessListTableConfig() {
  return useColumnConfig({
    getData: (t) => {
      return [
        {
          label: t('global.sequenceNumber', '序号'),
          minWidth: 100,
          prop: 'indexNo',
          render: (row: object, $index: number) => <>{$index + 1}</>,
        },
        {
          label: t('bizLimitCondition.bizSceneLimitConditionDesc', '条件名称'),
          prop: 'bizSceneLimitConditionDesc',
          minWidth: 280,
        },
        {
          label: t('bizLimitCondition.valueTypeDesc', '值类型'),
          prop: 'valueTypeDesc',
          minWidth: 280,
        },
      ];
    },
  });
}
