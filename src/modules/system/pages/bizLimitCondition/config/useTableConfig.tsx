import { useColumnConfig } from 'sun-biz';
import BusinessScenarioLimitations from '../components/BusinessScenarioLimitations.vue';
import { ref, Ref } from 'vue';
interface PopoverInstance {
  bizSceneLimitRDetailList: { value: string; endValue: string }[];

  wipeData: () => void;
  retrieveDataList: () => void;
  nullData: () => void;
}
const popoverRefs = ref<Record<number, PopoverInstance | undefined>>({});
const setPopoverRef = (el: PopoverInstance | null, id: number) => {
  if (el) {
    popoverRefs.value[id] = el;
  }
};
export function useBusinessAvailabilityRestrictions(
  selectionChange: (val: DizLimitCondition.RestrictiveConditionsList[]) => void,
  tableRef: Ref,
  queryBusinessRestrictions: () => void,
) {
  return useColumnConfig({
    getData: (t) => {
      return [
        {
          type: 'expand',
          render: (row: DizLimitCondition.BusinessRulesList, index: number) => {
            return (
              <div class={'px-7 py-5'}>
                <BusinessScenarioLimitations
                  ref={(el: unknown) =>
                    setPopoverRef(el as PopoverInstance | null, index)
                  }
                  onRefresh={() => {
                    queryBusinessRestrictions();
                  }}
                  onSelection={selectionChange}
                  bizSceneCode={row.bizSceneCode}
                ></BusinessScenarioLimitations>
              </div>
            );
          },
        },
        {
          prop: 'indexNo',
          editable: false,
          type: 'selection',
        },
        {
          label: t('global.sequenceNumber', '序号'),
          minWidth: 100,
          prop: 'indexNo',
          render: (row: object, $index: number) => <>{$index + 1}</>,
        },
        {
          label: t('bizLimitCondition.bizSceneDesc', '业务场景名'),
          prop: 'bizSceneDesc',
          minWidth: 280,
        },
        {
          label: t('bizLimitCondition.bizSceneXConditionList', '可用条件'),
          prop: 'bizSceneXConditionList',
          minWidth: 400,
          render: (row: DizLimitCondition.BusinessRulesList) => {
            return (
              <>
                {row?.bizSceneXConditionList
                  .map((item) => {
                    return item.bizSceneLimitConditionDesc;
                  })
                  .join(',')}
              </>
            );
          },
        },
        {
          label: t('bizLimitCondition.departmentManageTable.orgDesc', '操作'),
          prop: 'orgDesc',
          minWidth: 140,
          render: (row: DizLimitCondition.BusinessRulesList) => {
            return (
              <>
                <el-button
                  link
                  type="primary"
                  onClick={() => {
                    tableRef.value?.proTableRef.toggleRowExpansion(row);
                  }}
                >
                  {t('bizLimitCondition.availableConditions', '可用条件')}
                </el-button>
              </>
            );
          },
        },
      ];
    },
  });
}
