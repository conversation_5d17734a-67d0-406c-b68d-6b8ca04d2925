<script setup lang="ts">
  import { nextTick, onBeforeUnmount, ref } from 'vue';
  import { useContentOfLimitingConditionsTableConfig } from '../config/useLimitersTableConfig.tsx';
  import {
    queryBizSceneLimitConditionByExample,
    saveBizSceneLimitCondition,
    deleteBizSceneLimitConditionById,
    addBizSceneXLimitCondition,
    queryBizSceneXLimitConditionByExample,
    deleteBizSceneXLimitConditionById,
  } from '@/modules/system/api/bizScene';
  import { ProTable } from 'sun-biz';
  import { queryCodeSystemListByExample } from '@/modules/system/api/codeSystem.ts';
  import { ONE_PAGE_SIZE } from '@sun-toolkit/enums';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { cloneDeep } from 'es-toolkit';
  interface Props {
    isCloudEnv?: boolean;
    bizSceneCode?: string;
  }
  const emit = defineEmits<{
    selection: [val: DizLimitCondition.RestrictiveConditionsList[]];
    refresh: [];
  }>();
  const { t } = useTranslation();
  const loading = ref(true);
  const tableRef = ref();
  const codeSystemNoList = ref<
    { codeSystemNo: string; codeSystemName: string }[]
  >([]);
  const limitedList = ref<DizLimitCondition.RestrictiveConditionsList[]>([]);
  const { isCloudEnv = true, bizSceneCode = '' } = defineProps<Props>();
  const tableData = ref<DizLimitCondition.RestrictiveConditionsList[]>([]);
  const tableDataCope = ref<DizLimitCondition.RestrictiveConditionsList[]>([]);

  //新增数据
  const addValue = (type?: boolean) => {
    let data: DizLimitCondition.RestrictiveConditionsList = {
      bizSceneLimitConditionId: undefined,
      bizSceneLimitConditionDesc: '', //条件名称
      valueTypeDesc: '', //值类型
      dataSearchBizIdTypeDesc: '', //数据检索业务标识类型
      codeSystemNo: '', //编码体系NO
      conditionSymbolList: [], //可用符号
      bizSceneLimitConditionCode: '',
      valueTypeCode: '',
      editable: true,
    };
    if (type) {
      data.edit = true;
      data.editable = false;
    }
    tableData.value.push(data);
    nextTick(() => {
      const row = tableRef.value?.proTableRef?.$el?.querySelector(
        `.el-table__body-wrapper tbody tr:nth-child(${tableData.value.length - 1})`,
      );
      row?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });
  };

  const queryBusinessRestrictions = async (keyWord: string = '') => {
    loading.value = true;
    // queryBizSceneXLimitConditionByExample
    if (bizSceneCode) {
      const [, res] = await queryBizSceneXLimitConditionByExample({
        bizSceneCodes: [bizSceneCode],
        keyWord: keyWord,
      });
      if (res?.success) {
        const [data] = res?.data || [];
        tableData.value = (data.bizSceneXConditionList || [])?.map((item) => {
          return {
            ...item,
            editable: false,
          };
        });
      }
    } else {
      const [, res] = await queryBizSceneLimitConditionByExample({ keyWord });
      if (res?.success) {
        tableData.value = (res?.data || []).map((item) => {
          return {
            ...item,
            editable: false,
            bizSceneXConditionSelectList: item?.bizSceneXConditionList?.map(
              (item) => item.bizSceneCode,
            ),
          };
        });
        tableDataCope.value = cloneDeep(tableData.value);
      }
    }
    loading.value = false;
  };

  //单独获取限定条件
  const limitedListFun = async (key: string = '') => {
    const [, res] = await queryBizSceneLimitConditionByExample({
      keyWord: key,
    });
    if (res?.success) {
      limitedList.value = res?.data || [];
    }
  };
  //根据条件查询编码体系列表
  const queryCodingSystem = async (keyWord?: string) => {
    const [, res] = await queryCodeSystemListByExample({
      pageNumber: 1,
      pageSize: ONE_PAGE_SIZE,
      keyWord,
    });
    if (res?.success) {
      codeSystemNoList.value = res?.data || [];
    }
  };
  const initFun = () => {
    queryBusinessRestrictions();
  };

  //保存限定条件
  const addNewRestrictions = async (id: string) => {
    const [, res] = await addBizSceneXLimitCondition({
      bizSceneCode,
      bizSceneLimitConditionId: id,
    });
    if (res?.success) {
      emit('refresh');
      queryBusinessRestrictions();
    }
  };
  //保存编码体系
  const preservationSystem = async (index: number) => {
    const [, res] = await saveBizSceneLimitCondition({
      bizSceneLimitConditionId:
        tableData.value[index]?.bizSceneLimitConditionId,
      bizSceneLimitConditionCode:
        tableData.value[index].bizSceneLimitConditionCode,
      valueTypeCode: tableData.value[index].valueTypeCode,
      dataSearchBizIdTypeCode: tableData.value[index]?.dataSearchBizIdTypeCode,
      codeSystemNo: tableData.value[index]?.codeSystemNo,
      bizSceneXConditionList: tableData.value[
        index
      ]?.bizSceneXConditionSelectList?.map((item) => {
        return {
          bizSceneCode: item,
        };
      }),
      conditionSymbolList: tableData.value[index]?.conditionSymbolList?.map(
        (item) => {
          return {
            symbolTypeCode: item?.symbolTypeCode,
            symbolTypeDesc: item?.symbolTypeDesc,
          };
        },
      ),
      editable: false,
    });
    if (res?.success) {
      ElMessage.success(t('global:save.success'));
      queryBusinessRestrictions();
    }
  };
  //删除编码体系
  const deleteEncodingSystem = (id: string, type = false, name?: string) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要删除 “{{name}}” 吗？', {
        name: name,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      if (type) {
        const [, res] = await deleteBizSceneXLimitConditionById({
          bizSceneXConditionId: id,
        });
        if (res?.success) {
          ElMessage.success(t('global:delete.success'));
          queryBusinessRestrictions();
        }
        emit('refresh');
        return;
      }
      const [, res] = await deleteBizSceneLimitConditionById({
        bizSceneLimitConditionId: id,
      });
      if (res?.success) {
        ElMessage.success(t('global:delete.success'));
        queryBusinessRestrictions();
      }
    });
  };
  initFun();
  const limitingConditions = useContentOfLimitingConditionsTableConfig(
    isCloudEnv,
    tableRef,
    codeSystemNoList,
    tableData,
    tableDataCope,
    preservationSystem,
    deleteEncodingSystem,
    !!bizSceneCode,
    limitedList,
    limitedListFun,
    addNewRestrictions,
    queryCodingSystem,
  );
  //选项变动
  const selectionChange = (
    val: DizLimitCondition.RestrictiveConditionsList[],
  ) => {
    emit('selection', val);
  };
  onBeforeUnmount(() => {
    tableRef.value.eleTable.clearSelection();
  });
  defineExpose({
    queryBusinessRestrictions,
    proTableRef: tableRef,
    addValue,
  });
</script>
<template>
  <ProTable
    ref="tableRef"
    row-key="bizSceneLimitConditionId"
    @selection-change="selectionChange"
    :data="tableData"
    :columns="limitingConditions"
    :loading="loading"
    :editable="true"
  ></ProTable>
  <div
    v-if="!!bizSceneCode"
    @click="addValue(true)"
    class="w-full cursor-pointer border-b-2 border-l-2 border-r-2 py-3 text-center hover:bg-blue-100"
  >
    新增
  </div>
</template>
