<script setup lang="ts">
  import { ref } from 'vue';
  import { ProTable } from 'sun-biz';
  import { useBusinessAvailabilityRestrictions } from '../config/useTableConfig.tsx';
  import { queryBizSceneXLimitConditionByExample } from '@/modules/system/api/bizScene.ts';
  const businessRulesList = ref<DizLimitCondition.BusinessRulesList[]>();

  const loading = ref(true);
  const tableRef = ref();
  const emit = defineEmits<{
    selection: [val: DizLimitCondition.RestrictiveConditionsList[]];
  }>();
  const queryBusinessRestrictions = async (keyWord: string = '') => {
    loading.value = true;
    const [, res] = await queryBizSceneXLimitConditionByExample({
      keyWord,
    });
    if (res?.success) {
      businessRulesList.value = (res?.data || []).map((item) => {
        return {
          ...item,
          editable: false,
        };
      });
    }
    loading.value = false;
  };

  const initFun = () => {
    queryBusinessRestrictions();
  };
  //选项变动
  const selectionChange = (
    val: DizLimitCondition.RestrictiveConditionsList[],
  ) => {
    emit('selection', val);
  };
  initFun();
  const tableColumnsConfig = useBusinessAvailabilityRestrictions(
    selectionChange,
    tableRef,
    queryBusinessRestrictions,
  );
  //查看选中数据
  const proTableChange = (val: DizLimitCondition.BusinessRulesList[]) => {
    val.forEach((element) => {
      tableRef.value?.proTableRef.toggleRowExpansion(element);
    });
  };
  defineExpose({ queryBusinessRestrictions });
</script>
<template>
  <ProTable
    :data="businessRulesList"
    :columns="tableColumnsConfig"
    :loading="loading"
    row-key="bizSceneCode"
    @selection-change="proTableChange"
    ref="tableRef"
  ></ProTable>
</template>
