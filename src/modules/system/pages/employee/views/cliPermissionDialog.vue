<script setup lang="ts">
  import { pickBy } from 'es-toolkit';
  import { isEmpty } from 'es-toolkit/compat';
  import { useTranslation } from 'i18next-vue';
  import { FLAG } from '@sun-toolkit/enums';
  import { ElMessage, FormInstance } from 'element-sun';
  import { ProDialog, ProForm } from 'sun-biz';
  import { onBeforeMount, ref, useTemplateRef } from 'vue';
  import { useCliPermissionForm } from '../config/useCliPermissionForm';
  import { saveUserCliPermission } from '@/modules/system/api/employee';

  const { t } = useTranslation();
  const model = defineModel<boolean>();
  const props = defineProps<{
    currentUser: Employee.Item;
    cliPermissionList: Employee.CliPermission[];
    userCliPermissionList: Employee.UserCliPermissionItem[];
  }>();
  const dialogRef = useTemplateRef<{ close: () => void }>('dialog');
  const formRef = useTemplateRef<{ ref: FormInstance }>('form');
  const formConfig = useCliPermissionForm(props.cliPermissionList);

  /**
   * form model 成员结构设计
   * 复选
   * { [codeSystemId]: ["{codeSystemId, dataValueId, cliPermissionValueId}", ...] }
   * 单选
   * { [codeSystemId]: "{codeSystemId, dataValueId, cliPermissionValueId}" }
   */
  const formModel = ref<Employee.UserCliPermissionFormModel>({});

  function clearFormAndcloseDialog() {
    formRef.value?.ref.resetFields();
    model.value = false;
  }

  async function handleConfirm() {
    // 过滤formModel成员值不为空成员
    const selected = pickBy(formModel.value, (value) => !isEmpty(value));
    const params: Employee.SaveUserCliPermissionReqParam = {
      userId: props.currentUser.userId,
      userCliPermissionList:
        genUserCliPermissionList(
          selected as Employee.UserCliPermissionFormModel,
        ) || [],
    };
    const [, res] = await saveUserCliPermission(params);
    if (res?.success) {
      ElMessage.success(t('global:save.success'));
      clearFormAndcloseDialog();
    }
  }

  function genUserCliPermissionList(
    selected: Employee.UserCliPermissionFormModel,
  ) {
    return Object.values(selected)
      .flat()
      .map((item) => JSON.parse(item));
  }

  // 初始化用户权限
  function initUserCliPermissonList() {
    props.userCliPermissionList.forEach((permission) => {
      const { cliPermissionValueId, codeSystemId, dataValueId } = permission;
      const permissionItem = JSON.stringify({
        codeSystemId,
        dataValueId,
        cliPermissionValueId,
      });

      if (Array.isArray(formModel.value[codeSystemId])) {
        // 复选项
        formModel.value[codeSystemId].push(permissionItem);
      } else {
        // 单选项
        formModel.value[codeSystemId] = permissionItem;
      }
    });
  }

  // 初始化formModel
  function initModelWithCliPermissionList(
    permissions: Employee.CliPermission[],
  ) {
    const form: Employee.UserCliPermissionFormModel = {};
    permissions.forEach((permission) => {
      const { multiplyCheckFlag, codeSystemId } = permission;

      if (multiplyCheckFlag === FLAG.YES) {
        // 复选
        form[codeSystemId] = [];
      } else if (multiplyCheckFlag === FLAG.NO) {
        // 单选
        form[codeSystemId] = '';
      }
    });

    formModel.value = form;
  }

  function clearAll() {
    Object.keys(formModel.value).forEach((key) => {
      if (Array.isArray(formModel.value[key])) {
        // 复选
        formModel.value[key] = [];
      } else {
        // 单选
        formModel.value[key] = '';
      }
    });
    formRef.value?.ref.resetFields();
  }

  onBeforeMount(() => {
    initModelWithCliPermissionList(props.cliPermissionList);
    initUserCliPermissonList();
  });

  defineExpose({
    dialogRef,
  });
</script>

<template>
  <div>
    <ProDialog
      ref="dialog"
      :include-footer="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :width="900"
      destroy-on-close
      @close="clearFormAndcloseDialog"
    >
      <ProForm
        ref="form"
        column="1"
        label-position="top"
        v-model="formModel"
        :data="formConfig"
      ></ProForm>

      <template #header>
        <span>
          {{ $t('system.employee.cliPermission', '临床权限') }}
          <span class="pl-2 text-sm"
            >{{ currentUser.nameDisplay }}[{{ currentUser.empNo }}]</span
          >
        </span>
      </template>
      <template #footer>
        <el-button @click="clearAll">{{ $t('global:clearAll') }}</el-button>
        <el-button @click="clearFormAndcloseDialog">{{
          $t('global:cancel')
        }}</el-button>
        <el-button type="primary" @click="handleConfirm">{{
          $t('global:confirm')
        }}</el-button>
      </template>
    </ProDialog>
  </div>
</template>
