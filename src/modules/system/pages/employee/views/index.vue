<script lang="ts" name="employeeManage" setup>
  import { nextTick, ref, shallowRef, useTemplateRef } from 'vue';
  import { useRouter } from 'vue-router';
  import { queryEmployeeListByExample } from '@modules/system/api/employee.ts';
  import { initPassword } from '@modules/system/api/user.ts';
  import { ProForm, ProTable, Title, useRequest } from 'sun-biz';
  import { ElMessageBox, type TableInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { useSearchFormConfig } from '../config/useSearchConfigData';
  import { useFetchPasswordRule } from '@/hooks/useFetchPasswordRule';
  import { useEmployeeColumnConfig } from '../config/useEmployeeColumnConfig.tsx';
  import { FLAG } from '@/utils/constant';
  import {
    queryCliPermissionDictAndValueByExample,
    queryUserCliPermissionByUserId,
  } from '@/modules/system/api/employee.ts';
  import CliPermissionDialog from './cliPermissionDialog.vue';

  const { t } = useTranslation();
  const multipleSelection = shallowRef<Employee.Item[]>([]);
  const proTableRef = useTemplateRef<{ eleTable: TableInstance }>('proTable');
  // 检索条件配置数据
  const searchConfig = useSearchFormConfig({
    search: (data) => {
      run(data);
    },
  });
  const passwordRule = useFetchPasswordRule();
  const showDefaultPassword = () => {
    ElMessageBox.alert(
      t(
        'resetEmployeeSuccessip',
        `重置成功，默认密码: ${passwordRule.value?.defaultPassword ?? '--'}`,
      ),
      t('global:tip'),
      {
        confirmButtonText: t('hasKnow', '知道了'),
      },
    );
  };
  const handleResetPassword = async (data: string[]) => {
    const [err] = await initPassword({ userIds: data });
    if (!err) {
      showDefaultPassword();
      clearSelection();
    }
  };
  // 职工表头配置数据
  // const employeeColumns = useEmployeeColumnConfig(handleResetPassword);

  // 临床权限及域值列表
  const cliPermissionList = ref<Employee.CliPermission[]>([]);
  // 用户对应权限列表
  const userCliPermissionList = ref<Employee.UserCliPermissionItem[]>([]);
  // 设置临床权限的用户Id
  const currentUser = ref<Employee.Item>();
  const cliPermissionDialogRef = useTemplateRef<{
    dialogRef: {
      open: () => void;
    };
  }>('cliPermissionDialog');
  const showCliPermissionDialog = ref(false);
  const queryCliPermissionList = async () => {
    const [, res] = await queryCliPermissionDictAndValueByExample({
      enabledFlag: FLAG.YES,
    });
    if (res?.success) {
      const { data } = res;
      cliPermissionList.value = data;
    }
  };
  const queryUserCliPermission = async (userId: string) => {
    const [, res] = await queryUserCliPermissionByUserId({ userId });
    if (res?.success) {
      const { data = [] } = res;
      userCliPermissionList.value = data;
    }
  };
  const handleUsrCliPermission = async (user: Employee.Item) => {
    currentUser.value = user;
    // 查询权限列表
    await queryCliPermissionList();
    // 查询用户临床权限
    await queryUserCliPermission(user.userId);
    showCliPermissionDialog.value = true;
    await nextTick();
    cliPermissionDialogRef.value!.dialogRef.open();
  };
  const employeeColumns = useEmployeeColumnConfig(handleUsrCliPermission);
  const router = useRouter();
  // 查询职工条件
  const searchParams = ref({
    pageSize: 25,
    pageNumber: 1,
    keyWord: '',
    empTypeCode: '',
    hospitalId: '',
    deptId: '',
    appReleaseVersionCode: '',
  });
  const clearSelection = () => {
    proTableRef.value?.eleTable?.clearSelection();
    multipleSelection.value = [];
  };
  /**
   * 查询职工列表
   * @param params 查询参数
   */
  const { data, run, total, loading } = useRequest<
    Employee.Item[],
    [Partial<Employee.ReqParams>]
  >(
    (data: Partial<Employee.ReqParams>) => {
      if (data) {
        searchParams.value = {
          ...searchParams.value,
          ...data,
        };
      }
      clearSelection();
      return queryEmployeeListByExample(searchParams.value);
    },
    {
      manual: true,
    },
  );
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('employee.manage.title', '职工管理')" />
    <!-- 检索框 -->
    <div class="mt-3 flex justify-between">
      <div class="el-form-item flex-1">
        <ProForm
          :data="searchConfig"
          :inline="true"
          :show-search-button="true"
          layout-mode="inline"
          @model-change="
            (data: Partial<Employee.ReqParams>) => {
              run(data);
            }
          "
        />
      </div>
      <div>
        <el-button
          v-if="multipleSelection.length > 0"
          v-permission="'ZGGL-CSHMM'"
          type="primary"
          @click="
            () => {
              handleResetPassword(
                multipleSelection.map((item) => (item as any).userId),
              );
            }
          "
          >{{ $t('initpassword', '初始化密码') }}
        </el-button>
        <el-button
          type="primary"
          @click="
            () => {
              router.push('/detail/add');
            }
          "
          >{{ $t('global:add') }}
        </el-button>
      </div>
    </div>
    <pro-table
      ref="proTable"
      :columns="employeeColumns"
      :data="data"
      :loading="loading"
      :page-info="{
        total: total,
        pageNumber: searchParams.pageNumber,
        pageSize: searchParams.pageSize,
      }"
      :pagination="true"
      row-key="userId"
      @selection-change="
        (data: Employee.Item[]) => {
          multipleSelection = data;
        }
      "
      @current-page-change="
        (val: number) => {
          run({ pageNumber: val });
        }
      "
      @size-page-change="
        (val: number) => {
          run({ pageSize: val, pageNumber: 1 });
        }
      "
    />
  </div>
  <CliPermissionDialog
    v-if="showCliPermissionDialog"
    ref="cliPermissionDialog"
    v-model="showCliPermissionDialog"
    :cli-permission-list="cliPermissionList"
    :current-user="currentUser"
    :user-cli-permission-list="userCliPermissionList"
  ></CliPermissionDialog>
</template>
