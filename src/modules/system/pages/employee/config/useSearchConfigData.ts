/** 职工列表查询条件配置文件 */
import {
  APP_RELEASE_VERSION_CODE_NAME,
  USER_JOB_TYPE_CODE,
  USER_STATUS_CODE,
} from '@/utils/constant';
import { useGetDeptData } from '@/hooks/useGetOrgData';
import { useFormConfig } from 'sun-biz';

/**
 * 使用搜索表单配置的自定义钩子
 *
 * 该钩子用于获取和配置搜索表单的数据和结构它主要依赖于部门数据的获取，
 * 并生成包含所属医院、所属科室、职工类型和关键字搜索等字段的表单配置
 *
 * @returns {Object} 返回一个包含表单配置数据的对象
 */
export function useSearchFormConfig(options: {
  search: (data: { keyWord: string }) => void;
}) {
  const { search } = options;
  const { deptList, getDeptList } = useGetDeptData();
  const data = useFormConfig({
    dataSetCodes: [
      USER_JOB_TYPE_CODE,
      USER_STATUS_CODE,
      APP_RELEASE_VERSION_CODE_NAME,
    ],
    getData: (t, data) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        component: 'hospitalSelect',
        triggerModelChange: true,
        extraProps: {
          style: { width: '160px' },
          onChange: (val: string) => {
            if (val) {
              getDeptList({ hospitalId: val });
            } else {
              deptList.value = [];
            }
          },
        },
      },
      {
        label: t('employee.deptName', '所属科室'),
        name: 'deptId',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('employee.deptName', '所属科室'),
        }),
        extraProps: {
          style: { width: '160px' },
          options: deptList.value,
          props: {
            label: 'orgName',
            value: 'orgId',
          },
        },
      },
      {
        label: t('employee.userJob', '用户岗位'),
        name: 'userJobCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('employee.userJob', '用户岗位'),
        }),
        extraProps: {
          filterable: true,
          options: data?.value ? data.value[USER_JOB_TYPE_CODE] : [],
          style: { width: '140px' },
        },
      },
      {
        label: t('employee.userStatus', '职工状态'),
        name: 'userStatusCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('employee.userJob', '职工状态'),
        }),
        extraProps: {
          filterable: true,
          options: data?.value ? data.value[USER_STATUS_CODE] : [],
          style: { width: '140px' },
        },
      },
      {
        label: t('employee.appReleaseVersionCode', '程序版本'),
        name: 'appReleaseVersionCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('employee.appReleaseVersionCode', '程序版本'),
        }),
        extraProps: {
          filterable: true,
          options: data?.value ? data.value[APP_RELEASE_VERSION_CODE_NAME] : [],
          style: { width: '140px' },
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword', '请输入关键字查询'),
        formItemProps: {
          style: 'margin-right: 0;',
        },
        extraProps: {
          prefixIcon: 'Search',
          style: { width: '180px' },
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              search({ keyWord: (e.target as HTMLInputElement)?.value || '' });
            }
          },
        },
      },
    ],
  });
  return data;
}
