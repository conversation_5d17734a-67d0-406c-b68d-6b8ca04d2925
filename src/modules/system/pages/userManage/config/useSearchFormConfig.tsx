import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import {
  APP_RELEASE_VERSION_CODE_NAME,
  USER_JOB_TYPE_CODE,
  USER_TYPE_CODE_NAME,
} from '@/utils/constant';

/** 用户查询form配置 */
export function useSearchFormConfig(options: {
  flatOrgList: Ref<Org.FlatOrgReqItem[]>;
  keydownEnterFn: () => Promise<void>;
  getFlatOrgListData: (keyWord?: string) => Promise<void>;
}) {
  const {
    //  flatOrgList,
    keydownEnterFn,
    // getFlatOrgListData
  } = options;

  const data = useFormConfig({
    dataSetCodes: [
      USER_TYPE_CODE_NAME,
      USER_JOB_TYPE_CODE,
      APP_RELEASE_VERSION_CODE_NAME,
    ],
    getData: (t, dataSet) => [
      {
        label: t('user.userTypeCode', '用户类型'),
        name: 'userTypeCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('user.userTypeCode', '用户类型'),
        }),
        extraProps: {
          clearable: true,
          className: 'w-36',
          options: dataSet?.value ? dataSet.value[USER_TYPE_CODE_NAME] : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
      {
        label: t('user.hospitalId', '服务医院'),
        name: 'hospitalId',
        component: 'hospitalSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('user.hospitalId', '服务医院'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-56',
          // remote: true,
          // filterable: true,
          // remoteShowSuffix: true,
          // remoteMethod: (keyWord: string) => getFlatOrgListData(keyWord),
          // options: flatOrgList.value ?? [],
          // props: {
          //   label: 'orgNameDisplay',
          //   value: 'orgId',
          // },
        },
      },
      {
        label: t('user.userJobCodes', '用户岗位'),
        name: 'userJobCodes',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('user.userJobCodes', '用户岗位'),
        }),
        extraProps: {
          clearable: true,
          className: 'w-56',
          multiple: true,
          collapseTags: true,
          collapseTagsTooltip: true,
          options: dataSet?.value ? dataSet.value[USER_JOB_TYPE_CODE] : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-24',
        },
      },
      {
        label: t('user.appReleaseVersionCode', '程序版本'),
        name: 'appReleaseVersionCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('user.appReleaseVersionCode', '程序版本'),
        }),
        extraProps: {
          clearable: true,
          filterable: true,
          className: 'w-56',
          options: dataSet?.value
            ? dataSet.value[APP_RELEASE_VERSION_CODE_NAME]
            : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-72',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              keydownEnterFn();
            }
          },
        },
      },
    ],
  });
  return data;
}

/** 登录记录查询form配置 */
export function useSearchLoginRecordFormConfig() {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('loginLog.code', '编码'),
        name: 'userNo',
        component: 'text',
        className: 'w-32',
      },
      {
        label: t('loginLog.userNameDisplay', '用户名'),
        name: 'userNameDisplay',
        component: 'text',
        className: 'w-64',
      },
      {
        label: t('loginLog.loginTime', '登录时间'),
        name: 'loginTime',
        type: 'daterange',
        triggerModelChange: true,
        component: 'date-picker',
        placeholder: t('global:placeholder.select.template', {
          name: t('loginLog.loginTime', '登录时间'),
        }),
        className: 'w-96',
        extraProps: {
          clearable: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          startPlaceholder: t('global:startTime'),
          endPlaceholder: t('global:endTime'),
        },
      },
    ],
  });
  return data;
}
