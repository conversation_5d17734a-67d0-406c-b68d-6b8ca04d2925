import { useFormConfig } from 'sun-biz';
import { UserReqItem } from '@/api/types';
import {
  APP_RELEASE_VERSION_CODE_NAME,
  FLAG,
  PAY_SUM_TYPE_CODE_NAME,
  USER_JOB_TYPE_CODE,
} from '@/utils/constant';
import { ComputedRef, Ref } from 'vue';

export function useUserInfoFormConfig(options: {
  isAdd: ComputedRef<boolean>;
  disabled: ComputedRef<boolean>;
  userList: Ref<UserReqItem[]>;
  userLoading: Ref<boolean>;
  changeRole: (role: string) => Promise<void>;
  getUserList: (keyWord?: string) => Promise<void>;
}) {
  const { isAdd, disabled, userList, userLoading, changeRole, getUserList } =
    options;

  const data = useFormConfig({
    dataSetCodes: [
      USER_JOB_TYPE_CODE,
      PAY_SUM_TYPE_CODE_NAME,
      APP_RELEASE_VERSION_CODE_NAME,
    ],
    getData: (t, dataSet) => [
      {
        label: t('user.userJobCode', '用户岗位'),
        name: 'userJobCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('user.userJobCode', '用户岗位'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('user.userJobCode', '用户岗位'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        extraProps: {
          disabled: disabled.value,
          onChange: (role: string) => changeRole(role),
          options: dataSet?.value ? dataSet.value[USER_JOB_TYPE_CODE] : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('day.payment.way', '日结方式'),
        name: 'paySumTypeCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('day.payment.way', '日结方式'),
        }),
        extraProps: {
          disabled: disabled.value,
          options: dataSet?.value ? dataSet.value[PAY_SUM_TYPE_CODE_NAME] : [],
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('day.payment.way', '日结方式'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
      },
      {
        label: t('person.payment.belong', '日结归属'),
        name: 'paySumBelongUserId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('person.payment.belong', '日结归属'),
        }),
        rules: [
          {
            required: !isAdd.value,
            message: t('global:placeholder.select.template', {
              name: t('person.payment.belong', '日结归属'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        extraProps: {
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          disabled: disabled.value,
          loading: userLoading.value,
          remoteMethod: getUserList,
          options: userList.value,
          props: {
            label: 'userNameDisplay',
            value: 'userId',
          },
        },
      },
      {
        label: t('person.payment.invoice.proxy', '开票代理'),
        name: 'invoiceAgentUserId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('person.payment.invoice.proxy', '开票代理'),
        }),
        rules: [
          {
            required: !isAdd.value,
            message: t('global:placeholder.select.template', {
              name: t('person.payment.invoice.proxy', '开票代理'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        extraProps: {
          remote: true,
          remoteShowSuffix: true,
          filterable: true,
          disabled: disabled.value,
          options: userList.value,
          loading: userLoading.value,
          remoteMethod: getUserList,
          props: {
            label: 'userNameDisplay',
            value: 'userId',
          },
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          disabled: disabled.value,
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
      {
        label: t('user.appReleaseVersionCode', '程序版本'),
        name: 'appReleaseVersionCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('user.appReleaseVersionCode', '程序版本'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('user.appReleaseVersionCode', '程序版本'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        extraProps: {
          clearable: true,
          filterable: true,
          disabled: disabled.value,
          className: 'w-56',
          options: dataSet?.value
            ? dataSet.value[APP_RELEASE_VERSION_CODE_NAME]
            : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
    ],
  });
  return data;
}
