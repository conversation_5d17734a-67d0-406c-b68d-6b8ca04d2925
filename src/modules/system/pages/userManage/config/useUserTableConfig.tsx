import { UserReqItem } from '@/api/types';
import { OPERATION_CODE } from '../utils/constant';
import { FLAG } from '@/utils/constant';
import { useColumnConfig } from 'sun-biz';

export function useUserTableConfig(options: {
  handleEnableSwitch: (row: UserReqItem) => Promise<void>;
  operationFn: (row: UserReqItem, type: OPERATION_CODE) => Promise<void>;
}) {
  const { handleEnableSwitch, operationFn } = options;

  const data = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: UserReqItem, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('user.userNo', '用户编码'),
        prop: 'userNo',
        minWidth: 100,
      },
      {
        label: t('user.userNameDisplay', '用户名称'),
        prop: 'userNameDisplay',
        minWidth: 100,
      },
      {
        label: t('global:secondName'),
        prop: 'user2ndName',
        minWidth: 100,
      },
      {
        label: t('global:thirdName'),
        prop: 'userExtName',
        minWidth: 100,
      },
      {
        label: t('user.userTypeDesc', '用户类型'),
        prop: 'userTypeDesc',
        minWidth: 100,
      },
      {
        label: t('user.userJobDesc', '用户岗位'),
        prop: 'userJobDesc',
        minWidth: 100,
      },
      {
        label: t('user.lockedFlag', '锁定标志'),
        prop: 'lockedFlag',
        minWidth: 100,
        render: (row: UserReqItem) => {
          return (
            <div class="w-full text-center">
              {row.lockedFlag === FLAG.YES ? t('global:yes') : t('global:no')}
            </div>
          );
        },
      },
      {
        label: t('user.loginFlag', '登录标志'),
        prop: 'loginFlag',
        minWidth: 100,
        render: (row: UserReqItem) => {
          return (
            <div class="w-full text-center">
              {row.loginFlag === FLAG.YES ? t('global:yes') : t('global:no')}
            </div>
          );
        },
      },
      {
        label: t('user.appReleaseVersionCode', '程序版本'),
        prop: 'appReleaseVersionDesc',
        minWidth: 100,
      },
      {
        label: t('user.lastLoginAt', '最近登录时间'),
        prop: 'lastLoginAt',
        minWidth: 180,
      },
      {
        label: t('user.loginIp', '登录IP'),
        prop: 'loginIp',
        minWidth: 120,
      },
      {
        label: t('user.loginMac', '登录MAC'),
        prop: 'loginMac',
        minWidth: 150,
      },
      {
        label: t('user.loginTypeDesc', '登录方式'),
        prop: 'loginTypeDesc',
        minWidth: 100,
      },
      {
        label: t('user.adminFlag', '管理员标志'),
        prop: 'adminFlag',
        minWidth: 100,
        render: (row: UserReqItem) => {
          return (
            <div class="w-full text-center">
              {row.adminFlag === FLAG.YES ? t('global:yes') : t('global:no')}
            </div>
          );
        },
      },
      // {
      //   label: t('user.loginTypeDesc', '创建人'),
      //   prop: 'loginTypeDesc',
      //   minWidth: 220,
      // },

      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: UserReqItem) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('user.loginOrgLocationList', '服务院区'),
        prop: 'loginOrgLocationList',
        minWidth: 220,
        render: (row: UserReqItem) => {
          return (
            <div class="w-full text-wrap">
              {row.loginOrgLocationList && row.loginOrgLocationList?.length > 0
                ? row.loginOrgLocationList
                    ?.map((item) => item?.orgLocationName)
                    .join('、')
                : '--'}
            </div>
          );
        },
      },
      {
        label: t('user.bizUnitList', '服务科室'),
        prop: 'bizUnitList',
        minWidth: 220,
        render: (row: UserReqItem) => {
          return (
            <div class="w-full text-wrap">
              {row.bizUnitList && row.bizUnitList?.length > 0
                ? row.bizUnitList?.map((item) => item?.bizUnitName).join('、')
                : '--'}
            </div>
          );
        },
      },
      {
        label: t('user.userRoleList', '角色'),
        prop: 'userRoleList',
        minWidth: 220,
        render: (row: UserReqItem) => {
          return (
            <div class="w-full text-wrap">
              {row.userRoleList && row.userRoleList?.length > 0
                ? row.userRoleList?.map((item) => item?.roleName).join('、')
                : '--'}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 300,
        fixed: 'right',
        render: (row: UserReqItem) => {
          return (
            <div class={'flex items-center justify-around'}>
              <el-button
                key="detail"
                type="primary"
                link={true}
                onClick={() => operationFn(row, OPERATION_CODE.DETAIL)}
              >
                {t('detail', '详情')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                key="edit"
                onClick={() => operationFn(row, OPERATION_CODE.EDIT)}
              >
                {t('global:edit')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                key="log"
                onClick={() => operationFn(row, OPERATION_CODE.LOGIN_LOG)}
              >
                {t('login.log', '登录日志')}
              </el-button>
              <el-button
                type="danger"
                link={true}
                key="password"
                v-permission={'YHGL-CSHMM'}
                onClick={() => operationFn(row, OPERATION_CODE.INIT_PASSWORD)}
              >
                {t('init.password', '初始化密码')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return data;
}
