import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';
import {
  CERTIFICATE_TYPE_CODE_NAME,
  CERTIFICATE_TYPE_CODE,
  CONTACT_TYPE_CODE_NAME,
  CONTACT_TYPE_CODE,
} from '@/utils/constant';
import { validateID, validatePhone } from '@sun-toolkit/shared';
import { Ref, ComputedRef } from 'vue';

/** 证件tableConfig */
export function useCertificateTableConfig(options: {
  disabled: ComputedRef<boolean>;
  tableRef: Ref<TableRef>;
  tableData: Ref<Employee.CertificateItem[]>;
}) {
  const { tableRef, tableData, disabled } = options;
  const { toggleEdit, addItem, cancelEdit } = useEditableTable({
    tableRef,
    data: tableData,
    id: 'perCertificateId',
  });

  const data = useColumnConfig({
    dataSetCodes: [CERTIFICATE_TYPE_CODE_NAME],
    getData: (t, dataSet) => [
      {
        label: t('person.certificateType', '证件类型'),
        prop: 'certificateTypeCode',
        minWidth: 120,
        editable: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('person.certificateType', '证件类型'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('person.certificateType', '证件类型'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: Employee.CertificateItem, index: number) => {
          const certificateTypeList =
            dataSet?.value?.[CERTIFICATE_TYPE_CODE_NAME];
          return row.editable ? (
            <el-select
              v-model={row.certificateTypeCode}
              filterable
              placeholder={t('global:placeholder.select.template', {
                name: t('person.certificateType', '证件类型'),
              })}
              onChange={async (val: string) => {
                row.certificateTypeDesc =
                  certificateTypeList?.find((item) => item.dataValueNo === val)
                    ?.dataValueNameDisplay || '';
                if (row.certificateNo) {
                  await tableRef.value?.formRef.validateField([
                    `tableData.${index}.certificateNo`,
                  ]);
                }
              }}
            >
              {(certificateTypeList ?? [])?.map((item) => (
                <el-option
                  key={item.dataValueNo}
                  label={item.dataValueNameDisplay}
                  value={item.dataValueNo}
                />
              ))}
            </el-select>
          ) : (
            <>{row.certificateTypeDesc}</>
          );
        },
      },
      {
        label: t('person.certificateNo', '证件号码'),
        prop: 'certificateNo',
        minWidth: 140,
        editable: true,
        rules: (row: Employee.CertificateItem) => [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('person.certificateNo', '证件号码'),
            }),
            trigger: ['blur', 'change'],
          },
          {
            validator: (
              rule: unknown,
              value: string,
              callback: (data?: Error) => void,
            ) => {
              if (row.certificateTypeCode === CERTIFICATE_TYPE_CODE.ID_CARD) {
                validateID(rule, value, callback);
              } else {
                callback();
              }
            },
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: Employee.CertificateItem) => {
          return row.editable ? (
            <>
              <el-input
                v-model={row.certificateNo}
                placeholder={t('global:placeholder.input.template', {
                  content: t('person.certificateNo', '证件号码'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.certificateNo}</>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        width: 120,
        render: (row: Employee.CertificateItem, index: number) => {
          return row.editable ? (
            <>
              <el-button
                key="cancel"
                onClick={() => cancelEdit(row, index)}
                type="danger"
                link
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                key="confirm"
                onClick={() => toggleEdit(row)}
                type="primary"
                link
              >
                {t('global:confirm')}
              </el-button>
            </>
          ) : (
            <>
              <el-button
                onClick={() => toggleEdit(row)}
                type="primary"
                disabled={disabled.value}
                key="edit"
                link
              >
                {t('global:edit')}
              </el-button>
            </>
          );
        },
      },
    ],
  });
  return { addItem, data };
}

/** 联系方式tableConfig */
export function useContactNoTableConfig(options: {
  disabled: ComputedRef<boolean>;
  tableRef: Ref<TableRef>;
  tableData: Ref<Employee.ContactNoItem[]>;
}) {
  const { tableRef, tableData, disabled } = options;

  const { toggleEdit, addItem, cancelEdit } = useEditableTable({
    tableRef,
    data: tableData,
    id: 'perContactId',
  });

  const data = useColumnConfig({
    dataSetCodes: [CONTACT_TYPE_CODE_NAME],
    getData: (t, dataSet) => [
      {
        label: t('person.contactType', '联系方式'),
        prop: 'contactTypeCode',
        minWidth: 120,
        editable: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('person.contactType', '联系方式'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('person.contactType', '联系方式'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: Employee.ContactNoItem, index: number) => {
          const contactTypeList = dataSet?.value?.[CONTACT_TYPE_CODE_NAME];
          return row.editable ? (
            <el-select
              v-model={row.contactTypeCode}
              filterable
              placeholder={t('global:placeholder.select.template', {
                name: t('person.contactType', '联系方式'),
              })}
              onChange={async (val: string) => {
                row.contactTypeDesc =
                  contactTypeList?.find((item) => item.dataValueNo === val)
                    ?.dataValueNameDisplay || '';
                if (row.contactNo) {
                  await tableRef.value?.formRef.validateField([
                    `tableData.${index}.contactNo`,
                  ]);
                }
              }}
            >
              {(contactTypeList ?? [])?.map((item) => (
                <el-option
                  key={item.dataValueNo}
                  label={item.dataValueNameDisplay}
                  value={item.dataValueNo}
                />
              ))}
            </el-select>
          ) : (
            <>{row.contactTypeDesc}</>
          );
        },
      },
      {
        label: t('person.contactNo', '联系号码'),
        prop: 'contactNo',
        minWidth: 140,
        editable: true,
        rules: (row: Employee.ContactNoItem) => [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('person.contactNo', '联系号码'),
            }),
            trigger: ['blur', 'change'],
          },
          {
            validator: (
              rule: unknown,
              value: string,
              callback: (data?: Error) => void,
            ) => {
              if (row.contactTypeCode === CONTACT_TYPE_CODE.MOBILE) {
                validatePhone(rule, value, callback);
              } else {
                callback();
              }
            },
            trigger: 'blur',
          },
        ],
        render: (row: Employee.ContactNoItem) => {
          return row.editable ? (
            <>
              <el-input
                v-model={row.contactNo}
                placeholder={t('global:placeholder.input.template', {
                  content: t('person.contactNo', '联系号码'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.contactNo}</>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        width: 120,
        render: (row: Employee.ContactNoItem, index: number) => {
          return row.editable ? (
            <>
              <el-button
                key="cancel"
                onClick={() => cancelEdit(row, index)}
                type="danger"
                link
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                key="confirm"
                onClick={() => toggleEdit(row)}
                type="primary"
                link
              >
                {t('global:confirm')}
              </el-button>
            </>
          ) : (
            <el-button
              onClick={() => toggleEdit(row)}
              type="primary"
              disabled={disabled.value}
              key="edit"
              link
            >
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });
  return { addItem, data };
}
