import { ComputedRef, Ref } from 'vue';
import { useColumnConfig } from 'sun-biz';
import { UserReqItem } from '@/api/types';
import { userInfoType, userRelationType } from '../views/detail.vue';
import { ORG_TYPE_CODE, USER_JOB_CODE } from '@/utils/constant';
import BizUnitSelect from '@/components/biz-unit-select/index.vue';

export function useUserInfoTableConfig(options: {
  isBizUnitRequired: ComputedRef<boolean>;
  bizUnitLoading: Ref<boolean>;
  disabled: ComputedRef<boolean>;
  bizUnitList: Ref<UserReqItem['bizUnitList']>;
  userInfoModel: Ref<userInfoType>;
  bizUnitListData: Ref<
    | {
        bizUnitId: string;
        bizUnitName: string;
      }[]
    | undefined
  >;
  getBizUnitList: (row: userRelationType, keyWord: string) => Promise<void>;
}) {
  const {
    disabled,
    bizUnitList,
    userInfoModel,
    // bizUnitLoading,
    // getBizUnitList,
    // bizUnitListData,
    isBizUnitRequired,
  } = options;

  const data = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('group&hospital', '集团&医院'),
        minWidth: 120,
        prop: 'orgName',
        render: (row: { orgName: string }) => <>{row.orgName}</>,
      },
      {
        label: t('working.hospital.zone', '服务院区'),
        prop: 'loginOrgLocationList',
        minWidth: 250,
        editable: true,
        rules: [
          {
            required: isBizUnitRequired.value,
            message: t('global:placeholder.select.template', {
              name: t('working.hospital.zone', '服务院区'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: {
          orgTypeCode: string;
          loginOrgLocationList: string[];
          orgLocationList: Org.LocationItem[];
          editable: boolean;
        }) => {
          return (
            <div style="height: 34px" class="w-full">
              <el-select
                v-model={row.loginOrgLocationList}
                multiple
                filterable
                collapse-tags
                clearable={true}
                collapse-tags-tooltip
                disabled={disabled.value}
                placeholder={t('global:placeholder.select.template', {
                  name: t('working.hospital.zone', '服务院区'),
                })}
              >
                {(row?.orgLocationList ?? [])?.map((item) => (
                  <el-option
                    key={item.orgLocationId}
                    label={item.orgLocationName}
                    value={item.orgLocationId}
                  />
                ))}
              </el-select>
            </div>
          );
        },
      },
      {
        label:
          userInfoModel.value.userJobCode === USER_JOB_CODE.NURSE
            ? t('working.Ward.zone', '服务病区')
            : t('working.Department.zone', '服务科室'),
        prop: 'bizUnitList',
        minWidth: 250,
        editable: true,
        render: (row: {
          orgTypeCode: string;
          orgId: string;
          bizUnitList: Employee.BizUnitItem[];
          editable: boolean;
        }) => {
          return row.orgTypeCode === ORG_TYPE_CODE.GROUP ? (
            <div class={'flex-1 justify-center'}>--</div>
          ) : (
            <div style="height: 34px" class="w-full">
              <BizUnitSelect
                v-model={row.bizUnitList}
                hospitalId={row.orgId}
                multiSelectFlag={true}
                defaultValue={(bizUnitList.value ?? [])
                  .filter((bizUnit) => bizUnit.hospitalId === row.orgId)
                  .map((item) => ({
                    ...item,
                    label: item.bizUnitName,
                    value: item.bizUnitId,
                  }))}
                orgTypeCodes={
                  userInfoModel.value.userJobCode === USER_JOB_CODE.ADMIN
                    ? undefined
                    : [
                        userInfoModel.value.userJobCode === USER_JOB_CODE.NURSE
                          ? ORG_TYPE_CODE.AREA
                          : ORG_TYPE_CODE.DEPARTMENT,
                      ]
                }
                selectProps={{
                  disabled: disabled.value,
                  placeholder: t('global:placeholder.select.template', {
                    name:
                      userInfoModel.value.userJobCode === USER_JOB_CODE.NURSE
                        ? t('working.Ward.zone', '服务病区')
                        : t('working.Department.zone', '服务科室'),
                  }),
                }}
              />
              {/* <el-select
                loading={bizUnitLoading.value}
                v-model={row.bizUnitList}
                remote
                multiple
                filterable
                collapse-tags
                clearable={true}
                remote-show-suffix
                collapse-tags-tooltip
                disabled={disabled.value}
                remote-method={async (keyWord: string) => {
                  getBizUnitList(row as unknown as userRelationType, keyWord);
                }}
                placeholder={t('global:placeholder.select.template', {
                  name:
                    userInfoModel.value.userJobCode === USER_JOB_CODE.NURSE
                      ? t('working.Ward.zone', '服务病区')
                      : t('working.Department.zone', '服务科室'),
                })}
              >
                {(bizUnitListData.value ?? bizUnitList.value ?? [])?.map(
                  (item) => (
                    <el-option
                      key={item.bizUnitId}
                      label={item.bizUnitName}
                      value={item.bizUnitId}
                    ></el-option>
                  ),
                )}
              </el-select> */}
            </div>
          );
        },
      },
      {
        label: t('role.roleName', '角色'),
        prop: 'userRolesList',
        minWidth: 320,
        editable: true,
        render: (row: {
          orgTypeCode: string;
          userRolesList: string[];
          roleList: { roleId: string; roleName: string }[];
        }) => {
          return row.orgTypeCode === ORG_TYPE_CODE.GROUP ? (
            <div class={'flex-1 justify-center'}>--</div>
          ) : (
            <>
              <el-select
                v-model={row.userRolesList}
                multiple
                filterable
                collapse-tags
                clearable={true}
                collapse-tags-tooltip
                disabled={disabled.value}
                placeholder={t('global:placeholder.select.template', {
                  name: t('role.roleName', '角色'),
                })}
              >
                {(row?.roleList ?? [])?.map((item) => (
                  <el-option
                    key={item.roleId}
                    label={item.roleName}
                    value={item.roleId}
                  />
                ))}
              </el-select>
            </>
          );
        },
      },
    ],
  });
  return data;
}
