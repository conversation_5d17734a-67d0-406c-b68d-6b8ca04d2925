<script lang="ts" name="UserManage" setup>
  import { onMounted, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { queryUserList, updateEnabledFlagById } from '@/api/common';
  import { UserReqItem } from '@/api/types';
  import { initPassword } from '@/modules/system/api/user.ts';
  import { FLAG, ORG_TYPE_CODE } from '@/utils/constant';
  import { useGetOrgFlat } from '../hooks/useGetOrgFlat';
  import {
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    Title,
    useAppConfigData,
  } from 'sun-biz';
  import { useSearchFormConfig } from '../config/useSearchFormConfig';
  import { useUserTableConfig } from '../config/useUserTableConfig';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { OPERATION_CODE } from '../utils/constant';
  import { useFetchPasswordRule } from '@/hooks/useFetchPasswordRule';
  import loginLogDialog from '../components/loginLogDialog.vue';

  type SearchModelType = {
    userTypeCode?: string;
    hospitalId?: string;
    userJobCodes?: string[];
    enabledFlag?: number;
    keyWord?: string;
    appReleaseVersionCode?: string;
  };
  const num = ref(FLAG.NO);

  const formRef = ref();
  const loginLogDialogRef = ref();
  const loading = ref<boolean>(false);
  const userlist = ref<UserReqItem[]>([]);
  const modelValue = ref<SearchModelType>({
    userTypeCode: undefined,
    hospitalId: undefined,
    userJobCodes: undefined,
    enabledFlag: -1,
    keyWord: undefined,
    appReleaseVersionCode: undefined,
  });
  const pageInfo = ref({
    pageNumber: 1,
    pageSize: 25,
    total: 0,
  });

  const { t } = useTranslation();
  const router = useRouter();
  const passwordRule = useFetchPasswordRule();
  const { flatOrgList, getFlatOrgList } = useGetOrgFlat();
  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);

  /** 初始化 */
  const init = async () => {
    modelValue.value.hospitalId = currentOrg?.orgId;
    await fetchData();
    await getFlatOrgList();
    num.value = FLAG.YES;
  };

  /** model变化 */
  const modelChange = async (model: SearchModelType) => {
    console.log(model, 'modelChange');
    if (num.value === FLAG.NO) return;
    modelValue.value = {
      ...modelValue.value,
      ...model,
    };
    await resetPageInfo();
    await fetchData();
  };

  /** 重置分页 */
  const resetPageInfo = async () => {
    pageInfo.value = {
      pageNumber: 1,
      pageSize: 25,
      total: 0,
    };
  };

  /** 获取用户列表 */
  const fetchData = async () => {
    loading.value = true;

    const [, res] = await queryUserList({
      pageNumber: pageInfo.value.pageNumber,
      pageSize: pageInfo.value.pageSize,
      userTypeCode: modelValue.value?.userTypeCode,
      hospitalId: modelValue.value?.hospitalId,
      userJobCodes: modelValue.value?.userJobCodes,
      appReleaseVersionCode: modelValue.value?.appReleaseVersionCode,
      enabledFlag:
        modelValue.value?.enabledFlag === FLAG.ALL
          ? undefined
          : modelValue.value?.enabledFlag,
      keyWord: modelValue.value?.keyWord,
    });

    loading.value = false;
    if (res?.success) {
      userlist.value = res.data ?? [];
      pageInfo.value.total = res.total;
    }
  };

  /** 新增 */
  const addFn = async () => {
    router.push({
      name: 'detail',
      params: {
        hospitalId: modelValue.value?.hospitalId,
      },
    });
  };

  /** 关键字检索 */
  const keydownEnterFn = async () => {
    await fetchData();

    const keyWordRef = formRef.value?.getItemRef('keyWord');
    keyWordRef.blur();
  };

  /** 切换服务院区 */
  const getFlatOrgListData = async (val?: string) => {
    await getFlatOrgList({
      pageNumber: 1,
      pageSize: 100,
      keyWord: val,
      orgTypeCodes: [ORG_TYPE_CODE.HOSPITAL],
    });
  };

  /** 停启用切换 */
  const handleEnableSwitch = async (row: UserReqItem) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.userNameDisplay,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        userId: row.userId,
        enabledFlag: (row.enabledFlag === FLAG.YES
          ? FLAG.NO
          : FLAG.YES) as number,
      };
      const [, res] = await updateEnabledFlagById(params);
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );

        await fetchData();
      }
    });
  };

  /** 展示默认密码 */
  const showDefaultPassword = () => {
    ElMessageBox.alert(
      t(
        'resetEmployeeSuccessip',
        `重置成功，默认密码: ${passwordRule.value?.defaultPassword ?? '--'}`,
      ),
      t('global:tip'),
      {
        confirmButtonText: t('hasKnow', '知道了'),
      },
    );
  };

  /** 初始化密码 */
  const handleResetPassword = async (userId?: string) => {
    const [, res] = await initPassword({
      userIds: userId ? [userId] : [],
    });
    if (res?.success) {
      showDefaultPassword();
    }
  };

  /** 操作方法 */
  const operationFn = async (row: UserReqItem, type: OPERATION_CODE) => {
    if (type === OPERATION_CODE.INIT_PASSWORD) {
      await handleResetPassword(row?.userId);
    } else if (type === OPERATION_CODE.LOGIN_LOG) {
      loginLogDialogRef.value.open(row);
    } else if (type === OPERATION_CODE.EDIT) {
      router.push(`/detail/${modelValue.value?.hospitalId}/${row?.userId}`);
    } else if (type === OPERATION_CODE.DETAIL) {
      router.push({
        name: 'detail',
        params: {
          userId: row?.userId,
          hospitalId: modelValue.value?.hospitalId,
        },
        query: {
          status: '1',
        },
      });
    }
  };

  const formConfig = useSearchFormConfig({
    keydownEnterFn,
    flatOrgList,
    getFlatOrgListData,
  });
  const tableColumns = useUserTableConfig({ handleEnableSwitch, operationFn });

  onMounted(() => {
    init();
  });
</script>
<template>
  <!-- 用户管理 -->
  <div class="p-box flex size-full flex-col">
    <Title :title="$t('user.list', '用户列表')" class="mb-3" />

    <ProForm
      ref="formRef"
      v-model="modelValue"
      :data="formConfig"
      class="flex flex-wrap"
      layout-mode="inline"
      @model-change="modelChange"
    >
      <div class="flex-1 text-right">
        <el-button type="primary" @click="fetchData"
          >{{ $t('global:query') }}
        </el-button>
        <el-button type="primary" @click="addFn"
          >{{ $t('global:add') }}
        </el-button>
      </div>
    </ProForm>

    <div class="flex size-full flex-col overflow-hidden">
      <ProTable
        ref="userTableRef"
        :columns="tableColumns"
        :data="userlist"
        :loading="loading"
        :page-info="{
          total: pageInfo.total,
          pageNumber: pageInfo.pageNumber,
          pageSize: pageInfo.pageSize,
        }"
        :pagination="true"
        row-key="userId"
        @current-page-change="
          (val: number) => {
            pageInfo.pageNumber = val;
            fetchData();
          }
        "
        @size-page-change="
          (val: number) => {
            pageInfo.pageSize = val;
            fetchData();
          }
        "
      />
    </div>

    <loginLogDialog ref="loginLogDialogRef" />
    <!-- -->
  </div>
</template>
