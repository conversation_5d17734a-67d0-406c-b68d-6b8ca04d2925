<script setup lang="ts">
  import { ref, useAttrs, computed, watch } from 'vue';
  import { addSystem, updateSystemById } from '../../../api/menu';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import {
    ENABLED_FLAG,
    SYSTEM_ID,
    DEV_GROUP_CODE_NAME,
    SYSTEM_SOURCE_CODE_NAME,
    SYSTEM_SOURCE_CODE,
  } from '@/utils/constant';
  import { ProForm, ProDialog, useFormConfig, useFetchDataset } from 'sun-biz';

  // 类型定义和 props 放顶部
  export type Props = {
    row?: {
      sysId: string;
      sysName: string;
      sys2ndName: string;
      sysExtName: string;
      url: string;
      enabledFlag: 0 | 1;
      wbNo: string;
      spellNo: string;
      sort: number;
      devGroupCode: string;
    };
    initSort: number;
  };

  const props = defineProps<Props>();
  const attrs = useAttrs();
  const emits = defineEmits<{ success: [] }>();

  const formRef = ref<{ ref: FormInstance; model: Record<string, unknown> }>();
  const formModel = ref<Record<string, unknown>>({});
  const dialogRef = ref();

  const { t } = useTranslation();
  const dataSetList = useFetchDataset([DEV_GROUP_CODE_NAME]);

  const devGroupCodeData = computed(() =>
    (dataSetList?.value?.[DEV_GROUP_CODE_NAME] || []).map((item) => ({
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );

  // 优化 watch 写法
  watch(
    () => props.row,
    (row) => {
      formModel.value = row || {};
    },
    { immediate: true },
  );

  // useGetBaseInfoData 提取到 setup 外部
  function useGetBaseInfoData() {
    const dataSetCodes = [SYSTEM_SOURCE_CODE_NAME];
    const data = useFormConfig<typeof dataSetCodes>({
      dataSetCodes,
      getData: (t, dataSet) => [
        {
          name: 'sysName',
          label: t('addOrEditSystem.sysName', '系统名称'),
          autoConvertSpellNoAndWbNo: true,
          component: 'input',
          placeholder: t(
            'addOrEditSystem.sysName.placeholder',
            '请输入系统名称',
          ),
          rules: [
            {
              required: true,
              message: t(
                'addOrEditSystem.sysName.placeholder',
                '请输入系统名称',
              ),
              trigger: 'change',
            },
          ],
        },
        {
          name: 'sys2ndName',
          label: t('addOrEditSystem.sys2ndName', '系统辅助名称'),
          component: 'input',
          placeholder: t(
            'addOrEditSystem.sys2ndName.placeholder',
            '请输入系统辅助名称',
          ),
        },
        {
          name: 'sysExtName',
          label: t('addOrEditSystem.sysExtName', '系统扩展名称'),
          component: 'input',
          placeholder: t(
            'addOrEditSystem.sysExtName.placeholder',
            '请输入系统扩展名称',
          ),
        },
        {
          name: 'spellNo',
          component: 'input',
          placeholder: t('addOrEditSystem.spellNo.placeholder', '请输入拼音码'),
          label: t('global:spellNo'),
        },
        {
          name: 'wbNo',
          component: 'input',
          placeholder: t('addOrEditSystem.wbNo.placeholder', '请输入五笔码'),
          label: t('global:wbNo'),
        },
        {
          name: 'systemSourceCode',
          component: 'select',
          placeholder: t('global:placeholder.select.template', {
            name: t('addOrEditSystem.systemSourceCode', '系统来源'),
          }),
          defaultValue: SYSTEM_SOURCE_CODE.OUR_SYSTEM,
          label: t('addOrEditSystem.systemSourceCode', '系统来源'),
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('addOrEditSystem.systemSourceCode', '系统来源'),
              }),
              trigger: 'change',
            },
          ],
          extraProps: {
            options: dataSet?.value?.[SYSTEM_SOURCE_CODE_NAME]?.length
              ? dataSet?.value[SYSTEM_SOURCE_CODE_NAME]
              : [],
          },
        },
        {
          name: 'url',
          component: 'input',
          placeholder: t('addOrEditSystem.url.placeholder', '请输入系统地址'),
          label: t('addOrEditSystem.url', '系统地址'),
          isHidden:
            formModel.value?.systemSourceCode !== SYSTEM_SOURCE_CODE.THIRD,
          rules: [
            {
              required: true,
              message: t('addOrEditSystem.url.placeholder', '请输入系统地址'),
              trigger: 'change',
            },
          ],
        },
        {
          name: 'devGroupCode',
          component: 'select',
          placeholder: t(
            'addOrEditSystem.devGroupCode.placeholder',
            '请输入开发组别',
          ),
          label: t('addOrEditSystem.devGroupCode', '开发组别'),
          extraProps: {
            options: devGroupCodeData.value,
          },
        },
        {
          name: 'enabledFlag',
          component: 'switch',
          placeholder: t(
            'addOrEditSystem.enabledFlag.placeholder',
            '请输入启用状态',
          ),
          label: t('addOrEditSystem.enabledFlag', '启用状态'),
          defaultValue: ENABLED_FLAG.YES,
          extraProps: {
            'active-value': ENABLED_FLAG.YES,
            'inactive-value': ENABLED_FLAG.NO,
          },
        },
        {
          name: 'accessFlag',
          component: 'switch',
          placeholder: t(
            'addOrEditSystem.accessFlag.placeholder',
            '请输入访问标志',
          ),
          label: t('addOrEditSystem.accessFlag', '访问标志'),
          defaultValue: ENABLED_FLAG.YES,
          extraProps: {
            disabled: props.row?.sysId === SYSTEM_ID,
            'active-value': ENABLED_FLAG.YES,
            'inactive-value': ENABLED_FLAG.NO,
          },
        },
        {
          name: 'mdmApiUrl',
          label: t('addOrEditSystem.MDMInterfaceAddress', 'MDM接口地址'),
          component: 'input',
          placeholder: t(
            'addOrEditSystem.sysExtName.MDMInterfaceAddress',
            '请输入MDM接口地址',
          ),
        },
      ],
    });
    return data;
  }

  const baseInfoDescData = useGetBaseInfoData();

  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        const model = formRef?.value?.model;
        if (valid) {
          let [, result] = props.row?.sysId
            ? await updateSystemById({
                ...(model as unknown as Menu.ResUpdateSystemParams),
                sysId: props.row?.sysId,
                sort: props.row?.sort || 99,
              })
            : await addSystem({
                ...(model as unknown as Menu.ResAddSystemParams),
                sort: props.initSort,
              });

          if (result?.success) {
            ElMessage({
              type: 'success',
              message: t(
                props.row?.sysId
                  ? 'global:modify.success'
                  : 'global:create.success',
              ),
            });
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  }

  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :width="900"
    ref="dialogRef"
    :title="attrs.title"
    :link="attrs.link"
    :button-text="attrs['button-text']"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm
      ref="formRef"
      v-model="formModel"
      :column="2"
      :data="baseInfoDescData"
    />
  </ProDialog>
</template>
