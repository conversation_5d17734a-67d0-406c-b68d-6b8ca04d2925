<script setup lang="ts">
  import { CaretRight } from '@element-sun/icons-vue';
  import {
    FLAG,
    MS_CHARGE_TYPE_CODE,
    MS_CHARGE_TYPE_CODE_NAME,
  } from '@sun-toolkit/enums';
  import { ProForm } from 'sun-biz';
  import { computed, onBeforeMount, ref } from 'vue';
  import ChargeSettingListTable from './chargeSettingListTable.vue';
  import { useChargeSettingListForm } from '../../config/useChargeSettingListForm';
  import { updateEnabledFlagById } from '@/modules/system/api/msChargeSetting';
  import { useTranslation } from 'i18next-vue';
  import { queryDataSetByCodeSystemCodes2 } from '@/api/common';
  import { ElMessage } from 'element-sun';
  const { t } = useTranslation();

  export interface IFormModel {
    csTypeCode: string;
    date: string[];
    enabledFlag: number;
  }

  export interface IListItem extends MsChargeSetting.MsChargeTypeSetting {
    containRangeColumn: boolean;
    baseForm: IFormModel;
    tableData: MsChargeSetting.MsChargeTableItem[];
  }

  const props = defineProps<{
    // 列表数据
    list: MsChargeSetting.MsChargeTypeSetting[];
    // 计费对象列表
    msChargeObjectList: MsChargeSetting.MsChargeTypeSupported[];
  }>();
  const emits = defineEmits(['refresh']);
  // 包含区间设置的计费方式集合
  const MS_CHARGE_TYPE_CONTAIN_RANGES = [
    MS_CHARGE_TYPE_CODE.EXAM_COUNT_RANGE,
    MS_CHARGE_TYPE_CODE.TEST_COUNT_RANGE,
  ];
  const msChargeTypeOptions = ref<{ label: string; value: string }[]>([]);
  /**
   * 计费对象Map<msChargeTypeCode, Set<msChargeObjectTypeCode>>
   * 计费对象Map<计费对象类型Code, Set<计费列表项 code>>
   */
  const msChargetObjectListMap = computed(() => {
    const map = new Map<string, Set<string>>();

    props.msChargeObjectList.forEach(
      ({ msChargeTypeCode, msChargeObjectList }) => {
        const set = new Set<string>();
        msChargeObjectList?.forEach(({ msChargeObjectTypeCode }) =>
          set.add(msChargeObjectTypeCode),
        );

        map.set(msChargeTypeCode, set);
      },
    );

    return map;
  });
  const listComputed = computed<IListItem[]>(() => {
    return props.list.map((item: MsChargeSetting.MsChargeTypeSetting) => {
      let tableData: MsChargeSetting.MsChargeTableItem[] = [];
      const { msChargeTypeCode, msChargeTypeDesc, msChargeTypeDtList } = item;
      if (msChargeTypeDtList?.length) {
        msChargeTypeDtList.forEach(
          ({
            msChargeObjectId,
            msChargeObjectName,
            msChargeObjectTypeCode,
            msChargeObjectTypeDesc,
            rangeLowerNum,
            rangeUpperNum,
            msChargeDtList,
          }: MsChargeSetting.MsChargeTypeDt) => {
            if (msChargeDtList?.length) {
              const list = msChargeDtList.map(
                ({
                  num,
                  price,
                  unitName,
                  commodityId,
                  commodityNo,
                  commodityNameDisplay,
                  commodityTypeDesc,
                }: MsChargeSetting.MsChargeDt) => {
                  return {
                    msChargeTypeCode,
                    msChargeTypeDesc,
                    msChargeObjectId,
                    msChargeObjectName,
                    msChargeObjectTypeCode,
                    msChargeObjectTypeDesc,
                    rangeLowerNum,
                    rangeUpperNum,
                    num,
                    price,
                    unitName,
                    commodityId,
                    commodityNo,
                    commodityNameDisplay,
                    commodityTypeDesc,
                  };
                },
              );
              tableData = tableData.concat(list);
            }
          },
        );
      }
      return {
        ...item,
        containRangeColumn: MS_CHARGE_TYPE_CONTAIN_RANGES.includes(
          msChargeTypeCode as MS_CHARGE_TYPE_CODE,
        ),
        baseForm: {
          csTypeCode: item.msChargeTypeCode,
          date: [item.startAt, item.endAt],
          enabledFlag: item.enabledFlag as 0 | 1,
        },
        tableData,
      };
    });
  });
  const activeName = ref(0);
  const formConfigs = computed(() => {
    return listComputed.value.map((item) => {
      const config = useChargeSettingListForm(
        t,
        item,
        msChargeTypeOptions.value,
        handleModelChange,
      );
      return config;
    });
  });

  async function handleModelChange(
    msChargeTypeSettingId: string,
    enabledFlag: number,
  ) {
    const [, res] = await updateEnabledFlagById({
      msChargeTypeSettingId,
      enabledFlag,
    });
    if (res?.success) {
      ElMessage.success(t('global:modify.success'));
    }
    emits('refresh');
  }

  async function getMsChargeTypeList() {
    const params = {
      codeSystemCodes: [MS_CHARGE_TYPE_CODE_NAME],
      enabledFlag: FLAG.YES,
    };
    const [, res] = await queryDataSetByCodeSystemCodes2(params);

    if (res?.success) {
      const { data } = res;
      if (data[MS_CHARGE_TYPE_CODE_NAME]?.length) {
        msChargeTypeOptions.value = data[MS_CHARGE_TYPE_CODE_NAME].map(
          ({ dataValueNo, dataValueNameDisplay }) => ({
            label: dataValueNameDisplay ?? '',
            value: dataValueNo ?? '',
          }),
        );
      }
    }
  }

  onBeforeMount(() => {
    getMsChargeTypeList();
  });
</script>

<template>
  <div>
    <el-collapse v-model="activeName">
      <el-collapse-item
        v-for="(item, ind) in listComputed"
        :key="`${item.msChargeTypeCode}-${ind}`"
        :name="ind"
        :icon="CaretRight"
        class="collapse-item"
      >
        <template #title>
          <!-- 使用状态角标 -->
          <div v-if="item.usingFlag" class="absolute -rotate-45 text-center">
            <p
              class="inline-block h-6 w-28 translate-x-[-16px] translate-y-[-40px] bg-[#24b068] p-0 text-center text-sm leading-6 text-white"
            >
              {{ $t('system.msChargeSetting.using', '使用中') }}
            </p>
          </div>
          <ProForm
            @click.stop
            class="pl-20"
            :key="ind"
            layout-mode="inline"
            v-model="item.baseForm"
            :data="formConfigs[ind]"
          ></ProForm>
        </template>
        <!-- 计费明细表格 -->
        <ChargeSettingListTable
          :key="ind"
          :data="item.tableData"
          :contain-range-column="item.containRangeColumn"
          :ms-charget-object-list-map="msChargetObjectListMap"
        ></ChargeSettingListTable>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<style lang="scss" scoped>
  .collapse-item :deep(.el-collapse-item__header) {
    position: relative;
    overflow: hidden;
    background: var(--el-fill-color-light);
  }

  .collapse-item :deep(.el-date-editor .el-range-input) {
    // daterange 组件 el-range-input 无法完整显示问题
    width: 42%;
  }
</style>
