<script setup lang="ts">
  import { computed, onBeforeMount, reactive, ref, useTemplateRef } from 'vue';
  import { ProForm, useDataChangeDetector, getSystemTime } from 'sun-biz';
  import { dayjs, ElMessage, FormInstance } from 'element-sun';
  import { FLAG, FLAG_STR, MS_CHARGE_TYPE_CODE } from '@sun-toolkit/enums';
  import { useTranslation } from 'i18next-vue';
  import { useChargeSettingForm } from '../../config/useChargeSettingForm';
  import ChargeSettingEditableTable from './chargeSettingEditableTable.vue';
  import {
    addMsChargeSetting,
    queryHospitalChargeItemListByExample,
  } from '@/modules/system/api/msChargeSetting';

  const props = defineProps<{
    // 计费对象列表
    msChargeObjectList: MsChargeSetting.MsChargeTypeSupported[];
    hospitalidMsid: Pick<
      MsChargeSetting.IServiecInfoHospitalIdMsId,
      'hospitalId' | 'msId'
    >;
  }>();
  const emit = defineEmits(['clean']);
  const { t } = useTranslation();
  const tableEditedRef = useTemplateRef<{
    proTableRef: {
      validateRow: (index: string, callback?: (valid: boolean) => void) => void;
      formRef: FormInstance;
    };
  }>('tableEdited');
  const formConfig = useChargeSettingForm();
  const baseForm = reactive<MsChargeSetting.MsChargeSelectForm>({
    csTypeCode: MS_CHARGE_TYPE_CODE.NO_COUNT, // 计费方式（默认不计费）
    startAt: '', // 生效日期(默认当前日期)
    state: FLAG.YES, // 状态（默认启用）
  });
  const tableData = ref<MsChargeSetting.MsChargeTableItem[]>([]);
  const { resetDetector } = useDataChangeDetector([tableData]);
  const costMode = ref<MS_CHARGE_TYPE_CODE>(MS_CHARGE_TYPE_CODE.NO_COUNT);
  // 包含区间设置的计费方式集合
  const MS_CHARGE_TYPE_CONTAIN_RANGES = [
    MS_CHARGE_TYPE_CODE.EXAM_COUNT_RANGE,
    MS_CHARGE_TYPE_CODE.TEST_COUNT_RANGE,
  ];
  // 包含区间选项
  const containRangeColumn = computed(() =>
    MS_CHARGE_TYPE_CONTAIN_RANGES.includes(costMode.value),
  );
  /**
   * 计费对象Map<msChargeTypeCode, Set<msChargeObjectTypeCode>>
   * 计费对象Map<计费对象类型Code, Set<计费列表项 code>>
   */
  const msChargetObjectListMap = computed(() => {
    const map = new Map<string, Set<string>>();

    props.msChargeObjectList.forEach(
      ({ msChargeTypeCode, msChargeObjectList }) => {
        const set = new Set<string>();
        msChargeObjectList?.forEach(({ msChargeObjectTypeCode }) =>
          set.add(msChargeObjectTypeCode),
        );

        map.set(msChargeTypeCode, set);
      },
    );

    return map;
  });
  const chargeItems = ref<MsChargeSetting.ListOfPaidDrugs[]>([]);
  const saveLoading = ref(false);

  function handleModelChange(model: MsChargeSetting.MsChargeSelectForm) {
    // 清除表单校验
    tableEditedRef.value?.proTableRef.formRef.resetFields();
    const csTypeCode = model.csTypeCode;
    costMode.value = model.csTypeCode as MS_CHARGE_TYPE_CODE;
    const chargeObject = props.msChargeObjectList.filter(
      ({ msChargeTypeCode }) => msChargeTypeCode === csTypeCode,
    )[0];
    if (chargeObject) {
      const { msChargeTypeCode, msChargeTypeDesc, msChargeObjectList } =
        chargeObject;
      if (msChargeObjectList?.length) {
        tableData.value = msChargeObjectList.map((item) => ({
          msChargeTypeCode,
          msChargeTypeDesc,
          editable: true,
          msChargeObjectId: item.msChargeObjectId,
          msChargeObjectName: item.msChargeObjectName,
          msChargeObjectTypeCode: item.msChargeObjectTypeCode,
          msChargeObjectTypeDesc: item.msChargeObjectTypeDesc,
          num: FLAG.YES, // 数量默认为 1
        }));
        // 包含区间设置
        if (containRangeColumn.value) {
          // 额外添加一行：包含【添加区间】按钮
          const msChargeObjectOneItem = msChargeObjectList[0];
          tableData.value.push({
            msChargeTypeCode,
            msChargeTypeDesc,
            containAddButton: true, // 是否包【含添加区间】按钮
            msChargeObjectId: msChargeObjectOneItem.msChargeObjectId,
            msChargeObjectName: msChargeObjectOneItem.msChargeObjectName,
            msChargeObjectTypeCode:
              msChargeObjectOneItem.msChargeObjectTypeCode,
            msChargeObjectTypeDesc:
              msChargeObjectOneItem.msChargeObjectTypeDesc,
          });
        }
      }
    }

    resetDetector();
  }

  // 获取【收费项目】列表
  async function queryChargeItems() {
    const [, res] = await queryHospitalChargeItemListByExample({
      hospitalId: props.hospitalidMsid.hospitalId,
      priceAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      encounterTypeCode: FLAG_STR.YES,
    });
    if (res?.success) {
      chargeItems.value = res?.data || [];
    }
  }

  function cancel() {
    emit('clean');
  }

  async function save() {
    saveLoading.value = true;
    // 不计费
    if (baseForm.csTypeCode === MS_CHARGE_TYPE_CODE.NO_COUNT) {
      const params = {
        msChargeTypeCode: baseForm.csTypeCode,
        msId: props.hospitalidMsid.msId,
        /** 获取服务器时间 */
        startAt: baseForm.startAt,
      };
      const [, res] = await addMsChargeSetting(params);
      if (res?.success) {
        ElMessage.success(t('global:add.success'));
      }
      saveLoading.value = false;
      emit('clean');
      return;
    }

    tableEditedRef.value?.proTableRef.formRef.validate(async (valid) => {
      if (valid) {
        // 表单验证通过
        const params: MsChargeSetting.MsChargeSaveReqParam = {
          msChargeTypeCode: baseForm.csTypeCode,
          msId: props.hospitalidMsid.msId,
          startAt: baseForm.startAt,
          msChargeTypeDtList: [],
        };
        let msChargeTypeDtList: MsChargeSetting.msChargeTypeDtItem[] = [];
        const csObjectSet = msChargetObjectListMap.value.get(
          baseForm.csTypeCode,
        );
        csObjectSet?.values().forEach((msChargeObjectTypeCode) => {
          // 获取计费对象设置列表
          const listOfMsChargeObject = tableData.value.filter(
            (item) => item.msChargeObjectTypeCode === msChargeObjectTypeCode,
          );
          if (!containRangeColumn.value) {
            const csObjectItem = {
              msChargeObjectTypeCode,
              msChargeObjectId: listOfMsChargeObject[0].msChargeObjectId,
              // 计费明细
              msChargeDtList: listOfMsChargeObject.map((item) => {
                return {
                  hospitalCommodityId: item.hospitalCommodityId!,
                  commodityId: item.commodityId!,
                  num: item.num!,
                };
              }),
            };
            msChargeTypeDtList.push(csObjectItem);
          } else {
            // 获取当前计费对象设置列表的 区间集合
            const splitStr = '-';
            const map = new Map<string, MsChargeSetting.msChargeTypeDtItem>();
            listOfMsChargeObject.forEach(
              ({
                rangeUpperNum,
                rangeLowerNum,
                hospitalCommodityId,
                commodityId,
                num,
              }) => {
                if (rangeLowerNum && rangeUpperNum) {
                  const key = `${rangeLowerNum}${splitStr}${rangeUpperNum}`;
                  const rangeList = map.get(key)?.msChargeDtList || [];
                  map.set(key, {
                    msChargeObjectTypeCode,
                    msChargeObjectId: listOfMsChargeObject[0].msChargeObjectId,
                    rangeLowerNum,
                    rangeUpperNum,
                    msChargeDtList: [
                      ...rangeList,
                      {
                        hospitalCommodityId: hospitalCommodityId!,
                        commodityId: commodityId!,
                        num: num!,
                      },
                    ],
                  });
                }
              },
            );
            msChargeTypeDtList = [...map.values()];
          }
        });
        params.msChargeTypeDtList = msChargeTypeDtList;
        const [, res] = await addMsChargeSetting(params);
        if (res?.success) {
          ElMessage.success(t('global:add.success'));
        }
        emit('clean');
        saveLoading.value = false;
      } else {
        saveLoading.value = false;
      }
    });
  }

  onBeforeMount(async () => {
    await queryChargeItems();
    baseForm.startAt = (await getSystemTime()) as string;
  });
</script>

<template>
  <div>
    <div class="bg-[#f5f7fa] p-2 pl-20">
      <ProForm
        v-model="baseForm"
        :data="formConfig"
        @model-change="handleModelChange"
      ></ProForm>
    </div>
    <div class="relative min-h-[200] border">
      <ChargeSettingEditableTable
        class="mb-20"
        v-if="costMode !== MS_CHARGE_TYPE_CODE.NO_COUNT"
        :ms-charget-object-list-map="msChargetObjectListMap"
        ref="tableEdited"
        v-model:data="tableData"
        v-model:charge-items="chargeItems"
        v-model:contain-range-column="containRangeColumn"
      ></ChargeSettingEditableTable>
      <div class="absolute bottom-5 right-5">
        <el-button @click="cancel">{{ $t('global:cancel') }}</el-button>
        <el-button :loading="saveLoading" type="primary" @click="save">{{
          $t('global:save')
        }}</el-button>
      </div>
    </div>
  </div>
</template>
