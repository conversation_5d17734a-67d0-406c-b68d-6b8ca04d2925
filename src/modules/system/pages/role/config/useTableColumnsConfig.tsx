import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import { Ref } from 'vue';
import { FLAG } from '@/utils/constant.ts';

export function useTableColumnsConfig(options: {
  availableUsers: Ref<User.Item[]>;
  saveEdit: (row: Role.UserRoleList) => Promise<void>;
  deleteEdit: (row: Role.UserRoleList) => Promise<void>;
  tableRef: Ref<TableRef>;
  tableData: Ref<Role.UserRoleList[]>;
  fetchUserList: (data: { keyWord: string }) => Promise<void>;
}) {
  const {
    availableUsers,
    saveEdit,
    deleteEdit,
    tableRef,
    tableData,
    fetchUserList,
  } = options;
  const { cancelEdit, addItem, delItem } = useEditableTable({
    tableRef,
    data: tableData as Ref<{ [key: string]: unknown; editable: boolean }[]>,
    id: 'userRoleId',
  });
  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        prop: 'selection',
        editable: false,
        type: 'selection',
      },
      {
        label: t('global:sequence'),
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('userRoleList.userNo', '用户编码'),
        prop: 'userNo',
        minWidth: 150,
      },
      {
        label: t('userRoleList.userName', '用户名称'),
        prop: 'userName',
        minWidth: 150,
        editable: true,
        render: (row: Role.UserRoleList) => {
          if (row.editable) {
            return (
              <div class="flex items-center justify-around">
                <el-select
                  value-key={'userId'}
                  v-model={row.userId}
                  remote={true}
                  remoteShowSuffix={true}
                  clearable={true}
                  filterable
                  remoteMethod={(keyWord: string) => {
                    fetchUserList({
                      keyWord: keyWord,
                    });
                  }}
                  onChange={(val: string) => {
                    const tmp = availableUsers?.value?.find(
                      (item: User.Item) => item.userId === val,
                    );
                    if (tmp) {
                      row.userId = tmp.userId;
                      row.userNameDisPlay = tmp.userName;
                      row.userNo = tmp.userNo;
                      row.userJobDesc = tmp.userJobDesc;
                      row.enabledFlag = tmp.enabledFlag;
                      row.appReleaseVersionDesc = tmp.appReleaseVersionDesc;
                    }
                  }}
                >
                  {{
                    default: () =>
                      availableUsers.value?.map((item: User.Item) => (
                        <el-option
                          key={item.userId}
                          label={item.userName}
                          value={item.userId}
                        />
                      )),
                  }}
                </el-select>
              </div>
            );
          } else {
            return (
              <div class="flex items-center justify-around">
                {row.userNameDisPlay}
              </div>
            );
          }
        },
      },
      {
        label: t('userRoleList.userJobDesc', '用户岗位'),
        prop: 'userJobDesc',
        minWidth: 120,
      },
      {
        label: t('userRoleList.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (
          row: Role.UserRoleList & {
            editable: boolean;
          },
        ) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              disabled
              before-change={async () => {
                if (row.enabledFlag === FLAG.YES) {
                  row.enabledFlag = FLAG.NO;
                } else {
                  row.enabledFlag = FLAG.YES;
                }
              }}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('userRoleList.appReleaseVersionDesc', '程序版本'),
        prop: 'appReleaseVersionDesc',
        minWidth: 120,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 150,
        fixed: 'right',
        render: (row: Role.UserRoleList, $index: number) => {
          if (row.editable) {
            return (
              <div class="flex items-center justify-around" key="editable">
                <el-button
                  type="danger"
                  link={true}
                  onClick={() => cancelEdit(row, $index)}
                >
                  {t('global:cancel')}
                </el-button>
                <el-button
                  type="primary"
                  link={true}
                  onClick={() => saveEdit(row)}
                >
                  {t('global:save')}
                </el-button>
              </div>
            );
          } else {
            return (
              <div class="flex items-center justify-around">
                <el-button
                  type="danger"
                  link={true}
                  onClick={() => deleteEdit(row)}
                >
                  {t('global:delete')}
                </el-button>
              </div>
            );
          }
        },
      },
    ],
  });
  return { tableColumns, addItem, delItem };
}
