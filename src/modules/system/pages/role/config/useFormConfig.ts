import { useFormConfig } from 'sun-biz';
import { APP_RELEASE_VERSION_CODE_NAME } from '@/utils/constant.ts';

export function useUserRoleListFormConfig(
  filterUserRoleList: (data?: { keyWord?: string }) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-60',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              filterUserRoleList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            filterUserRoleList({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}

export function useAppReleaseVersionCodeFormConfig() {
  const dataSetCodes = [APP_RELEASE_VERSION_CODE_NAME];
  const data = useFormConfig({
    dataSetCodes,
    getData: (t, dataSet) => [
      {
        name: 'appReleaseVersionCode',
        label: t('userRoleList.selectVersion', '程序版本'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('userRoleList.selectVersion', '程序版本'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('userRoleList.selectVersion', '程序版本'),
            }),
            trigger: 'change',
          },
        ],
        triggerModelChange: true,
        extraProps: {
          options: dataSet?.value
            ? dataSet.value[APP_RELEASE_VERSION_CODE_NAME]
            : [],
          filterable: true,
        },
      },
    ],
  });
  return data;
}
