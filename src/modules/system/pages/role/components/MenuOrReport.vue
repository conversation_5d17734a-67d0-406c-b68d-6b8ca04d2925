<script lang="tsx" setup>
  import { ref } from 'vue';
  import SystemMenu from './SystemMenu.vue';
  import UserList from './UserList.vue';
  import { useTranslation } from 'i18next-vue';

  const { t } = useTranslation();
  const MENU = 'MENU';
  const REPORT = 'REPORT';
  const USER = 'USER';
  const activeName = ref('MENU');
  const systemMenuRef = ref();
  const userListRef = ref();
  type Props = {
    selectRoleRow: Role.SystemRoleInfo | null;
    refreshRoleList: () => void;
    changeModifiedStatus: (value: boolean) => void;
  };
  const props = defineProps<Props>();
  defineExpose({
    saveClick: () => {
      systemMenuRef.value?.saveClick();
    },
  });
</script>
<template>
  <!-- 你的模板内容 -->

  <el-tabs
    v-model="activeName"
    class="flex h-full flex-1 overflow-hidden p-[1px]"
    type="card"
  >
    <el-tab-pane
      :label="t('menuOrReport.tabs.menu', '系统菜单')"
      :name="MENU"
      class="flex h-full flex-1"
    >
      <SystemMenu
        ref="systemMenuRef"
        :change-modified-status="props.changeModifiedStatus"
        :refresh-role-list="props.refreshRoleList"
        :select-role-row="props.selectRoleRow"
      />
    </el-tab-pane>
    <el-tab-pane
      :label="t('menuOrReport.tabs.report', '系统报表')"
      :name="REPORT"
      disabled
      >Config
    </el-tab-pane>
    <el-tab-pane
      :label="t('menuOrReport.tabs.userList', '用户列表')"
      :name="USER"
      class="flex h-full flex-1"
      lazy
    >
      <UserList
        v-if="activeName === USER"
        ref="userListRef"
        :active-name="activeName"
        :change-modified-status="props.changeModifiedStatus"
        :refresh-role-list="props.refreshRoleList"
        :select-role-row="props.selectRoleRow"
      />
    </el-tab-pane>
  </el-tabs>
</template>
