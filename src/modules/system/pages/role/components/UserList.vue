<script lang="ts" name="userList" setup>
  import { onBeforeMount, onMounted, Ref, ref, watch } from 'vue';
  import {
    addSystemUserRole,
    deleteUserRoleByIds,
    queryUserRoleListByRoleId,
    updateAppReleaseVersionCodeByUserIds,
  } from '@/modules/system/api/menu';
  import { APP_RELEASE_VERSION_CODE } from '@/utils/constant';
  import { ProForm, ProTable } from 'sun-biz';
  import {
    useAppReleaseVersionCodeFormConfig,
    useUserRoleListFormConfig,
  } from '@/modules/system/pages/role/config/useFormConfig.ts';
  import { queryUserList } from '@/modules/system/api/user';
  import { useTableColumnsConfig } from '@/modules/system/pages/role/config/useTableColumnsConfig.tsx';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';

  type Props = {
    selectRoleRow: Role.SystemRoleInfo | null;
    activeName: string;
    refreshRoleList: () => void;
    changeModifiedStatus: (value: boolean) => void;
  };
  const { t } = useTranslation();
  const selections = ref<Role.UserRoleList[]>([]); // 选中的行
  const props = defineProps<Props>();
  const userRoleListForm = ref({
    keyword: '',
  });
  const appReleaseVersionFormRef = ref(); // 程序版本表单引用
  const appReleaseVersionForm = ref({
    appReleaseVersionCode: APP_RELEASE_VERSION_CODE.RELEASE, // 程序版本
  }); // 程序版本
  const dialogVisible = ref(false); // 程序版本
  const tableRef = ref();
  const userRoleListSource = ref<Role.UserRoleList[]>([]);
  const userRoleList = ref<Role.UserRoleList[]>([]);
  const userList = ref<User.Item[]>([]);
  const availableUsers = ref<User.Item[]>([]);
  const queryUserRoleList = async () => {
    const [, res] = await queryUserRoleListByRoleId({
      roleIds: props.selectRoleRow?.roleId.split(',') || [],
    });
    if (res?.success) {
      userRoleListSource.value = (res.data || []) as Role.UserRoleList[];
      filterUserRoleList(); // 重新应用过滤
      // 重置选择状态
      selections.value = [];
      tableRef.value?.proTableRef.clearSelection();
    } else {
      userRoleListSource.value = [];
      userRoleList.value = [];
      selections.value = [];
      tableRef.value?.proTableRef.clearSelection();
    }
  };
  const fetchUserList = async (data?: { keyWord: string }) => {
    let defaultParams = {
      pageNumber: 1,
      pageSize: 200,
      enabledFlag: 1,
      hospitalId: props.selectRoleRow?.hospitalId || '',
      keyWord: '',
    };
    if (data?.keyWord) {
      defaultParams = {
        ...defaultParams,
        keyWord: data.keyWord || '',
      };
    }
    const [, res] = await queryUserList(defaultParams);
    if (res?.success) {
      userList.value = Array.isArray(res.data) ? res.data : [];
      // 获取已存在的用户ID列表
      const existingUserIds = userRoleListSource.value.map(
        (item) => item.userId,
      );

      // 过滤出未添加的用户
      availableUsers.value = userList.value.filter(
        (user) => !existingUserIds.includes(user.userId),
      );
    }
  };
  const filterUserRoleList = (data?: { keyWord?: string }) => {
    if (data?.keyWord) {
      userRoleList.value = userRoleListSource.value.filter((item) => {
        return (
          (item.userNo || '').includes(data.keyWord || '') ||
          (item.userNameDisPlay || '').includes(data.keyWord || '')
        );
      });
    } else {
      userRoleList.value = userRoleListSource.value;
    }
  };
  const openBatchAddDialog = () => {
    if (!canUpsertTableRow()) return;
    appReleaseVersionForm.value.appReleaseVersionCode =
      APP_RELEASE_VERSION_CODE.RELEASE; // 程序版本
    dialogVisible.value = true;
  };
  const handleBatchAdd = async () => {
    appReleaseVersionFormRef.value?.ref.validate(async (valid) => {
      if (valid) {
        const [, res] = await updateAppReleaseVersionCodeByUserIds({
          userIds: selections.value.map((item) => item.userId || ''),
          appReleaseVersionCode:
            appReleaseVersionForm.value.appReleaseVersionCode,
        });
        if (res?.success) {
          queryUserRoleList();
          props.refreshRoleList();
          dialogVisible.value = false;
          selections.value = [];
        }
      }
    });
  };
  const addNew = () => {
    if (!canUpsertTableRow()) return;
    fetchUserList();

    const newUserRole = {
      userId: '',
      userNameDisPlay: '',
      userNo: '',
      userJobDesc: '',
      enabledFlag: undefined,
      appReleaseVersionDesc: '',
      editable: true,
    };
    addItem(newUserRole);
  };
  const saveEdit = async (row: Role.UserRoleList) => {
    const [, res] = await addSystemUserRole({
      userId: row.userId,
      roleId: props.selectRoleRow?.roleId || '',
    });
    if (res?.success) {
      row.editable = false;
      queryUserRoleList();
      props.refreshRoleList();
    }
  };
  const deleteEdit = async (row: Role.UserRoleList, index: number) => {
    if (!canUpsertTableRow()) return;

    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} "{{name}}" 吗？', {
        action: t('global:delete', '删除'),
        name: row.userNameDisPlay,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await deleteUserRoleByIds({
        userRoleId: row.userRoleId,
      });
      if (res?.success) {
        delItem(index);
        queryUserRoleList();
        props.refreshRoleList();
      }
    });
  };
  const canUpsertTableRow = () => {
    const isEditing = userRoleList.value.some((item) => !!item.editable);
    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    } else {
      return true;
    }
  };
  const handleDialogClose = () => {
    dialogVisible.value = false;
    appReleaseVersionForm.value.appReleaseVersionCode = '';
  };
  //获取选中数据
  const selectionChange = (val: Role.UserRoleList[]) => {
    selections.value = val;
  };
  const userRoleListFormConfig = useUserRoleListFormConfig(filterUserRoleList);
  const appReleaseVersionCodeFormConfig = useAppReleaseVersionCodeFormConfig();
  // 表格配置数据
  const { tableColumns, addItem, delItem } = useTableColumnsConfig({
    availableUsers: availableUsers as Ref<User.Item[]>,
    saveEdit: (row: Role.UserRoleList) => saveEdit(row),
    deleteEdit: (row: Role.UserRoleList) => deleteEdit(row, 0),
    tableRef: tableRef as Ref<InstanceType<typeof ProTable>>,
    tableData: userRoleList as Ref<Role.UserRoleList[]>,
    fetchUserList: fetchUserList,
  });
  watch(
    () => props.selectRoleRow,
    () => {
      console.log('activeName changed', props.activeName);
      if (props.activeName === 'USER') {
        queryUserRoleList();
      }
    },
    { deep: true },
  );
  onBeforeMount(() => {
    console.log('selectRoleRow', props.selectRoleRow);
    // queryUserRoleList();
  });
  onMounted(() => {
    queryUserRoleList();
  });
</script>

<template>
  <div class="flex w-full flex-1 flex-col">
    <div class="flex items-center justify-between">
      <ProForm
        ref="formRef"
        v-model="userRoleListForm"
        :column="2"
        :data="userRoleListFormConfig"
      />
      <div class="mb-4 flex items-center justify-end">
        <el-button
          :disabled="selections.length === 0"
          type="primary"
          @click="openBatchAddDialog"
        >
          {{ $t('userRoleList.batchAddDialogTitle', '批量修改程序版本') }}
        </el-button>
        <el-button type="primary" @click="addNew">
          {{ $t('userRoleList.add', '新增') }}
        </el-button>
      </div>
    </div>

    <ProTable
      ref="tableRef"
      :columns="tableColumns"
      :data="userRoleList"
      :editable="true"
      :loading="false"
      row-key="userRoleId"
      @selection-change="selectionChange"
    />

    <el-dialog
      v-model="dialogVisible"
      :title="$t('userRoleList.batchAddDialogTitle', '批量修改程序版本')"
      width="400px"
      @close="handleDialogClose"
    >
      <div class="p-4">
        <div class="mb-4 flex items-center justify-center">
          <ProForm
            ref="appReleaseVersionFormRef"
            v-model="appReleaseVersionForm"
            :column="1"
            :data="appReleaseVersionCodeFormConfig"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">
            {{ $t('global:cancel', '取消') }}
          </el-button>
          <el-button type="primary" @click="handleBatchAdd">
            {{ $t('global:confirm', '确定') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
