<script setup lang="ts" name="systemLog">
  import { isEqual } from 'es-toolkit';
  import { useTranslation } from 'i18next-vue';
  import { ref, onMounted, computed } from 'vue';
  import { Menu, List } from '@element-sun/icons-vue';
  import { downloadFile } from '@sun-toolkit/shared';
  import { useGetUserInfo } from '@/hooks/useGetUserList.ts';
  import { useLogTableConfig } from './config/useTableConfig.tsx';
  import { useSearchFormConfig } from './config/useSearchFormConfig.tsx';
  import {
    queryLogListByExample,
    downloadLogByExample,
  } from '@/modules/system/api/systemLog.ts';
  import {
    Title,
    ProTable,
    ProForm,
    MAIN_APP_CONFIG,
    useAppConfigData,
  } from 'sun-biz';
  import DetailDialog from './components/detailDialog.vue';
  import TreeStructure from './components/treeStructure.vue';

  export type FormModelType = {
    userId?: string;
    logGenerateDate?: string[]; //同时
    clientTraceId?: string;
    traceId?: string;
    invokeUseTimeLower?: number;
    clientIpAddress?: string;
    serverIpAddress?: string;
    logTypeCode?: string;
    logWriterTypeCode?: string;
    logWriterName?: string;
    logKeyword?: string;
  };

  const { t } = useTranslation();
  const { userList, getUserList } = useGetUserInfo();
  const { currentOrg } = useAppConfigData([MAIN_APP_CONFIG.CURRENT_ORG]);
  const orgId = computed(() => currentOrg?.orgId);

  const detailRef = ref();
  const searchFormRef = ref();
  const loading = ref(false);
  const isTree = ref(false); //是否是树结构
  const logTableData = ref<SystemLog.LogReqItem[]>([]);
  const rowValue = ref<SystemLog.LogReqItem>();
  const oldFromSearchModel = ref<FormModelType>();
  const searchModel = ref<FormModelType>({
    userId: undefined,
    logGenerateDate: undefined,
    clientTraceId: undefined,
    traceId: undefined,
    invokeUseTimeLower: undefined,
    clientIpAddress: undefined,
    serverIpAddress: undefined,
    logTypeCode: undefined,
    logWriterTypeCode: undefined,
    logWriterName: undefined,
    logKeyword: undefined,
  });
  const pageInfoParams = ref({
    pageNumber: 1,
    pageSize: 25,
    total: 0,
  });

  /** 初始化pageInfoParams */
  const initPageInfoParams = async () => {
    pageInfoParams.value = { pageNumber: 1, pageSize: 25, total: 0 };
  };

  /** 查询条件变化 */
  const modelChangeSearch = async (data: FormModelType) => {
    searchModel.value = {
      ...searchModel.value,
      ...data,
    };
    await initPageInfoParams();
    await queryFn();
  };

  /** 获取日志列表 */
  const queryFn = async () => {
    await searchFormRef.value?.ref?.validate();
    loading.value = true;
    const [, res] = await queryLogListByExample({
      pageNumber: pageInfoParams.value.pageNumber,
      pageSize: pageInfoParams.value.pageSize,
      ...searchModel.value,
      logGenerateBeginAt:
        (searchModel.value.logGenerateDate ?? [])[0] ?? undefined,
      logGenerateEndAt:
        (searchModel.value.logGenerateDate ?? [])[1] ?? undefined,
      invokeUseTimeLower: searchModel.value.invokeUseTimeLower
        ? Number(searchModel.value.invokeUseTimeLower)
        : undefined,
    });
    loading.value = false;
    if (res?.success) {
      logTableData.value = res?.data ?? [];
      pageInfoParams.value.total = res?.total ?? 0;
    }
  };

  /** 清空 */
  const reset = async () => {
    await initPageInfoParams();
    searchModel.value = {
      userId: undefined,
      logGenerateDate: undefined,
      clientTraceId: undefined,
      traceId: undefined,
      invokeUseTimeLower: undefined,
      clientIpAddress: undefined,
      serverIpAddress: undefined,
      logTypeCode: undefined,
      logWriterTypeCode: undefined,
      logWriterName: undefined,
      logKeyword: undefined,
    };
    await queryFn();
  };

  /** 下载 */
  const downloadLog = async () => {
    await searchFormRef.value?.ref?.validate();
    const [, res] = await downloadLogByExample({
      userId: searchModel.value?.userId ?? undefined,
      logGenerateBeginAt:
        (searchModel.value.logGenerateDate ?? [])[0] ?? undefined,
      logGenerateEndAt:
        (searchModel.value.logGenerateDate ?? [])[1] ?? undefined,
      clientTraceId: searchModel.value?.clientTraceId ?? undefined,
      traceId: searchModel.value?.traceId ?? undefined,
      invokeUseTimeLower: searchModel.value.invokeUseTimeLower
        ? Number(searchModel.value.invokeUseTimeLower)
        : undefined,
      clientIpAddress: searchModel.value?.clientIpAddress ?? undefined,
      serverIpAddress: searchModel.value?.serverIpAddress ?? undefined,
      logTypeCode: searchModel.value?.logTypeCode ?? undefined,
      logWriterTypeCode: searchModel.value?.logWriterTypeCode ?? undefined,
      logWriterName: searchModel.value?.logWriterName ?? undefined,
      logKeyword: searchModel.value?.logKeyword ?? undefined,
    });
    if (res?.success) {
      downloadFile({
        data: res?.data?.logFile,
        fileName: t('systemLog', '系统日志'),
      });
    }
  };

  /** 详情 */
  const handleDetail = async (row: SystemLog.LogReqItem) => {
    rowValue.value = row;
    detailRef.value.open();
  };

  /** 树结构和table结构切换 */
  const structureChange = async () => {
    if (loading.value === true) return;
    if (oldFromSearchModel.value) {
      const isSame = isEqual(oldFromSearchModel.value, searchModel.value);
      if (!isSame) {
        await queryFn();
        oldFromSearchModel.value = { ...searchModel.value };
      }
    } else {
      oldFromSearchModel.value = { ...searchModel.value };
    }
    isTree.value = !isTree.value;
  };

  const searchFormConfig = useSearchFormConfig({
    userList,
    getUserList,
    orgId,
    searchModel,
  });
  const logTableConfig = useLogTableConfig({ handleDetail });

  onMounted(async () => {
    // 数据量太大，初始不允许查数据,直接拿tranceId跟踪id去查询指定的
    // await queryFn();
  });
</script>
<template>
  <div class="p-box z-20 flex h-full flex-col overflow-hidden bg-white">
    <div :class="{ 'sticky top-0 z-20 bg-white': isTree }">
      <Title :title="$t('log.list', '日志列表')" class="mb-2">
        <div @click="structureChange" class="cursor-pointer">
          <el-icon v-if="isTree" :size="24"><List /></el-icon>
          <el-icon v-else :size="24"><Menu /></el-icon>
        </div>
      </Title>
      <!-- 查询条件 -->
      <ProForm
        ref="searchFormRef"
        class="flex flex-1 flex-wrap"
        layout-mode="inline"
        :data="searchFormConfig"
        v-model="searchModel"
        @model-change="modelChangeSearch"
      >
        <div class="flex flex-1 justify-end">
          <div>
            <el-button type="primary" @click="queryFn">{{
              $t('global:query')
            }}</el-button>
            <el-button type="primary" @click="reset">{{
              $t('global:clearAll')
            }}</el-button>
            <el-button type="primary" @click="downloadLog">{{
              $t('download', '下载')
            }}</el-button>
          </div>
        </div>
      </ProForm>
    </div>

    <DetailDialog :app-log-id="rowValue?.appLogId" ref="detailRef" />

    <!-- 日志列表 -->
    <div class="flex h-full flex-col overflow-hidden" v-if="!isTree">
      <ProTable
        row-key="appLogId"
        :columns="logTableConfig"
        :data="logTableData"
        :pagination="true"
        :loading="loading"
        :page-info="pageInfoParams"
        :tree-props="{
          children: 'subLogList',
        }"
        @current-page-change="
          (val: number) => {
            pageInfoParams.pageNumber = val;
            queryFn();
          }
        "
        @size-page-change="
          (val: number) => {
            pageInfoParams.pageSize = val;
            queryFn();
          }
        "
      />
    </div>

    <div v-else v-loading="loading" class="h-[calc(100%-300px)] flex-1">
      <el-scrollbar height="100%">
        <div v-if="logTableData.length > 0" class="z-10">
          <div v-for="item in logTableData ?? []" :key="item.appLogId">
            <TreeStructure :data="item" />
          </div>
        </div>
        <el-empty v-else description="暂无数据"></el-empty>
      </el-scrollbar>
    </div>
  </div>
</template>
