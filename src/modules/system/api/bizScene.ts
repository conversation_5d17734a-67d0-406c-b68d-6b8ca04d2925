import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10447-1]	根据条件查询业务场景限定条件
 * @param params
 * @returns
 */
export const queryBizSceneLimitConditionByExample = (
  params: DizLimitCondition.ConditionalParticipation,
) => {
  return dictRequest<DizLimitCondition.RestrictiveConditionsList[]>(
    '/BizScene/queryBizSceneLimitConditionByExample',
    params,
  );
};

/**
 * [1-10436-1]	根据条件查询业务场景的可用限定条件
 * @param params
 * @returns
 */
export const queryBizSceneXLimitConditionByExample = (params: {
  bizSceneCodes?: string[];
  keyWord?: string;
}) => {
  return dictRequest<DizLimitCondition.BusinessRulesList[]>(
    '/BizScene/queryBizSceneXLimitConditionByExample',
    params,
    {
      cancel: false,
    },
  );
};

/**
 * [1-10446-1]	保存业务场景限定条件
 * @param params
 * @returns
 */
export const saveBizSceneLimitCondition = (
  params: DizLimitCondition.RestrictiveConditionsList,
) => {
  return dictRequest<string>('/BizScene/saveBizSceneLimitCondition', params);
};

/**
 * [1-10448-1]	根据标识删除业务场景限定条件
 * @param params
 * @returns
 */
export const deleteBizSceneLimitConditionById = (params: {
  bizSceneLimitConditionId: string;
}) => {
  return dictRequest<string>(
    '/BizScene/deleteBizSceneLimitConditionById',
    params,
  );
};

/**
 * [1-10441-1]	根据条件查询业务场景限制规则
 * @param params
 * @returns
 */
export const queryBizSceneLimitRuleByExample = (params: {
  bizSceneCode: string;
}) => {
  return dictRequest<DizLimitCondition.RuleList[]>(
    '/BizScene/queryBizSceneLimitRuleByExample',
    params,
  );
};

/**
 * [1-10435-1]	根据条件查询业务场景限制规则
 * @param params
 * @returns
 */
export const addBizSceneXLimitCondition = (params: {
  bizSceneCode: string;
  bizSceneLimitConditionId: string;
}) => {
  return dictRequest<DizLimitCondition.RestrictiveConditionsList[]>(
    '/BizScene/addBizSceneXLimitCondition',
    params,
  );
};

/**
 * [1-10441-1]	根据条件查询业务场景限制规则
 * @param params
 * @returns
 */
export const deleteBizSceneXLimitConditionById = (params: {
  bizSceneXConditionId: string;
}) => {
  return dictRequest<DizLimitCondition.RestrictiveConditionsList[]>(
    '/BizScene/deleteBizSceneXLimitConditionById',
    params,
  );
};

/**
 * [1-10442-1]	根据条件标识查询条件可检索数据
 * @param params
 * @returns
 */
export const queryDictDataListByConditionId = (
  params: DizLimitCondition.QueryDictData,
) => {
  return dictRequest<
    DizLimitCondition.RestrictiveConditionsList[] & {
      primaryKey: string;
      displayKey: string;
      data: {
        data: { dataValueNo: string; dataValueNameDisplay: string }[];
      };
    }
  >('/BizScene/queryDictDataListByConditionId', params);
};

/**
 * [1-10439-1]	新增业务场景限制规则
 * @param params
 * @returns
 */
export const addBizSceneLimitRule = (params: DizLimitCondition.RuleList) => {
  return dictRequest<DizLimitCondition.RestrictiveConditionsList[]>(
    '/BizScene/addBizSceneLimitRule',
    params,
  );
};

/**
 * [1-10440-1]	根据标识修改业务场景限制规则
 * @param params
 * @returns
 */
export const updateBizSceneLimitRuleById = (
  params: DizLimitCondition.RuleList,
) => {
  return dictRequest<DizLimitCondition.RestrictiveConditionsList[]>(
    '/BizScene/updateBizSceneLimitRuleById',
    params,
  );
};

/**
 * [1-10445-1]	根据标识停启用业务场景限制规则
 * @param params
 * @returns
 */
export const updateEnableFlagByBizSceneLimitRuleId = (params: {
  bizSceneLimitRuleId?: string;
  enabledFlag?: number;
}) => {
  return dictRequest<string>(
    '/BizScene/updateEnableFlagByBizSceneLimitRuleId',
    params,
  );
};
