import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10017-1]根据条件查询应用系统列表
 * @param data
 * @returns
 */
export const querySystemListByExample = (params: {
  keyWord?: string;
  enabledFlag?: 0 | 1 | undefined;
  accessFlag?: number;
  sysId?: number;
}) => {
  return dictRequest<Menu.SystemInfo[]>(
    '/system/querySystemListByExample',
    params,
    { cancel: false },
  );
};

/**
 * [1-10022-1]
 * @param data 根据条件查询应用菜单列表
 * @returns
 */
export const queryMenuListByExample = (params: {
  sysIds?: string[];
  keyWord?: string;
  enabledFlag?: 0 | 1 | undefined;
  menuId?: number;
  pageNumber: number;
  pageSize: number;
}) => {
  return dictRequest<
    Menu.MenuInfo,
    {
      pageNumber: number;
      pageSize: number;
    }
  >('/menu/queryMenuListByExample', params, {
    cancel: false,
  });
};

/**
 * [1-10018-1]  新增应用系统
 * @param data
 * @returns
 */
export const addSystem = (params: Menu.ResAddSystemParams) => {
  return dictRequest<boolean>('/system/addSystem', params);
};

/**
 * [1-10019-1]  修改应用系统
 * @param data
 * @returns
 */
export const updateSystemById = (params: Menu.ResUpdateSystemParams) => {
  return dictRequest<boolean>('/system/updateSystemById', params);
};

/**
 * [1-10023-1]  新增应用菜单
 * @param data
 * @returns
 */
export const addMenu = (params: Menu.ResAddMenuParams) => {
  return dictRequest<boolean>('/menu/addMenu', params);
};

/**
 * [1-10024-1]  根据标识修改应用菜单
 * @param data
 * @returns
 */
export const updateMenuById = (params: Menu.ResUpdateMenuParams) => {
  return dictRequest<boolean>('/menu/updateMenuById', params);
};

/**
 * [1-10020-1]  根据标识停启用应用系统
 * @param data
 * @returns
 */
export const updateSystemEnabledFlagById = (params: {
  sysId: string;
  enabledFlag: 0 | 1;
}) => {
  return dictRequest<boolean>('/system/updateEnabledFlagById', params);
};

/**
 * [1-10025-1]  	根据标识停启用应用菜单
 * @param data
 * @returns
 */
export const updateMenuEnabledFlagById = (params: {
  menuId: string;
  enabledFlag: 0 | 1;
}) => {
  return dictRequest<boolean>('/menu/updateEnabledFlagById', params);
};

/**
 * [1-10021-1]  	根据标识修改应用系统排序
 * @param data
 * @returns
 */
export const updateSystemSortByIds = (params: {
  sysSortList: { sysId: string; sort: number }[];
}) => {
  return dictRequest<boolean>('/system/updateSystemSortByIds', params);
};

/**
 * [1-10026-1]  	根据条件查询菜单结构列表
 * @param data
 * @returns
 */
export const queryMenuStructListByExample = (params: {
  sysId?: string;
  enabledFlag?: 0 | 1;
  accessFlag?: number;
  userId?: string;
}) => {
  return dictRequest<Menu.SystemInfo[]>(
    '/menustruct/queryMenuStructListByExample',
    params,
  );
};

/**
 * [1-10027-1]  	新增菜单结构
 * @param data
 * @returns
 */
export const addMenuStruct = (params: Menu.ResAddMenuStructParams) => {
  return dictRequest<Menu.SystemInfo[]>('/menustruct/addMenuStruct', params);
};

/**
 * [1-10028-1]  	根据标识修改菜单结构
 * @param data
 * @returns
 */
export const updateMenuStructById = (
  params: Menu.ResUpdateMenuStructByIdParams,
) => {
  return dictRequest<boolean>('/menustruct/updateMenuStructById', params);
};

/**
 * [1-10030-1]  	根据标识移除菜单结构
 * @param data
 * @returns
 */
export const deleteMenuStructById = (params: { sysXMenuId: string }) => {
  return dictRequest<boolean>('/menustruct/deleteMenuStructById', params);
};

/**
 * [1-10029-1]  	根据标识修改菜单结构排序
 * @param data
 * @returns
 */
export const updateSortByIds = (params: {
  sysXMenuList: { sysXMenuId: string; sort: number }[];
}) => {
  return dictRequest<boolean>('/menustruct/updateSortByIds', params);
};

/**
 * [1-10161-1]根据条件查询页面元素列表
 * @param data
 * @returns
 */
export const queryPageElementByExample = (params: {
  keyWord?: string;
  enabledFlag?: number;
  menuIds?: string[] | undefined;
  defaultAllowUseFlag?: number;
  pageNumber: number;
  pageSize: number;
}) => {
  return dictRequest<
    Menu.ElementInfo[],
    { pageNumber: number; pageSize: number }
  >('/pageelement/queryPageElementByExample', params);
};

/**
 * [1-10162-1]根据条件查询页面元素列表
 * @param data
 * @returns
 */
export const addPageElement = (params: Menu.AddElement) => {
  return dictRequest<Menu.ElementInfo[]>('/pageelement/addPageElement', params);
};

/**
 * [1-10163-1]根据标识修改页面元素
 * @param data
 * @returns
 */
export const updatePageElementById = (params: Menu.UpdateElement) => {
  return dictRequest<Menu.ElementInfo[]>(
    '/pageelement/updatePageElementById',
    params,
  );
};

/**
 * [1-10164-1]根据标识集合修改页面元素排序
 * @param data
 * @returns
 */
export const updatePageElementSortByIds = (params: {
  pageElementList: {
    pageElementId: string;
    sort: number;
  }[];
}) => {
  return dictRequest<Menu.ElementInfo[]>(
    '/pageelement/updatePageElementSortByIds',
    params,
  );
};

/**
 * [1-10165-1]根据标识修改页面元素
 * @param data
 * @returns
 */
export const updatePageElementEnabledFlagById = (params: {
  enabledFlag: number;
  pageElementId: string;
}) => {
  return dictRequest<boolean>(
    '/pageelement/updatePageElementEnabledFlagById',
    params,
  );
};

/**
 * [1-10226-1]根据条件查询代码仓库
 * @param data
 * @returns
 */
export const queryCodeRepositoryByExample = (params: {
  keyWord?: string;
  enabledFlag?: number;
  codeRepositoryTypeCode?: string;
}) => {
  return dictRequest<Menu.CodeRepositoryInfo[]>(
    '/codeRepository/queryCodeRepositoryByExample',
    params,
  );
};

/**
 * [1-10248-1]保存系统定义
 * @param data
 * @returns
 */
export const saveSystemDefinition = (params: {
  sysId: string;
  codeRepositoryList?: {
    systemDefId?: string;
    codeRepositoryId: string;
  }[];
}) => {
  return dictRequest('/system/saveSystemDefinition', params);
};

/**
 * [1-10243-1]  	根据条件查询医院系统对应关系
 * @param data
 * @returns
 */
export const queryHospitalXSyetemByExample = (params: {
  hospitalId?: string;
}) => {
  return dictRequest<Menu.HospitalXSysInfo[]>(
    '/system/queryHospitalXSyetemByExample',
    params,
  );
};

/**
 * [1-10254-1]保存医院的系统信息
 * @param data
 * @returns
 */
export const saveHospitalXSyetem = (params: {
  hospitalId: string;
  hospitalXSysList?: {
    sysId: string;
    sort: number;
  }[];
}) => {
  return dictRequest('/system/saveHospitalXSyetem', params);
};

/**
 * [1-10466-1] 根据条件查询角色的用户列表
 * @param data
 * @returns
 */
export const queryUserRoleListByRoleId = (params?: Role.UserRoleList) => {
  return dictRequest('/systemRole/queryUserRoleListByRoleId', params);
};

/**
 * [1-10465-1] 新增角色与用户关系
 * @param data
 * @returns
 */
export const addSystemUserRole = (params: {
  roleId?: string;
  userId?: string;
}) => {
  return dictRequest('/systemRole/addSystemUserRole', params, {
    successMsg: translation('global:add.success'),
  });
};
/**
 * [1-10465-1] 新增标识删除角色与用户关系
 * @param data
 * @returns
 */
export const deleteUserRoleByIds = (params: { userRoleId?: string }) => {
  return dictRequest('/systemRole/deleteUserRoleByIds', params, {
    successMsg: translation('global:delete.success'),
  });
};
/**
 * [1-10465-1] 新增标识删除角色与用户关系
 * @param data
 * @returns
 */
export const updateAppReleaseVersionCodeByUserIds = (params: {
  appReleaseVersionCode?: string;
  userIds: string[];
}) => {
  return dictRequest('/user/updateAppReleaseVersionCodeByUserIds', params, {
    successMsg: translation('operation.success', '操作成功'),
  });
};
