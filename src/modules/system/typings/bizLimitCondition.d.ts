declare namespace DizLimitCondition {
  interface ConditionalParticipation {
    keyWord?: string;
    bizSceneLimitConditionCodes?: string[];
  }
  interface BizSceneXConditionList {
    bizSceneLimitConditionDesc: string;
  }
  interface BusinessRulesList {
    bizSceneCode: string;
    bizSceneDesc: string;
    bizSceneXConditionList: RestrictiveConditionsList[];
    editable?: boolean;
  }
  interface ConditionSymbolList {
    conditionSymbolId?: string;
    symbolTypeDesc: string;
    symbolTypeCode: string;
  }
  interface RestrictiveConditionsList {
    bizSceneXConditionId?: string; //业务场景可用限定条件
    bizSceneLimitConditionDesc?: string; //条件名称
    dataSearchBizIdTypeCode?: string; //数据检索业务标识类型代码
    valueTypeCode: string; //值类型代码
    bizSceneLimitConditionId?: string; //业务场景限定条件标识
    bizSceneLimitConditionCode: string; //业务场景限定条件代码
    valueTypeDesc?: string; //值类型
    dataSearchBizIdTypeDesc?: string; //数据检索业务标识类型
    codeSystemNo?: string; //编码体系NO
    conditionSymbolList?: ConditionSymbolList[]; //可用符号列表
    editable: boolean; //是否修改
    edit?: boolean;
    bizSceneLimitConditionList?: [];
    bizSceneXConditionList?: { bizSceneCode: string }[];
    bizSceneXConditionSelectList?: string[];
    codeSystemName?: string;
    selectedList?: { [key: string]: string }[];
  }

  interface CodeSystem {
    codeSystemNo?: string;
    keyWord?: string;
    enabledFlag?: number;
  }
  interface Query {
    pageNumber: number;
    pageSize: number;
    tota?: number;
  }

  interface DizSceneLimitRSettingList {
    editable: boolean;
    bizSceneLimitConditionDesc?: string;
    bizSceneLimitConditionCode: string;
    conditionList: [];
    symbolList: [];
    symbolTypeDesc: string;
    conditionSymbolList?: ConditionSymbolList[];
    valueTypeDesc: string;
    bizSceneLimitConditionId: string;
    valueTypeCode: string;
    symbolTypeCode: string;
    dataSearchBizIdTypeCode: string;
    bizSceneLimitRDetailList: { value: string; endValue: string }[];
    bizSceneLimitCondition: object;
    collectionOfValueTypes: string;
  }
  interface RuleList {
    ruleName?: string; //规则名称
    ruleDesc?: string; //规则描述
    controlModeTypeDesc?: string; //控制方式
    tipsMessage?: string; //提示信息
    enabledFlag?: number; //启用标识
    bizSceneCode?: string;
    bizSceneLimitRuleId?: string;
    bizSceneLimitRSettingList?: DizSceneLimitRSettingList[];
  }

  interface QueryDictData {
    bizSceneLimitConditionId: string;
    menuId: string;
    hospitalId: string;
    keyWord?: string;
  }
  interface CheckReactive {
    [key: string]: string | boolean | number | unknown;
  }
}
