declare namespace Org {
  export interface LocationItem {
    indexNo?: number;
    enabledFlag: 0 | 1;
    loginSelectFlag: 0 | 1;
    orgLocation2ndName: string;
    orgLocationId?: number;
    orgLocationName?: string;
    orgLocationNameDisPlay?: string;
    orgLocationNo: string;
    editable?: boolean;
    addrName?: string;
    addressNo?: string;
    provinceNo?: string;
    cityNo?: string;
    countyNo?: string;
  }
  export interface Item {
    deptExtInfo?: {
      deptXEncounterTypeList?: {
        deptXEncounterTypeId: string;
        encounterTypeCode: string;
        encounterTypeDesc: string;
      }[];
      deptTypeCode: string;
      deptTypeDescDisplay: string;
    };
    enabledFlag?: number;
    hospitalExtInfo?: {
      hospitalLevelCode: string;
      hospitalLevelDescDisplay: string;
      tenantId: string;
    };
    orgId?: string;
    tenantId?: string;
    orgXContactList?: {
      contactNo?: string;
      contactTypeCode?: string;
    }[];
    contactNo?: string;
    contactTypeCode?: string;
    orgDesc?: string;
    org2ndName?: string;
    orgExtName?: string;
    orgEngName?: string;
    orgNameDisplay?: string;
    orgNameDisPlay?: string;
    orgLocationList?: LocationItem[];
    subOrgList?: Item[];
    orgName?: string;
    orgNo?: string;
    orgTypeCode?: string;
    org2ndDesc?: string;
    orgExtDesc?: string;
    storageTypeCodes?: string[];
    medicineTypeCodes?: string[];
    orgStorageTypeList?: {
      storageTypeCode: string;
    }[];
    orgMedicineTypeList?: {
      medicineTypeCode: string;
    }[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    parentOrgId?: any;
    parentOrgName?: string;
    spellNo?: string;
    wbNo?: string;
  }

  export interface queryReqParams {
    enabledFlag?: number;
    keyWord?: string;
    orgId?: number;
    orgNo?: string;
    orgTypeCodes?: string[];
    parentOrgId?: string;
    personId?: string;
    userNo?: string;
  }
  export interface AddResData {
    orgId: string;
  }

  interface queryReqFlatPageParams extends queryReqParams {
    pageNumber?: number;
    pageSize?: number;
  }

  interface queryDepartmentParams {
    keyWord?: string;
    enabledFlag?: number;
    hospitalId?: string;
    deptTypeCodes?: string[];
    encounterTypeCode?: string;
  }

  interface queryWardParams {
    keyWord?: string;
    enabledFlag?: number;
    deptId?: string;
    hospitalId?: string;
  }

  interface queryStorageParams {
    keyWord?: string;
    userId?: string;
    hospitalId?: string;
    storageTypeCodes?: string[];
  }

  interface Storage {
    storageIdTypeCode: string;
    commodityStorageId: string;
    storageNameDisplay: string;
    orgStorageTypeList: {
      orgStorageTypeId: string;
      storageTypeCode: string;
      storageTypeDesc: string;
    }[];
  }

  interface FlatOrgReqParams {
    pageNumber: number;
    pageSize: number;
    keyWord?: string;
    enabledFlag?: number;
    orgTypeCodes?: string[];
    parentOrgId?: string;
    orgId?: string;
    orgNo?: string;
  }
  interface FlatOrgReqItem {
    orgId: string;
    orgNo: string;
    orgName: string;
    org2ndName?: string;
    orgExtName?: string;
    orgNameDisplay: string;
    orgTypeCode: string;
    orgTypeDescDisplay: string;
    orgDesc?: string;
    org2ndDesc?: string;
    orgExtDesc?: string;
    orgDescDisplay?: string;
    spellNo?: string;
    wbNo?: string;
    parentOrgId?: string;
    enabledFlag: number;
    orgXContactList?: {
      orgXContactId: string;
      contactTypeCode: string;
      contactTypeCodeDesc: string;
      contactNo: string;
    }[];
    orgStorageTypeList?: {
      orgStorageTypeId: string;
      storageTypeCode: string;
      storageTypeDesc: string;
    }[];
    orgMedicineTypeList?: {
      orgMedicineTypeId: string;
      medicineTypeCode: string;
      medicineTypeDesc: string;
    }[];
    hospitalExtInfo?: {
      hospitalLevelCode?: string;
      hospitalLevelDesc?: string;
      tenantId: string;
      tenantName: string;
    }[];
    deptExtInfo?: {
      deptTypeCode?: string;
      deptTypeDesc?: string;
    }[];
    orgEnvSetting?: {
      logo?: string;
      backgroundPic?: string;
      applicationTitle?: string;
      copyrightDesc?: string;
    }[];
    orgLocationList?: {
      orgLocationId: string;
      orgLocationNo: string;
      orgLocationName: string;
      orgLocation2ndName?: string;
      orgLocationExtName?: string;
      orgLocationNameDisplay: string;
      enabledFlag: number;
      loginSelectFlag: number;
      provinceNo?: string;
      cityNo?: string;
      countyNo?: string;
      addrName?: string;
      addrDetail?: string;
    }[];
    orgXDirectorList?: {
      orgXDirectorId?: string;
      userId?: string;
      userName?: string;
      orgDirectorCode?: string;
      orgDirectorCodeDesc?: string;
    }[];
  }
}
