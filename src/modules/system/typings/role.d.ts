declare namespace Role {
  export interface RolePermission {
    rolePermissionId: string;
    limitObjectTypeCode: string;
    limitObjectTypeDesc: string;
    limitObjectId: string;
  }

  export interface SystemRoleInfo {
    roleId: string;
    enabledFlag: 0 | 1;
    roleName: string;
    role2ndName: string;
    roleExtName: string;
    roleNameDisplay: string;
    hospitalId: string;
    hospitalNameDisplay: string;
    createdOrgLocationId: string;
    createdOrgLocationName: string;
    createdUserId: string;
    createdUserName: string;
    createdAt: string;
    modifiedOrgLocationId: string;
    modifiedOrgLocationName: string;
    modifiedUserId: string;
    modifiedUserName: string;
    modifiedAt: string;
    rolePermissionList: RolePermission[];
    edit: boolean;
  }

  export interface ReqSystemRole {
    enabledFlag: 0 | 1;
    roleName: string;
    role2ndName: string;
    roleExtName: string;
    hospitalId: string;
  }

  export interface ReqUpdateSystemRoleById {
    roleId: string;
    enabledFlag: 0 | 1;
    roleName: string;
    role2ndName?: string;
    roleExtName?: string;
  }

  export interface ReqSaveSystemRolePermission {
    roleId: string;
    rolePermissionList?: {
      rolePermissionId?: string;
      limitObjectTypeCode: string;
      limitObjectId: string;
    }[];
  }

  export interface UserRoleList {
    userRoleId?: string;
    roleId?: string;
    roleIds?: string[];
    roleName?: string;
    userId?: string;
    userNo?: string;
    userNameDisPlay?: string;
    loginFlag?: number;
    adminFlag?: number;
    lockedFlag?: number;
    userTypeCode?: string;
    userTypeDesc?: string;
    enabledFlag?: number;
    spellNo?: string;
    wbNo?: string;
    userJobCode?: string;
    userJobDesc?: string;
    appReleaseVersionCode?: string;
    appReleaseVersionDesc?: string;
    editable?: boolean;
  }
}
