declare namespace User {
  export interface queryReqParams {
    enabledFlag?: number;
    keyWord?: string;
    orgId?: number;
    orgNo?: string;
    orgTypeCodes?: string[];
    parentOrgId?: number;
    hospitalId?: string;
    userJobCodes?: string[];
    personId?: string;
    userNo?: string;
    pageNumber: number;
    pageSize: number;
  }

  export interface AddResData {
    orgId: string;
  }

  export interface EditReqParams {
    userId?: string;
    userNo: string;
    employeeId?: string;
    userName: string;
    user2ndName?: string;
    userExtName?: string;
    userTypeCode: string;
    enabledFlag: number;
    spellNo?: string;
    wbNo?: string;
    paySumTypeCode: string;
    paySumBelongUserId?: string;
    invoiceAgentUserId?: string;
    userJobCode: string;
    personId?: string;
    titleCode?: string;
    personSimpleDesc?: string;
    person2ndSimpleDesc?: string;
    personExtSimpleDesc?: string;
    genderCode: string;
    perCertificateList: {
      perCertificateId?: string;
      certificateTypeCode: string;
      certificateNo: string;
    }[];
    perContactList?: {
      perContactId?: string;
      contactTypeCode: string;
      contactNo: string;
    }[];
    loginOrgLocationList?: {
      userXOrgLocationId?: string;
      orgLocationId: string;
      sort: number;
    }[];
    userRoles?: string[];
    bizUnitIds?: string[];
  }
}

declare namespace People {
  export interface ReqParams {
    personInfoTypes: string[];
    certificateNo: string;
    certificateTypeCode: string;
  }

  export interface Item {
    personName: string;
    personId: string;
    genderCode?: string;
    person2ndName: string;
    personExtName: string;
    nameDisplay: string;
    spellNo?: string;
    wbNo?: string;
    titleCode: string;
  }
}

declare namespace User {
  export interface ReqParams {
    enabledFlag: string;
    pageSize: number;
    pageNumber: number;
    keyWord: string;
    userNo: string;
    personId: string;
  }

  export interface Item {
    userId: string;
    userName: string;
    userNameDisplay: string;
    userCode: string;
    enabledFlag: 0 | 1;
    orgId: string;
    orgName: string;
    userNo: string;
    userJobDesc?: string;
    appReleaseVersionDesc?: string;
  }

  export interface LoginRecordReqParams {
    pageNumber: number;
    pageSize: number;
    userId: string;
    loginInBeginAt?: string;
    loginInEndAt?: string;
  }

  export interface LoginRecordReqItem {
    userId: string;
    loginInAt: string;
    loginOutAt?: string;
    loginTypeCode: string;
    loginTypeDesc: string;
    loginInIp?: string;
    loginInMac?: string;
    loginInPosition?: string;
    loginOrgId?: string;
    loginOrgNameDisplay?: string;
    loginOrgLocationNameDisplay?: string;
    loginAddrDetail?: string;
  }
}

declare namespace Employee {
  export interface ReqParams {
    keyWord: string;
    pageSize: number;
    employeeId?: string;
    pageNumber: number;
    empTypeCode: string;
    hospitalId: string;
    deptId: string;
    userId?: string;
    appReleaseVersionCode?: string;
  }

  export interface RoleItem {
    hospitalId: string;
    roleId: string;
    roleName: string;
  }

  export interface LocationItem {
    orgId: string;
    orgLocationId: string;
    orgLocationName: string;
  }

  export interface CertificateItem {
    certificateTypeCode: string;
    certificateNo: string;
    perCertificateId: string;
    certificateTypeDesc?: string;
    editable: boolean;
  }

  export interface ContactNoItem {
    contactTypeCode: string;
    contactTypeDesc?: string;
    perContactId: string;
    contactNo: string;
    editable: boolean;
  }

  export interface UserInfo {
    paySumTypeCode: string;
    userJobCode: string;
    paySumBelongUserId: string;
    userEnabledFlag: number;
    invoiceAgentUserId: string;
    appReleaseVersionCode?: string;
  }

  export interface Item extends UserInfo {
    personId: string;
    personName: string;
    nameDisplay: string;
    roleId: string;
    roleTypeCode: string;
    roleTypeDesc: string;
    empNo: string;
    contactNo: string;
    genderCode: string;
    empTypeCode: string;
    empTypeDesc: string;
    certificateTypeCode: string;
    certificateNo: string;
    hospitalId: string;
    paySumBelongUserId: string;
    paySumBelongUserName: string;
    invoiceAgentUserName: string;
    appReleaseVersionCode?: string;
    appReleaseVersionDesc?: string;
    userRoleList?: RoleItem[];
    loginOrgLocationList?: LocationItem[];
    perCertificateList?: CertificateItem[];
    perContactList?: ContactNoItem[];
    bizUnitList?: BizUnitItem[];
  }

  export interface BizUnitItem {
    bizUnitId: string;
    bizUnitName: string;
    orgTypeCode: string;
    orgTypeDesc: string;
    hospitalId: string;
    hospitalName: string;
  }
}
