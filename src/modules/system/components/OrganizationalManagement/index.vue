<script setup lang="ts">
  import { computed, ref, watchEffect } from 'vue';
  import type { FormInstance } from 'element-sun';
  import { ElMessageBox } from 'element-sun';
  import { useRoute, useRouter } from 'vue-router';
  import {
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    Title,
    useAppConfigData,
  } from 'sun-biz';
  import { useOrgStore } from '@modules/system/store/org.ts';
  import { ORG_TYPE_CODE, CELL_PHONE_NUMBER } from '@/utils/constant';
  import {
    useOrgExtInfoConfig,
    useOrgInfoConfig,
  } from './config/useOrgInfoConfig.tsx';
  import { useOrgEnvConfig } from './config/useOrgEnvConfig';
  import { addOrg, updateOrgItem } from '@modules/system/api/org';
  import {
    OrgInfo,
    useLocationColumnConfig,
  } from './config/useOrgColumnConfig';
  import { useTranslation } from 'i18next-vue';
  import { cloneDeep, isEqualWith } from 'es-toolkit';
  import { phoneRegex } from '@sun-toolkit/shared';

  const { t } = useTranslation();
  const infoFormRef = ref<{
    ref: FormInstance;
    model: Org.Item;
  }>();

  const extFormRef = ref<{
    ref: FormInstance;
    model: {
      deptTypeCode: string;
      encounterTypeCodes: string[];
      hospitalLevelCode: string;
      tenantId: string;
    };
  }>();
  const {
    updateFn,
    orgTypeCode: propsOrgTypeCode,
    parentOrgId: propsParentOrgId,
    parentOrgName: propsParentOrgName,
    tenantId: propsTenantId,
    disabledOrgType: propsDisabledOrgType,
  } = defineProps<{
    updateFn?: () => void;
    orgTypeCode?: string;
    parentOrgId?: string;
    parentOrgName?: string;
    tenantId?: string;
    disabledOrgType?: boolean;
  }>();

  const tableFormRef = ref();
  const router = useRouter();
  const route = useRoute();
  // params 组织参数
  const orgId = computed(() => route.params?.orgId);
  const isAdd = computed(() => orgId.value === 'add');
  const loading = ref(false);
  const orgStore = useOrgStore();
  const flatOrgList = computed(() => orgStore.flatOrgList);
  //存储初始化数据
  let initializeTheStorage: {
    orgLocationList: Org.LocationItem[];
    deptTypeCode?: string;
    encounterTypeCodes?: string[];
  } = {
    orgLocationList: [], //位置分布
  };

  const orgInfo = ref<OrgInfo>({
    tenantId: '',
    contactTypeCode: '',
    contactNo: '',
    orgXContactList: [],
  } as OrgInfo);
  // 组织信息
  const orgItem = computed(() => {
    let data = {};
    const querySun = Object.keys(route?.query).length;
    //判断当前路由是否存在参数
    if (querySun) {
      //若存在参数则使用路由参数
      data = { ...(route?.query || {}) };
    } else {
      //若路由不存在参数则使用组件传入参数 props（用于区分组织管理和科室管理中不同传参问题，解决科室管理跳转路由导致科室信息丢失）
      data = {
        orgTypeCode: propsOrgTypeCode,
        parentOrgId: propsParentOrgId,
        parentOrgName: propsParentOrgName,
        tenantId: propsTenantId,
        disabledOrgType: propsDisabledOrgType,
      };
    }
    if (isAdd.value) {
      return {
        // 点击新增按钮 默认组织类型为医院
        orgTypeCode:
          !route?.query?.orgTypeCode && !propsOrgTypeCode
            ? ORG_TYPE_CODE.HOSPITAL
            : '',
        ...(data || {}),
      };
    } else {
      return flatOrgList.value.find(
        (item: Org.Item) => item.orgId === route.params.orgId,
      );
    }
  });
  const currentOrg = useAppConfigData(MAIN_APP_CONFIG.CURRENT_ORG);
  const locationList = ref<Org.LocationItem[]>([]);
  /** 当组织类型为集团或者医院时 环境配置信息 */
  const orgEnvSetting = ref({
    ...(orgItem?.value?.orgEnvSetting ?? {}),
  });
  // 基本信息配置
  const orgFormConfig = useOrgInfoConfig({
    onlyRootOrg: isAdd.value && !route?.query?.orgTypeCode && !propsOrgTypeCode,
    initData: orgInfo,
    formRef: infoFormRef,
    orgInfo: orgInfo,
    extFormRef,
  });
  // 医院扩展信息
  const orgExtInfoConfig = useOrgExtInfoConfig(orgItem, infoFormRef);
  // 环境配置信息
  const orgEnvInfoConfig = useOrgEnvConfig(orgInfo, orgEnvSetting);
  // 位置table 表头配置
  const { locationTableConfig, addItem: locationAddItem } =
    useLocationColumnConfig({
      tableFormRef,
      locationData: locationList,
      orgInfo,
    });

  function getParentId(data: string[] | string) {
    if (Array.isArray(data)) {
      return data[data.length - 1];
    }
    return data;
  }

  // 组织扩展信息数据
  function formatExtInfo() {
    let extInfo = {};
    if (
      infoFormRef?.value &&
      [ORG_TYPE_CODE.DEPARTMENT, ORG_TYPE_CODE.HOSPITAL].indexOf(
        infoFormRef.value.model.orgTypeCode as ORG_TYPE_CODE,
      ) !== -1
    ) {
      // 组织为科室
      if (infoFormRef.value.model.orgTypeCode === ORG_TYPE_CODE.DEPARTMENT) {
        extInfo = {
          deptExtInfo: {
            ...(isAdd.value ? {} : orgItem.value?.deptExtInfo || {}),
            deptTypeCode: extFormRef?.value?.model?.deptTypeCode,
            encounterTypeCodes: extFormRef?.value?.model?.encounterTypeCodes,
          },
        };
      } else if (
        // 组织为医院
        infoFormRef.value.model.orgTypeCode === ORG_TYPE_CODE.HOSPITAL
      ) {
        extInfo = {
          hospitalExtInfo: {
            tenantId: extFormRef?.value?.model.tenantId ?? currentOrg?.tenantId,
            hospitalLevelCode: extFormRef?.value?.model?.hospitalLevelCode,
          },
        };
      }
    }
    return extInfo;
  }
  /**
   * 提交组织数据
   */
  const handleSubmit = async () => {
    if (!infoFormRef.value) return;
    if (
      (!orgInfo.value.contactTypeCode && orgInfo.value.contactNo) ||
      (!orgInfo.value.contactNo && orgInfo.value.contactTypeCode) ||
      (orgInfo.value.contactTypeCode &&
        orgInfo.value.contactNo &&
        orgInfo.value.contactTypeCode === CELL_PHONE_NUMBER.PHONE &&
        !phoneRegex.test(orgInfo.value.contactNo))
    ) {
      return;
    }
    // 校验基本信息
    const isInfoValid = await infoFormRef.value.ref.validate();
    // 校验位置信息
    const isLcValid = await tableFormRef?.value?.formRef?.validate();

    if (!isInfoValid || !isLcValid) {
      return;
    }
    // 校验位置非编辑状态
    if (locationList.value.find((item: Org.LocationItem) => item.editable)) {
      await ElMessageBox.confirm(
        t('global:tip.edit.submit.template', {
          name: t('org.locationDistribution', '位置分布'),
        }),
        t('global:tip'),
      );
      locationList.value.forEach((item: Org.LocationItem) => {
        item.editable = false;
      });
    }
    let extInfo = {};
    // 科室或者医院
    // 校验扩展信息并赋值
    if (extFormRef?.value?.ref) {
      const isExtValid = await extFormRef?.value?.ref.validate();
      if (!isExtValid) return;
      // 获取扩展信息提交数据
      extInfo = formatExtInfo();
    }
    // 新增组织信息
    const submitFn = isAdd.value ? addOrg : updateOrgItem;
    loading.value = true;
    const [err, res] = await submitFn({
      ...(orgItem?.value || {}),
      ...infoFormRef.value.model,
      ...extInfo,
      parentOrgId: getParentId(infoFormRef.value.model?.parentOrgId),
      orgLocationList: locationList.value || [],
      orgEnvSetting: orgEnvInfoConfig?.value?.length
        ? orgEnvSetting.value
        : null,
      orgXContactList:
        orgInfo.value.contactTypeCode && orgInfo.value.contactNo
          ? [
              {
                contactTypeCode: orgInfo.value.contactTypeCode,
                contactNo: orgInfo.value.contactNo,
              },
            ]
          : undefined,
    });
    loading.value = false;
    if (!err && res?.success) {
      if (updateFn) {
        updateFn();
      }
      //通过传参确定当前是从科室进入还是从组织管理进入
      if (propsDisabledOrgType) {
        await router.push('/list');
      } else {
        await router.push(`/list/${res?.data?.orgId ?? orgId.value}`);
      }
    }
  };

  /**
   * 新增位置
   */
  const handleAddLocation = () => {
    locationAddItem({
      indexNo: locationList.value?.length,
      orgLocationNo: '',
      orgLocationName: '',
      orgLocation2ndName: '',
      loginSelectFlag: 1,
      enabledFlag: 1,
      editable: true,
    });
  };

  const goBack = () => {
    //初始化存储的数据 initializeTheStorage
    //点击返回获取当前所有数据
    let extendedInformation: {
      deptExtInfo?: { deptTypeCode?: string; encounterTypeCodes?: string[] };
    } = formatExtInfo();
    let finalData = {
      orgLocationList: cloneDeep(locationList.value),
      ...cloneDeep(infoFormRef.value?.model || {}),
      deptTypeCode: extendedInformation?.deptExtInfo?.deptTypeCode,
      encounterTypeCodes: extendedInformation?.deptExtInfo?.encounterTypeCodes,
    };
    //自定义对比方法
    const customizer = (a: unknown, b: unknown) => {
      //判断若果b可能是空对象则可能是unll或undefined ，b空字符串则将a和b转换为 boolean 进行对比
      if (b === '') {
        return !!b == !!a;
      }
    };
    let identical = isEqualWith(initializeTheStorage, finalData, customizer);
    if (identical) {
      //如果相同则表明未修改
      if (propsDisabledOrgType) {
        router.push('/list');
      } else {
        router.push(
          `/list/${isAdd.value ? flatOrgList.value[0]?.orgId || '' : orgId.value}`,
        );
      }
    } else {
      //当前有数据被修改
      ElMessageBox.confirm(
        t('global:confirm.switch', '当前存在尚未保存的数据，是否离开？'),
        t('global:confirm', '提示'),
        {
          confirmButtonText: t('global:confirm', '确定'),
          cancelButtonText: t('global:cancel', '取消'),
          type: 'warning',
        },
      )
        .then(() => {
          //通过传参确定当前是从科室进入还是从组织管理进入
          if (propsDisabledOrgType) {
            router.push('/list');
          } else {
            router.push(
              `/list/${isAdd.value ? flatOrgList.value[0]?.orgId || '' : orgId.value}`,
            );
          }
        })
        .catch(() => {});
    }
  };
  watchEffect(() => {
    if (!isAdd.value && orgItem?.value?.orgLocationList) {
      locationList.value = orgItem.value.orgLocationList.map(
        (item: Org.LocationItem, index: number) => {
          const addressNo = [item.provinceNo, item.cityNo, item.countyNo]
            .filter((item) => !!item)
            .join(',');
          return {
            ...item,
            addressNo,
            ...locationList.value[index],
          };
        },
      );
    }
  });

  //首次储存
  let firstStorage = false;
  watchEffect(() => {
    if (orgItem?.value) {
      orgInfo.value = {
        ...orgItem.value,
        storageTypeCodes: orgItem.value?.orgStorageTypeList?.map(
          (item: { storageTypeCode: string }) => item.storageTypeCode,
        ),
        medicineTypeCodes: orgItem.value?.orgMedicineTypeList?.map(
          (item: { medicineTypeCode: string }) => item.medicineTypeCode,
        ),
      };
      orgEnvSetting.value = {
        ...(orgItem?.value?.orgEnvSetting ?? {}),
      };
    }
    if (orgInfo.value?.orgXContactList?.length) {
      const contactTypeCode =
        orgInfo.value.orgXContactList[0]?.contactTypeCode || '';
      const contactNo = orgInfo.value.orgXContactList[0]?.contactNo || '';
      orgInfo.value.contactTypeCode = contactTypeCode;
      orgInfo.value.contactNo = contactNo;
    } else {
      orgInfo.value.contactTypeCode = '';
      orgInfo.value.contactNo = '';
    }

    if (infoFormRef.value?.model && !firstStorage) {
      //如果首次存储时表单不为undefined 则进行存储
      initializeTheStorage = {
        ...cloneDeep(infoFormRef.value?.model || {}),
        ...initializeTheStorage,
        deptTypeCode: cloneDeep(orgInfo.value?.deptExtInfo?.deptTypeCode),
        encounterTypeCodes: cloneDeep(
          orgInfo.value?.deptExtInfo?.deptXEncounterTypeList?.map(
            (item: {
              deptXEncounterTypeId: string;
              encounterTypeCode: string;
              encounterTypeDesc: string;
            }) => item.encounterTypeCode,
          ),
        ),
        orgLocationList: cloneDeep(locationList.value),
      };

      firstStorage = true;
    }
  });
</script>

<template>
  <el-page-header @back="goBack">
    <template #content>
      <span class="text-base">
        {{ isAdd ? $t('org.add', '新增组织') : orgItem?.orgName }}
      </span>
    </template>
  </el-page-header>
  <div
    class="flex h-full flex-col justify-between"
    style="height: calc(100% - 2rem)"
  >
    <el-scrollbar view-class="mr-2.5">
      <Title class="my-2.5" :title="$t('global:basicInfo')" />
      <ProForm
        ref="infoFormRef"
        v-model="orgInfo"
        :column="3"
        :data="orgFormConfig"
      />
      <Title :title="$t('org.locationDistribution', '位置分布')" class="mb-2">
        <el-button type="primary" @click="handleAddLocation">
          {{ $t('global:add') }}
        </el-button>
      </Title>
      <pro-table
        ref="tableFormRef"
        :data="locationList"
        :columns="locationTableConfig"
        :editable="true"
      />
      <!-- 扩展信息 -->
      <div v-if="orgExtInfoConfig && orgExtInfoConfig?.length">
        <Title :title="$t('global:extendInfo')" class="my-2" />
        <pro-form
          :key="orgExtInfoConfig?.length"
          ref="extFormRef"
          :column="3"
          :data="orgExtInfoConfig"
        />
      </div>
      <!-- 环境配置 -->
      <div v-if="orgEnvInfoConfig && orgEnvInfoConfig?.length">
        <Title :title="$t('org.envInfo', '环境信息')" class="my-2" />
        <pro-form
          v-model="orgEnvSetting"
          :column="3"
          :data="orgEnvInfoConfig"
        />
      </div>
    </el-scrollbar>
    <div class="mr-2.5 pt-2 text-right">
      <el-button @click="goBack">{{ $t('global:cancel') }}</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ $t('global:save') }}
      </el-button>
    </div>
  </div>
</template>
