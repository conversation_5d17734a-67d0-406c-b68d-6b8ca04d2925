import { Ref, ref, computed, onBeforeMount, nextTick, shallowRef } from 'vue';
import {
  ORG_TYPE_CODE_NAME,
  HOSPITAL_LEVEL_CODE,
  DEPT_TYPE_CODE_NAME,
  ORG_TYPE_CODE,
  ORG_TYPE_TOP_LEVEL_MAP,
  CONTACT_TYPE_CODE_NAME,
  CELL_PHONE_NUMBER,
  ENCOUNTER_TYPE_CODE_NAME,
  DEPT_TYPE_CODE,
} from '@/utils/constant';
import type { FormInstance } from 'element-sun';
import { useGetOrgData } from '@/hooks/useGetOrgData';
import { queryTenantList } from '@/api/common';
import { useAppConfigData, MAIN_APP_CONFIG, useFormConfig } from 'sun-biz';
import type { TenantResItem } from '@/api/types';
import { OrgInfo } from './useOrgColumnConfig';
import {
  MEDICINE_TYPE_CODE_NAME,
  STORAGE_TYPE_CODE_NAME,
  STORAGE_TYPE_CODE,
} from '@sun-toolkit/enums';

import { phoneRegex } from '@sun-toolkit/shared';
export function useOrgInfoConfig(options: {
  onlyRootOrg: boolean;
  initData: Ref<
    OrgInfo & { disabledOrgType?: boolean; parentOrgTypeCode?: string }
  >;
  formRef: Ref<
    | {
        ref: FormInstance;
        model: Org.Item;
      }
    | undefined
  >;
  orgInfo: Ref;
  extFormRef: Ref;
}) {
  const { formRef, initData, onlyRootOrg, orgInfo, extFormRef } = options;
  const dataSetCodes = [
    ORG_TYPE_CODE_NAME,
    MEDICINE_TYPE_CODE_NAME,
    STORAGE_TYPE_CODE_NAME,
    CONTACT_TYPE_CODE_NAME,
  ];
  const defaultOrgData = computed(() => {
    if (initData.value?.parentOrgName) {
      return [
        {
          label: initData.value?.parentOrgName,
          value: initData.value?.parentOrgId,
        },
      ];
    }
    return [];
  });
  const isSassMode = useAppConfigData(MAIN_APP_CONFIG.IS_SASS_MODE);
  const orgTypeCode = computed(() => formRef.value?.model?.orgTypeCode);
  const { orgList, loading, getFlatOrgList } = useGetOrgData(defaultOrgData);
  const tenantList = shallowRef<TenantResItem[]>([]);
  const parentTenantId = ref();
  onBeforeMount(async () => {
    const [, res] = await queryTenantList();
    if (res?.data) {
      tenantList.value = res.data;
    }
  });

  const data = useFormConfig<typeof dataSetCodes>({
    dataSetCodes,
    getData: (t, dataSet) => {
      return [
        {
          label: t('global:code'),
          name: 'orgNo',
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('global:code'),
          }),
          rules: [
            {
              required: true,
              message: t('global:placeholder.input.template', {
                content: t('global:code'),
              }),
              trigger: 'blur',
            },
          ],
        },
        {
          label: t('global:name'),
          name: 'orgName',
          component: 'input',
          autoConvertSpellNoAndWbNo: true,
          placeholder: t('global:placeholder.input.template', {
            content: t('global:name'),
          }),
          rules: [
            {
              required: true,
              message: t('global:placeholder.input.template', {
                content: t('global:name'),
              }),
              trigger: 'blur',
            },
          ],
        },
        {
          name: 'org2ndName',
          label: t('global:secondName'),
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('global:secondName'),
          }),
        },
        {
          name: 'orgExtName',
          label: t('global:thirdName'),
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('global:thirdName'),
          }),
        },
        {
          name: 'orgTypeCode',
          label: t('org.type', '组织类型'),
          component: 'select',
          placeholder: t('global:placeholder.select.template', {
            name: t('org.type', '组织类型'),
          }),
          extraProps: {
            disabled:
              initData?.value?.orgId || initData?.value?.disabledOrgType
                ? true
                : false,
            options:
              dataSet?.value && dataSet?.value?.[ORG_TYPE_CODE_NAME]?.length
                ? dataSet?.value[ORG_TYPE_CODE_NAME]?.filter((item) => {
                    if (initData?.value?.orgTypeCode && !onlyRootOrg) {
                      if (
                        initData?.value.parentOrgTypeCode ===
                        ORG_TYPE_CODE.HOSPITAL
                      ) {
                        return (
                          item?.dataValueNo === ORG_TYPE_CODE.DEPARTMENT ||
                          item?.dataValueNo === ORG_TYPE_CODE.AREA
                        );
                      }
                      return true;
                    } else {
                      return (
                        item?.dataValueNo === ORG_TYPE_CODE.GROUP ||
                        item?.dataValueNo === ORG_TYPE_CODE.HOSPITAL
                      );
                    }
                  })
                : [],
            onChange: () => {
              // if (formRef?.value?.model?.parentOrgId) {
              //   formRef.value.model.parentOrgId = '';
              // }
            },
          },
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('org.type', '组织类型'),
              }),
              trigger: 'blur',
            },
          ],
        },
        {
          name: 'parentOrgId',
          label: t('org.parentOrg', '上级组织'),
          placeholder: t('global:placeholder.select.template', {
            name: t('org.parentOrg', '上级组织'),
          }),
          component: 'select',
          extraProps: {
            options: orgList.value,
            remote: true,
            remoteShowSuffix: true,
            filterable: true,
            loading: loading.value,
            disabled:
              initData?.value?.orgId || initData?.value?.orgTypeCode
                ? true
                : false,
            onChange: (val: string) => {
              const org = (
                orgList?.value as unknown as {
                  value: string;
                  tenantId: string;
                }[]
              )?.find((item) => (item.value as string) === val);
              if (formRef?.value) {
                formRef.value.model.tenantId = org?.tenantId
                  ? org.tenantId
                  : '';
                parentTenantId.value = org?.tenantId ? org.tenantId : '';
              }
            },
            remoteMethod: (keyWord: string) => {
              return getFlatOrgList({
                keyWord,
                parentOrgId: '',
                orgTypeCodes: orgTypeCode.value
                  ? [
                      ORG_TYPE_TOP_LEVEL_MAP[
                        orgTypeCode.value as keyof typeof ORG_TYPE_TOP_LEVEL_MAP
                      ],
                    ]
                  : [],
              });
            },
          },
        },
        {
          label: t('org.storageType', '库房类型'),
          name: 'storageTypeCodes',
          placeholder: t('global:placeholder.select.template', {
            name: t('org.storageType', '库房类型'),
          }),
          rules: [
            {
              required:
                extFormRef?.value?.model?.deptTypeCode ===
                  DEPT_TYPE_CODE.PHARMACY_STORAGE ||
                extFormRef?.value?.model?.deptTypeCode ===
                  DEPT_TYPE_CODE.PHARMACY,
              message: t('global:placeholder.select.template', {
                name: t('org.storageType', '库房类型'),
              }),
              trigger: 'blur',
            },
          ],
          // .value?.deptExtInfo?.deptTypeCode
          isHidden:
            dataSet?.value?.[STORAGE_TYPE_CODE_NAME]?.length &&
            [ORG_TYPE_CODE.AREA, ORG_TYPE_CODE.DEPARTMENT].indexOf(
              initData.value?.orgTypeCode as ORG_TYPE_CODE,
            ) === -1,
          component: 'select',
          extraProps: {
            options:
              dataSet?.value && dataSet?.value?.[STORAGE_TYPE_CODE_NAME]?.length
                ? dataSet?.value[STORAGE_TYPE_CODE_NAME]
                : [],
            multiple: true,
            onChange(val: string[]) {
              // 库房类型为null 则修改 管理药品类型 为 []
              if (!val?.length && initData.value?.medicineTypeCodes?.length) {
                nextTick(() => {
                  initData.value.medicineTypeCodes = [];
                });
              }
            },
          },
        },
        {
          label: t('org.storageType', '管理药品类型'),
          name: 'medicineTypeCodes',
          placeholder: t('global:placeholder.select.template', {
            name: t('org.medicineType', '管理药品类型'),
          }),
          rules: [
            {
              required:
                extFormRef?.value?.model?.deptTypeCode ===
                  DEPT_TYPE_CODE.PHARMACY_STORAGE ||
                extFormRef?.value?.model?.deptTypeCode ===
                  DEPT_TYPE_CODE.PHARMACY,
              message: t('global:placeholder.select.template', {
                name: t('org.medicineType', '管理药品类型'),
              }),
              trigger: 'blur',
            },
          ],
          isHidden:
            dataSet?.value?.[STORAGE_TYPE_CODE_NAME]?.length &&
            [ORG_TYPE_CODE.AREA, ORG_TYPE_CODE.DEPARTMENT].indexOf(
              initData.value?.orgTypeCode as ORG_TYPE_CODE,
            ) === -1,
          component: 'select',
          extraProps: {
            disabled:
              !initData.value?.storageTypeCodes ||
              !initData.value?.storageTypeCodes?.length ||
              !initData.value?.storageTypeCodes.find(
                (code: string) =>
                  code === STORAGE_TYPE_CODE.DRUGSTORE ||
                  code === STORAGE_TYPE_CODE.PHARMACY,
              ),
            options:
              dataSet?.value &&
              dataSet?.value?.[MEDICINE_TYPE_CODE_NAME]?.length
                ? dataSet?.value[MEDICINE_TYPE_CODE_NAME]
                : [],
            collapseTags: true,
            // maxCollapseTags: 2,
            multiple: true,
          },
        },
        {
          label: t('org.tenant', '租户'),
          name: 'tenantId',
          component: 'select',
          placeholder: t('global:placeholder.select.template', {
            name: t('org.tenant', '租户'),
          }),
          isHidden:
            !isSassMode ||
            [ORG_TYPE_CODE.AREA, ORG_TYPE_CODE.DEPARTMENT].indexOf(
              orgTypeCode.value as ORG_TYPE_CODE,
            ) !== -1,
          extraProps: {
            disabled:
              initData?.value?.tenantId || parentTenantId.value ? true : false,
            options: tenantList?.value,
            props: {
              label: 'tenantName',
              value: 'tenantId',
            },
          },
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('org.tenant', '租户'),
              }),
              trigger: 'change',
            },
          ],
        },
        {
          name: 'spellNo',
          label: t('global:spellNo'),
          placeholder: t('global:placeholder.input.template', {
            content: t('global:spellNo'),
          }),
          component: 'input',
        },
        {
          name: 'wbNo',
          label: t('global:wbNo'),
          placeholder: t('global:placeholder.input.template', {
            content: t('global:wbNo'),
          }),
          component: 'input',
        },
        {
          name: 'enabledFlag',
          label: t('global:enabledFlag'),
          component: 'switch',
          defaultValue: 1,
          extraProps: {
            'active-value': 1,
            'inactive-value': 0,
          },
        },
        {
          name: 'contactNo',
          label: t('org.type', '联系方式'),
          render: () => {
            return (
              <div class="flex">
                <el-form-item
                  class={
                    orgInfo.value.contactNo && !orgInfo.value.contactTypeCode
                      ? 'is-error'
                      : ''
                  }
                >
                  <el-select
                    v-model={orgInfo.value.contactTypeCode}
                    placeholder={t('org.pleaseSelectType', '请选择联系方式')}
                    class="w-[150px] bg-white"
                    clearable={true}
                  >
                    {(dataSet?.value &&
                    dataSet?.value?.[CONTACT_TYPE_CODE_NAME]?.length
                      ? dataSet.value[CONTACT_TYPE_CODE_NAME]
                      : []
                    ).map((item) => {
                      return (
                        <>
                          <el-option
                            label={item.dataValueCnName}
                            value={item.dataValueNo}
                          ></el-option>
                        </>
                      );
                    })}
                  </el-select>
                  {orgInfo.value.contactNo && !orgInfo.value.contactTypeCode ? (
                    <div class="absolute left-0 top-full pt-[2px] text-[12px] leading-[1] text-[#f56c6c]">
                      {t('org.pleaseSelectType', '请选择联系方式')}
                    </div>
                  ) : (
                    <></>
                  )}
                </el-form-item>
                <el-form-item
                  class={
                    (!orgInfo.value.contactNo &&
                      orgInfo.value.contactTypeCode) ||
                    (orgInfo.value.contactNo &&
                      orgInfo.value.contactTypeCode &&
                      orgInfo.value.contactTypeCode ===
                        CELL_PHONE_NUMBER.PHONE &&
                      !phoneRegex.test(orgInfo.value.contactNo))
                      ? 'is-error'
                      : ''
                  }
                >
                  <el-input
                    v-model={orgInfo.value.contactNo}
                    placeholder={t('global:placeholder.input.template', {
                      content: t('联系号码'),
                    })}
                  ></el-input>
                  {!orgInfo.value.contactNo &&
                    orgInfo.value.contactTypeCode && (
                      <div class="absolute left-0 top-full pt-[2px] text-[12px] leading-[1] text-[#f56c6c]">
                        {t('global:placeholder.input.template', {
                          content: t('联系号码'),
                        })}
                      </div>
                    )}
                  {orgInfo.value.contactNo &&
                    orgInfo.value.contactTypeCode &&
                    orgInfo.value.contactTypeCode === CELL_PHONE_NUMBER.PHONE &&
                    !phoneRegex.test(orgInfo.value.contactNo) && (
                      <div class="absolute left-0 top-full pt-[2px] text-[12px] leading-[1] text-[#f56c6c]">
                        {t('global:placeholder.input.template', {
                          content: t('正确手机号'),
                        })}
                      </div>
                    )}
                </el-form-item>
              </div>
            );
          },
        },
        {
          name: 'orgDesc',
          component: 'input',
          label: t('global:orgDesc', '组织简介'),
          placeholder: t('global:placeholder.input.template', {
            content: t('global:orgDesc', '组织简介'),
          }),
          type: 'textarea',
          isFullWidth: true,
        },
        {
          label: t('global:secondDesc'),
          name: 'org2ndDesc',
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('global:secondDesc'),
          }),
          type: 'textarea',
          isFullWidth: true,
        },
        {
          label: t('global:thirdDesc'),
          name: 'orgExtDesc',
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('global:thirdDesc'),
          }),
          type: 'textarea',
          isFullWidth: true,
        },
      ];
    },
  });
  return data;
}

// 医院或者科室扩展信息
export function useOrgExtInfoConfig(
  initData: Ref<Org.Item | undefined, Org.Item | undefined>,
  formRef: Ref<
    | {
        ref: FormInstance;
        model: Org.Item;
      }
    | undefined,
    | {
        ref: FormInstance;
        model: Org.Item;
      }
    | undefined
  >,
) {
  const dataSetCodes = [
    DEPT_TYPE_CODE_NAME,
    HOSPITAL_LEVEL_CODE,
    ENCOUNTER_TYPE_CODE_NAME,
  ];

  const data = useFormConfig<typeof dataSetCodes>({
    dataSetCodes,
    getData: (t, dataSet) => {
      if (!formRef?.value?.model?.orgTypeCode) {
        return [];
      }
      if (formRef.value?.model.orgTypeCode === ORG_TYPE_CODE.DEPARTMENT) {
        return [
          {
            name: 'deptTypeCode',
            label: t('org.deptType', '科室类型'),
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t('org.deptType', '科室类型'),
            }),
            defaultValue: initData.value?.deptExtInfo?.deptTypeCode,
            extraProps: {
              options: dataSet?.value
                ? dataSet?.value[DEPT_TYPE_CODE_NAME]
                : [],
              onChange: (val: string) => {
                if (val !== DEPT_TYPE_CODE.PHARMACY_STORAGE) {
                  formRef.value?.ref?.clearValidate();
                }
              },
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select.template', {
                  name: t('org.deptType', '科室类型'),
                }),
                trigger: 'change',
              },
            ],
          },
          {
            name: 'encounterTypeCodes',
            label: t('org.encounterTypeCodes', '服务范围'),
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t('org.encounterTypeCodes', '服务范围'),
            }),
            defaultValue:
              initData.value?.deptExtInfo?.deptXEncounterTypeList?.map(
                (item) => item.encounterTypeCode,
              ),
            extraProps: {
              multiple: true,
              collapseTags: true,
              collapseTagsTooltip: true,
              maxCollapseTags: 2,
              options: dataSet?.value
                ? dataSet?.value[ENCOUNTER_TYPE_CODE_NAME]
                : [],
              props: {
                value: 'dataValueNo',
                label: 'dataValueNameDisplay',
              },
            },
          },
        ];
      } else if (formRef.value?.model.orgTypeCode === ORG_TYPE_CODE.HOSPITAL) {
        return [
          {
            name: 'hospitalLevelCode',
            label: t('org.hospitalLevel', '医院等级'),
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t('org.hospitalLevel', '医院等级'),
            }),
            defaultValue: initData.value?.hospitalExtInfo?.hospitalLevelCode,
            extraProps: {
              options: dataSet?.value
                ? dataSet?.value[HOSPITAL_LEVEL_CODE]
                : [],
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select.template', {
                  name: t('org.hospitalLevel', '医院等级'),
                }),
                trigger: 'change',
              },
            ],
          },
        ];
      }
      return [];
    },
  });
  return data;
}
