import { Ref } from 'vue';
import { useRouter } from 'vue-router';
import { SelectOptions } from '@/typings/common';
import { useColumnConfig, TableRef, useEditableTable } from 'sun-biz';
import { updateOrgEnabledFlag } from '@modules/system/api/org';
import { ORG_TYPE_CODE, DATA_SEARCH_BIZ_ID_TYPE_CODE } from '@/utils/constant';
import { DictSelect } from 'sun-biz';

export type OrgInfo = Omit<
  Org.Item,
  'hospitalExtInfo' | 'orgLocationList' | 'subOrgList'
>;

export function useOrgColumnConfig(updateOrg?: () => void) {
  const router = useRouter();

  const handleEnableSwitch = async (row: {
    enabledFlag: 0 | 1;
    orgId: string;
  }) => {
    try {
      const [, res] = await updateOrgEnabledFlag({
        enabledFlag: row.enabledFlag ? 0 : 1,
        orgId: row.orgId,
      });
      if (updateOrg) {
        updateOrg();
      }
      return res.success ? true : false;
    } catch {
      return false;
    }
  };

  const toggleEdit = (row: Org.Item) => {
    router.push(`/detail/${row.orgId}`);
  };

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 80,
        editable: false,
        render: (row: object, index: number) => <>{index + 1}</>,
      },
      {
        label: t('org.type', '组织类型'),
        prop: 'orgTypeDesc',
        minWidth: 100,
      },
      {
        label: t('org.code', '组织编码'),
        minWidth: 100,
        prop: 'orgNo',
      },
      {
        label: t('global:enableStatus'),
        prop: 'enabledFlag',
        minWidth: 120,
        render: (row: { orgId: string; enabledFlag: 0 | 1 }) => {
          return (
            <el-switch
              v-model={row.enabledFlag}
              active-value={1}
              before-change={() => handleEnableSwitch(row)}
              inactive-value={0}
            />
          );
        },
      },
      {
        label: t('org.name', '组织名称'),
        prop: 'orgName',
        minWidth: 150,
      },
      {
        label: t('org.2ndName', '组织辅助名称'),
        prop: 'org2ndName',
        minWidth: 150,
      },
      {
        label: t('org.thirdName', '组织扩展名称'),
        prop: 'orgExtName',
        minWidth: 150,
      },
      {
        label: t('org.desc', '组织描述'),
        prop: 'orgDesc',
        minWidth: 180,
      },
      {
        label: t('org.2ndDesc', '组织辅助简介'),
        prop: 'org2ndDesc',
        minWidth: 180,
      },
      {
        label: t('global:creator'),
        prop: 'createdUserName',
        minWidth: 120,
      },
      {
        label: t('global:createTime'),
        prop: 'createdAt',
        minWidth: 120,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 100,
        fixed: 'right',
        render: (row: Org.Item) => {
          return (
            <el-button
              onClick={(e: { preventDefault: () => void }) => {
                e.preventDefault();
                toggleEdit(row);
              }}
              type="primary"
              link
            >
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });
}

export function useLocationColumnConfig(options: {
  tableFormRef: Ref<TableRef>;
  locationData: Ref<Org.LocationItem[]>;
  orgInfo: Ref<OrgInfo>;
}) {
  const { tableFormRef, locationData, orgInfo } = options;

  const { toggleEdit, cancelEdit, addItem, delItem } = useEditableTable({
    tableRef: tableFormRef,
    data: locationData as Ref<(Org.LocationItem & { editable: boolean })[]>,
    id: 'orgLocationId',
  });
  // const toggleEdit = (row: Org.LocationItem) => {
  //   if (row.editable) {
  //     tableFormRef?.value?.formRef.validate((valid) => {
  //       if (valid) {
  //         row.editable = false;
  //       }
  //     });
  //   } else {
  //     row.editable = true;
  //   }
  // };

  // const handleCancelEdit = (row: Org.LocationItem) => {
  //   if (row?.orgLocationId) {
  //     const targetData: Org.LocationItem = cachedEditData[row.orgLocationId];
  //     Object.keys(targetData).forEach((key) => {
  //       // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  //       // @ts-expect-error
  //       row[key] = targetData[key];
  //     });
  //   } else {
  //     locationData.value.splice(row.indexNo!, 1);
  //   }
  //   row.editable = false;
  // };

  const locationTableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 80,
        editable: false,
        render: (row: object, index: number) => <>{index + 1}</>,
      },
      // {
      //   label: t('org.locationCode', '位置编码'),
      //   prop: 'orgLocationNo',
      //   minWidth: 160,
      //   editable: true,
      //   render: (row: { orgLocationNo: string; editable: boolean }) => {
      //     return row.editable ? (
      //       <>
      //         <el-input
      //           v-model={row.orgLocationNo}
      //           placeholder={t('global:placeholder.input.template', {
      //             content: t('org.locationCode', '位置编码'),
      //           })}
      //         ></el-input>
      //       </>
      //     ) : (
      //       <>{row.orgLocationNo}</>
      //     );
      //   },
      // },
      {
        label: t('org.locationName', '位置名称'),
        minWidth: 180,
        prop: 'orgLocationName',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('org.locationName', '位置名称'),
            }),
            trigger: 'blur',
          },
        ],
        render: (row: { orgLocationName: string; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-input
                v-model={row.orgLocationName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('org.locationName', '位置名称'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.orgLocationName}</>
          );
        },
      },
      {
        label: t('org.orgLocation2ndName', '辅助位置名称'),
        prop: 'orgLocation2ndName',
        minWidth: 180,
        editable: true,
        render: (row: { orgLocation2ndName: string; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-input
                v-model={row.orgLocation2ndName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('org.orgLocation2ndName', '辅助位置名称'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.orgLocation2ndName}</>
          );
        },
      },
      {
        label: t('org.orgLocationExtName', '扩展位置名称'),
        prop: 'orgLocationExtName',
        minWidth: 180,
        editable: true,
        render: (row: { orgLocationExtName: string; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-input
                v-model={row.orgLocationExtName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('org.orgLocationExtName', '扩展位置名称'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.orgLocationExtName}</>
          );
        },
      },
      {
        label: t('global:enableStatus'),
        prop: 'enabledFlag',
        minWidth: 120,
        render: (row: { enabledFlag: 0 | 1 }) => {
          return (
            <el-switch
              v-model={row.enabledFlag}
              active-value={1}
              inactive-value={0}
            />
          );
        },
      },
      {
        label: t('org.loginSelectFlag', '登录选择'),
        prop: 'loginSelectFlag',
        minWidth: 100,
        isHidden:
          orgInfo?.value?.orgTypeCode === ORG_TYPE_CODE.DEPARTMENT ||
          orgInfo?.value?.orgTypeCode === ORG_TYPE_CODE.AREA,
        render: (row: { loginSelectFlag: 0 | 1 }) => {
          return (
            <el-checkbox
              true-value={1}
              false-value={0}
              disabled={
                orgInfo?.value?.orgTypeCode === ORG_TYPE_CODE.DEPARTMENT ||
                orgInfo?.value?.orgTypeCode === ORG_TYPE_CODE.AREA
              }
              v-model={row.loginSelectFlag}
              checked={row.loginSelectFlag === 1}
            />
          );
        },
      },
      {
        label: t('org.address', '地址'),
        prop: 'addressNo',
        minWidth: 270,
        isHidden: orgInfo?.value?.orgTypeCode !== ORG_TYPE_CODE.HOSPITAL,
        render: (row: Org.LocationItem) =>
          row.editable ? (
            <DictSelect
              v-model={row.addressNo}
              dataSearchBizIdTypeCode={
                DATA_SEARCH_BIZ_ID_TYPE_CODE.DICT_ADDRESS3
              }
              defaultValueName={row.addrName}
              defaultValue={row.addressNo}
              label={t('org.address', '地址')}
              onChange={(key: string, name: string) => {
                const address = key?.split(',') || [];
                row.addrName = name;
                row.provinceNo = address[0];
                row.cityNo = address[1];
                row.countyNo = address[2];
              }}
            />
          ) : (
            <>{row.addrName}</>
          ),
      },
      {
        label: t('org.detailAddress', '详细地址'),
        prop: 'addrDetail',
        minWidth: 200,
        isHidden: orgInfo?.value?.orgTypeCode !== ORG_TYPE_CODE.HOSPITAL,
        render: (row: { addrDetail: string; editable: boolean }) => {
          return row.editable ? (
            <>
              <el-input
                v-model={row.addrDetail}
                placeholder={t('global:placeholder.input.template', {
                  content: t('org.detailAddress', '详细地址'),
                })}
              ></el-input>
            </>
          ) : (
            <>{row.addrDetail}</>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'action',
        fixed: 'right',
        minWidth: 150,
        render: (
          row: Required<Org.LocationItem> & { key: string },
          index: number,
        ) => {
          return row.editable ? (
            <div key="editable">
              <el-button
                onClick={() => cancelEdit(row, index)}
                type="danger"
                link
              >
                {t('global:cancel')}
              </el-button>
              <el-button onClick={() => toggleEdit(row)} type="primary" link>
                {t('global:confirm')}
              </el-button>
            </div>
          ) : (
            <div>
              <el-button onClick={() => toggleEdit(row)} type="primary" link>
                {t('global:edit')}
              </el-button>
              {row.key && (
                <el-button onClick={() => delItem(index)} type="danger" link>
                  {t('global:remove')}
                </el-button>
              )}
            </div>
          );
        },
      },
    ],
  });
  return { locationTableConfig, addItem };
}

type HospitalSystemRowData = Menu.HospitalXSysInfo & { editable: boolean };
type SysSelections = SelectOptions & {
  spellNo: string;
  wbNo: string;
};
export function useHospitalXSysTableConfig(
  tableRef: Ref<TableRef>,
  data: Ref<HospitalSystemRowData[]>,
  sysSelections: Ref<SysSelections[]>,
) {
  const { toggleEdit, cancelEdit, addItem, delItem } = useEditableTable({
    tableRef,
    data,
    id: 'hospitalXSysId',
  });

  const onItemConfirm = (data: HospitalSystemRowData) => {
    toggleEdit(data);
  };

  const hospitalXSysTableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('org.hospitalXSysTable.sysId', '系统名称'),
        prop: 'sysId',
        minWidth: 220,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('org.hospitalXSysTable.sys', '系统'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: HospitalSystemRowData) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.sysId}
                  clearable={false}
                  filterable={true}
                  placeholder={t('global:placeholder.select.template', {
                    name: t('org.hospitalXSysTable.sys', '系统'),
                  })}
                  onChange={(val: string) => {
                    const item = sysSelections.value.find(
                      (item) => item.value === val,
                    );
                    row.sysNameDisplay = item?.label as string;
                  }}
                >
                  {{
                    default: () =>
                      sysSelections.value?.map((item) => (
                        <el-option
                          key={item.value}
                          label={`${item.label}|${item.spellNo}|${item.wbNo}`}
                          value={item.value}
                        >
                          {item.label}
                        </el-option>
                      )),
                    label: (scope: { label: string }) => (
                      <span>{scope.label?.split('|')?.[0]}</span>
                    ),
                  }}
                </el-select>
              ) : (
                <>{row.sysNameDisplay}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 200,
        render: (row: HospitalSystemRowData, $index: number) => {
          return (
            <>
              {row.editable ? (
                <div class={'flex justify-around'}>
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => cancelEdit(row, $index)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => onItemConfirm(row)}
                  >
                    {t('global:confirm')}
                  </el-button>
                </div>
              ) : (
                <el-button
                  type="danger"
                  link={true}
                  onClick={() => delItem($index)}
                >
                  {t('global:delete')}
                </el-button>
              )}
            </>
          );
        },
      },
    ],
  });
  return {
    hospitalXSysTableConfig,
    addItem,
  };
}
