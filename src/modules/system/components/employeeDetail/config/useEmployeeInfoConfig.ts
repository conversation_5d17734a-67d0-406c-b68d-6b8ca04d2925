import { computed, ComputedRef, reactive, Ref, ref } from 'vue';
import {
  APP_RELEASE_VERSION_CODE,
  APP_RELEASE_VERSION_CODE_NAME,
  CERTIFICATE_TYPE_CODE,
  CERTIFICATE_TYPE_CODE_NAME,
  EMPLOYEE_STATUS,
  ENABLED_FLAG,
  PAY_SUM_TYPE_CODE_NAME,
  PROFESSION_TYPE_CODE,
  USER_JOB_TYPE_CODE,
  USER_STATUS_CODE,
} from '@/utils/constant';
import { SEX_CODE_NAME } from '@sun-toolkit/enums';
import { ElMessageBox, type FormInstance } from 'element-sun';
import { useFormConfig } from 'sun-biz';
import { queryUserList } from '@modules/system/api/user';
import { useGetDeptData } from '@/hooks/useGetOrgData';
import { queryPersonListByExample } from '@/modules/system/api/people';
import { validateID, validatePhone } from '@sun-toolkit/shared';
import { getSexCode } from '@/utils';

/**
 * @description: 获取员工信息
 */
function useGetEmployeeInfo(employeeInfo: Employee.Item) {
  const state = reactive({ loading: false, data: [] });
  const getEmployeeList = async (keyWord: string) => {
    state.loading = true;
    const [, res] = await queryUserList({
      keyWord,
      enabledFlag: ENABLED_FLAG.YES,
      pageNumber: 1,
      pageSize: 100,
      hospitalId: employeeInfo.hospitalId,
    });
    state.loading = false;
    if (res?.data) {
      state.data = res.data.map((item: User.Item) => ({
        value: item.userId,
        label: item.userName,
      }));
    } else {
      state.data = [];
    }
  };
  return {
    userState: state,
    getEmployeeList,
  };
}

type EmployeeRef =
  | {
      ref: FormInstance;
      model: Employee.Item;
    }
  | undefined;

/**
 * 获取员工信息配置项
 */
export function useEmployeeInfoConfig(options: {
  isAdd: boolean;
  isDisabled: ComputedRef<boolean>;
  employeeData: Ref<Employee.Item | undefined, Employee.Item | undefined>;
  employRef: Ref<EmployeeRef, EmployeeRef>;
  userInfo: Ref<Employee.UserInfo, Employee.UserInfo>;
  employeeInfo: Ref<Employee.Item, Employee.Item>;
  changeRole: (role: string) => Promise<void>;
}) {
  const {
    isAdd,
    employeeData,
    employRef,
    userInfo,
    employeeInfo,
    changeRole,
    isDisabled,
  } = options;
  const dataSetCodes = !isAdd
    ? [
        USER_JOB_TYPE_CODE,
        PROFESSION_TYPE_CODE,
        PAY_SUM_TYPE_CODE_NAME,
        SEX_CODE_NAME,
        USER_STATUS_CODE,
        APP_RELEASE_VERSION_CODE_NAME,
      ]
    : <const>[
        USER_JOB_TYPE_CODE,
        PROFESSION_TYPE_CODE,
        CERTIFICATE_TYPE_CODE_NAME,
        PAY_SUM_TYPE_CODE_NAME,
        SEX_CODE_NAME,
        USER_STATUS_CODE,
        APP_RELEASE_VERSION_CODE_NAME,
      ];

  const { userState, getEmployeeList } = useGetEmployeeInfo(employeeInfo.value);
  const { userState: invoiceProxyUser, getEmployeeList: getProxyEmployeeList } =
    useGetEmployeeInfo(employeeInfo.value);
  const { deptList, loading: deptLoading, getDeptList } = useGetDeptData();

  const quoteConfig = ref({
    personName: false,
    person2ndName: false,
  });

  const currentHospitalId = ref();
  // 跟踪是否已设置程序版本默认值
  const hasSetAppVersionDefault = ref(false);
  // 跟踪是否已设置日结方式默认值
  const hasSetPaySumDefault = ref(false);
  // 人员下拉数据
  const userOptions = computed(() => {
    if (!employeeData?.value) return userState.data;

    const additionalOption = employeeData.value?.paySumBelongUserId
      ? {
          label: employeeData.value?.paySumBelongUserName || '--',
          value: employeeData.value?.paySumBelongUserId,
        }
      : null;

    return additionalOption
      ? [...(userState.data ?? []), additionalOption]
      : userState.data;
  });
  const invoiceProxyUserOptions = computed(() => {
    if (!employeeData?.value) return invoiceProxyUser.data;

    const additionalOption = employeeData.value?.invoiceAgentUserId
      ? {
          label: employeeData.value?.invoiceAgentUserName || '--',
          value: employeeData.value?.invoiceAgentUserId,
        }
      : null;

    return additionalOption
      ? [...(invoiceProxyUser.data ?? []), additionalOption]
      : invoiceProxyUser.data;
  });
  const data = useFormConfig<typeof dataSetCodes, ['employeeInfo', 'userInfo']>(
    {
      dataSetCodes,
      getData: (t, dataSet) => {
        /** 校验人信息 */
        async function validatePeopleInfo(params: People.ReqParams) {
          const [, res] = await queryPersonListByExample(params);
          if (res?.data) {
            if (!res.data?.length) return false;
            const info = res.data[0];
            ElMessageBox.confirm(
              `${t('people.info.tip.content', '当前证件存在档案信息，是否引用？')}<br/>
               ${t('global:personName')}: ${info.nameDisplay}`,
              t('people.info.tip', '档案提醒'),
              {
                dangerouslyUseHTMLString: true,
                type: 'warning',
              },
            )
              .then(() => {
                Object.keys(quoteConfig.value).forEach((name) => {
                  if (employRef.value && info[name as keyof People.Item]) {
                    (employRef.value.model as unknown as People.Item)[
                      name as keyof People.Item
                    ] = info[name as keyof People.Item] as string;
                    quoteConfig.value[name as keyof typeof quoteConfig.value] =
                      true;
                  }
                });
              })
              .catch((e: Error) => {
                console.log(e);
              });
          }
        }

        // 设置日结方式默认值
        if (
          isAdd &&
          dataSet?.value?.[PAY_SUM_TYPE_CODE_NAME] &&
          !userInfo.value.paySumTypeCode &&
          !hasSetPaySumDefault.value
        ) {
          const paySumItem =
            dataSet.value[PAY_SUM_TYPE_CODE_NAME].find(
              (item) => item.dataValueNo === '1',
            ) || dataSet.value[PAY_SUM_TYPE_CODE_NAME][0];
          userInfo.value.paySumTypeCode = paySumItem.dataValueNo;
          hasSetPaySumDefault.value = true;
        }
        // 设置程序版本默认值
        if (
          isAdd &&
          dataSet?.value?.[APP_RELEASE_VERSION_CODE_NAME] &&
          !userInfo.value.appReleaseVersionCode &&
          !hasSetAppVersionDefault.value
        ) {
          const paySumItem =
            dataSet.value[APP_RELEASE_VERSION_CODE_NAME].find(
              (item) => item.dataValueNo === APP_RELEASE_VERSION_CODE.RELEASE,
            ) || dataSet.value[APP_RELEASE_VERSION_CODE_NAME][0];
          userInfo.value.appReleaseVersionCode = paySumItem.dataValueNo;
          hasSetAppVersionDefault.value = true;
        }
        // 设置证件类型默认值
        if (
          isAdd &&
          dataSet?.value?.[CERTIFICATE_TYPE_CODE_NAME] &&
          !employeeInfo.value.certificateTypeCode
        ) {
          const idcard =
            dataSet.value[CERTIFICATE_TYPE_CODE_NAME].find(
              (item) => item.dataValueNo === CERTIFICATE_TYPE_CODE.ID_CARD,
            ) || dataSet.value[CERTIFICATE_TYPE_CODE_NAME][0];
          if (idcard) {
            employeeInfo.value.certificateTypeCode = idcard.dataValueNo;
          }
        }
        //设置职工状态的默认值
        if (
          isAdd &&
          dataSet?.value?.[USER_STATUS_CODE] &&
          !employeeInfo.value.userStatusCode
        ) {
          const idcard =
            dataSet.value[USER_STATUS_CODE].find(
              (item) => item.dataValueNo === EMPLOYEE_STATUS.SERVING,
            ) || dataSet.value[USER_STATUS_CODE][0];
          if (idcard) {
            employeeInfo.value.userStatusCode = idcard.dataValueNo;
          }
        }
        return {
          employeeInfo: [
            {
              label: t('employee.code', '职工编码'),
              name: 'empNo',
              component: 'input',
              placeholder: t('global:placeholder.input.template', {
                content: t('employee.code', '职工编码'),
              }),
              rules: [
                {
                  required: true,
                  message: t('global:placeholder.input.template', {
                    content: t('employee.code', '职工编码'),
                  }),
                  trigger: 'blur',
                },
              ],
            },
            {
              label: t('employee.name', '姓名'),
              name: 'personName',
              component: 'input',
              placeholder: t('global:placeholder.input.template', {
                content: t('employee.name', '姓名'),
              }),
              autoConvertSpellNoAndWbNo: true,
              extraProps: {
                disabled: quoteConfig.value.personName || isDisabled.value,
              },
              rules: [
                {
                  required: true,
                  message: t('global:placeholder.input'),
                  trigger: 'blur',
                },
              ],
            },
            {
              label: t('global:secondName'),
              name: 'person2ndName',
              component: 'input',
              placeholder: t('global:placeholder.input'),
              defaultValue: '',
              extraProps: {
                disabled: quoteConfig.value.person2ndName,
              },
            },
            {
              label: t('global:thirdName'),
              name: 'personExtName',
              component: 'input',
              placeholder: t('global:placeholder.input'),
              defaultValue: '',
            },
            {
              label: t('person.certificateType', '证件类型'),
              name: 'certificateTypeCode',
              component: 'select',
              isHidden: !isAdd,
              placeholder: t('global:placeholder.select'),
              extraProps: {
                options: dataSet?.value
                  ? dataSet.value?.[CERTIFICATE_TYPE_CODE_NAME]
                  : [],
                props: {
                  valueKey: 'dataValueNo',
                  label: 'dataValueNameDisplay',
                },
              },
              rules: [
                {
                  required: true,
                  message: t('global:placeholder.select'),
                  trigger: 'blur',
                },
              ],
            },
            {
              label: t('person.certificateNo', '证件号码'),
              name: 'certificateNo',
              component: 'input',
              isHidden: !isAdd,
              placeholder: t('global:placeholder.input.template', {
                content: t('person.certificateNo', '证件号码'),
              }),
              extraProps: {
                onChange: async (val: string) => {
                  if (employRef?.value?.model?.certificateTypeCode) {
                    await validatePeopleInfo({
                      personInfoTypes: ['4'],
                      certificateNo: val,
                      certificateTypeCode:
                        employRef.value.model.certificateTypeCode,
                    });
                  }
                },
              },
              rules: [
                {
                  required: true,
                  message: t('global:placeholder.input'),
                  trigger: 'blur',
                },
                {
                  /** 检验身份证号码 */
                  validator: (
                    rule: { filed: string },
                    value: string,
                    callback: (data?: Error) => void,
                  ) => {
                    if (
                      employeeInfo.value.certificateTypeCode ===
                      CERTIFICATE_TYPE_CODE.ID_CARD
                    ) {
                      validateID(rule, value, (error?: Error) => {
                        if (error) {
                          callback(error);
                        } else {
                          callback();
                          employeeInfo.value.genderCode = getSexCode(value);
                        }
                      });
                    } else {
                      callback();
                    }
                  },
                  trigger: ['blur'],
                },
              ],
            },
            {
              label: t('gender', '性别'),
              name: 'genderCode',
              component: 'select',
              placeholder: t('global:placeholder.select.template', {
                name: t('gender', '性别'),
              }),
              extraProps: {
                options: dataSet?.value ? dataSet.value?.[SEX_CODE_NAME] : [],
                props: {
                  valueKey: 'dataValueNo',
                  label: 'dataValueNameDisplay',
                },
              },
              rules: [
                {
                  required: true,
                  message: t('global:placeholder.select.template', {
                    name: t('gender', '性别'),
                  }),
                  trigger: 'blur',
                },
              ],
            },
            {
              label: t('person.mobileContactNo', '移动电话'),
              name: 'contactNo',
              component: 'input',
              isHidden: !isAdd,
              placeholder: t('global:placeholder.input.template', {
                content: t('person.mobileContactNo', '移动电话'),
              }),
              rules: [
                {
                  /** 检验手机号 */
                  validator: (
                    rule: { filed: string },
                    value: string,
                    callback: (data?: Error) => void,
                  ) => {
                    if (value) {
                      validatePhone(rule, value, callback);
                    } else {
                      callback();
                    }
                  },
                  trigger: ['blur', 'change'],
                },
              ],
            },
            {
              label: t('employee.hospitalName', '所属医院'),
              name: 'hospitalId',
              component: 'hospitalSelect',
              placeholder: t('global:placeholder.select'),
              extraProps: {
                onChange: (val: string) => {
                  currentHospitalId.value = val;
                  getDeptList({
                    hospitalId: val,
                  });
                },
              },
              rules: [
                {
                  required: true,
                  message: t('global:placeholder.select.template', {
                    name: t('employee.hospitalName', '所属医院'),
                  }),
                  trigger: 'blur',
                },
              ],
            },
            {
              label: t('employee.deptName', '所属科室'),
              name: 'deptId',
              component: 'select',
              placeholder: t('global:placeholder.select.template', {
                name: t('employee.deptName', '所属科室'),
              }),
              extraProps: {
                options: deptList.value,
                remote: true,
                filterable: true,
                remoteShowSuffix: true,
                loading: deptLoading.value,
                remoteMethod: (query: string) => {
                  getDeptList({
                    keyWord: query,
                    hospitalId: currentHospitalId.value,
                  });
                },
                props: {
                  label: 'orgName',
                  value: 'orgId',
                },
              },
            },
            {
              label: t('person.title', '职称'),
              name: 'titleCode',
              component: 'select',
              placeholder: t('global:placeholder.select'),
              extraProps: {
                disabled: isDisabled.value,
                filterable: true,
                options: dataSet?.value?.[PROFESSION_TYPE_CODE] || [],
                props: {
                  valueKey: 'dataValueNo',
                  label: 'dataValueNameDisplay',
                },
              },
            },
            {
              label: t('global:spellNo'),
              name: 'spellNo',
              component: 'input',
              placeholder: t('global:placeholder.input'),
            },
            {
              label: t('global:wbNo'),
              name: 'wbNo',
              component: 'input',
              placeholder: t('global:placeholder.input'),
            },
            {
              label: t('employee.userStatusDesc', '职工状态'),
              name: 'userStatusCode',
              component: 'select',
              placeholder: t('global:placeholder.select.template', {
                name: t('employee.userStatusDesc', '职工状态'),
              }),
              rules: [
                {
                  required: true,
                  message: t('global:placeholder.select.template', {
                    name: t('userStatus', '职工状态'),
                  }),
                  trigger: 'blur',
                },
              ],
              extraProps: {
                disabled: isDisabled.value,
                options: dataSet?.value ? dataSet.value[USER_STATUS_CODE] : [],
              },
            },
            {
              label: t('employee.desc', '职工简介'),
              name: 'personSimpleDesc',
              component: 'input',
              type: 'textarea',
              isFullWidth: true,
              placeholder: t('global:placeholder.input'),
            },
            {
              label: t('employee.secondDesc', '辅助简介'),
              name: 'person2ndSimpleDesc',
              component: 'input',
              type: 'textarea',
              isFullWidth: true,
              placeholder: t('global:placeholder.input'),
            },
          ],
          userInfo: [
            {
              label: t('employee.userJob', '用户岗位'),
              name: 'userJobCode',
              component: 'select',
              placeholder: t('global:placeholder.select', {
                name: t('employee.userJob', '用户岗位'),
              }),
              extraProps: {
                disabled: isDisabled.value,
                onChange: (role: string) => changeRole(role),
                options: dataSet?.value
                  ? dataSet.value[USER_JOB_TYPE_CODE]
                  : [],
                props: {
                  valueKey: 'dataValueNo',
                  label: 'dataValueNameDisplay',
                },
              },
              rules: [
                {
                  required: true,
                  message: t('global:placeholder.select', {
                    name: t('employee.userJob', '用户岗位'),
                  }),
                  trigger: 'blur',
                },
              ],
            },
            {
              label: t('day.payment.way', '日结方式'),
              name: 'paySumTypeCode',
              component: 'select',
              placeholder: t('global:placeholder.select.template', {
                name: t('day.payment.way', '日结方式'),
              }),
              extraProps: {
                disabled: isDisabled.value,
                options: dataSet?.value
                  ? dataSet.value[PAY_SUM_TYPE_CODE_NAME]
                  : [],
              },
              rules: [
                {
                  required: true,
                  message: t('global:placeholder.select'),
                  trigger: 'blur',
                },
              ],
            },
            {
              label: t('person.payment.proxy', '日结代理'),
              name: 'paySumBelongUserId',
              component: 'select',
              placeholder: t('global:placeholder.keyword'),
              extraProps: {
                remote: true,
                remoteShowSuffix: true,
                filterable: true,
                disabled: isDisabled.value,
                options: userOptions.value,
                loading: userState.loading,
                remoteMethod: getEmployeeList,
              },
            },
            {
              label: t('person.payment.invoice', '发票代理'),
              name: 'invoiceAgentUserId',
              component: 'select',
              placeholder: t('global:placeholder.keyword'),
              extraProps: {
                remote: true,
                remoteShowSuffix: true,
                filterable: true,
                disabled: isDisabled.value,
                options: invoiceProxyUserOptions.value,
                loading: invoiceProxyUser.loading,
                remoteMethod: getProxyEmployeeList,
              },
            },
            {
              label: t('global:enabledFlag'),
              name: 'userEnabledFlag',
              component: 'switch',
              defaultValue: 1,
              extraProps: {
                disabled: isDisabled.value,
                'active-value': 1,
                'inactive-value': 0,
              },
            },
            {
              label: t('employee.appReleaseVersionCode', '程序版本'),
              name: 'appReleaseVersionCode',
              component: 'select',
              placeholder: t('global:placeholder.select.template', {
                name: t('employee.appReleaseVersionCode', '程序版本'),
              }),
              rules: [
                {
                  required: true,
                  message: t('global:placeholder.select.template', {
                    name: t('employee.appReleaseVersionCode', '程序版本'),
                  }),
                  trigger: ['blur', 'change'],
                },
              ],
              extraProps: {
                clearable: true,
                filterable: true,
                className: 'w-56',
                options: dataSet?.value
                  ? dataSet.value[APP_RELEASE_VERSION_CODE_NAME]
                  : [],
                props: {
                  label: 'dataValueNameDisplay',
                  value: 'dataValueNo',
                },
              },
            },
          ],
        };
      },
    },
  );
  return data;
}
