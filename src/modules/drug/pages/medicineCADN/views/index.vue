<script setup lang="ts" name="medicineCadn">
  import { ref, onMounted, nextTick } from 'vue';
  import { useRouter } from 'vue-router';
  import { DEFAULT_PAGE_SIZE } from '@/utils/constant';
  import { Title, ProForm, ProTable } from 'sun-biz';
  import { SelectOptions } from '@/typings/common';
  import { queryCadnListByExample } from '@/modules/drug/api/cadn';
  import { queryPharmacologyClassListByExample } from '@/modules/baseConfig/api/code';
  import { useMedicineCadnSearchFormConfig } from '@/modules/drug/pages/medicineCADN/config/useFormConfig';
  import { useMedicineCadnTableColumnConfig } from '@/modules/drug/pages/medicineCADN/config/useTableConfig';

  const router = useRouter();
  const formRef = ref();
  const searchParams = ref<Cadn.CadnListQueryParams>({
    pharmacologyClassCode: '',
    pageSize: DEFAULT_PAGE_SIZE,
    pageNumber: 1,
  });
  const pharmacologyClassOptions = ref<SelectOptions[]>([]); // 药理分类
  const loading = ref(false);
  const total = ref(0);
  const medicineCadnList = ref<Cadn.CadnListItem[]>([]);

  const queryMedicineCadnList = async (
    data?: Partial<Cadn.CadnListQueryParams>,
  ) => {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryCadnListByExample(searchParams.value);
    if (res?.success) {
      medicineCadnList.value = res.data || [];
      total.value = res.total;
    }
    loading.value = false;
  };

  const handleEdit = (data: Cadn.CadnListItem) => {
    router.push({
      name: 'detail',
      params: {
        id: data.cadnId,
      },
    });
  };

  // 查询药理分类
  const getPharmacologyClassList = async (keyWord?: string) => {
    const [, res] = await queryPharmacologyClassListByExample({ keyWord });
    if (res?.data) {
      pharmacologyClassOptions.value = res.data.map((item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
        children: item?.dataValueSubList
          ? item?.dataValueSubList.map((child) => ({
              value: child?.dataValueNo,
              label: child?.dataValueCnName,
            }))
          : undefined,
      }));
    }
  };

  const handleReset = () => {
    formRef.value?.ref?.resetFields();
    nextTick(() => {
      searchParams.value = {
        pageNumber: 1,
        pageSize: DEFAULT_PAGE_SIZE,
      };
      queryMedicineCadnList();
    });
  };

  const searchConfig = useMedicineCadnSearchFormConfig(
    pharmacologyClassOptions,
    getPharmacologyClassList,
    queryMedicineCadnList,
  );
  const medicineTableColumns = useMedicineCadnTableColumnConfig(handleEdit);

  onMounted(() => {
    queryMedicineCadnList();
  });
</script>

<template>
  <div class="flex h-full flex-col">
    <Title class="mb-3" :title="$t('medicineCadn.list.title', '药品列表')" />
    <div class="flex justify-between">
      <ProForm
        v-model="searchParams"
        ref="formRef"
        class="flex-wrap"
        layout-mode="inline"
        :data="searchConfig"
        @model-change="queryMedicineCadnList"
      >
        <div>
          <el-button type="primary" @click="() => queryMedicineCadnList()">
            {{ $t('global:query') }}
          </el-button>
          <el-button @click="() => handleReset()">
            {{ $t('global:reset') }}
          </el-button>
        </div>
      </ProForm>
      <div class="flex-shrink-0">
        <el-button
          type="primary"
          @click="
            () => {
              router.push('/detail/add');
            }
          "
        >
          {{ $t('global:add') }}
        </el-button>
      </div>
    </div>
    <pro-table
      row-key="commodityId"
      :data="medicineCadnList"
      :page-info="{
        total: total,
        pageNumber: searchParams.pageNumber,
        pageSize: searchParams.pageSize,
      }"
      :pagination="true"
      :loading="loading"
      :columns="medicineTableColumns"
      @current-page-change="
        (val: number) => {
          queryMedicineCadnList({ pageNumber: val });
        }
      "
      @size-page-change="
        (val: number) => {
          queryMedicineCadnList({ pageSize: val, pageNumber: 1 });
        }
      "
    />
  </div>
</template>
