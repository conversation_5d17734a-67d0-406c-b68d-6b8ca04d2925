import { useColumnConfig } from 'sun-biz';

export function useMedicineCadnTableColumnConfig(
  handleEdit: (data: Cadn.CadnListItem) => void,
) {
  return useColumnConfig({
    getData: (t) => {
      const data = [
        {
          label: t('global:sequenceNumber'),
          prop: 'indexNo',
          editable: false,
          minWidth: 80,
          render: (row: object, index: number) => <>{index + 1}</>,
        },
        {
          label: t('medicineCadn.medicineCadnTable.cadn', '通用名'),
          minWidth: 320,
          prop: 'cadn',
        },
        {
          label: t('medicineCadn.medicineCadnTable.cadnExt', '辅助名称'),
          minWidth: 140,
          prop: 'cadnExt',
        },
        {
          label: t('medicineCadn.medicineCadnTable.cadn2nd', '扩展名称'),
          minWidth: 140,
          prop: 'cadn2nd',
        },
        {
          label: t('medicineCadn.medicineCadnTable.cadnEng', '通用英文名'),
          minWidth: 140,
          prop: 'cadnEng',
        },
        {
          label: t(
            'medicineCadn.medicineCadnTable.medicineTypeDesc',
            '药品类型',
          ),
          minWidth: 100,
          prop: 'medicineTypeDesc',
        },
        {
          label: t('medicineCadn.medicineCadnTable.dosageFormDesc', '药品剂型'),
          minWidth: 150,
          prop: 'dosageFormDesc',
        },
        {
          label: t(
            'medicineCadn.medicineCadnTable.pharmacologyClassDesc',
            '药理分类',
          ),
          minWidth: 130,
          prop: 'pharmacologyClassDesc',
        },
        {
          label: t('global:operation'),
          prop: 'operation',
          fixed: 'right',
          width: 140,
          render: (row: Cadn.CadnListItem) => {
            return (
              <div class={'flex justify-around'}>
                <el-button
                  type="primary"
                  link={true}
                  onClick={() => handleEdit(row)}
                >
                  {t('global:edit')}
                </el-button>
              </div>
            );
          },
        },
      ];
      return data;
    },
  });
}
