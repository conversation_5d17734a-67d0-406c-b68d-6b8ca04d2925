import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import { MEDICINE_TYPE_CODE_NAME, CV08_50_002_NAME } from '@/utils/constant';
import { SelectOptions } from '@/typings/common';

export function useMedicineCadnSearchFormConfig(
  pharmacologyClassOptions: Ref<SelectOptions[]>,
  getPharmacologyClassList: (keyWord?: string) => void,
  queryMedicineCadnList: (data?: Partial<Cadn.CadnListQueryParams>) => void,
) {
  const data = useFormConfig({
    dataSetCodes: [MEDICINE_TYPE_CODE_NAME, CV08_50_002_NAME],
    getData: (t, dataSet) => [
      {
        label: t('medicineCadn.searchFrom.medicineTypeCode', '药品类型'),
        name: 'medicineTypeCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('medicineCadn.searchFrom.medicineTypeCode', '药品类型'),
        }),
        extraProps: {
          options: dataSet?.value ? dataSet.value[MEDICINE_TYPE_CODE_NAME] : [],
          filterable: true,
          className: 'w-52',
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('medicineCadn.searchFrom.pharmacologyClassCode', '药理分类'),
        name: 'pharmacologyClassCode',
        component: 'tree-select',
        placeholder: t('global:placeholder.select.template', {
          name: t('medicineCadn.searchFrom.pharmacologyClassCode', '药理分类'),
        }),
        triggerModelChange: true,
        extraProps: {
          props: {
            children: 'children',
            label: 'label',
            value: 'value',
          },
          'check-strictly': true,
          'default-expand-all': true,
          data: pharmacologyClassOptions.value || [],
          remote: true,
          filterable: true,
          remoteMethod: (keyWord?: string) => {
            getPharmacologyClassList(keyWord);
          },
        },
      },
      {
        label: t('medicineCadn.searchFrom.dosageFormCode', '药品剂型'),
        name: 'dosageFormCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('medicineCadn.searchFrom.dosageFormCode', '药品剂型'),
        }),
        extraProps: {
          options: dataSet?.value ? dataSet.value[CV08_50_002_NAME] : [],
          filterable: true,
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-60',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryMedicineCadnList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryMedicineCadnList({
              keyWord: undefined,
            });
          },
        },
      },
    ],
  });
  return data;
}
