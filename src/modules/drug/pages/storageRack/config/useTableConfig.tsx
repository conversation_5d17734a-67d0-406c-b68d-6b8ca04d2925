import { Ref } from 'vue';
import { ElMessage } from 'element-sun';
import { useTranslation } from 'i18next-vue';
import { ENABLED_FLAG } from '@/utils/constant';
import { CirclePlus, Remove } from '@element-sun/icons-vue';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';
import {
  addStorageRack,
  updateStorageRackById,
} from '@modules/drug/api/storageRack';

type StorageRackTableItem = StorageRack.StorageRackItem & {
  completeStorageRackName: string;
  completeStorageRackId: string;
  editable: boolean;
  isExpand: boolean;
  level: number;
  key?: string;
  parentStorageRackName?: string;
  parentStorageRackList?: string;
  subStorageRackList?: StorageRackTableItem[];
};

export function useStorageRackTableConfig(
  tableRef: Ref<TableRef>,
  tableData: Ref<StorageRackTableItem[]>,
  queryStorageRackList: (
    data?: StorageRack.SearchStorageRackParams,
    userBackup?: boolean | undefined,
  ) => void,
  handleEnableSwitch: (data: StorageRackTableItem) => void,
  handleAddSubStorageRack: (data: StorageRackTableItem, index: number) => void,
  handleExpand: (data: StorageRackTableItem, index: number) => void,
  handleRetract: (data: StorageRackTableItem, index: number) => void,
  handelEdit: (data: StorageRackTableItem) => void,
  handleCancelEdit: (data: StorageRackTableItem, index: number) => void,
) {
  const { t } = useTranslation();

  const { toggleEdit, cancelEdit, addItem, insertItem, updateItem } =
    useEditableTable({
      tableRef,
      data: tableData,
      id: 'storageRackId',
    });

  const handleSave = async (data: StorageRackTableItem) => {
    const {
      storageRackId,
      storageRackName,
      storageIdTypeCode,
      commodityStorageId,
      enabledFlag,
      parentStorageRackList,
    } = data;
    const params = {
      storageRackId: storageRackId || undefined,
      storageRackName,
      storageIdTypeCode,
      commodityStorageId,
      enabledFlag,
      parentStorageRackList,
    };
    let isSuccess = false;
    if (data.storageRackId) {
      const [, res] = await updateStorageRackById(params);
      isSuccess = !!res?.success;
    } else {
      const [, res] = await addStorageRack(params);
      isSuccess = !!res?.success;
    }
    if (isSuccess) {
      ElMessage.success(
        t(data.storageRackId ? 'global:edit.success' : 'global:add.success'),
      );
      toggleEdit(data);
      queryStorageRackList({}, true);
    }
  };

  const storageRackTableConfig = useColumnConfig({
    getData: (t) => [
      {
        prop: 'customizeExpand',
        minWidth: 45,
        label: '',
        render: (row: StorageRackTableItem, index: number) => {
          return (
            <div>
              {row.subStorageRackList?.length ? (
                <div
                  class={`px-3 ${row.level === 1 ? 'text-left' : 'text-center'}`}
                >
                  {row.isExpand ? (
                    <el-icon
                      class="cursor-pointer"
                      onClick={() => handleRetract(row, index)}
                    >
                      <Remove />
                    </el-icon>
                  ) : (
                    <el-icon
                      class="cursor-pointer"
                      onClick={() => handleExpand(row, index)}
                    >
                      <CirclePlus />
                    </el-icon>
                  )}
                </div>
              ) : (
                <span> </span>
              )}
            </div>
          );
        },
      },
      {
        label: t('storageRack.table.storageRackName', '货位名称'),
        prop: 'storageRackName',
        minWidth: 220,
        align: 'left',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('storageRack.table.storageRackName', '货位名称'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: StorageRackTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.storageRackName}
                  maxLength={20}
                  placeholder={t('global:placeholder.input.template', {
                    content: t('storageRack.table.storageRackName', '货位名称'),
                  })}
                />
              ) : (
                <div style={{ marginLeft: `${((row.level || 1) - 1) * 20}px` }}>
                  {row.storageRackName}
                </div>
              )}
            </div>
          );
        },
      },
      {
        label: t('storageRack.table.completeStorageRackName', '完整货位名称'),
        prop: 'completeStorageRackName',
        minWidth: 300,
        align: 'left',
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: StorageRackTableItem) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 100,
        render: (row: StorageRackTableItem, $index: number) => {
          return row.editable ? (
            <div class={'flex justify-around'} key="editable">
              <el-button
                type="danger"
                link={true}
                onClick={() => handleCancelEdit(row, $index)}
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => handleSave(row)}
              >
                {t('global:save')}
              </el-button>
            </div>
          ) : (
            <div class={'flex justify-around'}>
              <el-button
                type="primary"
                link={true}
                onClick={() => handelEdit(row)}
              >
                {t('global:edit')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => handleAddSubStorageRack(row, $index)}
              >
                {t('storageRack.table.addSubStorageRack', '新增下级')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return {
    storageRackTableConfig,
    addItem,
    cancelEdit,
    toggleEdit,
    insertItem,
    updateItem,
  };
}
