<script setup lang="tsx" name="MedicineUseInfoUpsertDialog">
  import { ref, onMounted, nextTick } from 'vue';
  import { type FormInstance } from 'element-sun';
  import { ProForm, ProDialog } from 'sun-biz';
  import { SelectOptions } from '@/typings/common';
  import {
    addMedicineUseInfo,
    updateMedicineUseInfoById,
  } from '@/modules/drug/api/medicineManage';
  import { ENABLED_FLAG } from '@/utils/constant';
  import {
    queryAdminRouteByExample,
    queryAdminFreqByExample,
  } from '@/modules/cisOutp/api/adminRouteFreq';
  import { useMedicineUseInfoUpsertFormConfig } from '@/modules/drug/pages/medicineManage/config/useFormConfig';

  const formRef = ref<{
    ref: FormInstance;
    model: Medicine.MedicineUseInfoUpsertParams;
  }>();
  const dialogRef = ref();
  const formData = ref<Medicine.MedicineUseInfoUpsertParams>({});
  const adminRouteList = ref<SelectOptions[]>([]); // 默认给药途径
  const adminFreqList = ref<SelectOptions[]>([]); // 默认给药频次
  const emits = defineEmits<{ success: [] }>();

  // 清空数据
  const handleReset = async () => {
    formRef.value?.ref.resetFields();
    formRef.value?.ref.clearValidate();
  };

  const queryAdminRouteList = async (keyWord?: string) => {
    const [, res] = await queryAdminRouteByExample({
      enabledFlag: ENABLED_FLAG.YES,
      keyWord,
    });
    if (res?.success) {
      adminRouteList.value = (res.data || []).map((item) => ({
        label: item.adminRouteDisplayName,
        value: item.adminRouteId,
      }));
    }
  };

  const queryAdminFreqList = async (keyWord?: string) => {
    const [, res] = await queryAdminFreqByExample({
      enabledFlag: ENABLED_FLAG.YES,
      keyWord,
    });
    if (res?.success) {
      adminFreqList.value = (res.data || []).map((item) => ({
        label: item.adminFreqDisplayName,
        value: item.adminFreqId,
      }));
    }
  };

  const onConfirm = async () => {
    await formRef.value?.ref.validate();
    const params = { ...formData.value, ...formRef?.value?.model };
    return params.medicineUseInfoId
      ? updateMedicineUseInfoById(params)
      : addMedicineUseInfo(params);
  };

  const handleOpen = (data?: Medicine.MedicineUseInfoListItem) => {
    formData.value = data || {};
    dialogRef.value.open();
  };

  onMounted(() => {
    queryAdminRouteList();
    queryAdminFreqList();
  });

  const formConfig = useMedicineUseInfoUpsertFormConfig(
    formData,
    adminRouteList,
    adminFreqList,
    queryAdminRouteList,
    queryAdminFreqList,
  );

  defineExpose({ dialogRef, open: handleOpen });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    class="w-2/5"
    :title="$t('medicine.medicineUseInfoUpsertDialog.title', '默认用法用量')"
    destroy-on-close
    align-center
    :confirm-fn="onConfirm"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          done();
          handleReset();
        });
      }
    "
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @success="emits('success')"
  >
    <div class="px-10">
      <ProForm
        ref="formRef"
        v-model="formData"
        :column="1"
        :data="formConfig"
      />
    </div>
  </ProDialog>
</template>
