<script setup lang="ts" name="cadnUnitTab">
  import { ElMessage } from 'element-sun';
  import { ref, nextTick, watch } from 'vue';
  import { ProTable, ProForm } from 'sun-biz';
  import { useTranslation } from 'i18next-vue';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { SelectOptions } from '@/typings/common';
  import { useUnit } from '@/modules/finance/pages/chargeItem/hooks/useOptions';
  import { useCadnMedicineSpecFormConfig } from '@/modules/drug/pages/medicineManage/config/useFormConfig';
  import { useCadnMedicineSpecDosageUnitTableConfig } from '@/modules/drug/pages/medicineManage/config/useTableConfig';

  type MedicineSpecItem = Partial<
    Cadn.MedicineSpecItem & { medicineSpec?: string; key?: string }
  >;
  type CadnMedicineSpecDosageUnitTableRow =
    Partial<Cadn.MedicineSpecDosageUnitItem> & {
      editable: boolean;
      key?: string;
      isDefault?: boolean;
    };
  type Props = {
    alreadyUseFlag?: boolean;
    data: MedicineSpecItem;
    selectOptionsMap: {
      [key: string]: SelectOptions[];
    };
  };

  const props = withDefaults(defineProps<Props>(), {
    data: () => ({}),
  });
  const emit = defineEmits(['change']);
  const { t } = useTranslation();
  const { unitOptions, getUnitList } = useUnit(); // 计价单位 options
  const medicineSpecItem = ref<MedicineSpecItem>({});
  const medicineSpecDosageUnitList = ref<CadnMedicineSpecDosageUnitTableRow[]>(
    [],
  );

  const optionsMap = ref<{
    [key: string]: SelectOptions[];
  }>({});

  const formRef = ref();
  const medicineSpecDosageUnitListTableRef = ref();

  watch(
    () => props.data,
    () => {
      medicineSpecItem.value = props.data ? { ...props.data } : {};
      medicineSpecDosageUnitList.value =
        props.data.medicineSpecDosageUnitList?.map((item) => ({
          ...item,
          editable: !!item?.editable,
        })) || [];
    },
    { deep: true, immediate: true },
  );
  watch(
    () => props.selectOptionsMap,
    () => {
      if (props.selectOptionsMap) {
        optionsMap.value = {
          ...optionsMap.value,
          ...props.selectOptionsMap,
        };
      }
    },
    { deep: true, immediate: true },
  );

  const handleChange = () => {
    emit('change', {
      ...medicineSpecItem.value,
      medicineSpecDosageUnitList: medicineSpecDosageUnitList.value,
    });
  };

  const onAddUnitItem = () => {
    addItem({
      editable: true,
      enabledFlag: ENABLED_FLAG.YES,
    });
  };

  // 行确认方法
  const handleRowConfirm = (row: CadnMedicineSpecDosageUnitTableRow) => {
    if (
      (!row.convertFactorNumerator &&
        typeof row.convertFactorNumerator !== 'number') ||
      (!row.convertFactorDenominator &&
        typeof row.convertFactorDenominator !== 'number')
    ) {
      ElMessage.warning(
        t(
          'medicineManage.cadnUnitTab.errorTip.convertFactorIsEmpty',
          '请完善换算系数信息！',
        ),
      );
      return;
    }
    toggleEdit(row);
  };

  const formConfig = useCadnMedicineSpecFormConfig(
    props.alreadyUseFlag,
    medicineSpecItem,
    unitOptions,
    optionsMap,
    getUnitList,
    handleChange,
  );

  const { cadnMedicineSpecDosageUnitTableConfig, addItem, toggleEdit } =
    useCadnMedicineSpecDosageUnitTableConfig(
      props.alreadyUseFlag,
      medicineSpecDosageUnitListTableRef,
      medicineSpecDosageUnitList,
      medicineSpecItem,
      optionsMap,
      handleRowConfirm,
    );

  const handleFormChange = (formData: MedicineSpecItem, name?: string) => {
    switch (name) {
      case 'miniUnitId': {
        let unitItem =
          unitOptions.value?.find(
            (item) => item.value === formData.miniUnitId,
          ) ||
          optionsMap.value?.unitOptions?.find(
            (item) => item.value === formData.miniUnitId,
          );
        if (unitItem) {
          nextTick(() => {
            medicineSpecItem.value.miniUnitName = unitItem.label;
            medicineSpecItem.value.medicineSpec = `${medicineSpecItem.value.doseFactor || ''}${medicineSpecItem.value.doseUnitDesc || ''}/${medicineSpecItem.value.miniUnitName || ''}`;
            handleChange();
          });
        }
        break;
      }
      default:
        break;
    }
  };

  defineExpose({
    formRef,
    medicineSpecDosageUnitListTableRef,
    medicineSpecItem,
    medicineSpecDosageUnitList,
  });
</script>
<template>
  <div class="flex flex-col">
    <div class="flex items-center justify-between">
      <ProForm
        ref="formRef"
        class="mb-4"
        v-model="medicineSpecItem"
        :data="formConfig"
        @model-change="handleFormChange"
      />
      <el-button type="primary" @click="onAddUnitItem">
        {{ $t('global:add') }}
      </el-button>
    </div>
    <ProTable
      ref="medicineSpecDosageUnitListTableRef"
      row-key="medicineSpecDosageUnitId"
      :max-height="400"
      :editable="true"
      :data="medicineSpecDosageUnitList"
      :columns="cadnMedicineSpecDosageUnitTableConfig"
    />
  </div>
</template>
