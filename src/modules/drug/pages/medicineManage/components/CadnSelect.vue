<script setup lang="tsx" name="CadnSelect">
  import { WarningFilled, CirclePlus } from '@element-sun/icons-vue';
  import { useAttrs, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { ProTable } from 'sun-biz';
  import { ONE_PAGE_SIZE } from '@sun-toolkit/enums';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { debounce } from '@sun-toolkit/shared';
  import { queryCadnAndMedicineSpecListByExample } from '@/modules/drug/api/cadn';
  import { useCadnMedicineSpecListTableColumns } from '@/modules/drug/pages/medicineManage/config/useTableConfig';

  const props = defineProps<{
    query?: object;
  }>();

  const attrs: {
    modelValue?: string;
    disabled?: boolean | undefined;
    'onUpdate:modelValue'?: (value: string) => void;
  } = useAttrs();

  const router = useRouter();
  const popoverRef = ref();
  const emit = defineEmits(['change']);

  const cadnName = ref('');
  const selectedCadn = ref<Cadn.CadnMedicineSpecItem>();
  const cadnMedicineSpecList = ref<Cadn.CadnMedicineSpecItem[]>([]); // 通用名，药品服务列表

  const onClick = () => {
    if (attrs.disabled) {
      const page = {
        name: 'cadnDetail',
        params: { id: selectedCadn.value?.cadnId },
        query: { ...(props.query || {}) },
      };
      router.push(page);
      return;
    }
    router.push('/cadnDetail/add');
  };

  const queryMedicineServiceList = async (
    params?: Cadn.MedicineServiceListQueryParams,
  ) => {
    const [, res] = await queryCadnAndMedicineSpecListByExample({
      pageSize: ONE_PAGE_SIZE,
      pageNumber: 1,
      enabledFlag: attrs?.disabled ? undefined : ENABLED_FLAG.YES,
      ...params,
    });
    if (res?.data) {
      cadnMedicineSpecList.value = res.data;
    }
  };

  const handleTableRowClick = (data: Cadn.CadnMedicineSpecItem) => {
    if (attrs['onUpdate:modelValue']) {
      attrs['onUpdate:modelValue'](data.medicineSpecId);
    }
    emit('change', data);
    cadnName.value = `${data.cadn}（${data.medicineSpec}）`;
    selectedCadn.value = data;
    popoverRef.value?.hide();
  };

  const handleInput = debounce(async function (value) {
    queryMedicineServiceList({ keyWord: value || '' });
  }, 500);

  const cadnMedicineSpecListTableColumns =
    useCadnMedicineSpecListTableColumns();

  watch(
    () => attrs?.modelValue,
    async () => {
      await queryMedicineServiceList({
        medicineSpecIds: attrs?.modelValue ? [attrs.modelValue] : undefined,
      });
      if (attrs?.modelValue && cadnMedicineSpecList.value.length) {
        selectedCadn.value = cadnMedicineSpecList.value[0];
        cadnName.value = `${cadnMedicineSpecList.value[0].cadn}（${cadnMedicineSpecList.value[0].medicineSpec}）`;
      }
    },
    {
      once: true,
    },
  );
</script>
<template>
  <div class="flex w-full items-center">
    <el-popover
      placement="bottom"
      width="1000"
      trigger="click"
      ref="popoverRef"
      :disabled="!!attrs?.disabled"
    >
      <div class="h-[260px] overflow-hidden">
        <ProTable
          highlight-current-row
          :columns="cadnMedicineSpecListTableColumns"
          :data="cadnMedicineSpecList"
          :max-height="260"
          row-class-name="cursor-pointer"
          @row-click="handleTableRowClick"
        />
      </div>
      <template #reference>
        <el-input
          v-model="cadnName"
          class="w-full"
          :placeholder="
            $t('global:placeholder.input.template', {
              content: $t('medicine.cadnSelect.cadnName', '通用名'),
            })
          "
          :disabled="!!attrs?.disabled"
          @input="handleInput"
          @focus="() => handleInput('')"
        />
      </template>
    </el-popover>
    <el-icon
      v-if="attrs.disabled"
      class="ml-2 cursor-pointer text-blue-500"
      size="20"
      @click="onClick"
    >
      <WarningFilled />
    </el-icon>
    <el-icon
      v-else
      class="ml-2 cursor-pointer text-blue-500"
      size="20"
      @click="onClick"
    >
      <CirclePlus />
    </el-icon>
  </div>
</template>
