<script setup lang="tsx" name="ChangeAuditDialog">
  import { ref, nextTick, computed } from 'vue';
  import { ProDialog } from 'sun-biz';
  import {
    FLAG,
    COMMODITY_CHANGE_APPROVAL_CODE,
    DATA_OPERATE_TYPE_CODE,
  } from '@/utils/constant';
  import { dayjs, ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  // import { Menu } from '@element-sun/icons-vue';
  import { approvalCommodityChangeByIds } from '@/modules/drug/api/commodityChangeLog';

  const dialogMode = ref(''); // view audit
  const viewMode = ref('list'); // sideBySide list
  const commodityChangeLog = ref<CommodityChangeLog.CommodityChangeLogItem>();

  const dialogTitle = computed(() => {
    switch (dialogMode.value) {
      case 'view':
        return t(
          'medicineManage.changeAuditDialog.modalTitle.view',
          '变更详情',
        );
      case 'audit':
        return t(
          'medicineManage.changeAuditDialog.modalTitle.audit',
          '变更审核',
        );
    }
    return '';
  });

  const { t } = useTranslation();
  const dialogRef = ref();
  const immediatelyExecuteFlag = ref(FLAG.YES); // 立即执行标志
  const executeAt = ref(); // 执行时间

  const emits = defineEmits(['success']);

  const commodityChangeApprovalCodeTagType = computed(() => {
    let type = '';
    if (!commodityChangeLog.value) return type;
    switch (commodityChangeLog.value.commodityChangeApprovalCode) {
      case COMMODITY_CHANGE_APPROVAL_CODE.UN_REVIEWED:
        type = 'primary';
        break;
      case COMMODITY_CHANGE_APPROVAL_CODE.REVIEWED:
        type = 'success';
        break;
      case COMMODITY_CHANGE_APPROVAL_CODE.EXECUTED:
        type = 'warning';
        break;
      case COMMODITY_CHANGE_APPROVAL_CODE.REJECTED:
        type = 'danger';
        break;
    }
    return type;
  });
  const dataOperateTypeCodeTagType = computed(() => {
    let type = '';
    if (!commodityChangeLog.value) return type;
    switch (commodityChangeLog.value.dataOperateTypeCode) {
      case DATA_OPERATE_TYPE_CODE.ADD:
        type = 'primary';
        break;
      case DATA_OPERATE_TYPE_CODE.MODIFY:
        type = 'warning';
        break;
      case DATA_OPERATE_TYPE_CODE.DELETE:
        type = 'danger';
        break;
    }
    return type;
  });

  const onConfirm = () => {
    return [''];
  };

  const handleOpen = (
    mode: string,
    data: CommodityChangeLog.CommodityChangeLogItem,
  ) => {
    dialogMode.value = mode || 'view';
    commodityChangeLog.value = data || undefined;
    dialogRef.value.open();
  };

  const handleClose = () => {
    dialogRef.value.close();
  };

  // 重置弹窗数据
  const handleResetValues = () => {
    immediatelyExecuteFlag.value = FLAG.YES;
    dialogMode.value = 'view';
    commodityChangeLog.value = undefined;
  };

  const handleReject = async () => {
    if (!commodityChangeLog.value) return;
    const [, res] = await approvalCommodityChangeByIds({
      commodityChangeLogIds: [commodityChangeLog.value.commodityChangeLogId],
      agreeFlag: FLAG.NO,
      immediatelyExecuteFlag: immediatelyExecuteFlag.value,
    });
    if (res?.success) {
      emits('success');
      handleClose();
    }
  };

  const handleApprove = async () => {
    if (!commodityChangeLog.value) return;
    if (immediatelyExecuteFlag.value === FLAG.NO && !executeAt.value) {
      ElMessage.warning(
        t(
          'medicineManage.changeAuditDialog.errorTip.handleApproveExecuteAtNeedSelect',
          '审核通过并且非立即执行场景下，执行日期时间不能为空！',
        ),
      );
      return;
    }
    const [, res] = await approvalCommodityChangeByIds({
      commodityChangeLogIds: [commodityChangeLog.value.commodityChangeLogId],
      agreeFlag: FLAG.YES,
      immediatelyExecuteFlag: immediatelyExecuteFlag.value,
      executeAt: executeAt.value ? `${executeAt.value} 00:00:00` : undefined,
    });
    if (res?.success) {
      emits('success');
      handleClose();
    }
  };

  // const handleViewModeChange = () => {
  //   viewMode.value = viewMode.value === 'sideBySide' ? 'list' : 'sideBySide';
  // };

  // 格式化数据展示
  const formattedJson = (data?: string) => {
    if (!data) return '';
    try {
      return JSON.stringify(JSON.parse(data), null, 2);
    } catch {
      return data;
    }
  };

  defineExpose({ dialogRef, open: handleOpen });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    class="w-4/5"
    :title="dialogTitle"
    destroy-on-close
    align-center
    :confirm-fn="onConfirm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :include-footer="false"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          handleResetValues();
          done && done();
        });
      }
    "
  >
    <div v-if="commodityChangeLog" class="p-box flex w-full flex-col">
      <div class="relative mb-3 flex items-center">
        <div class="mr-5 flex items-center">
          <div class="mr-2">
            {{
              $t(
                'medicineManage.changeAuditDialog.commodityChangeApprovalDesc',
                '审核状态',
              )
            }}
          </div>
          <el-tag :type="commodityChangeApprovalCodeTagType">
            {{ commodityChangeLog.commodityChangeApprovalDesc }}
          </el-tag>
        </div>
        <div class="mr-5 flex items-center">
          <div class="mr-2">
            {{
              $t(
                'medicineManage.changeAuditDialog.dataOperateTypeDesc',
                '操作类型',
              )
            }}
          </div>
          <el-tag :type="dataOperateTypeCodeTagType">
            {{ commodityChangeLog.dataOperateTypeDesc }}
          </el-tag>
        </div>
        <div class="mr-5 flex items-center">
          <div class="mr-2">
            {{
              $t('medicineManage.changeAuditDialog.createdUserName', '变更人')
            }}
          </div>
          <div>{{ commodityChangeLog.createdUserName }}</div>
        </div>
        <div class="flex items-center">
          <div class="mr-2">
            {{ $t('medicineManage.changeAuditDialog.createdAt', '变更时间') }}
          </div>
          <div>{{ commodityChangeLog.createdAt }}</div>
        </div>
        <!-- <el-icon
          size="30"
          class="absolute right-0 top-0 cursor-pointer text-gray-500"
          @click="handleViewModeChange"
        >
          <Menu />
        </el-icon> -->
      </div>
      <div
        v-if="viewMode === 'sideBySide'"
        class="flex h-[560px] w-full items-stretch overflow-auto"
      >
        <div class="flex w-1/2 flex-col border border-gray-300">
          <div class="border-b border-gray-300 bg-gray-200 p-4 font-semibold">
            {{ $t('medicineManage.changeAuditDialog.beforeChange', '变更前') }}
          </div>
          <div
            class="flex-1 overflow-auto border-t border-gray-400 bg-white p-2"
          >
            <pre class="whitespace-pre-wrap break-all font-mono">{{
              formattedJson(commodityChangeLog.oldCommodityInfo)
            }}</pre>
          </div>
        </div>
        <div class="flex w-1/2 flex-col border border-gray-300">
          <div class="border-b border-gray-300 bg-gray-200 p-4 font-semibold">
            {{ $t('medicineManage.changeAuditDialog.afterChange', '变更后') }}
          </div>
          <div
            class="flex-1 overflow-auto border-t border-gray-400 bg-white p-2"
          >
            <pre class="whitespace-pre-wrap break-all font-mono">{{
              formattedJson(commodityChangeLog.commodityInfo)
            }}</pre>
          </div>
        </div>
      </div>
      <div
        v-else-if="viewMode === 'list'"
        class="max-h-[500px] w-full overflow-auto"
      >
        <div class="flex w-full">
          <div
            style="width: 20%"
            class="border border-solid border-gray-300 bg-gray-200 p-4 font-semibold"
          >
            {{ $t('medicineManage.changeAuditDialog.field', '标题') }}
          </div>
          <div
            style="width: 40%"
            class="border border-solid border-gray-300 bg-gray-200 p-4 font-semibold"
          >
            {{ $t('medicineManage.changeAuditDialog.beforeChange', '变更前') }}
          </div>
          <div
            style="width: 40%"
            class="border border-solid border-gray-300 bg-gray-200 p-4 font-semibold"
          >
            {{ $t('medicineManage.changeAuditDialog.afterChange', '变更后') }}
          </div>
        </div>
        <div
          v-if="commodityChangeLog.commodityChangeCompareList?.length"
          class="w-full"
        >
          <div
            v-for="(
              item, index
            ) in commodityChangeLog.commodityChangeCompareList"
            :key="index"
            class="flex w-full"
          >
            <div
              class="w-1/5 border border-solid border-gray-300 bg-gray-200 p-4 font-semibold"
            >
              {{ item.changeColumnDisplay }}
            </div>
            <!-- <div class="w-2/5 border border-solid border-gray-300 p-4">
              {{ item.changeColumnOldValue }}
            </div> -->
            <div class="w-2/5 border border-gray-400 bg-white p-4">
              <pre class="whitespace-pre-wrap break-all font-mono">{{
                formattedJson(item.changeColumnOldValue)
              }}</pre>
            </div>
            <!-- <div class="w-2/5 border border-solid border-gray-300 p-4">
              {{ item.changeColumnValue }}
            </div> -->
            <div class="w-2/5 border border-gray-400 bg-white p-4">
              <pre class="whitespace-pre-wrap break-all font-mono">{{
                formattedJson(item.changeColumnValue)
              }}</pre>
            </div>
          </div>
        </div>
        <div v-else>
          <el-empty :description="$t('global:noData')" class="p-0"></el-empty>
        </div>
      </div>
      <div
        v-if="dialogMode === 'audit'"
        class="mt-5 flex items-center justify-end text-right"
      >
        <el-checkbox
          v-model="immediatelyExecuteFlag"
          :label="
            $t(
              'medicineManage.changeAuditDialog.immediatelyExecuteFlag',
              '立即执行',
            )
          "
          :true-value="FLAG.YES"
          :false-value="FLAG.NO"
          @change="
            () => {
              executeAt = undefined;
            }
          "
        />
        <div class="mx-3">
          {{ $t('medicineManage.changeAuditDialog.executeAt', '执行时间') }}
        </div>
        <el-date-picker
          v-model="executeAt"
          :placeholder="
            $t('global:placeholder.select.template', {
              name: $t(
                'medicineManage.changeAuditDialog.executeAt',
                '执行时间',
              ),
            })
          "
          clearable
          type="date"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :disabled="immediatelyExecuteFlag === FLAG.YES"
          :disabled-date="(time: Date) => dayjs(time).isBefore(
                      dayjs(),
                      'second',
                    )"
        />
        <el-button class="ml-3" @click="handleClose">
          {{ $t('global:cancel') }}
        </el-button>
        <el-button type="danger" @click="handleReject">
          {{ $t('medicineManage.changeAuditDialog.reject', '拒绝') }}
        </el-button>
        <el-button type="primary" @click="handleApprove">
          {{ $t('medicineManage.changeAuditDialog.approve', '通过') }}
        </el-button>
      </div>
      <div v-else-if="dialogMode === 'view'" class="mt-5 text-right">
        <el-button type="primary" @click="handleClose">
          {{ $t('global:close') }}
        </el-button>
      </div>
    </div>
  </ProDialog>
</template>
