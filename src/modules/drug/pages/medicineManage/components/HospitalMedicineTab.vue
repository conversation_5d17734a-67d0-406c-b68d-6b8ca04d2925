<script setup lang="ts" name="hospitalMedicineTab">
  import { ref, nextTick, watch, onMounted, computed } from 'vue';
  import { ProTable, ProForm } from 'sun-biz';
  import { SelectOptions } from '@/typings/common';
  import {
    FLAG,
    COMMODITY_CATEGORY_WAY_CODE,
    MEDICINE_TYPE_CODE,
    OUT_MEDICINE_CALC_TYPE_CODE,
  } from '@/utils/constant';
  import { queryCommodityCategoryListByExample } from '@/modules/finance/api/comodityCategory';
  import { useHospitalMedicineFormConfig } from '@/modules/drug/pages/medicineManage/config/useFormConfig';
  import { useMedicineUseSceneXUnitTableConfig } from '@/modules/drug/pages/medicineManage/config/useTableConfig';

  type Props = {
    cadnForm: Partial<Cadn.CadnMedicineSpecItem>;
    editMedicineInfo: Medicine.MedicineListItem | undefined;
    hospitalItem: Partial<Medicine.HospitalMedicineItem>;
    hospitalMedicineOptionsMap: {
      [key: string]: { label?: string; value?: string; description?: string }[];
    };
    medicinePackUnitList: Partial<Medicine.MedicinePackUnitItem>[];
    isAdd: boolean;
  };
  type MedicineUseSceneXUnitTableRow = {
    medicineUseSceneXUnitId?: string;
    medicineUseSceneCode: string;
    medicineUseSceneDesc?: string;
    packUnitId: string;
    packUnitName?: string;
    editable: boolean;
  };

  const props = withDefaults(defineProps<Props>(), {
    hospitalItem: () => ({}),
    hospitalMedicineOptionsMap: () => ({}),
  });
  const emit = defineEmits(['change']);

  // 药品已使用
  const alreadyUseFlag = computed(
    () => props.editMedicineInfo?.alreadyUseFlag === FLAG.YES,
  );

  const hospitalMedicineItem = ref<Partial<Medicine.HospitalMedicineItem>>({});
  const medicineUseSceneXUnitList = ref<MedicineUseSceneXUnitTableRow[]>([]);

  const optionsMap = ref<{
    [key: string]: { label?: string; value?: string; description?: string }[];
  }>({});
  const selectedUnitOptions = ref<SelectOptions[]>([]);
  const hospitalMedicineFormRef = ref();
  const medicineUseSceneXUnitListTableRef = ref();
  const commodityCategoryList = ref<ComodityCategory.ComodityCategoryInfo[]>(
    [],
  );
  const currentCadnForm = ref<Partial<Cadn.CadnListItem>>({});

  // 费用分类Options
  const getCommodityCategoryList = async (
    params: ComodityCategory.QueryParams,
  ) => {
    const [, res] = await queryCommodityCategoryListByExample({ ...params });
    if (res?.data) {
      commodityCategoryList.value = res.data;
      const map = {
        commodityCategory: {},
        outCommodityCategory: {},
        inCommodityCategory: {},
        accCommodityCategory: {},
        fncCommodityCategory: {},
        mrCommodityCategory: {},
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } as any;
      if (commodityCategoryList.value.length) {
        commodityCategoryList.value.forEach((item) => {
          map.commodityCategory[item.commodityCategoryId] = {
            ...item,
            label: item.commodityCategoryName,
            value: item.commodityCategoryId,
          };
          if (item.outCommodityCategoryId) {
            map.outCommodityCategory[item.outCommodityCategoryId] = {
              label: item.outCommodityCategoryName,
              value: item.outCommodityCategoryId,
            };
          }
          if (item.inCommodityCategoryId) {
            map.inCommodityCategory[item.inCommodityCategoryId] = {
              label: item.inCommodityCategoryName,
              value: item.inCommodityCategoryId,
            };
          }
          if (item.accCommodityCategoryId) {
            map.accCommodityCategory[item.accCommodityCategoryId] = {
              label: item.accCommodityCategoryName,
              value: item.accCommodityCategoryId,
            };
          }
          if (item.fncCommodityCategoryId) {
            map.fncCommodityCategory[item.fncCommodityCategoryId] = {
              label: item.fncCommodityCategoryName,
              value: item.fncCommodityCategoryId,
            };
          }
          if (item.mrCommodityCategoryId) {
            map.mrCommodityCategory[item.mrCommodityCategoryId] = {
              label: item.mrCommodityCategoryName,
              value: item.mrCommodityCategoryId,
            };
          }
        });
      }
      map.commodityCategory = Object.values(map.commodityCategory);
      map.outCommodityCategory = Object.values(map.outCommodityCategory);
      map.inCommodityCategory = Object.values(map.inCommodityCategory);
      map.accCommodityCategory = Object.values(map.accCommodityCategory);
      map.fncCommodityCategory = Object.values(map.fncCommodityCategory);
      map.mrCommodityCategory = Object.values(map.mrCommodityCategory);
      optionsMap.value = {
        ...optionsMap.value,
        ...map,
      };
    }
  };

  watch(
    () => props.hospitalItem,
    () => {
      hospitalMedicineItem.value = props.hospitalItem
        ? { ...props.hospitalItem }
        : {};
      medicineUseSceneXUnitList.value =
        props.hospitalItem.medicineUseSceneXUnitList?.map((item) => ({
          ...item,
          editable: !!item?.editable,
        })) || [];
    },
    { deep: true, immediate: true },
  );
  watch(
    () => props.hospitalMedicineOptionsMap,
    () => {
      if (props.hospitalMedicineOptionsMap) {
        optionsMap.value = {
          ...optionsMap.value,
          ...props.hospitalMedicineOptionsMap,
        };
      }
    },
    { deep: true, immediate: true },
  );
  watch(
    () => props.medicinePackUnitList,
    () => {
      selectedUnitOptions.value = (props.medicinePackUnitList || []).map(
        (item) => ({
          label: item?.packUnitName || '',
          value: item?.packUnitId || '',
        }),
      );
    },
    { deep: true, immediate: true },
  );
  watch(
    () => props.cadnForm,
    (newVal) => {
      currentCadnForm.value = newVal;
      if (
        currentCadnForm.value.medicineTypeCode ===
        MEDICINE_TYPE_CODE.CHINESE_HERBAL_MEDICINE
      ) {
        nextTick(() => {
          // 如果药品类型是“中草药”则置灰门诊发药计算方式，并默认为“按实际发”
          const needChange =
            hospitalMedicineItem.value.outMedicineCalcTypeCode !==
            OUT_MEDICINE_CALC_TYPE_CODE.ACTUAL_DISPENSED;
          hospitalMedicineItem.value.outMedicineCalcTypeCode =
            OUT_MEDICINE_CALC_TYPE_CODE.ACTUAL_DISPENSED;
          if (needChange) {
            handleChange();
          }
        });
      }
    },
    { deep: true, immediate: true },
  );

  const handleChange = () => {
    emit('change', {
      ...hospitalMedicineItem.value,
      medicineUseSceneXUnitList: medicineUseSceneXUnitList.value,
    });
  };

  const handleConfirm = (data: MedicineUseSceneXUnitTableRow) => {
    toggleEdit(data);
    handleChange();
  };

  const formConfig = useHospitalMedicineFormConfig(
    alreadyUseFlag,
    optionsMap,
    currentCadnForm,
  );

  const { medicineUseSceneXUnitTableConfig, toggleEdit } =
    useMedicineUseSceneXUnitTableConfig(
      medicineUseSceneXUnitListTableRef,
      medicineUseSceneXUnitList,
      optionsMap,
      selectedUnitOptions,
      handleConfirm,
    );

  const handleHospitalMedicineFormChange = (
    formData: Partial<Medicine.HospitalMedicineItem>,
    name?: string,
  ) => {
    switch (name) {
      case 'commodityCategoryId': {
        const commodityCategoryItem = (formData.commodityCategoryId
          ? optionsMap.value.commodityCategory?.find(
              (item) => item.value === formData.commodityCategoryId,
            )
          : undefined) as unknown as
          | ComodityCategory.ComodityCategoryInfo
          | undefined;
        if (commodityCategoryItem) {
          nextTick(() => {
            if (commodityCategoryItem.outCommodityCategoryId) {
              hospitalMedicineItem.value.outCommodityCategoryId =
                commodityCategoryItem.outCommodityCategoryId;
            }
            if (commodityCategoryItem.inCommodityCategoryId) {
              hospitalMedicineItem.value.inCommodityCategoryId =
                commodityCategoryItem.inCommodityCategoryId;
            }
            if (commodityCategoryItem.accCommodityCategoryId) {
              hospitalMedicineItem.value.accCommodityCategoryId =
                commodityCategoryItem.accCommodityCategoryId;
            }
            if (commodityCategoryItem.fncCommodityCategoryId) {
              hospitalMedicineItem.value.fncCommodityCategoryId =
                commodityCategoryItem.fncCommodityCategoryId;
            }
            if (commodityCategoryItem.mrCommodityCategoryId) {
              hospitalMedicineItem.value.mrCommodityCategoryId =
                commodityCategoryItem.mrCommodityCategoryId;
            }
          });
        }
        break;
      }
      default:
        break;
    }
  };

  onMounted(() => {
    getCommodityCategoryList({
      hospitalId: hospitalMedicineItem.value?.hospitalId,
      commodityCategoryWayCode: COMMODITY_CATEGORY_WAY_CODE.BASIC,
    });
  });

  defineExpose({
    hospitalMedicineFormRef,
    medicineUseSceneXUnitListTableRef,
    hospitalMedicineItem,
    medicineUseSceneXUnitList,
  });
</script>
<template>
  <div class="flex flex-col">
    <ProForm
      ref="hospitalMedicineFormRef"
      class="mb-4"
      v-model="hospitalMedicineItem"
      :data="formConfig"
      @model-change="handleHospitalMedicineFormChange"
    />
    <ProTable
      ref="medicineUseSceneXUnitListTableRef"
      row-key="medicineUseSceneXUnitId"
      :max-height="400"
      :editable="true"
      :data="medicineUseSceneXUnitList"
      :columns="medicineUseSceneXUnitTableConfig"
    />
  </div>
</template>
