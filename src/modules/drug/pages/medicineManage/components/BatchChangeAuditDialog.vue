<script setup lang="tsx" name="ChangeAuditDialog">
  import { ref, nextTick } from 'vue';
  import { ProDialog } from 'sun-biz';
  import { FLAG } from '@/utils/constant';
  import { dayjs, ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { approvalCommodityChangeByIds } from '@/modules/drug/api/commodityChangeLog';

  const props = defineProps<{
    logList?: CommodityChangeLog.CommodityChangeLogItem[];
  }>();

  const { t } = useTranslation();
  const dialogRef = ref();
  const immediatelyExecuteFlag = ref(FLAG.YES); // 立即执行标志
  const executeAt = ref(); // 执行时间

  const emits = defineEmits(['success']);

  const onConfirm = () => {
    return [''];
  };

  const handleOpen = () => {
    dialogRef.value.open();
  };

  const handleClose = () => {
    dialogRef.value.close();
  };

  // 重置弹窗数据
  const handleResetValues = () => {
    immediatelyExecuteFlag.value = FLAG.YES;
  };

  const handleReject = async () => {
    if (!props.logList?.length) return;
    const [, res] = await approvalCommodityChangeByIds({
      commodityChangeLogIds: props.logList.map(
        (item) => item.commodityChangeLogId,
      ),
      agreeFlag: FLAG.NO,
      immediatelyExecuteFlag: immediatelyExecuteFlag.value,
    });
    if (res?.success) {
      emits('success');
      handleClose();
    }
  };

  const handleApprove = async () => {
    if (!props.logList?.length) return;
    if (immediatelyExecuteFlag.value === FLAG.NO && !executeAt.value) {
      ElMessage.warning(
        t(
          'medicineManage.changeAuditDialog.errorTip.handleApproveExecuteAtNeedSelect',
          '审核通过并且非立即执行场景下，执行日期时间不能为空！',
        ),
      );
      return;
    }
    const [, res] = await approvalCommodityChangeByIds({
      commodityChangeLogIds: props.logList.map(
        (item) => item.commodityChangeLogId,
      ),
      agreeFlag: FLAG.YES,
      immediatelyExecuteFlag: immediatelyExecuteFlag.value,
      executeAt: executeAt.value ? `${executeAt.value} 00:00:00` : undefined,
    });
    if (res?.success) {
      emits('success');
      handleClose();
    }
  };

  defineExpose({ dialogRef, open: handleOpen });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    class="w-2/5"
    :title="
      $t('medicineManage.batchChangeAuditDialog.modalTitle', '批量变更审核')
    "
    destroy-on-close
    align-center
    :confirm-fn="onConfirm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :include-footer="false"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          handleResetValues();
          done && done();
        });
      }
    "
  >
    <div class="p-box flex w-full flex-col">
      <div class="flex items-center justify-center">
        <el-checkbox
          v-model="immediatelyExecuteFlag"
          :label="
            $t(
              'medicineManage.batchChangeAuditDialog.immediatelyExecuteFlag',
              '立即执行',
            )
          "
          :true-value="FLAG.YES"
          :false-value="FLAG.NO"
          @change="
            () => {
              executeAt = undefined;
            }
          "
        />
        <div class="mx-3">
          {{
            $t('medicineManage.batchChangeAuditDialog.executeAt', '执行时间')
          }}
        </div>
        <el-date-picker
          v-model="executeAt"
          :placeholder="
            $t('global:placeholder.select.template', {
              name: $t(
                'medicineManage.batchChangeAuditDialog.executeAt',
                '执行时间',
              ),
            })
          "
          clearable
          type="date"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :disabled="immediatelyExecuteFlag === FLAG.YES"
          :disabled-date="(time: Date) => dayjs(time).isBefore(
                      dayjs(),
                      'second',
                    )"
        />
      </div>
      <div class="mt-5 text-right">
        <el-button @click="handleClose">{{ $t('global:cancel') }}</el-button>
        <el-button type="danger" @click="handleReject">
          {{ $t('medicineManage.batchChangeAuditDialog.reject', '拒绝') }}
        </el-button>
        <el-button type="primary" @click="handleApprove">
          {{ $t('medicineManage.batchChangeAuditDialog.approve', '通过') }}
        </el-button>
      </div>
    </div>
  </ProDialog>
</template>
