<script setup lang="ts" name="medicineManage">
  import { computed } from 'vue';
  import { useRoute } from 'vue-router';
  const route = useRoute();
  const key = computed(() => {
    const [, moduleName] = route.path.split('/');
    return moduleName;
  });
</script>
<template>
  <router-view v-slot="{ Component }">
    <component :is="Component" :key="key" class="p-box" />
  </router-view>
</template>
