<script setup lang="ts" name="cadnDetail">
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { Plus } from '@element-sun/icons-vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ref, watch, computed, nextTick, onMounted } from 'vue';
  import {
    ENABLED_FLAG,
    CV08_50_002_NAME,
    DOSE_UNIT_CODE_NAME,
    MEDICINE_TYPE_CODE_NAME,
    SPECIAL_MANAGE_MEDICINE_CODE_NAME,
    FLAG,
  } from '@/utils/constant';
  import { getDictPageFrom } from '@/utils/common';
  import {
    Title,
    ProForm,
    useFetchDataset,
    useDataChangeDetector,
  } from 'sun-biz';
  import { SelectOptions } from '@/typings/common';
  import { generateUUID, cloneDeep } from '@sun-toolkit/shared';
  import { useUnit } from '@/modules/finance/pages/chargeItem/hooks/useOptions';
  import {
    queryCadnListByExample,
    addCadn,
    updateCadnById,
  } from '@/modules/drug/api/cadn';
  import { queryPharmacologyClassListByExample } from '@/modules/baseConfig/api/code';
  import { useCadnFormConfig } from '@/modules/drug/pages/medicineManage/config/useFormConfig';
  import CadnUnitTab from '@modules/drug/pages/medicineManage/components/CadnUnitTab.vue';

  type CadnUpsertInfo = Partial<Cadn.CadnListItem>;
  type MedicineSpecItem = {
    medicineSpecId?: string;
    miniUnitId?: string;
    miniUnitName?: string;
    doseFactor?: number;
    doseUnitCode?: string;
    doseUnitDesc?: string;
    enabledFlag?: number;
    key?: string;
    medicineSpec?: string;
    medicineSpecDosageUnitList?: {
      medicineSpecDosageUnitId?: string;
      doseUnitCode?: string;
      doseUnitDesc?: string;
      convertFactorNumerator?: number;
      convertFactorDenominator?: number;
      enabledFlag?: number;
      editable?: boolean;
      key?: string;
      isDefault?: boolean;
    }[];
  };

  const route = useRoute();
  const router = useRouter();
  const { t } = useTranslation();
  const submitLoading = ref(false);
  const cadnId = computed(() => route?.params?.id as string);
  const isAdd = computed(() => cadnId.value === 'add'); // 是否是新增
  const alreadyUseFlag = computed(
    () => editCadnInfo?.value?.alreadyUseFlag === FLAG.YES,
  );

  const { unitOptions, getUnitList } = useUnit(); // 计价单位 options
  const editCadnInfo = ref<Cadn.CadnListItem>(); // 编辑-通用名对象信息

  const medicineSpecTabsRef = ref();
  const cadnFormRef = ref();
  const cadnForm = ref<CadnUpsertInfo>();
  const medicineSpecList = ref<MedicineSpecItem[]>([]);
  const cadnUnitTabRef = ref();
  const currentMedicineSpecTab = ref('');
  const pageLoading = ref(false);
  const pageFrom = ref(''); // 页面来源

  const optionsMap = ref<{
    [key: string]: SelectOptions[];
  }>({}); // 选择项数据源

  const { updateOriginals } = useDataChangeDetector([
    cadnForm,
    medicineSpecList,
  ]);

  const dataSetList = useFetchDataset([
    MEDICINE_TYPE_CODE_NAME,
    CV08_50_002_NAME,
    SPECIAL_MANAGE_MEDICINE_CODE_NAME,
    DOSE_UNIT_CODE_NAME,
  ]);

  watch(
    () => dataSetList.value,
    () => {
      // 药品类型
      optionsMap.value.medicineTypeOptions =
        dataSetList.value?.[MEDICINE_TYPE_CODE_NAME]?.map(
          (item: Code.CodeSystemInfo) => ({
            value: item?.dataValueNo,
            label: item?.dataValueCnName,
          }),
        ) || [];
      // 药物剂型
      optionsMap.value.cvOptions =
        dataSetList.value?.[CV08_50_002_NAME]?.map(
          (item: Code.CodeSystemInfo) => ({
            value: item?.dataValueNo,
            label: item?.dataValueCnName,
          }),
        ) || [];
      // 特殊管理药物(精麻毒放)
      optionsMap.value.specialManageMedicineOptions =
        dataSetList.value?.[SPECIAL_MANAGE_MEDICINE_CODE_NAME]?.map(
          (item: Code.CodeSystemInfo) => ({
            value: item?.dataValueNo,
            label: item?.dataValueCnName,
          }),
        ) || [];
      // 剂量单位
      optionsMap.value.doseUnitOptions =
        dataSetList.value?.[DOSE_UNIT_CODE_NAME]?.map(
          (item: Code.CodeSystemInfo) => ({
            value: item?.dataValueNo,
            label: item?.dataValueCnName,
          }),
        ) || [];
      nextTick(() => {
        initData();
      });
    },
  );

  const getPharmacologyClassList = async (params?: { keyWord?: string }) => {
    const [, res] = await queryPharmacologyClassListByExample(params || {});
    if (res?.data) {
      optionsMap.value.pharmacologyClassOptions = res.data.map((item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
        children: item?.dataValueSubList
          ? item?.dataValueSubList.map((child) => ({
              value: child?.dataValueNo,
              label: child?.dataValueCnName,
            }))
          : undefined,
      }));
    }
  };

  const goBack = () => {
    if (pageFrom.value === 'medicineManage') {
      router.push({
        name: 'detail',
        params: {
          id: route.query.commodityId as string,
        },
        query: {
          hospitalId: route.query.hospitalId,
        },
      });
    } else if (pageFrom.value === 'medicineCADN') {
      router.push('/');
    }
  };

  const handleTabRemove = (targetName: string) => {
    const index = medicineSpecList.value.findIndex(
      (item) => item.key === targetName,
    );
    medicineSpecList.value?.splice(index, 1);
    currentMedicineSpecTab.value = medicineSpecList.value[
      medicineSpecList.value.length - 1
    ].key as string;
  };

  const checkAllTab = async () => {
    if (!cadnUnitTabRef.value?.length) {
      return true;
    }
    let errorIndex = -1;
    for (let i = 0; i < cadnUnitTabRef.value.length; i++) {
      const { formRef, medicineSpecDosageUnitList } = cadnUnitTabRef.value[i];
      let isValid = true;
      try {
        await formRef?.ref?.validate();
      } catch {
        isValid = false;
      }
      if (!isValid) {
        ElMessage.warning(
          t(
            'medicineManage.cadnDetail.errorTip.formValidateWaring',
            '请正确录入规格表单信息',
          ),
        );
        errorIndex = i;
        break;
      }
      const isEditing = medicineSpecDosageUnitList.some(
        (
          m: Partial<Cadn.MedicineSpecDosageUnitItem> & {
            editable: boolean;
            key?: string;
            isDefault?: boolean;
          },
        ) => m.editable === true,
      );
      if (isEditing) {
        ElMessage.warning(
          t(
            'medicineManage.cadnDetail.errorTip.listIsEditing',
            '列表存在编辑状态中的信息，请先确定！',
          ),
        );
        errorIndex = i;
        break;
      }
    }
    if (errorIndex >= 0) {
      currentMedicineSpecTab.value = medicineSpecList.value[errorIndex]
        .key as string;
      medicineSpecTabsRef.value?.scrollToActiveTab();
    }
    return errorIndex === -1;
  };

  const handleAddMedicineSpec = (isDefault?: boolean) => {
    // isDefault 控制自动生成一条对应的 药品规格可用剂量单位 并且禁止编辑
    const key = generateUUID();
    const item: MedicineSpecItem = {
      key,
      enabledFlag: ENABLED_FLAG.YES,
      miniUnitId: unitOptions.value[0]?.value as string,
      miniUnitName: unitOptions.value[0]?.label,
      doseUnitCode: optionsMap.value.doseUnitOptions[0]?.value as string,
      doseUnitDesc: optionsMap.value.doseUnitOptions[0]?.label,
      medicineSpecDosageUnitList: [
        {
          key,
          isDefault,
          enabledFlag: ENABLED_FLAG.YES,
          doseUnitCode: optionsMap.value.doseUnitOptions[0]?.value as string,
          doseUnitDesc: optionsMap.value.doseUnitOptions[0]?.label,
          convertFactorDenominator: 1,
        },
      ],
    };
    item.medicineSpec = `${item.doseFactor || ''}${item.doseUnitDesc || ''}/${item.miniUnitName || ''}`;
    medicineSpecList.value.push(item);
    currentMedicineSpecTab.value = key;
  };

  const initData = async () => {
    pageLoading.value = true;
    await getUnitList({ pageSize: 400, pageNumber: 1 });
    optionsMap.value = { ...optionsMap.value, unitOptions: unitOptions.value };
    if (isAdd.value) {
      cadnForm.value = {};
      medicineSpecList.value = [];
      handleAddMedicineSpec(true);
    } else {
      const [, res] = await queryCadnListByExample({
        cadnIds: [cadnId.value],
        pageNumber: 1,
        pageSize: 1,
      });
      if (res?.success && res.data.length) {
        editCadnInfo.value = cloneDeep(res.data[0]);
        cadnForm.value = cloneDeep(res.data[0]);
        medicineSpecList.value = (res.data[0].medicineSpecList || []).map(
          (item: MedicineSpecItem) => {
            const medicineSpecDosageUnitList =
              item.medicineSpecDosageUnitList || [];
            const isDefaultIndex = medicineSpecDosageUnitList.findIndex(
              (medicineSpecDosageUnitItem) =>
                medicineSpecDosageUnitItem.convertFactorDenominator === 1 &&
                medicineSpecDosageUnitItem.doseUnitCode === item.doseUnitCode,
            );
            if (isDefaultIndex >= 0) {
              medicineSpecDosageUnitList[isDefaultIndex].isDefault = true;
            }
            return {
              ...item,
              key: item.medicineSpecId,
              medicineSpec: `${item.doseFactor || ''}${item.doseUnitDesc || ''}/${item.miniUnitName || ''}`,
              medicineSpecDosageUnitList,
            };
          },
        );
      }
    }
    currentMedicineSpecTab.value = medicineSpecList.value[0]?.key || '';
    pageLoading.value = false;
    nextTick(() => {
      updateOriginals();
    });
  };

  const onMedicineSpecChange = (data: MedicineSpecItem) => {
    if (data.medicineSpec && data.medicineSpecDosageUnitList?.[0]?.isDefault) {
      data.medicineSpecDosageUnitList[0] = {
        ...data.medicineSpecDosageUnitList[0],
        doseUnitCode: data.doseUnitCode,
        doseUnitDesc: data.doseUnitDesc,
        convertFactorNumerator: Number(data.doseFactor),
        convertFactorDenominator: 1,
      };
    }
    const index = medicineSpecList.value.findIndex(
      (item) => item.key === data.key,
    );
    medicineSpecList.value.splice(index, 1, data);
  };

  const handleSubmit = async () => {
    if (!cadnForm.value || !currentMedicineSpecTab.value) return;
    const cadnFormValid = await cadnFormRef.value.ref.validate();
    if (!cadnFormValid) return;

    const canSave = await checkAllTab();
    if (!canSave) {
      return;
    }
    const medicineSpecListFormat = medicineSpecList.value.map((item, index) => {
      const { medicineSpecItem, medicineSpecDosageUnitList } =
        cadnUnitTabRef.value[index];
      return {
        ...item,
        ...medicineSpecItem,
        medicineSpecDosageUnitList,
      };
    });

    const params = {
      cadnId: isAdd.value ? undefined : cadnId.value,
      ...cadnForm.value,
      medicineSpecList: medicineSpecListFormat,
    };

    const [, res] = isAdd.value
      ? await addCadn(params)
      : await updateCadnById(params);
    if (res?.success) {
      updateOriginals();
      goBack();
    }
  };

  const cadnFormConfig = useCadnFormConfig(
    alreadyUseFlag,
    optionsMap,
    getPharmacologyClassList,
  );

  onMounted(async () => {
    pageLoading.value = true;
    getPharmacologyClassList();
    pageFrom.value = getDictPageFrom();
  });
</script>

<template>
  <div v-loading="pageLoading" class="p-box size-full pr-0">
    <el-page-header @back="goBack">
      <template #content>
        <span class="text-base">
          {{
            isAdd
              ? $t('medicineManage.addCadn', '新增通用名')
              : `${$t('global:edit', '编辑')}-${editCadnInfo?.cadn}`
          }}
        </span>
      </template>
    </el-page-header>
    <div class="my-2 h-full overflow-auto" style="height: calc(100% - 5rem)">
      <el-scrollbar view-class="mr-2.5 mb-4">
        <Title
          :title="$t('medicineManage.cadnDetail.cadnInfo', '通用名信息')"
          class="my-3"
        />
        <ProForm ref="cadnFormRef" v-model="cadnForm" :data="cadnFormConfig" />
        <Title
          :title="$t('medicineManage.cadnDetail.medicineSpecList', '可用规格')"
          class="my-3"
        />
        <el-tabs
          ref="medicineSpecTabsRef"
          v-model="currentMedicineSpecTab"
          class="h-full"
          @tab-remove="handleTabRemove"
        >
          <el-tab-pane
            v-for="item in medicineSpecList"
            :key="item.key"
            :name="item.key"
            :label="item.medicineSpec"
            class="h-full"
            :closable="!item.medicineSpecId && medicineSpecList.length > 1"
          >
            <template #default>
              <CadnUnitTab
                class="h-full"
                ref="cadnUnitTabRef"
                :already-use-flag="alreadyUseFlag"
                :data="item"
                :select-options-map="optionsMap"
                @change="onMedicineSpecChange"
              />
            </template>
          </el-tab-pane>
          <el-tab-pane>
            <template #label>
              <el-icon
                @click.stop="() => handleAddMedicineSpec(true)"
                class="h-full"
                size="20"
              >
                <Plus />
              </el-icon>
            </template>
          </el-tab-pane>
        </el-tabs>
      </el-scrollbar>
    </div>
    <div class="mr-2.5 text-right">
      <el-button @click="goBack">{{ $t('global:cancel') }}</el-button>
      <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
        {{ $t('global:save') }}
      </el-button>
    </div>
  </div>
</template>
