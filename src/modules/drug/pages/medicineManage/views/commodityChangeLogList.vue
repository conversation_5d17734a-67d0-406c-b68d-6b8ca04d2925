<script setup lang="ts" name="commodityChangeLogList">
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import {
    COMMODITY_TYPE_CODE,
    ONE_PAGE_SIZE,
    DEFAULT_PAGE_SIZE,
    COMMODITY_CHANGE_APPROVAL_CODE,
  } from '@/utils/constant';
  import { ProForm, ProTable } from 'sun-biz';
  import { queryMedicineListByExample } from '@/modules/drug/api/medicineManage';
  import {
    queryCommodityChangeLogListByExample,
    executeCommodityChangeByIds,
  } from '@/modules/drug/api/commodityChangeLog';
  import { useCommodityChangeLogSearchFormConfig } from '@/modules/drug/pages/medicineManage/config/useFormConfig';
  import { useCommodityChangeLogListTableColumns } from '@/modules/drug/pages/medicineManage/config/useTableConfig';
  import BatchChangeAuditDialog from '@/modules/drug/pages/medicineManage/components/BatchChangeAuditDialog.vue';
  import ChangeAuditDialog from '@/modules/drug/pages/medicineManage/components/ChangeAuditDialog.vue';

  type CommodityChangeLogItem = CommodityChangeLog.CommodityChangeLogItem & {
    commodityInfoJson?: Medicine.MedicineListItem;
    oldCommodityInfoJson?: Medicine.MedicineListItem;
  };

  const { t } = useTranslation();
  const router = useRouter();
  const batchChangeAuditDialogRef = ref();
  const changeAuditDialogRef = ref();
  const searchParams = ref<CommodityChangeLog.CommodityChangeLogQueryParams>({
    commodityTypeCode: COMMODITY_TYPE_CODE.MEDICATION,
    commodityChangeApprovalCodes: [
      COMMODITY_CHANGE_APPROVAL_CODE.UN_REVIEWED,
      COMMODITY_CHANGE_APPROVAL_CODE.REVIEWED,
    ],
    pageSize: DEFAULT_PAGE_SIZE,
    pageNumber: 1,
  });
  const loading = ref(false);
  const total = ref(0);
  const commodityChangeLogList = ref<CommodityChangeLogItem[]>([]);
  const selections = ref<CommodityChangeLogItem[]>([]);
  const medicineList = ref<Medicine.MedicineListItem[]>([]);

  // 获取药品列表
  const queryMedicineList = async (keyWord?: string) => {
    const params = {
      keyWord,
      pageNumber: 1,
      pageSize: ONE_PAGE_SIZE,
    };
    const [, res] = await queryMedicineListByExample(params);
    if (res?.success) {
      medicineList.value = res.data ?? [];
    }
  };

  // 查询变更记录
  const queryCommodityChangeLogList = async (
    data?: Partial<CommodityChangeLog.CommodityChangeLogQueryParams>,
  ) => {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryCommodityChangeLogListByExample({
      ...searchParams.value,
      commodityTypeCode: COMMODITY_TYPE_CODE.MEDICATION,
    });
    loading.value = false;
    if (res?.data) {
      commodityChangeLogList.value = (res.data ?? []).map((item) => ({
        ...item,
        commodityInfoJson: item.commodityInfo
          ? JSON.parse(item.commodityInfo)
          : {},
        oldCommodityInfoJson: item.oldCommodityInfo
          ? JSON.parse(item.oldCommodityInfo)
          : {},
      }));
      total.value = res.total;
    }
  };

  // 返回主页
  const goBack = () => {
    router.push('/');
  };

  const handleToDetail = (data: CommodityChangeLogItem) => {
    changeAuditDialogRef.value.open('view', data);
  };

  const handleRowAudit = (data: CommodityChangeLogItem) => {
    changeAuditDialogRef.value.open('audit', data);
  };

  const handleRowExecute = async (data: CommodityChangeLogItem) => {
    const [, res] = await executeCommodityChangeByIds({
      commodityChangeLogIds: [data.commodityChangeLogId],
    });
    if (res?.success) {
      queryCommodityChangeLogList();
    }
  };

  const handleBatchAudit = () => {
    if (
      selections.value.some(
        (item) =>
          item.commodityChangeApprovalCode !==
          COMMODITY_CHANGE_APPROVAL_CODE.UN_REVIEWED,
      )
    ) {
      ElMessage.warning(
        t(
          'medicineManage.commodityChangeLogList.errorTip.handleBatchAudit',
          '只有状态为【未审核】的明细方可进行审核！',
        ),
      );
      return;
    }
    batchChangeAuditDialogRef.value.open();
  };

  const handleBatchExecute = async () => {
    if (
      selections.value.some(
        (item) =>
          item.commodityChangeApprovalCode !==
          COMMODITY_CHANGE_APPROVAL_CODE.REVIEWED,
      )
    ) {
      ElMessage.warning(
        t(
          'medicineManage.commodityChangeLogList.errorTip.handleBatchExecute',
          '只有状态为【已审核】的明细方可执行！',
        ),
      );
      return;
    }
    const [, res] = await executeCommodityChangeByIds({
      commodityChangeLogIds: selections.value.map(
        (item) => item.commodityChangeLogId,
      ),
    });
    if (res?.success) {
      queryCommodityChangeLogList();
    }
  };

  const handleSelectChange = (value: CommodityChangeLogItem[]) => {
    selections.value = value;
  };

  const searchConfig = useCommodityChangeLogSearchFormConfig(
    medicineList,
    queryMedicineList,
    queryCommodityChangeLogList,
  );
  const tableColumns = useCommodityChangeLogListTableColumns(
    handleToDetail,
    handleRowAudit,
    handleRowExecute,
  );

  onMounted(() => {
    queryCommodityChangeLogList();
  });
</script>

<template>
  <div class="flex h-full flex-col">
    <el-page-header @back="goBack">
      <template #content>
        <span class="text-base">
          {{ $t('medicineManage.commodityChangeLogList', '药品变更记录') }}
        </span>
      </template>
    </el-page-header>
    <div class="mt-3 flex justify-between">
      <ProForm
        v-model="searchParams"
        :data="searchConfig"
        layout-mode="inline"
        @model-change="queryCommodityChangeLogList"
      />
      <div class="flex-shrink-0">
        <el-button
          v-permission="'YPZD-SHZH'"
          :disabled="!selections.length"
          type="primary"
          @click="handleBatchAudit"
        >
          {{
            $t('medicineManage.commodityChangeLogList.batchAudit', '批量审核')
          }}
        </el-button>
        <el-button
          v-permission="'YPZD-SHZH'"
          :disabled="!selections.length"
          type="primary"
          @click="handleBatchExecute"
        >
          {{
            $t('medicineManage.commodityChangeLogList.batchExecute', '批量执行')
          }}
        </el-button>
      </div>
    </div>
    <pro-table
      row-key="commodityChangeLogId"
      :data="commodityChangeLogList"
      :page-info="{
        total: total,
        pageNumber: searchParams.pageNumber,
        pageSize: searchParams.pageSize,
      }"
      :pagination="true"
      :loading="loading"
      :columns="tableColumns"
      @selection-change="handleSelectChange"
      @current-page-change="
        (val: number) => {
          queryCommodityChangeLogList({ pageNumber: val });
        }
      "
      @size-page-change="
        (val: number) => {
          queryCommodityChangeLogList({ pageSize: val, pageNumber: 1 });
        }
      "
    />
    <BatchChangeAuditDialog
      ref="batchChangeAuditDialogRef"
      :log-list="selections"
      @success="queryCommodityChangeLogList"
    />
    <ChangeAuditDialog
      ref="changeAuditDialogRef"
      @success="queryCommodityChangeLogList"
    />
  </div>
</template>
