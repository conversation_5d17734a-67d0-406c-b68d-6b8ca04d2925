<script setup lang="ts" name="medicineManage">
  import { ref, onMounted, nextTick } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    FLAG,
    FLAG_STR,
    ENABLED_FLAG,
    ORG_TYPE_CODE,
    ONE_PAGE_SIZE,
    DEFAULT_PAGE_SIZE,
    COMMODITY_TYPE_CODE,
    COMMODITY_CHANGE_APPROVAL_CODE,
  } from '@/utils/constant';
  import {
    Title,
    ProForm,
    ProTable,
    useAppConfigData,
    usePrintReceipt,
    MAIN_APP_CONFIG,
  } from 'sun-biz';
  import { SelectOptions } from '@/typings/common';
  import { queryOrgListByExampleFlat } from '@modules/system/api/org';
  import {
    queryMedicineListByExample,
    queryMedicineUseInfoByCommodityId,
  } from '@/modules/drug/api/medicineManage';
  import { queryPharmacologyClassListByExample } from '@/modules/baseConfig/api/code';
  import { queryCommodityChangeLogListByExample } from '@/modules/drug/api/commodityChangeLog';
  import { useMedicineSearchFormConfig } from '@/modules/drug/pages/medicineManage/config/useFormConfig';
  import { useMedicineTableColumnConfig } from '@/modules/drug/pages/medicineManage/config/useTableConfig';
  import MedicineUseInfoUpsertDialog from '@/modules/drug/pages/medicineManage/components/MedicineUseInfoUpsertDialog.vue';

  type MedicineTableItem = Medicine.MedicineListItem & {
    [key: string]: {
      hospitalId?: string;
      hospitalName?: string;
      commodityCategoryName?: string;
      commodityPurchasePrice?: number;
      stockContrilStatusDesc?: string;
      price?: number;
      enabledFlag?: number;
    };
  };

  const router = useRouter();
  const formRef = ref();
  const medicineUseInfoUpsertDialogRef = ref();
  const searchParams = ref<Medicine.MedicineListQueryParams>({
    hospitalId: '',
    enabledFlag: ENABLED_FLAG.ALL,
    valuableFlag: FLAG_STR.ALL,
    pageSize: DEFAULT_PAGE_SIZE,
    pageNumber: 1,
  });
  const loading = ref(false);
  const total = ref(0);
  const medicineList = ref<MedicineTableItem[]>([]);
  const medicineListHospitalList = ref<Medicine.HospitalMedicineItem[]>([]);

  const pharmacologyClassOptions = ref<SelectOptions[]>([]); // 药理分类
  const producedOrgList = ref<Org.Item[]>([]); // 生产厂家
  const isSearchFormExpand = ref(false); // 折叠
  const changeLogTotal = ref(0); // 变更记录数量

  const { currentOrg, menuId } = useAppConfigData([
    MAIN_APP_CONFIG.CURRENT_ORG,
    MAIN_APP_CONFIG.MENU_ID,
  ]);
  const { menuXReceiptList } = usePrintReceipt({
    menuId: menuId as string,
    enabledFlag: FLAG.YES,
  });

  const getFormatMedicineListData = (list: Medicine.MedicineListItem[]) => {
    if (!list?.length) {
      return [];
    }
    const hospitalMap = new Map();
    // hospitalMap.set('test123', {
    //   hospitalName: '测试医院123',
    //   hospitalId: 'test123',
    // });
    list.forEach((item) => {
      item.hospitalMedicineList?.forEach((hospital) => {
        if (!hospitalMap.has(hospital.hospitalId)) {
          hospitalMap.set(hospital.hospitalId, {
            hospitalName: hospital.hospitalName,
            hospitalId: hospital.hospitalId,
          });
        }
      });
    });
    const allHospitals = Array.from(hospitalMap.values());
    medicineListHospitalList.value = allHospitals;
    if (!allHospitals.length) {
      return list as MedicineTableItem[];
    }
    const formattedList: MedicineTableItem[] = list.map((item) => {
      const currentMap = new Map(
        item.hospitalMedicineList?.map((h) => [h.hospitalId, h]),
      );
      const fullHospitalList = allHospitals.map((hospital) => {
        return currentMap.has(hospital.hospitalId)
          ? (currentMap.get(
              hospital.hospitalId,
            ) as Medicine.HospitalMedicineItem)
          : ({
              hospitalId: hospital.hospitalId,
              hospitalName: hospital.hospitalName,
            } as Medicine.HospitalMedicineItem);
      });
      const tableItem = {
        ...item,
        hospitalMedicineList: fullHospitalList,
      } as MedicineTableItem;
      tableItem.hospitalMedicineList?.forEach(
        (hospitalMedicineListItem, index) => {
          tableItem[`hospital${index}`] = {
            hospitalId: hospitalMedicineListItem.hospitalId,
            hospitalName: hospitalMedicineListItem.hospitalName,
            commodityCategoryName:
              hospitalMedicineListItem.commodityCategoryName,
            commodityPurchasePrice:
              hospitalMedicineListItem.commodityPurchasePrice,
            price: hospitalMedicineListItem.price,
            enabledFlag: hospitalMedicineListItem.enabledFlag,
            stockContrilStatusDesc:
              hospitalMedicineListItem.stockContrilStatusDesc,
          };
        },
      );
      return tableItem;
    });
    return formattedList;
  };

  const queryMedicineList = async (
    data?: Partial<Medicine.MedicineListQueryParams>,
  ) => {
    loading.value = true;
    let queryData = {
      ...searchParams.value,
    };
    if (data) {
      queryData = {
        ...searchParams.value,
        ...data,
      };
    }
    if (!queryData.hospitalId) {
      queryData.enabledFlag = ENABLED_FLAG.ALL;
      queryData.stockContrilStatusCode = undefined;
      queryData.encounterTypeCode = undefined;
    }
    searchParams.value = queryData;
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL ||
        !searchParams.value.hospitalId
          ? undefined
          : searchParams.value.enabledFlag,
      valuableFlag:
        searchParams.value.valuableFlag === FLAG_STR.ALL
          ? undefined
          : searchParams.value.valuableFlag,
      stockContrilStatusCode: !searchParams.value.hospitalId
        ? undefined
        : searchParams.value.stockContrilStatusCode,
      encounterTypeCode: !searchParams.value.hospitalId
        ? undefined
        : searchParams.value.encounterTypeCode,
    };
    const [, res] = await queryMedicineListByExample(params);
    if (res?.success) {
      medicineList.value = getFormatMedicineListData(res.data);
      total.value = res.total;
    }
    loading.value = false;
  };

  const handleEdit = (data: MedicineTableItem) => {
    router.push({
      name: 'detail',
      params: {
        id: data.commodityId,
      },
      query: {
        hospitalId:
          currentOrg?.orgTypeCode === ORG_TYPE_CODE.HOSPITAL &&
          searchParams.value.hospitalId
            ? searchParams.value.hospitalId
            : undefined,
      },
    });
  };

  const handleUseInfoClick = async (data: MedicineTableItem) => {
    const [, res] = await queryMedicineUseInfoByCommodityId({
      commodityIds: [data.commodityId],
    });
    if (res?.success) {
      medicineUseInfoUpsertDialogRef.value.open(
        res.data?.[0] || { commodityId: data.commodityId },
      );
    }
  };

  // 查询药理分类
  const getPharmacologyClassList = async (keyWord?: string) => {
    const [, res] = await queryPharmacologyClassListByExample({ keyWord });
    if (res?.data) {
      pharmacologyClassOptions.value = res.data.map((item) => ({
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
        children: item?.dataValueSubList
          ? item?.dataValueSubList.map((child) => ({
              value: child?.dataValueNo,
              label: child?.dataValueCnName,
            }))
          : undefined,
      }));
    }
  };

  // 获取生产厂家列表
  const getOrgList = async (params?: Org.queryReqFlatPageParams) => {
    const [, res] = await queryOrgListByExampleFlat({
      pageSize: ONE_PAGE_SIZE,
      pageNumber: 1,
      enabledFlag: ENABLED_FLAG.YES,
      orgTypeCodes: [ORG_TYPE_CODE.MANUFACTURER],
      ...params,
    });
    if (res?.data) {
      producedOrgList.value = res.data;
    }
  };

  // 查询变更记录
  const queryCommodityChangeLogList = async () => {
    const [, res] = await queryCommodityChangeLogListByExample({
      commodityTypeCode: COMMODITY_TYPE_CODE.MEDICATION,
      commodityChangeApprovalCodes: [
        COMMODITY_CHANGE_APPROVAL_CODE.UN_REVIEWED,
        COMMODITY_CHANGE_APPROVAL_CODE.REVIEWED,
      ],
      pageSize: ONE_PAGE_SIZE,
      pageNumber: 1,
    });
    if (res?.data) {
      changeLogTotal.value = res.total;
    }
  };

  const getRowClassName = (data: {
    row: MedicineTableItem;
    rowIndex: number;
  }) => {
    if (
      searchParams.value.hospitalId &&
      data.row.hospital0.enabledFlag === ENABLED_FLAG.NO
    ) {
      return 'text-red-500';
    }
  };

  const handleReset = () => {
    formRef.value?.ref?.resetFields();
    nextTick(() => {
      searchParams.value = {
        enabledFlag: ENABLED_FLAG.ALL,
        valuableFlag: FLAG_STR.ALL,
        pageNumber: 1,
        pageSize: DEFAULT_PAGE_SIZE,
      };
      queryMedicineList();
    });
  };

  const searchConfig = useMedicineSearchFormConfig(
    searchParams,
    pharmacologyClassOptions,
    getPharmacologyClassList,
    producedOrgList,
    getOrgList,
    queryMedicineList,
  );
  const medicineTableColumns = useMedicineTableColumnConfig(
    menuXReceiptList,
    menuId,
    medicineListHospitalList,
    handleEdit,
    handleUseInfoClick,
  );

  onMounted(() => {
    queryCommodityChangeLogList();
  });
</script>

<template>
  <div class="flex h-full flex-col">
    <Title :title="$t('medicineManage.list.title', '药品列表')" />
    <div class="flex justify-between">
      <div
        :class="`${isSearchFormExpand ? 'h-auto' : 'h-10'} mb-3 mr-8 flex justify-between overflow-y-hidden`"
      >
        <ProForm
          v-model="searchParams"
          ref="formRef"
          class="flex-wrap"
          layout-mode="inline"
          :data="searchConfig"
          @model-change="queryMedicineList"
        />
        <div class="flex-shrink-0">
          <el-button
            type="primary"
            @click="isSearchFormExpand = !isSearchFormExpand"
          >
            {{ $t('medicineManage.list.moreSearch', '更多条件') }}
            <el-icon v-if="isSearchFormExpand">
              <ArrowUp />
            </el-icon>
            <el-icon v-else>
              <ArrowDown />
            </el-icon>
          </el-button>
          <el-button type="primary" @click="() => queryMedicineList()">
            {{ $t('global:query') }}
          </el-button>
          <el-button @click="() => handleReset()">
            {{ $t('global:reset') }}
          </el-button>
        </div>
      </div>
      <div class="flex-shrink-0">
        <el-button
          type="primary"
          @click="
            () => {
              router.push('/commodityChangeLogList');
            }
          "
        >
          {{
            $t(
              'medicineManage.list.changeLogTotal',
              `变更记录(${changeLogTotal})`,
            )
          }}
        </el-button>
        <el-button
          type="primary"
          @click="
            () => {
              router.push('/detail/add');
            }
          "
        >
          {{ $t('global:add') }}
        </el-button>
      </div>
    </div>
    <pro-table
      row-key="commodityId"
      :data="medicineList"
      :page-info="{
        total: total,
        pageNumber: searchParams.pageNumber,
        pageSize: searchParams.pageSize,
      }"
      :row-class-name="getRowClassName"
      :pagination="true"
      :loading="loading"
      :columns="medicineTableColumns"
      @current-page-change="
        (val: number) => {
          queryMedicineList({ pageNumber: val });
        }
      "
      @size-page-change="
        (val: number) => {
          queryMedicineList({ pageSize: val, pageNumber: 1 });
        }
      "
    />
    <MedicineUseInfoUpsertDialog
      ref="medicineUseInfoUpsertDialogRef"
      @success="queryMedicineList"
    />
  </div>
</template>
