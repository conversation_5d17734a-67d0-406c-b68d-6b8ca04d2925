import { Ref } from 'vue';
import {
  useColumnConfig,
  useEditableTable,
  TableRef,
  PrintReceiptBtn,
  type MenuXReceiptListReqItem,
} from 'sun-biz';
import {
  FLAG,
  ENABLED_FLAG,
  FLAG_STR,
  BIZ_ID_TYPE_CODE,
  PRINT_TYPE,
  FORM_OPERATION_TYPE,
  DATA_OPERATE_TYPE_CODE,
  COMMODITY_CHANGE_APPROVAL_CODE,
  MEDICINE_TYPE_CODE,
} from '@/utils/constant';
import { SelectOptions } from '@/typings/common';

type MedicineTableItem = Medicine.MedicineListItem & {
  [key: string]: {
    hospitalId?: string;
    hospitalName?: string;
    commodityCategoryName?: string;
    commodityPurchasePrice?: number;
    price?: number;
    enabledFlag?: number;
  };
};

export function useMedicineTableColumnConfig(
  menuXReceiptList: Ref<MenuXReceiptListReqItem[]>,
  menuId: string | undefined,
  medicineListHospitalList: Ref<Medicine.HospitalMedicineItem[]>,
  handleEdit: (data: MedicineTableItem) => void,
  handleUseInfoClick: (data: MedicineTableItem) => void,
) {
  return useColumnConfig({
    getData: (t) => {
      const data = [
        {
          label: t('global:sequenceNumber'),
          prop: 'indexNo',
          editable: false,
          minWidth: 80,
          render: (row: object, index: number) => <>{index + 1}</>,
        },
        {
          label: t('medicine.medicineTable.commodityNo', '药品编码'),
          minWidth: 120,
          prop: 'commodityNo',
        },
        {
          label: t('medicine.medicineTable.commodityName', '药品名称'),
          minWidth: 140,
          prop: 'commodityName',
        },
        {
          label: t('medicine.medicineTable.commodity2ndName', '药品辅助名称'),
          minWidth: 140,
          prop: 'commodity2ndName',
        },
        {
          label: t('medicine.medicineTable.commodityExtName', '药品扩展名称'),
          minWidth: 140,
          prop: 'commodityExtName',
        },
        {
          label: t('medicine.medicineTable.commoditySpec', '药品规格'),
          minWidth: 110,
          prop: 'commoditySpec',
        },
        {
          label: t('medicine.medicineTable.unitName', '单位'),
          minWidth: 100,
          prop: 'unitName',
        },
        {
          label: t('medicine.medicineTable.producedByOrgName', '生产厂家'),
          minWidth: 150,
          prop: 'producedByOrgName',
        },
        {
          label: t('medicine.medicineTable.validPeriod', '有效期(月)'),
          minWidth: 130,
          prop: 'validPeriod',
        },
        {
          label: t(
            'medicine.medicineTable.chargeItemLevelDesc',
            '医保收费等级',
          ),
          minWidth: 130,
          prop: 'chargeItemLevelDesc',
        },
        {
          label: t(
            'medicine.medicineTable.antibacterialLevelDesc',
            '抗菌药物等级',
          ),
          minWidth: 130,
          prop: 'antibacterialLevelDesc',
        },
        {
          label: t(
            'medicine.medicineTable.antitumorDrugLevelDesc',
            '抗肿瘤药等级',
          ),
          minWidth: 130,
          prop: 'antitumorDrugLevelDesc',
        },
        {
          label: t('medicine.medicineTable.valuableFlag', '贵重标志'),
          minWidth: 100,
          prop: 'valuableFlag',
          render: (row: MedicineTableItem) => (
            <div>
              {row.valuableFlag === FLAG_STR.YES
                ? t('global:yes')
                : t('global:no')}
            </div>
          ),
        },

        {
          label: t('global:operation'),
          prop: 'operation',
          fixed: 'right',
          width: 180,
          render: (row: MedicineTableItem) => {
            return (
              <div class="flex items-center">
                <PrintReceiptBtn
                  class="mr-2"
                  receiptList={menuXReceiptList.value || []}
                  printParams={{
                    bizIds: [row.commodityId],
                    receiptId: '1705419821992484864',
                    menuId: menuId,
                    designFlag: FLAG.NO,
                    bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_COMMODITY,
                    printType: PRINT_TYPE.PRINT,
                    formOperationType: FORM_OPERATION_TYPE.PRINT,
                  }}
                />
                <el-button
                  type="primary"
                  link={true}
                  disabled={row.existsApprovalChangeFlag === FLAG.YES}
                  onClick={() => handleEdit(row)}
                >
                  {t('global:edit')}
                </el-button>
                {row.medicineTypeCode &&
                  [
                    MEDICINE_TYPE_CODE.WESTERN_MEDICINE as string,
                    MEDICINE_TYPE_CODE.PROPRIETARY_CHINESE_MEDICINE,
                  ].includes(row.medicineTypeCode) && (
                    <el-dropdown
                      class="ml-2"
                      v-slots={{
                        default: () => (
                          <el-button link type="primary">
                            {t('medicine.medicineTable.more', '更多')}
                          </el-button>
                        ),
                        dropdown: () => (
                          <el-dropdown-menu>
                            <el-dropdown-item
                              onClick={() => handleUseInfoClick(row)}
                            >
                              {t(
                                'medicine.medicineTable.UseInfo',
                                '默认用法用量',
                              )}
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        ),
                      }}
                    ></el-dropdown>
                  )}
              </div>
            );
          },
        },
      ];
      if (medicineListHospitalList.value?.length === 1) {
        const hospitalItemColumnList = [
          {
            label: t(
              'medicine.medicineTable.stockContrilStatusDesc',
              '库存控制状态',
            ),
            minWidth: 160,
            prop: 'hospital0.stockContrilStatusDesc',
          },
          {
            label: t(
              'medicine.medicineTable.commodityCategoryName',
              '费用分类',
            ),
            minWidth: 130,
            prop: 'hospital0.commodityCategoryName',
          },
          {
            label: t(
              'medicine.medicineTable.commodityPurchasePrice',
              '参考进价',
            ),
            minWidth: 120,
            autoFormatterNumber: true,
            prop: 'hospital0.commodityPurchasePrice',
          },
          {
            label: t('medicine.medicineTable.price', '参考零售价'),
            minWidth: 120,
            prop: 'hospital0.price',
            autoFormatterNumber: true,
          },
          {
            label: t('global:enabledFlag'),
            prop: 'enabledFlag',
            minWidth: 100,
            render: (row: MedicineTableItem) => {
              return (
                <el-switch
                  modelValue={row.hospital0.enabledFlag}
                  inline-prompt
                  active-value={ENABLED_FLAG.YES}
                  inactive-value={ENABLED_FLAG.NO}
                  disabled={true}
                  active-text={t('global:enabled')}
                  inactive-text={t('global:disabled')}
                />
              );
            },
          },
        ];
        data.splice(13, 0, ...hospitalItemColumnList);
      } else if (medicineListHospitalList.value?.length > 1) {
        const hospitalItemColumnList = [];
        for (let i = 0; i < medicineListHospitalList.value.length; i++) {
          hospitalItemColumnList.push({
            label: medicineListHospitalList.value[i].hospitalName,
            prop: `hospital${i}`,
            _children: [
              {
                label: t(
                  'medicine.medicineTable.stockContrilStatusDesc',
                  '库存控制状态',
                ),
                minWidth: 160,
                prop: `hospital${i}.stockContrilStatusDesc`,
              },
              {
                label: t(
                  'medicine.medicineTable.commodityCategoryName',
                  '费用分类',
                ),
                minWidth: 130,
                prop: `hospital${i}.commodityCategoryName`,
              },
              {
                label: t(
                  'medicine.medicineTable.commodityPurchasePrice',
                  '参考进价',
                ),
                minWidth: 120,
                prop: `hospital${i}.commodityPurchasePrice`,
                autoFormatterNumber: true,
              },
              {
                label: t('medicine.medicineTable.price', '参考零售价'),
                minWidth: 120,
                prop: `hospital${i}.price`,
                autoFormatterNumber: true,
              },
              {
                label: t('global:enabledFlag'),
                prop: 'enabledFlag',
                minWidth: 100,
                render: (row: MedicineTableItem) => {
                  return (
                    <el-switch
                      modelValue={row[`hospital${i}`]?.enabledFlag}
                      inline-prompt
                      active-value={ENABLED_FLAG.YES}
                      inactive-value={ENABLED_FLAG.NO}
                      disabled={true}
                      active-text={t('global:enabled')}
                      inactive-text={t('global:disabled')}
                    />
                  );
                },
              },
            ],
          });
        }
        data.splice(13, 0, ...hospitalItemColumnList);
      }
      return data;
    },
  });
}

type MedicinePackUnitTableRow = Partial<Medicine.MedicinePackUnitItem> & {
  isUnit?: boolean;
  editable: boolean;
};

export function useMedicinePackUnitTableConfig(
  tableRef: Ref<TableRef>,
  tableData: Ref<MedicinePackUnitTableRow[]>,
  cadnFormData: Ref<Partial<Cadn.CadnMedicineSpecItem>>,
  alreadyUseFlag: Ref<boolean>,
  unitOptions: Ref<SelectOptions[]>,
  getUnitList: (params?: Unit.QueryParams) => void,
  handleIsUnitChange: (index: number) => void,
  handleDelMedicinePackUnit: (
    data: MedicinePackUnitTableRow,
    index: number,
  ) => void,
  handleMedicinePackUnitConfirm: (data: MedicinePackUnitTableRow) => void,
) {
  const { toggleEdit, cancelEdit, addItem, delItem } = useEditableTable({
    tableRef,
    data: tableData,
    id: 'medicinePackUnitId',
  });

  const medicinePackUnitTableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        width: 80,
        render: (row: MedicinePackUnitTableRow, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t('medicine.medicinePackUnitTable.packUnitName', '包装单位'),
        prop: 'packUnitId',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'medicine.medicinePackUnitTable.packUnitName',
                '包装单位',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: MedicinePackUnitTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.packUnitId}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'medicine.medicinePackUnitTable.packUnitName',
                      '包装单位',
                    ),
                  })}
                  clearable={false}
                  filterable={true}
                  remote={true}
                  remoteMethod={(keyWord: string) => {
                    getUnitList({ keyWord, pageNumber: 1, pageSize: 100 });
                  }}
                  onChange={(val: string) => {
                    const item = unitOptions.value?.find(
                      (item) => item.value === val,
                    );
                    row.packUnitName = item?.label as string;
                    if (val && val === cadnFormData.value.miniUnitId) {
                      row.convertFactor = 1;
                    }
                  }}
                >
                  {unitOptions.value?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                      disabled={tableData.value.some(
                        (dataItem) => dataItem.packUnitId === item.value,
                      )}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.packUnitName}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('medicine.medicinePackUnitTable.convertFactor', '换算系数'),
        prop: 'convertFactor',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'medicine.medicinePackUnitTable.convertFactor',
                '换算系数',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: MedicinePackUnitTableRow) => {
          return (
            <div class="w-full">
              {row.editable ? (
                <el-input
                  v-model={row.convertFactor}
                  onInput={(val: string) => {
                    row.convertFactor = Number(
                      val.replace(/[^\d]/g, '').replace(/^0+/, ''),
                    );
                  }}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'medicine.medicinePackUnitTable.convertFactor',
                      '换算系数',
                    ),
                  })}
                  disabled={row.packUnitId === cadnFormData.value.miniUnitId}
                  v-slots={{
                    append: () => (
                      <span>
                        {cadnFormData.value?.miniUnitName && row.packUnitName
                          ? `${cadnFormData.value?.miniUnitName}/${row.packUnitName}`
                          : ''}
                      </span>
                    ),
                  }}
                ></el-input>
              ) : (
                <div class="relative w-full">
                  <div>{row.convertFactor}</div>
                  <div class="absolute right-1 top-0">
                    {cadnFormData.value?.miniUnitName && row.packUnitName
                      ? `${cadnFormData.value?.miniUnitName}/${row.packUnitName}`
                      : ''}
                  </div>
                </div>
              )}
            </div>
          );
        },
      },
      {
        label: t('medicine.medicinePackUnitTable.isUnit', '计价单位'),
        prop: 'isUnit',
        width: 120,
        render: (row: MedicinePackUnitTableRow, index: number) => {
          return (
            <el-radio
              v-model={row.isUnit}
              disabled={!!alreadyUseFlag.value}
              value={true}
              onChange={() => handleIsUnitChange(index)}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 160,
        render: (row: MedicinePackUnitTableRow, $index: number) => {
          return row.editable ? (
            <div class={'flex justify-around'} key="editable">
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => handleMedicinePackUnitConfirm(row)}
              >
                {t('global:confirm')}
              </el-button>
            </div>
          ) : (
            <div class={'flex justify-around'}>
              <el-button
                type="danger"
                link={true}
                disabled={
                  !!row.isUnit ||
                  !!(alreadyUseFlag.value && row.medicinePackUnitId)
                }
                onClick={() => handleDelMedicinePackUnit(row, $index)}
              >
                {t('global:remove')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                disabled={!!(alreadyUseFlag.value && row.medicinePackUnitId)}
                onClick={() => toggleEdit(row)}
              >
                {t('global:edit')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return {
    medicinePackUnitTableConfig,
    addItem,
    delItem,
    toggleEdit,
  };
}

type MedicineUseSceneXUnitTableRow = {
  medicineUseSceneXUnitId?: string;
  medicineUseSceneCode: string;
  medicineUseSceneDesc?: string;
  packUnitId: string;
  packUnitName?: string;
  editable: boolean;
};

export function useMedicineUseSceneXUnitTableConfig(
  tableRef: Ref<TableRef>,
  tableData: Ref<MedicineUseSceneXUnitTableRow[]>,
  optionsMap: Ref<{
    [key: string]: { label?: string; value?: string; description?: string }[];
  }>,
  selectedUnitOptions: Ref<SelectOptions[]>,
  handleConfirm: (row: MedicineUseSceneXUnitTableRow) => void,
) {
  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef,
    data: tableData,
    id: 'medicineUseSceneXUnitId',
  });

  const medicineUseSceneXUnitTableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        width: 80,
        render: (row: MedicineUseSceneXUnitTableRow, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t(
          'medicine.medicineUseSceneXUnitTable.medicineUseSceneDesc',
          '应用场景',
        ),
        width: 380,
        prop: 'medicineUseSceneCode',
        // editable: true,
        // rules: [
        //   {
        //     required: true,
        //     message: t('global:placeholder.select.template', {
        //       name: t(
        //         'medicine.medicineUseSceneXUnitTable.medicineUseSceneDesc',
        //         '应用场景',
        //       ),
        //     }),
        //     trigger: ['blur', 'change'],
        //   },
        // ],
        render: (row: MedicineUseSceneXUnitTableRow) => {
          return (
            <div class={'w-full'}>
              {/* {row.editable ? (
                <el-select
                  v-model={row.medicineUseSceneCode}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'medicine.medicineUseSceneXUnitTable.medicineUseSceneDesc',
                      '应用场景',
                    ),
                  })}
                  onChange={(val: string) => {
                    const item = (
                      optionsMap.value.medicineUseSceneOptions || []
                    )?.find((item) => item.value === val);
                    row.medicineUseSceneDesc = item?.label as string;
                  }}
                >
                  {(optionsMap.value.medicineUseSceneOptions || []).map(
                    (item) => (
                      <el-option
                        key={item.value}
                        label={item.label}
                        value={item.value}
                      />
                    ),
                  )}
                </el-select>
              ) : ( */}
              <>{row.medicineUseSceneDesc}</>
              {/* )} */}
            </div>
          );
        },
      },
      {
        label: t(
          'medicine.medicineUseSceneXUnitTable.packUnitName',
          '包装单位',
        ),
        prop: 'packUnitId',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'medicine.medicineUseSceneXUnitTable.packUnitName',
                '包装单位',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: MedicineUseSceneXUnitTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.packUnitId}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'medicine.medicineUseSceneXUnitTable.packUnitName',
                      '包装单位',
                    ),
                  })}
                  onChange={(val: string) => {
                    const item = selectedUnitOptions.value?.find(
                      (item) => item.value === val,
                    );
                    row.packUnitName = item?.label as string;
                  }}
                >
                  {selectedUnitOptions.value?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.packUnitName}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 200,
        render: (row: MedicineUseSceneXUnitTableRow, $index: number) => {
          return row.editable ? (
            <div class={'flex justify-around'} key="editable">
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index, false)}
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => handleConfirm(row)}
              >
                {t('global:confirm')}
              </el-button>
            </div>
          ) : (
            <div class={'flex justify-around'}>
              {/* <el-button
                    type="danger"
                    link={true}
                    onClick={() => delItem($index)}
                  >
                    {t('global:remove')}
                  </el-button> */}
              <el-button
                type="primary"
                link={true}
                onClick={() => toggleEdit(row)}
              >
                {t('global:edit')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return {
    medicineUseSceneXUnitTableConfig,
    toggleEdit,
  };
}

type CadnMedicineSpecDosageUnitTableRow =
  Partial<Cadn.MedicineSpecDosageUnitItem> & {
    editable: boolean;
    isDefault?: boolean;
  };

export function useCadnMedicineSpecDosageUnitTableConfig(
  alreadyUseFlag: boolean | undefined,
  tableRef: Ref<TableRef>,
  tableData: Ref<CadnMedicineSpecDosageUnitTableRow[]>,
  medicineSpecItem: Ref<
    Partial<Cadn.MedicineSpecItem & { medicineSpec?: string; key?: string }>
  >,
  optionsMap: Ref<{
    [key: string]: SelectOptions[];
  }>,
  handleRowConfirm: (row: CadnMedicineSpecDosageUnitTableRow) => void,
) {
  const { toggleEdit, cancelEdit, addItem } = useEditableTable({
    tableRef,
    data: tableData,
    id: 'medicineSpecDosageUnitId',
  });

  const cadnMedicineSpecDosageUnitTableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        width: 80,
        render: (row: CadnMedicineSpecDosageUnitTableRow, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t(
          'medicine.cadnMedicineSpecDosageUnitTable.doseUnitCode',
          '可用剂量单位',
        ),
        width: 450,
        prop: 'doseUnitCode',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'medicine.cadnMedicineSpecDosageUnitTable.doseUnitCode',
                '可用剂量单位',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: CadnMedicineSpecDosageUnitTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.doseUnitCode}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'medicine.cadnMedicineSpecDosageUnitTable.doseUnitCode',
                      '可用剂量单位',
                    ),
                  })}
                  filterable={true}
                  onChange={(val: string) => {
                    const item = optionsMap.value.doseUnitOptions?.find(
                      (item) => item.value === val,
                    );
                    row.doseUnitDesc = item?.label as string;
                  }}
                >
                  {optionsMap.value.doseUnitOptions?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                      disabled={tableData.value.some(
                        (dataItem) => dataItem.doseUnitCode === item.value,
                      )}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.doseUnitDesc}</>
              )}
            </div>
          );
        },
      },
      {
        label: t(
          'medicine.cadnMedicineSpecDosageUnitTable.convertFactorNumerator',
          '换算系数',
        ),
        prop: 'convertFactorNumerator',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'medicine.cadnMedicineSpecDosageUnitTable.convertFactorNumerator',
                '换算系数',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: CadnMedicineSpecDosageUnitTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <div class="flex items-center justify-center">
                  <el-input-number
                    v-model={row.convertFactorNumerator}
                    min={0}
                    precision={2}
                    controls={false}
                  />
                  <div class="ml-2">{row.doseUnitDesc || ''}</div>
                  <div class="mx-5">=</div>
                  <el-input-number
                    v-model={row.convertFactorDenominator}
                    min={0}
                    precision={2}
                    controls={false}
                  />
                  <div class="ml-2">
                    {medicineSpecItem.value.miniUnitName || ''}
                  </div>
                </div>
              ) : (
                <>
                  {row.convertFactorNumerator &&
                  row.convertFactorDenominator &&
                  row.doseUnitDesc &&
                  medicineSpecItem.value.doseUnitDesc
                    ? `${row.convertFactorNumerator}${row.doseUnitDesc}/${row.convertFactorDenominator}${medicineSpecItem.value.miniUnitName}`
                    : ''}
                </>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        width: 100,
        render: (row: CadnMedicineSpecDosageUnitTableRow) => {
          return (
            <el-switch
              v-model={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
              disabled={
                !!row.isDefault ||
                (!!alreadyUseFlag && !!row.medicineSpecDosageUnitId)
              }
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 200,
        render: (row: CadnMedicineSpecDosageUnitTableRow, $index: number) => {
          return row.editable ? (
            <div class={'flex justify-around'} key="editable">
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => handleRowConfirm(row)}
              >
                {t('global:confirm')}
              </el-button>
            </div>
          ) : (
            <div class={'flex justify-around'}>
              {/* <el-button
                    type="danger"
                    link={true}
                    onClick={() => delItem($index)}
                  >
                    {t('global:remove')}
                  </el-button> */}
              <el-button
                type="primary"
                disabled={
                  !!row.isDefault ||
                  (!!alreadyUseFlag && !!row.medicineSpecDosageUnitId)
                }
                link={true}
                onClick={() => toggleEdit(row)}
              >
                {t('global:edit')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return {
    cadnMedicineSpecDosageUnitTableConfig,
    addItem,
    toggleEdit,
  };
}

export function useCadnMedicineSpecListTableColumns() {
  return useColumnConfig({
    getData: (t) => {
      const data = [
        {
          label: t('global.sequenceNumber', '序号'),
          minWidth: 60,
          prop: 'indexNo',
          render: (row: object, $index: number) => <>{$index + 1}</>,
        },
        {
          label: t('medicine.CadnMedicineSpecListTable.cadn', '通用名'),
          prop: 'cadn',
          minWidth: 100,
        },
        {
          label: t(
            'medicine.CadnMedicineSpecListTable.medicineSpec',
            '药品规格',
          ),
          prop: 'medicineSpec',
          minWidth: 100,
        },
        {
          label: t(
            'medicine.CadnMedicineSpecListTable.medicineTypeDesc',
            '药品类型',
          ),
          prop: 'medicineTypeDesc',
          minWidth: 100,
        },
        {
          label: t('medicine.CadnMedicineSpecListTable.dosageFormDesc', '剂型'),
          prop: 'dosageFormDesc',
          minWidth: 100,
        },
      ];
      return data;
    },
  });
}

const getDataOperateTypeBgColor = (dataOperateTypeCode?: string) => {
  if (!dataOperateTypeCode) return '';
  switch (dataOperateTypeCode) {
    case DATA_OPERATE_TYPE_CODE.ADD:
      return 'bg-blue-400';
    case DATA_OPERATE_TYPE_CODE.MODIFY:
      return 'bg-orange-400';
    case DATA_OPERATE_TYPE_CODE.DELETE:
      return 'bg-red-400';
  }
};
// tag颜色
const getCommodityChangeApprovalCodeTagType = (status?: string) => {
  if (!status) return '';
  switch (status) {
    case COMMODITY_CHANGE_APPROVAL_CODE.UN_REVIEWED:
      return 'primary';
    case COMMODITY_CHANGE_APPROVAL_CODE.REVIEWED:
      return 'success';
    case COMMODITY_CHANGE_APPROVAL_CODE.EXECUTED:
      return 'warning';
    case COMMODITY_CHANGE_APPROVAL_CODE.REJECTED:
      return 'danger';
  }
};

type CommodityChangeLogItem = CommodityChangeLog.CommodityChangeLogItem & {
  commodityInfoJson?: Medicine.MedicineListItem;
  oldCommodityInfoJson?: Medicine.MedicineListItem;
};

export function useCommodityChangeLogListTableColumns(
  handleToDetail: (data: CommodityChangeLogItem) => void,
  handleRowAudit: (data: CommodityChangeLogItem) => void,
  handleRowExecute: (data: CommodityChangeLogItem) => void,
) {
  return useColumnConfig({
    getData: (t) => {
      const data = [
        {
          label: t('global:select'),
          prop: 'indexNo',
          type: 'selection',
          minWidth: 60,
        },
        {
          label: t(
            'medicine.commodityChangeLogListTable.commodityChangeLogId',
            '变更记录标识',
          ),
          prop: 'commodityChangeLogId',
          minWidth: 190,
        },
        {
          label: t(
            'medicine.commodityChangeLogListTable.dataOperateTypeDesc',
            '操作类型',
          ),
          prop: 'dataOperateTypeDesc',
          minWidth: 100,
          render: (row: CommodityChangeLogItem) => {
            return (
              <div class="flex items-center justify-center">
                <div
                  class={`mr-2 h-1 w-1 rounded-full ${getDataOperateTypeBgColor(
                    row.dataOperateTypeCode,
                  )}`}
                ></div>
                <div>{row.dataOperateTypeDesc}</div>
              </div>
            );
          },
        },
        {
          label: t(
            'medicine.commodityChangeLogListTable.commodityChangeApprovalDesc',
            '审核状态',
          ),
          prop: 'commodityChangeApprovalDesc',
          minWidth: 100,
          render: (row: CommodityChangeLogItem) => {
            return (
              <el-tag
                type={getCommodityChangeApprovalCodeTagType(
                  row.commodityChangeApprovalCode,
                )}
              >
                {row.commodityChangeApprovalDesc}
              </el-tag>
            );
          },
        },
        {
          label: t(
            'medicine.commodityChangeLogListTable.createdUserName',
            '变更人',
          ),
          prop: 'createdUserName',
          minWidth: 100,
        },
        {
          label: t(
            'medicine.commodityChangeLogListTable.createdAt',
            '变更日期时间',
          ),
          prop: 'createdAt',
          minWidth: 170,
        },
        {
          label: t(
            'medicine.commodityChangeLogListTable.executeAt',
            '执行日期时间',
          ),
          prop: 'executeAt',
          minWidth: 170,
        },
        {
          label: t(
            'medicine.commodityChangeLogListTable.commodityNo',
            '药品编码',
          ),
          prop: 'commodityNo',
          minWidth: 120,
          render: (row: CommodityChangeLogItem) => (
            <>{row.commodityInfoJson?.commodityNo || '--'}</>
          ),
        },
        {
          label: t(
            'medicine.commodityChangeLogListTable.commodityName',
            '药品名称',
          ),
          prop: 'commodityName',
          minWidth: 140,
          render: (row: CommodityChangeLogItem) => (
            <>{row.commodityInfoJson?.commodityName || '--'}</>
          ),
        },
        {
          label: t(
            'medicine.commodityChangeLogListTable.commoditySpec',
            '药品规格',
          ),
          prop: 'commoditySpec',
          minWidth: 110,
          render: (row: CommodityChangeLogItem) => (
            <>{row.commodityInfoJson?.commoditySpec || '--'}</>
          ),
        },
        {
          label: t('medicine.commodityChangeLogListTable.unitId', '单位'),
          prop: 'unitId',
          minWidth: 100,
          render: (row: CommodityChangeLogItem) => (
            <>
              {row.commodityChangeCompareList?.find(
                (item) => item.changeColumnName === 'unitId',
              )?.changeColumnOldValue || '--'}
            </>
          ),
        },
        {
          label: t(
            'medicine.commodityChangeLogListTable.producedByOrgId',
            '生产厂家',
          ),
          prop: 'producedByOrgId',
          minWidth: 140,
          render: (row: CommodityChangeLogItem) => (
            <>
              {row.commodityChangeCompareList?.find(
                (item) => item.changeColumnName === 'producedByOrgId',
              )?.changeColumnOldValue || '--'}
            </>
          ),
        },
        {
          label: t(
            'medicine.commodityChangeLogListTable.chargeItemLevelCode',
            '医保收费等级',
          ),
          prop: 'chargeItemLevelCode',
          minWidth: 120,
          render: (row: CommodityChangeLogItem) => (
            <>
              {row.commodityChangeCompareList?.find(
                (item) => item.changeColumnName === 'chargeItemLevelCode',
              )?.changeColumnOldValue || '--'}
            </>
          ),
        },
        {
          label: t('global:operation'),
          prop: 'operation',
          width: 180,
          fixed: 'right',
          render: (row: CommodityChangeLogItem) => {
            return (
              <div class={'flex justify-around'}>
                <el-button
                  type="primary"
                  link={true}
                  onClick={() => handleToDetail(row)}
                >
                  {t('medicine.commodityChangeLogListTable.detail', '详情')}
                </el-button>
                <el-button
                  v-permission={'YPZD-SHZH'}
                  disabled={
                    row.commodityChangeApprovalCode !==
                    COMMODITY_CHANGE_APPROVAL_CODE.UN_REVIEWED
                  }
                  type="primary"
                  link={true}
                  onClick={() => handleRowAudit(row)}
                >
                  {t('medicine.commodityChangeLogListTable.audit', '审核')}
                </el-button>
                <el-button
                  v-permission={'YPZD-SHZH'}
                  disabled={
                    row.commodityChangeApprovalCode !==
                    COMMODITY_CHANGE_APPROVAL_CODE.REVIEWED
                  }
                  type="primary"
                  link={true}
                  onClick={() => handleRowExecute(row)}
                >
                  {t('medicine.commodityChangeLogListTable.execute', '执行')}
                </el-button>
              </div>
            );
          },
        },
      ];
      return data;
    },
  });
}
