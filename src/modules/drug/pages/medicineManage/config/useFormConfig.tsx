import { Ref, ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import {
  FLAG,
  FLAG_STR,
  ENABLED_FLAG,
  MEDICINE_TYPE_CODE_NAME,
  ENCOUNTER_TYPE_CODE_NAME,
  ENCOUNTER_TYPE_CODE,
  CV08_50_002_NAME,
  CHARGE_ITEM_LEVEL_CODE_NAME,
  ANTIBACTERIAL_LEVEL_CODE_NAME,
  ANTITUMOR_DRUG_LEVEL_CODE_NAME,
  STOCK_CONTRIL_STATUS_CODE_NAME,
  DATA_OPERATE_TYPE_CODE_NAME,
  COMMODITY_CHANGE_APPROVAL_CODE_NAME,
  MEDICINE_TYPE_CODE,
  DOSE_UNIT_CODE_NAME,
} from '@/utils/constant';
import { SelectOptions } from '@/typings/common';
import { QuestionFilled } from '@element-sun/icons-vue';
import CadnSelect from '@modules/drug/pages/medicineManage/components/CadnSelect.vue';
import { AdminRouteResItem } from '@/modules/cisOutp/typing/adminRouteFreq';

export function useMedicineSearchFormConfig(
  searchParams: Ref<Medicine.MedicineListQueryParams>,
  pharmacologyClassOptions: Ref<SelectOptions[]>,
  getPharmacologyClassList: (keyWord?: string) => void,
  producedOrgList: Ref<Org.Item[]>,
  getOrgList: (params?: Org.queryReqFlatPageParams) => void,
  queryMedicineList: (data?: Partial<Medicine.MedicineListQueryParams>) => void,
) {
  const data = useFormConfig({
    dataSetCodes: [
      MEDICINE_TYPE_CODE_NAME,
      ENCOUNTER_TYPE_CODE_NAME,
      CV08_50_002_NAME,
      CHARGE_ITEM_LEVEL_CODE_NAME,
      ANTIBACTERIAL_LEVEL_CODE_NAME,
      ANTITUMOR_DRUG_LEVEL_CODE_NAME,
      STOCK_CONTRIL_STATUS_CODE_NAME,
    ],
    getData: (t, dataSet) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        triggerModelChange: true,
        component: 'hospitalSelect',
        formItemProps: { 'label-width': '70px' },
        extraProps: {
          clearable: true,
          className: 'w-40',
          hospitalId: '',
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        isHidden: !searchParams.value.hospitalId,
        formItemProps: { 'label-width': '70px' },
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-32',
        },
      },
      {
        label: t('medicine.searchFrom.medicineTypeCode', '药品类型'),
        name: 'medicineTypeCode',
        component: 'select',
        triggerModelChange: true,
        formItemProps: { 'label-width': '70px' },
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.searchFrom.medicineTypeCode', '药品类型'),
        }),
        extraProps: {
          options: dataSet?.value ? dataSet.value[MEDICINE_TYPE_CODE_NAME] : [],
          filterable: true,
          className: 'w-32',
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('medicine.searchFrom.pharmacologyClassCode', '药理分类'),
        name: 'pharmacologyClassCode',
        component: 'tree-select',
        formItemProps: { 'label-width': '70px' },
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.searchFrom.pharmacologyClassCode', '药理分类'),
        }),
        extraProps: {
          props: {
            children: 'children',
            label: 'label',
            value: 'value',
          },
          'check-strictly': true,
          'default-expand-all': true,
          data: pharmacologyClassOptions.value || [],
          remote: true,
          filterable: true,
          remoteMethod: (keyWord?: string) => {
            getPharmacologyClassList(keyWord);
          },
        },
      },
      {
        label: t('medicine.searchFrom.encounterTypeCode', '适用范围'),
        name: 'encounterTypeCode',
        component: 'select',
        triggerModelChange: true,
        isHidden: !searchParams.value.hospitalId,
        formItemProps: { 'label-width': '70px' },
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.searchFrom.encounterTypeCode', '适用范围'),
        }),
        extraProps: {
          className: 'w-44',
          options: dataSet?.value
            ? dataSet.value[ENCOUNTER_TYPE_CODE_NAME]?.filter((item) =>
                [
                  ENCOUNTER_TYPE_CODE.OUTPATIENT,
                  ENCOUNTER_TYPE_CODE.HOSPITALIZATION,
                ].includes(item.dataValueNo),
              )
            : [],
          filterable: true,
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-60',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryMedicineList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryMedicineList({
              keyWord: undefined,
            });
          },
        },
      },
      {
        label: t('medicine.searchFrom.producedByOrgId', '生产厂家'),
        name: 'producedByOrgId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.medicineForm.producedByOrgId', '生产厂家'),
        }),
        formItemProps: { 'label-width': '70px' },
        triggerModelChange: true,
        extraProps: {
          options: producedOrgList.value.map((item) => ({
            label: item.orgName,
            value: item.orgId,
          })),
          remote: true,
          filterable: true,
          remoteMethod: (keyWord: string) => {
            getOrgList({
              keyWord,
            });
          },
        },
      },
      {
        label: t('medicine.searchFrom.dosageFormCode', '剂型'),
        name: 'dosageFormCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.searchFrom.dosageFormCode', '剂型'),
        }),
        formItemProps: { 'label-width': '40px' },
        extraProps: {
          options: dataSet?.value ? dataSet.value[CV08_50_002_NAME] : [],
          filterable: true,
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('medicine.searchFrom.chargeItemLevelCode', '医保收费等级'),
        name: 'chargeItemLevelCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.searchFrom.chargeItemLevelCode', '医保收费等级'),
        }),
        extraProps: {
          className: 'w-44',
          options: dataSet?.value
            ? dataSet.value[CHARGE_ITEM_LEVEL_CODE_NAME]
            : [],
          filterable: true,
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('medicine.searchFrom.antibacterialLevelCode', '抗菌药物等级'),
        name: 'antibacterialLevelCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.searchFrom.antibacterialLevelCode', '抗菌药物等级'),
        }),
        extraProps: {
          className: 'w-44',
          options: dataSet?.value
            ? dataSet.value[ANTIBACTERIAL_LEVEL_CODE_NAME]
            : [],
          filterable: true,
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('medicine.searchFrom.antitumorDrugLevelCode', '抗肿瘤药等级'),
        name: 'antitumorDrugLevelCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.searchFrom.antitumorDrugLevelCode', '抗肿瘤药等级'),
        }),
        extraProps: {
          className: 'w-44',
          options: dataSet?.value
            ? dataSet.value[ANTITUMOR_DRUG_LEVEL_CODE_NAME]
            : [],
          filterable: true,
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('medicine.searchFrom.valuableFlag', '贵重标志'),
        name: 'valuableFlag',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('exBasicDataMapping.search.valuableFlag', '贵重标志'),
        }),
        triggerModelChange: true,
        formItemProps: { 'label-width': '70px' },
        extraProps: {
          className: 'w-36',
          options: [
            {
              label: t('global:all'),
              value: FLAG_STR.ALL,
            },
            {
              label: t('global:yes'),
              value: FLAG_STR.YES,
            },
            {
              label: t('global:no'),
              value: FLAG_STR.NO,
            },
          ],
        },
      },
      {
        label: t('medicine.searchFrom.stockContrilStatusCode', '库存控制状态'),
        name: 'stockContrilStatusCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.searchFrom.stockContrilStatusCode', '库存控制状态'),
        }),
        isHidden: !searchParams.value.hospitalId,
        extraProps: {
          options: dataSet?.value
            ? dataSet.value[STOCK_CONTRIL_STATUS_CODE_NAME]
            : [],
          filterable: true,
          className: 'w-40',
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
    ],
  });
  return data;
}

export function useMedicineUpsertFormConfig(
  cadnPageQuery: Ref<object>,
  isAdd: Ref<boolean>,
  alreadyUseFlag: Ref<boolean>,
  cadnFormData: Ref<Partial<Cadn.CadnMedicineSpecItem>>,
  optionsMap: Ref<{
    [key: string]: { label?: string; value?: string; description?: string }[];
  }>,
  producedOrgList: Ref<Org.Item[]>,
  getOrgList: (params?: Org.queryReqFlatPageParams) => void,
  adminRouteList: Ref<AdminRouteResItem[]>,
  queryAdminRouteList: (keyWord?: string) => void,
  handleCadnSelectChange: (data: Cadn.CadnMedicineSpecItem) => void,
) {
  const data = useFormConfig<string[], ['cadnForm', 'medicineForm']>({
    getData: (t) => {
      return {
        cadnForm: [
          {
            label: t('medicine.cadnForm.cadnId', '通用名'),
            name: 'medicineSpecId',
            // component: 'select',
            render: () => (
              <CadnSelect
                query={cadnPageQuery.value}
                onChange={handleCadnSelectChange}
              />
            ),
            placeholder: t('global:placeholder.select.template', {
              name: t('medicine.cadnForm.cadnId', '通用名'),
            }),
            extraProps: {
              disabled: !isAdd.value,
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select.template', {
                  name: t('medicine.cadnForm.cadnId', '通用名'),
                }),
                trigger: 'change',
              },
            ],
          },
          {
            label: t('medicine.cadnForm.medicineTypeCode', '药品类型'),
            name: 'medicineTypeCode',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t('medicine.cadnForm.medicineTypeCode', '药品类型'),
            }),
            extraProps: {
              disabled: true,
              options: [
                {
                  value: cadnFormData.value?.medicineTypeCode || '',
                  label: cadnFormData.value?.medicineTypeDesc || '',
                },
              ],
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select.template', {
                  name: t('medicine.cadnForm.medicineTypeCode', '药品类型'),
                }),
                trigger: 'change',
              },
            ],
          },
          {
            label: t('medicine.cadnForm.dosageFormCode', '剂型'),
            name: 'dosageFormCode',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t('medicine.cadnForm.dosageFormCode', '剂型'),
            }),
            extraProps: {
              disabled: true,
              options: [
                {
                  value: cadnFormData.value?.dosageFormCode || '',
                  label: cadnFormData.value?.dosageFormDesc || '',
                },
              ],
            },
          },
          {
            label: t('medicine.cadnForm.pharmacologyClassCode', '药理分类'),
            name: 'pharmacologyClassCode',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t('medicine.cadnForm.pharmacologyClassCode', '药理分类'),
            }),
            extraProps: {
              disabled: true,
              options: [
                {
                  value: cadnFormData.value?.pharmacologyClassCode || '',
                  label: cadnFormData.value?.pharmacologyClassDesc || '',
                },
              ],
            },
          },
          {
            label: t(
              'medicine.cadnForm.specialManageMedicineCode',
              '精麻毒分类',
            ),
            name: 'specialManageMedicineCode',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t(
                'medicine.cadnForm.specialManageMedicineCode',
                '精麻毒分类',
              ),
            }),
            extraProps: {
              disabled: true,
              options: [
                {
                  value: cadnFormData.value?.specialManageMedicineCode || '',
                  label: cadnFormData.value?.specialManageMedicineDesc || '',
                },
              ],
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select.template', {
                  name: t(
                    'medicine.cadnForm.specialManageMedicineCode',
                    '精麻毒分类',
                  ),
                }),
                trigger: 'change',
              },
            ],
          },
          {
            label: t('medicine.cadnForm.medicineSpec', '基本规格'),
            name: 'medicineSpec',
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('medicine.cadnForm.medicineSpec', '基本规格'),
            }),
            extraProps: {
              disabled: true,
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.input.template', {
                  content: t('medicine.cadnForm.medicineSpec', '基本规格'),
                }),
                trigger: 'change',
              },
            ],
          },
          {
            label: t('medicine.medicineForm.doseFactor', '规格剂量'),
            name: 'doseFactor',
            // component: 'input',
            extraProps: {
              type: 'number',
              disabled: true,
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.input.template', {
                  content: t('medicine.cadnForm.doseFactor', '规格剂量'),
                }),
                trigger: 'change',
              },
            ],
            render: () => (
              <el-input
                v-slots={{
                  append: () => {
                    return (
                      <el-select
                        v-model={cadnFormData.value.doseUnitCode}
                        style={{ width: '70px' }}
                        placeholder={''}
                        disabled={true}
                      >
                        <el-option
                          label={cadnFormData.value.doseUnitDesc || ''}
                          value={cadnFormData.value.doseUnitCode || ''}
                        />
                      </el-select>
                    );
                  },
                }}
              ></el-input>
            ),
          },
          {
            label: t('medicine.cadnForm.miniUnitId', '最小单位'),
            name: 'miniUnitId',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t('medicine.cadnForm.miniUnitId', '最小单位'),
            }),
            extraProps: {
              disabled: true,
              options: [
                {
                  label: cadnFormData.value?.miniUnitName || '',
                  value: cadnFormData.value?.miniUnitId || '',
                },
              ],
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select.template', {
                  name: t('medicine.cadnForm.miniUnitId', '最小单位'),
                }),
                trigger: 'change',
              },
            ],
          },
        ],
        medicineForm: [
          {
            label: t('medicine.medicineForm.commodityNo', '药品编码'),
            name: 'commodityNo',
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('medicine.medicineForm.commodityNo', '药品编码'),
            }),
            extraProps: {
              disabled: !!alreadyUseFlag.value,
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.input.template', {
                  content: t('medicine.medicineForm.commodityNo', '药品编码'),
                }),
                trigger: ['change', 'blur'],
              },
            ],
          },
          {
            label: t('medicine.medicineForm.commodityName', '药品名称'),
            name: 'commodityName',
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('medicine.medicineForm.commodityName', '药品名称'),
            }),
            autoConvertSpellNoAndWbNo: true,
            rules: [
              {
                required: true,
                message: t('global:placeholder.input.template', {
                  content: t('medicine.medicineForm.commodityName', '药品名称'),
                }),
                trigger: ['change', 'blur'],
              },
            ],
          },
          {
            name: 'commodity2ndName',
            label: t('global:secondName'),
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('global:secondName'),
            }),
          },
          {
            name: 'commodityExtName',
            label: t('global:thirdName'),
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('global:thirdName'),
            }),
          },
          {
            name: 'spellNo',
            label: t('global:spellNo'),
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('global:spellNo'),
            }),
          },
          {
            name: 'wbNo',
            label: t('global:wbNo'),
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('global:wbNo'),
            }),
          },
          {
            label: t('medicine.medicineForm.commoditySpec', '药品规格'),
            name: 'commoditySpec',
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('medicine.medicineForm.commoditySpec', '药品规格'),
            }),
            rules: [
              {
                required: true,
                message: t('global:placeholder.input.template', {
                  content: t('medicine.medicineForm.commoditySpec', '药品规格'),
                }),
                trigger: ['change', 'blur'],
              },
            ],
          },
          {
            label: t('medicine.medicineForm.producedByOrgId', '生产厂家'),
            name: 'producedByOrgId',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t('medicine.medicineForm.producedByOrgId', '生产厂家'),
            }),
            extraProps: {
              options: producedOrgList.value.map((item) => ({
                label: item.orgName,
                value: item.orgId,
              })),
              disabled: !!alreadyUseFlag.value,
              clearable: false,
              remote: true,
              filterable: true,
              remoteMethod: (keyWord: string) => {
                getOrgList({
                  keyWord,
                });
              },
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select.template', {
                  name: t('medicine.medicineForm.producedByOrgId', '生产厂家'),
                }),
                trigger: ['change', 'blur'],
              },
            ],
          },
          {
            label: t('medicine.medicineForm.approvalNo', '批准文号'),
            name: 'approvalNo',
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('medicine.medicineForm.approvalNo', '批准文号'),
            }),
          },
          {
            label: t('medicine.medicineForm.validPeriod', '有效期'),
            name: 'validPeriod',
            // component: 'input',
            extraProps: {
              placeholder: t('global:placeholder.input.template', {
                content: t('medicine.medicineForm.validPeriod', '有效期'),
              }),
            },
            triggerModelChange: true,
            render: () => (
              <el-input
                v-slots={{
                  append: () => (
                    <span>{t('medicine.medicineForm.month', '月')}</span>
                  ),
                }}
              ></el-input>
            ),
          },
          {
            label: t('medicine.medicineForm.memo', '备注'),
            name: 'memo',
            span: 2,
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('medicine.medicineForm.memo', '备注'),
            }),
          },
          {
            label: t(
              'medicine.medicineForm.chargeItemLevelCode',
              '医保收费等级',
            ),
            name: 'chargeItemLevelCode',
            component: 'select',
            triggerModelChange: true,
            placeholder: t('global:placeholder.select.template', {
              name: t(
                'medicine.medicineForm.chargeItemLevelCode',
                '医保收费等级',
              ),
            }),
            extraProps: {
              options: optionsMap.value.chargeItemLevelOptions || [],
              filterable: true,
            },
          },
          {
            label: t('medicine.medicineForm.upperPrice', '价格上限'),
            name: 'upperPrice',
            // component: 'input-number',
            placeholder: t('global:placeholder.input.template', {
              content: t('medicine.medicineForm.memo', '价格上限'),
            }),
            render: () => {
              return (
                <el-input-number
                  class="w-full"
                  min={0}
                  precision={4}
                  controls={false}
                  v-slots={{
                    suffix: () => (
                      <span>{t('medicine.medicineForm.priceUnit', '元')}</span>
                    ),
                  }}
                ></el-input-number>
              );
            },
          },
          {
            label: t('medicine.medicineForm.storageConditionCode', '贮存条件'),
            name: 'storageConditionCode',
            component: 'select',
            triggerModelChange: true,
            placeholder: t('global:placeholder.select.template', {
              name: t('medicine.medicineForm.storageConditionCode', '贮存条件'),
            }),
            extraProps: {
              options: optionsMap.value.storageConditionOptions || [],
              filterable: true,
            },
          },
          {
            label: t('medicine.medicineForm.storageDesc', '贮存说明'),
            name: 'storageDesc',
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('medicine.medicineForm.storageDesc', '贮存说明'),
            }),
          },
          {
            label: t(
              'medicine.medicineForm.antibacterialLevelCode',
              '抗菌药物等级',
            ),
            name: 'antibacterialLevelCode',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t(
                'medicine.medicineForm.antibacterialLevelCode',
                '抗菌药物等级',
              ),
            }),
            extraProps: {
              options: optionsMap.value.antibacterialLevelOptions || [],
              filterable: true,
              clearable: false,
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select.template', {
                  name: t(
                    'medicine.medicineForm.antibacterialLevelCode',
                    '抗菌药物等级',
                  ),
                }),
                trigger: ['change', 'blur'],
              },
            ],
          },
          {
            label: t(
              'medicine.medicineForm.antitumorDrugLevelCode',
              '抗肿瘤药等级',
            ),
            name: 'antitumorDrugLevelCode',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t(
                'medicine.medicineForm.antitumorDrugLevelCode',
                '抗肿瘤药等级',
              ),
            }),
            extraProps: {
              options: optionsMap.value.antitumorDrugLevelOptions || [],
              filterable: true,
              clearable: false,
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select.template', {
                  name: t(
                    'medicine.medicineForm.antitumorDrugLevelCode',
                    '抗肿瘤药等级',
                  ),
                }),
                trigger: ['change', 'blur'],
              },
            ],
          },
          {
            label: t(
              'medicine.medicineForm.dangerDrugLevelCode',
              '高危药品级别',
            ),
            name: 'dangerDrugLevelCode',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t(
                'medicine.medicineForm.dangerDrugLevelCode',
                '高危药品级别',
              ),
            }),
            extraProps: {
              options: optionsMap.value.dangerDrugLevelOptions || [],
              filterable: true,
              clearable: false,
            },
            rules: [
              {
                required: true,
                message: t('global:placeholder.select.template', {
                  name: t(
                    'medicine.medicineForm.dangerDrugLevelCode',
                    '高危药品级别',
                  ),
                }),
                trigger: ['change', 'blur'],
              },
            ],
          },
          {
            label: t('medicine.medicineForm.usageDesc', '用药说明'),
            name: 'usageDesc',
            component: 'input',
            placeholder: t('global:placeholder.input.template', {
              content: t('medicine.medicineForm.usageDesc', '用药说明'),
            }),
          },
          {
            name: 'valuableFlag',
            label: t('medicine.medicineForm.valuableFlag', '贵重药品'),
            component: 'switch',
            extraProps: {
              'inline-prompt': true,
              'active-value': FLAG_STR.YES,
              'inactive-value': FLAG_STR.NO,
              'active-text': t('global:yes'),
              'inactive-text': t('global:no'),
            },
          },
          {
            name: 'highPriceFlag',
            label: t('medicine.medicineForm.highPriceFlag', '高价药品'),
            component: 'switch',
            extraProps: {
              'inline-prompt': true,
              'active-value': FLAG.YES,
              'inactive-value': FLAG.NO,
              'active-text': t('global:yes'),
              'inactive-text': t('global:no'),
            },
          },
          {
            name: 'selfPayFlag',
            label: t('medicine.medicineForm.selfPayFlag', '自费药品'),
            component: 'switch',
            extraProps: {
              'inline-prompt': true,
              'active-value': FLAG.YES,
              'inactive-value': FLAG.NO,
              'active-text': t('global:yes'),
              'inactive-text': t('global:no'),
            },
          },
          {
            label: t(
              'medicine.medicineForm.medicineXAdminRouteList',
              '可用给药途径',
            ),
            name: 'medicineXAdminRouteList',
            component: 'select',
            placeholder: t('global:placeholder.select.template', {
              name: t(
                'medicine.medicineForm.medicineXAdminRouteList',
                '可用给药途径',
              ),
            }),
            extraProps: {
              options: adminRouteList.value.map((item) => ({
                label: item.adminRouteName,
                value: {
                  adminRouteId: item.adminRouteId,
                  adminRouteName: item.adminRouteName,
                },
              })),
              'value-key': 'adminRouteId',
              multiple: true,
              remote: true,
              filterable: true,
              'collapse-tags': true,
              'collapse-tags-tooltip': true,
              'max-collapse-tags': 2,
              remoteMethod: (keyWord: string) => {
                queryAdminRouteList(keyWord);
              },
            },
          },
          {
            name: 'fusionMediaDrugFlag',
            label: t('medicine.medicineForm.fusionMediaDrugFlag', '融媒药'),
            component: 'switch',
            extraProps: {
              'inline-prompt': true,
              'active-value': FLAG.YES,
              'inactive-value': FLAG.NO,
              'active-text': t('global:yes'),
              'inactive-text': t('global:no'),
            },
          },
          {
            name: 'skinTestFlag',
            label: t('medicine.medicineForm.skinTestFlag', '是否皮试'),
            component: 'switch',
            extraProps: {
              'inline-prompt': true,
              'active-value': FLAG.YES,
              'inactive-value': FLAG.NO,
              'active-text': t('global:yes'),
              'inactive-text': t('global:no'),
            },
          },
        ],
      };
    },
  });
  return data;
}

export function useHospitalMedicineFormConfig(
  alreadyUseFlag: Ref<boolean>,
  optionsMap: Ref<{
    [key: string]: { label?: string; value?: string; description?: string }[];
  }>,
  cadnForm: Ref<Partial<Cadn.CadnListItem>>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t(
          'medicine.hospitalMedicineForm.commodityCategoryId',
          '费用分类',
        ),
        name: 'commodityCategoryId',
        component: 'select',
        extraProps: {
          clearable: false,
          filterable: true,
          options: optionsMap.value.commodityCategory || [],
        },
        formItemProps: { 'label-width': '110px' },
        triggerModelChange: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'medicine.hospitalMedicineForm.commodityCategoryId',
                '费用分类',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t(
          'medicine.hospitalMedicineForm.outCommodityCategoryId',
          '门诊发票分类',
        ),
        name: 'outCommodityCategoryId',
        component: 'select',
        extraProps: {
          filterable: true,
          options: optionsMap.value.outCommodityCategory || [],
        },
      },
      {
        label: t(
          'medicine.hospitalMedicineForm.inCommodityCategoryId',
          '住院发票分类',
        ),
        name: 'inCommodityCategoryId',
        component: 'select',
        extraProps: {
          filterable: true,
          options: optionsMap.value.inCommodityCategory || [],
        },
      },
      {
        label: t(
          'medicine.hospitalMedicineForm.accCommodityCategoryId',
          '会计分类',
        ),
        name: 'accCommodityCategoryId',
        component: 'select',
        extraProps: {
          filterable: true,
          options: optionsMap.value.accCommodityCategory || [],
        },
      },
      {
        label: t(
          'medicine.hospitalMedicineForm.fncCommodityCategoryId',
          '财务分类',
        ),
        name: 'fncCommodityCategoryId',
        component: 'select',
        formItemProps: { 'label-width': '110px' },
        extraProps: {
          filterable: true,
          options: optionsMap.value.fncCommodityCategory || [],
        },
      },
      {
        label: t(
          'medicine.hospitalMedicineForm.mrCommodityCategoryId',
          '病案分类',
        ),
        name: 'mrCommodityCategoryId',
        component: 'select',
        extraProps: {
          filterable: true,
          options: optionsMap.value.mrCommodityCategory || [],
        },
      },
      {
        label: t(
          'medicine.hospitalMedicineForm.encounterTypeCodes',
          '使用范围',
        ),
        name: 'encounterTypeCodes',
        component: 'select',
        extraProps: {
          multiple: true,
          options: optionsMap.value.encounterTypeOptions || [],
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'medicine.hospitalMedicineForm.encounterTypeCodes',
                '使用范围',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
      {
        label: t(
          'medicine.hospitalMedicineForm.commodityPurchasePrice',
          '参考进价',
        ),
        name: 'commodityPurchasePrice',
        formItemProps: { 'label-width': '110px' },
        render: () => {
          return (
            <el-input-number
              precision={4}
              controls={false}
              v-slots={{
                suffix: () => (
                  <span>
                    {t('medicine.hospitalMedicineForm.priceUnit', '元')}
                  </span>
                ),
              }}
            ></el-input-number>
          );
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'medicine.hospitalMedicineForm.commodityPurchasePrice',
                '参考进价',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('medicine.hospitalMedicineForm.price', '参考零售价'),
        name: 'price',
        render: () => {
          return (
            <el-input-number
              precision={4}
              controls={false}
              v-slots={{
                suffix: () => (
                  <span>
                    {t('medicine.hospitalMedicineForm.priceUnit', '元')}
                  </span>
                ),
              }}
            ></el-input-number>
          );
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('medicine.hospitalMedicineForm.price', '参考零售价'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: () => (
          <div class="flex items-center">
            <div class="mr-2">
              {t(
                'medicine.hospitalMedicineForm.outMedicineCalcTypeCode',
                '门诊药品计算方式',
              )}
            </div>
            <el-tooltip
              effect="dark"
              placement="top-end"
              v-slots={{
                content: () => (
                  <div class="whitespace-pre-wrap">
                    {optionsMap.value.outMedicineCalcTypeOptions
                      ?.map((item) => `${item.label}：${item.description}`)
                      ?.join('\n')}
                  </div>
                ),
                default: () => (
                  <el-icon size={18} class="cursor-pointer text-gray-500">
                    <QuestionFilled />
                  </el-icon>
                ),
              }}
            ></el-tooltip>
          </div>
        ),
        name: 'outMedicineCalcTypeCode',
        component: 'select',
        extraProps: {
          options: optionsMap.value.outMedicineCalcTypeOptions || [],
          filterable: true,
          disabled:
            cadnForm.value.medicineTypeCode ===
            MEDICINE_TYPE_CODE.CHINESE_HERBAL_MEDICINE,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'medicine.hospitalMedicineForm.outMedicineCalcTypeCode',
                '门诊药品计算方式',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t(
          'medicine.hospitalMedicineForm.stockContrilStatusCode',
          '库存控制状态',
        ),
        name: 'stockContrilStatusCode',
        component: 'select',
        extraProps: {
          options: optionsMap.value.stockContrilStatusOptions || [],
          filterable: true,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'medicine.hospitalMedicineForm.stockContrilStatusCode',
                '库存控制状态',
              ),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
    ],
  });
  return data;
}

export function useCadnFormConfig(
  alreadyUseFlag: Ref<boolean>,
  optionsMap: Ref<{
    [key: string]: SelectOptions[];
  }>,
  getPharmacologyClassList: (data: { keyWord?: string }) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('medicine.cadn.cadn', '通用名称'),
        name: 'cadn',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('medicine.cadn.cadn', '通用名称'),
        }),
        autoConvertSpellNoAndWbNo: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('medicine.cadn.cadn', '通用名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('medicine.cadn.cadnEng', '英文名称'),
        name: 'cadnEng',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('medicine.cadn.cadnEng', '英文名称'),
        }),
      },
      {
        name: 'cadnExt',
        label: t('global:secondName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:secondName'),
        }),
      },
      {
        name: 'cadn2nd',
        label: t('global:thirdName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:thirdName'),
        }),
      },
      {
        name: 'spellNo',
        label: t('global:spellNo'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:spellNo'),
        }),
      },
      {
        name: 'wbNo',
        label: t('global:wbNo'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:wbNo'),
        }),
      },
      {
        label: t('medicine.cadn.medicineTypeCode', '药品类型'),
        name: 'medicineTypeCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.cadn.medicineTypeCode', '药品类型'),
        }),
        extraProps: {
          options: optionsMap.value.medicineTypeOptions || [],
          clearable: false,
          disabled: !!alreadyUseFlag.value,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('medicine.cadn.medicineTypeCode', '药品类型'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
      {
        label: t('medicine.cadn.dosageFormCode', '剂型'),
        name: 'dosageFormCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.cadn.dosageFormCode', '剂型'),
        }),
        extraProps: {
          options: optionsMap.value.cvOptions || [],
        },
      },
      {
        label: t('medicine.cadn.pharmacologyClassCode', '药理分类'),
        name: 'pharmacologyClassCode',
        component: 'tree-select',
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.cadn.pharmacologyClassCode', '药理分类'),
        }),
        extraProps: {
          props: {
            children: 'children',
            label: 'label',
            value: 'value',
          },
          'check-strictly': true,
          'default-expand-all': true,
          data: optionsMap.value.pharmacologyClassOptions || [],
          remote: true,
          filterable: true,
          remoteMethod: (keyWord: string) => {
            getPharmacologyClassList({
              keyWord,
            });
          },
        },
      },
      {
        label: t('medicine.cadn.specialManageMedicineCode', '精麻毒分类'),
        name: 'specialManageMedicineCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.cadn.specialManageMedicineCode', '精麻毒分类'),
        }),
        extraProps: {
          options: optionsMap.value.specialManageMedicineOptions || [],
          clearable: false,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('medicine.cadn.specialManageMedicineCode', '精麻毒分类'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
      },
    ],
  });
  return data;
}

export function useCadnMedicineSpecFormConfig(
  alreadyUseFlag: boolean | undefined,
  medicineSpecItem: Ref<
    Partial<Cadn.MedicineSpecItem & { medicineSpec?: string; key?: string }>
  >,
  unitOptions: Ref<SelectOptions[]>,
  optionsMap: Ref<{
    [key: string]: SelectOptions[];
  }>,
  getUnitList: (data?: Unit.QueryParams) => void,
  handleChange: () => void,
) {
  const filterKeyWord = ref('');
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('medicine.cadnMedicineSpecForm.miniUnitId', '最小单位'),
        name: 'miniUnitId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.cadnMedicineSpecForm.miniUnitId', '最小单位'),
        }),
        triggerModelChange: true,
        extraProps: {
          options: filterKeyWord.value
            ? unitOptions.value
            : optionsMap.value?.unitOptions || [],
          disabled: !!(
            alreadyUseFlag && medicineSpecItem.value?.medicineSpecId
          ),
          clearable: false,
          remote: true,
          filterable: true,
          remoteMethod: (keyWord: string) => {
            filterKeyWord.value = keyWord;
            getUnitList({
              keyWord,
              pageNumber: 1,
              pageSize: 100,
            });
          },
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('medicine.cadnMedicineSpecForm.miniUnitId', '最小单位'),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        label: t('medicine.cadnMedicineSpecForm.doseFactor', '剂量'),
        name: 'doseFactor',
        // component: 'input',
        extraProps: {},
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('medicine.cadnForm.doseFactor', '剂量'),
            }),
            trigger: 'change',
          },
        ],
        render: () => (
          <div class="flex w-full items-center">
            <el-input-number
              v-model={medicineSpecItem.value.doseFactor}
              controls={false}
              disabled={
                !!(alreadyUseFlag && medicineSpecItem.value?.medicineSpecId)
              }
              min={0}
              precision={2}
              onChange={(val: number) => {
                medicineSpecItem.value.medicineSpec = `${val || ''}${medicineSpecItem.value.doseUnitDesc || ''}/${medicineSpecItem.value.miniUnitName || ''}`;
                handleChange();
              }}
            />
            <el-select
              v-model={medicineSpecItem.value.doseUnitCode}
              style={{ width: '100px' }}
              disabled={
                !!(alreadyUseFlag && medicineSpecItem.value?.medicineSpecId)
              }
              placeholder={''}
              onChange={(val: string) => {
                const item = optionsMap.value.doseUnitOptions?.find(
                  (item) => item.value === val,
                );
                medicineSpecItem.value.doseUnitDesc = item?.label as string;
                medicineSpecItem.value.medicineSpec = `${medicineSpecItem.value.doseFactor || ''}${medicineSpecItem.value.doseUnitDesc || ''}/${medicineSpecItem.value.miniUnitName || ''}`;
                handleChange();
              }}
            >
              {(optionsMap.value.doseUnitOptions || []).map((item) => (
                <el-option
                  key={item.value}
                  label={item.label}
                  value={item.value}
                />
              ))}
            </el-select>
          </div>
        ),
      },
      {
        label: t('medicine.cadnMedicineSpecForm.medicineSpec', '基本规格'),
        name: 'medicineSpec',
        component: 'input',
        extraProps: {
          disabled: true,
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'medicine.cadnMedicineSpecForm.medicineSpec',
                '基本规格',
              ),
            }),
            trigger: 'change',
          },
        ],
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'switch',
        extraProps: {
          disabled: !!(
            alreadyUseFlag && medicineSpecItem.value?.medicineSpecId
          ),
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
    ],
  });
  return data;
}

export function useCommodityChangeLogSearchFormConfig(
  medicineList: Ref<Medicine.MedicineListItem[]>,
  queryMedicineList: (keyWord?: string) => void,
  queryCommodityChangeLogList: (
    data: Partial<CommodityChangeLog.CommodityChangeLogQueryParams>,
  ) => void,
) {
  const data = useFormConfig({
    dataSetCodes: [
      DATA_OPERATE_TYPE_CODE_NAME,
      COMMODITY_CHANGE_APPROVAL_CODE_NAME,
    ],
    getData: (t, dataSet) => [
      {
        label: t(
          'medicine.commodityChangeLogSearchForm.commodityId',
          '药品名称',
        ),
        name: 'commodityId',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.medicineForm.commodityId', '药品名称'),
        }),
        extraProps: {
          className: 'w-44',
          options: medicineList.value.map((item) => ({
            label: item.commodityNameDisplay,
            value: item.commodityId,
          })),
          remote: true,
          filterable: true,
          remoteMethod: (keyWord: string) => {
            queryMedicineList(keyWord);
          },
        },
      },
      {
        label: t(
          'medicine.commodityChangeLogSearchForm.dataOperateTypeCode',
          '操作类型',
        ),
        name: 'dataOperateTypeCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('medicine.searchFrom.dataOperateTypeCode', '操作类型'),
        }),
        extraProps: {
          className: 'w-44',
          options: dataSet?.value
            ? dataSet.value[DATA_OPERATE_TYPE_CODE_NAME]
            : [],
          filterable: true,
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t(
          'medicine.commodityChangeLogSearchForm.commodityChangeApprovalCodes',
          '审核状态',
        ),
        name: 'commodityChangeApprovalCodes',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t(
            'medicine.searchFrom.commodityChangeApprovalCodes',
            '审核状态',
          ),
        }),
        extraProps: {
          options: dataSet?.value
            ? dataSet.value[COMMODITY_CHANGE_APPROVAL_CODE_NAME]
            : [],
          filterable: true,
          multiple: true,
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        name: 'keyword',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-60',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryCommodityChangeLogList({
                keyword: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
    ],
  });
  return data;
}

// 默认用法用量表单配置
export function useMedicineUseInfoUpsertFormConfig(
  formData: Ref<Medicine.MedicineUseInfoUpsertParams>,
  adminRouteList: Ref<SelectOptions[]>,
  adminFreqList: Ref<SelectOptions[]>,
  queryAdminRouteList: (keyWord?: string) => void,
  queryAdminFreqList: (keyWord?: string) => void,
) {
  const data = useFormConfig({
    dataSetCodes: [DOSE_UNIT_CODE_NAME],
    getData: (t, dataSet) => [
      {
        label: t(
          'medicine.medicineUseInfoForm.defaultAdminRouteId',
          '默认给药途径',
        ),
        name: 'defaultAdminRouteId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t(
            'medicine.medicineUseInfoForm.defaultAdminRouteId',
            '默认给药途径',
          ),
        }),
        extraProps: {
          options: adminRouteList.value,
          remote: true,
          filterable: true,
          remoteMethod: (keyWord?: string) => {
            queryAdminRouteList(keyWord);
          },
        },
      },
      {
        label: t('medicine.medicineUseInfoForm.defaultPresDoseQty', '默认剂量'),
        name: 'defaultPresDoseQty',
        // component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t(
            'medicine.medicineUseInfoForm.defaultPresDoseQty',
            '默认剂量',
          ),
        }),
        render: () => (
          <div class="flex w-full items-center">
            <el-input-number
              class="w-full"
              v-model={formData.value.defaultPresDoseQty}
              controls={false}
              min={0}
              precision={2}
            />
            <el-select
              v-model={formData.value.doseUnitCode}
              style={{ width: '160px' }}
            >
              {(dataSet?.value ? dataSet.value[DOSE_UNIT_CODE_NAME] : []).map(
                (item) => (
                  <el-option
                    key={item.dataValueNo}
                    label={item.dataValueNameDisplay}
                    value={item.dataValueNo}
                  />
                ),
              )}
            </el-select>
          </div>
        ),
      },
      {
        label: t(
          'medicine.medicineUseInfoForm.defaultAdminFreqId',
          '默认给药频次',
        ),
        name: 'defaultAdminFreqId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t(
            'medicine.medicineUseInfoForm.defaultAdminFreqId',
            '默认给药频次',
          ),
        }),
        extraProps: {
          options: adminFreqList.value,
          remote: true,
          filterable: true,
          remoteMethod: (keyWord?: string) => {
            queryAdminFreqList(keyWord);
          },
        },
      },
      {
        name: 'defaultDays',
        label: t('medicine.medicineUseInfoForm.defaultDays', '默认天数'),
        component: 'input-number',
        placeholder: '',
        extraProps: {
          class: 'w-full',
          min: 1,
          step: 1,
          'step-strictly': true,
          'controls-position': 'right',
        },
      },
      {
        name: 'defaultEntrust',
        label: t('medicine.medicineUseInfoForm.defaultEntrust', '默认嘱托'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('medicine.medicineUseInfoForm.defaultEntrust', '默认嘱托'),
        }),
      },
    ],
  });
  return data;
}
