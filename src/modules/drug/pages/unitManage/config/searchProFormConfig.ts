import { UNIT_TYPE_CODE_NAME } from '@/utils/constant';
import { useFormConfig } from 'sun-biz';

export function useSearchProFormConfig() {
  return useFormConfig({
    dataSetCodes: [UNIT_TYPE_CODE_NAME],
    getData: (t, dataSet) => [
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-[12rem]',
        triggerModelChange: true,
        extraProps: {
          suffixIcon: 'Search',
        },
      },
      {
        label: t('drug.unitManage.unitTypeCode', '单位类型'),
        name: 'unitTypeCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('drug.unitManage.unitTypeCode', '单位类型'),
        }),
        className: 'w-60',
        extraProps: {
          clearable: true,
          options: dataSet?.value ? dataSet.value?.[UNIT_TYPE_CODE_NAME] : [],
          props: {
            value: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        className: 'w-40',
        extraProps: {
          clearable: false,
        },
      },
    ],
  });
}
