import { ColumnProps, useColumnConfig } from 'sun-biz';
import { UNIT_TYPE_CODE_NAME, FLAG } from '@/utils/constant';

export function useUnitListConfig(
  operations: UnitManage.IOPerations,
  handleEnableSwitch: (row: UnitManage.Unit) => Promise<void>,
) {
  return useColumnConfig({
    dataSetCodes: [UNIT_TYPE_CODE_NAME],
    getData: (t, dataSet) =>
      [
        {
          type: 'selection',
          width: 50,
        },
        {
          label: t('global:indexNo'),
          type: 'index',
          width: 80,
        },

        // 单位编码
        {
          label: t('drug.unitManage.unitNo', '单位编码'),
          prop: 'unitNo',
          editable: true,
          align: 'left',
          // rules: () => [
          //   {
          //     required: true,
          //     trigger: 'blur',
          //     message: t('global:placeholder.input.template', {
          //       content: t('drug.unitManage.unitNo', '单位编码'),
          //     }),
          //   },
          // ],
          render: (row: UnitManage.Unit) => {
            if (row.editable) {
              return (
                <el-input
                  v-model={row.unitNo}
                  placeholder={t('global:placeholder.input.template', {
                    content: t('drug.unitManage.unitNo', '单位编码'),
                  })}
                ></el-input>
              );
            } else {
              return row.unitNo || '--';
            }
          },
        },

        // 单位名称
        {
          label: t('drug.unitManage.unitName', '单位名称'),
          prop: 'unitName',
          editable: true,
          align: 'left',
          rules: () => [
            {
              required: true,
              trigger: 'blur',
              message: t('global:placeholder.input.template', {
                content: t('drug.unitManage.unitName', '单位名称'),
              }),
            },
          ],
          render: (row: UnitManage.Unit) => {
            if (row.editable) {
              return (
                <el-input
                  v-model={row.unitName}
                  placeholder={t('global:placeholder.input.template', {
                    content: t('drug.unitManage.unitName', '单位名称'),
                  })}
                ></el-input>
              );
            } else {
              return row.unitName || '--';
            }
          },
        },
        {
          label: t('drug.unitManage.unitTypeCode', '单位类型'),
          prop: 'unitTypeCode',
          editable: true,
          align: 'left',
          rules: () => [
            {
              required: true,
              trigger: 'blur',
              message: t('global:placeholder.input.template', {
                content: t('drug.unitManage.unitTypeCode', '单位类型'),
              }),
            },
          ],
          render: (row: UnitManage.Unit) => {
            if (row.editable) {
              return (
                <el-select
                  v-model={row.unitTypeCode}
                  filterable={true}
                  placeholder={t('global:placeholder.input.template', {
                    content: t('drug.unitManage.unitTypeCode', '单位类型'),
                  })}
                  onChange={(val: string) => {
                    const findObj = (
                      dataSet?.value ? dataSet.value[UNIT_TYPE_CODE_NAME] : []
                    )?.find((item) => item.dataValueNo === val);
                    row.unitTypeCodeDesc = findObj?.dataValueNameDisplay || '';
                  }}
                >
                  {(dataSet?.value
                    ? dataSet.value[UNIT_TYPE_CODE_NAME]
                    : []
                  ).map((item) => {
                    return (
                      <el-option
                        key={item.dataValueNo}
                        label={item.dataValueNameDisplay}
                        value={item.dataValueNo}
                      ></el-option>
                    );
                  })}
                </el-select>
              );
            } else {
              return row.unitTypeCodeDesc || '--';
            }
          },
        },
        {
          label: t('global:enabledFlag'),
          prop: 'enabledFlag',
          minWidth: 100,
          render: (row: UnitManage.Unit) => {
            return (
              <el-switch
                modelValue={row.enabledFlag}
                inline-prompt
                active-value={FLAG.YES}
                inactive-value={FLAG.NO}
                before-change={() => handleEnableSwitch(row)}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
              />
            );
          },
        },
        // 操作
        {
          label: t('global:operation'),
          width: 180,
          render: (row: UnitManage.Unit, index: number) => {
            if (!row.editable) {
              return (
                <>
                  <el-button
                    type="primary"
                    onClick={() => {
                      operations.edit(index);
                    }}
                    link
                  >
                    {t('global:edit')}
                  </el-button>
                </>
              );
            } else {
              return (
                <>
                  <el-button
                    type="danger"
                    onClick={() => {
                      operations.cancel(index);
                    }}
                    link
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    v-loading={row.saving}
                    onClick={() => {
                      operations.save(index);
                    }}
                    link
                  >
                    {t('global:save')}
                  </el-button>
                </>
              );
            }
          },
        },
      ] as ColumnProps[],
  });
}
