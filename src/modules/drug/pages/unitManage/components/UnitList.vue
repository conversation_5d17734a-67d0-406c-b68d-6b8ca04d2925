<script setup lang="ts">
  import { PageInfo, ProTable } from 'sun-biz';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { computed, nextTick, ref, useTemplateRef } from 'vue';
  import { ElMessage, TableInstance, ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { useUnitListConfig } from '../config/unitListConfig';
  import { queryUnitListByExample, saveUnit } from '../../../api/unitManage';
  import { FLAG } from '@/utils/constant';

  const { t } = useTranslation();
  const emit = defineEmits(['enable-add']);
  const props = defineProps<{
    keyWord: string;
    enabledFlag: number;
    unitTypeCode: string;
  }>();
  const proTableRef = useTemplateRef<{
    validateRow: (index: string, callback?: (valid: boolean) => void) => void;
    proTableRef: TableInstance;
  }>('proTable');
  const ElTableRef = computed(() => proTableRef.value?.proTableRef);
  const operations = { edit, cancel, save };
  const tableConfig = useUnitListConfig(operations, handleEnableSwitch);
  const pageInfo = defineModel<PageInfo>();
  const tableData = ref<UnitManage.Unit[]>([]);
  const saveError = ref(false);
  const loading = ref(false);
  const curEdited = ref<UnitManage.Unit | null>();
  const selections = ref<string[]>([]);

  function edit(index: number) {
    tableData.value.forEach((item, ind) => {
      if (ind !== index) {
        // 过滤新加行未保存
        if (item.unitId) {
          item.editable = false;
        }
      } else {
        curEdited.value = cloneDeep(item);
        item.editable = true;
      }
    });
  }

  function cancel(index: number) {
    if (saveError.value) {
      saveError.value = false;
      fetchList();
    } else {
      const row = tableData.value[index];
      if (!row?.unitId) {
        // 新增取消
        emit('enable-add');
        tableData.value.pop();
        return;
      }
      // 编辑取消
      row.unitNo = curEdited.value!.unitNo;
      row.unitName = curEdited.value!.unitName;
      row.enabledFlag = curEdited.value!.enabledFlag;
      row.unitTypeCode = curEdited.value!.unitTypeCode;
      row.unitTypeCodeDesc = curEdited.value!.unitTypeCodeDesc;
      row.editable = false;
      curEdited.value = null;
    }
  }

  function save(index: number) {
    proTableRef.value?.validateRow(index.toString(), async (valid) => {
      if (valid) {
        const row = tableData.value[index];
        row.saving = true;
        const baseParam: UnitManage.SaveUnitReqParam = {
          unitName: row.unitName,
          unitNo: row.unitNo,
          unitTypeCode: row.unitTypeCode,
          enabledFlag: row.enabledFlag,
        };
        if (row?.unitId) {
          // 编辑保存
          baseParam.unitId = row.unitId;
        }
        const [, res] = await saveUnit(baseParam);
        if (res?.success) {
          ElMessage.success(t('global:save.success'));
          row.editable = false;
          if (row?.unitId) {
            // 清空保存后的行
            curEdited.value = null;
          }
          fetchList();
        } else {
          saveError.value = true;
          row.saving = false;
        }
        emit('enable-add');
      }
    });
  }

  function currentPageChange(val: number) {
    pageInfo.value!.pageNumber = val;
    fetchList();
  }

  function sizePageChange(val: number) {
    pageInfo.value!.pageSize = val;
    fetchList();
  }

  async function addOne() {
    const newOne: UnitManage.Unit = {
      unitId: '',
      unitNo: '',
      unitName: '',
      enabledFlag: FLAG.YES,
      unitTypeCode: '',
      editable: true,
      saving: false,
    };
    tableData.value.forEach((item) => (item.editable = false));
    tableData.value.push(newOne);
    proTableRef.value?.proTableRef.setCurrentRow(newOne);
    // 滚动到新增行
    await nextTick();
    scrollToNewOne();
  }

  function scrollToNewOne() {
    if (ElTableRef.value) {
      const elRowSelector = '.el-table__body tbody .el-table__row';
      const rowEles = Array.from(
        (ElTableRef.value.$el as HTMLElement).querySelectorAll(elRowSelector),
      ) as HTMLElement[];
      const len = rowEles?.length;
      if (len) {
        const { top } = rowEles[len - 1].getBoundingClientRect();
        ElTableRef.value.setScrollTop(top);
      }
    }
  }

  function handleSelectionChange(newSelection: UnitManage.Unit[]) {
    selections.value = newSelection
      // 防止新增，未保存，则数据没有unitId；
      .filter((item) => item.unitId)
      .map((item) => item.unitId!);
  }

  async function fetchList() {
    loading.value = true;
    const [, res] = await queryUnitListByExample({
      ...pageInfo.value!,
      keyWord: props.keyWord,
      enabledFlag:
        props.enabledFlag === FLAG.ALL ? undefined : props.enabledFlag,
      unitTypeCode: props.unitTypeCode,
    });
    if (res?.success) {
      const { total, data = [] } = res;
      pageInfo.value!.total = total || 0;
      tableData.value = data.map((item) => ({
        ...item,
        editable: false,
        saving: false,
      }));
    }
    loading.value = false;
    emit('enable-add');
  }

  async function handleEnableSwitch(row: UnitManage.Unit) {
    if (row?.editable === true) {
      row.enabledFlag = row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES;
      return;
    }

    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要{{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.unitName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        unitId: row?.unitId as string,
        unitNo: row?.unitNo as string,
        unitName: row?.unitName as string,
        unitTypeCode: row?.unitTypeCode as string,
        enabledFlag: row?.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES,
      };
      const [, res] = await saveUnit(params);
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        await fetchList();
      }
    });
  }
  fetchList();

  defineExpose({
    fetchList,
    addOne,
    selections,
  });
</script>

<template>
  <ProTable
    ref="proTable"
    highlight-current-row
    row-key="unitId"
    :loading="loading"
    :columns="tableConfig"
    :data="tableData"
    :page-info="pageInfo"
    :pagination="true"
    :editable="true"
    @current-page-change="currentPageChange"
    @size-page-change="sizePageChange"
    @selection-change="handleSelectionChange"
  ></ProTable>
</template>
