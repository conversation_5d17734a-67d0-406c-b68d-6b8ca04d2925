<script setup lang="ts">
  import { computed } from 'vue';
  // 业务标识类型代码: DICT_UNIT（计量单位）
  import { DICT_UNIT } from '@sun-toolkit/enums';
  import {
    ProForm,
    DmlButton,
    useAppConfigData,
    MAIN_APP_CONFIG,
  } from 'sun-biz';
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  import { useSearchProFormConfig } from '../config/searchProFormConfig';
  // refresh: 刷新列表
  defineEmits(['refresh', 'addOne']);
  defineProps<{
    bizData: string[];
  }>();
  const addDisabled = defineModel<boolean>('addDisabled');
  const searchFormData = defineModel<{
    keyWord: string;
  }>();
  const btnDisabled = computed(() => !isCloudEnv || addDisabled.value);

  const searchFormConfig = useSearchProFormConfig();
</script>

<template>
  <ProForm
    class="ml-4 mt-3 flex flex-1 flex-wrap"
    v-model="searchFormData"
    :data="searchFormConfig"
    layout-mode="inline"
    @submit.prevent
    @model-change="$emit('refresh')"
  >
    <template #customButton>
      <div class="flex flex-1 justify-end">
        <el-button
          type="primary"
          class="mr-3"
          :disabled="btnDisabled"
          @click="$emit('addOne')"
          >{{ $t('global:add') }}</el-button
        >
        <DmlButton
          :biz-data="bizData"
          :full-down-flag="true"
          :code="DICT_UNIT"
        ></DmlButton>
      </div>
    </template>
  </ProForm>
</template>
