<script setup lang="ts">
  import { Title } from 'sun-biz';
  import SearchProForm from './components/SearchProForm.vue';
  import UnitList from './components/UnitList.vue';
  import { nextTick, ref, useTemplateRef } from 'vue';
  import { DEFAULT_PAGE_SIZE, FLAG } from '@sun-toolkit/enums';

  const searchParam = ref({
    keyWord: '',
    enabledFlag: FLAG.ALL,
    unitTypeCode: undefined,
  });
  const pageInfo = ref({
    pageNumber: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    total: 0,
  });
  const unitListRef = useTemplateRef('unitList');
  const addDisabled = ref(false);

  async function refresh() {
    addDisabled.value = false;
    await nextTick();
    pageInfo.value.pageNumber = 1;
    pageInfo.value.pageSize = DEFAULT_PAGE_SIZE;
    unitListRef.value?.fetchList();
  }

  function addOne() {
    addDisabled.value = true;
    unitListRef.value?.addOne();
  }
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title
      :title="$t('drug.unitManage.unit', '单位') + $t('global:list')"
    ></Title>
    <div>
      <SearchProForm
        v-model="searchParam"
        v-model:add-disabled="addDisabled"
        :biz-data="unitListRef?.selections || []"
        @refresh="refresh"
        @add-one="addOne"
      ></SearchProForm>
    </div>
    <UnitList
      ref="unitList"
      v-model="pageInfo"
      :key-word="searchParam.keyWord"
      :enabled-flag="searchParam.enabledFlag"
      :unit-type-code="searchParam.unitTypeCode as unknown as string"
      @enable-add="addDisabled = false"
    ></UnitList>
  </div>
</template>
