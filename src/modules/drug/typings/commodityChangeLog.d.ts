declare namespace CommodityChangeLog {
  interface CommodityChangeLogQueryParams {
    commodityTypeCode: string;
    dataOperateTypeCode?: string;
    commodityChangeApprovalCodes?: string[];
    commodityId?: string;
    commodityChangeLogIds?: string[];
    keyword?: string;
    executeDate?: string;
    pageNumber: number;
    pageSize: number;
  }

  interface CommodityChangeLogItem {
    commodityChangeLogId: string;
    commodityTypeCode: string;
    dataOperateTypeCode: string;
    dataOperateTypeDesc: string;
    oldCommodityInfo?: string;
    commodityInfo: string;
    executeAt: string;
    commodityChangeApprovalCode: string;
    commodityChangeApprovalDesc: string;
    commodityId?: string;
    createdUserId: string;
    createdUserName: string;
    createdAt: string;
    commodityChangeCompareList?: {
      changeColumnName: string;
      changeColumnDisplay: string;
      changeColumnOldValue?: string;
      changeColumnValue?: string;
      changeFlag: number;
    }[];
  }

  interface ApprovalCommodityChangeByIdsParams {
    commodityChangeLogIds: string[];
    agreeFlag: number;
    immediatelyExecuteFlag: number;
    executeAt?: string;
  }
}
