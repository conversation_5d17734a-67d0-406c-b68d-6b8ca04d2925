declare namespace Medicine {
  interface MedicineListQueryParams {
    keyWord?: string;
    hospitalId?: string;
    enabledFlag?: number;
    commodityId?: string;
    stockContrilStatusCode?: string;
    commodityId?: string;
    antibacterialLevelCode?: string;
    antitumorDrugLevelCode?: string;
    valuableFlag?: string;
    medicineTypeCode?: string;
    dosageFormCode?: string;
    pharmacologyClassCode?: string;
    specialManageMedicineCode?: string;
    chargeItemLevelCode?: string;
    producedByOrgId?: string;
    encounterTypeCode?: string;
    pageNumber: number;
    pageSize: number;
  }

  interface MedicinePackUnitItem {
    medicinePackUnitId: string;
    packUnitId: string;
    packUnitName: string;
    convertFactor: number;
  }

  interface HospitalMedicineItem {
    hospitalCommodityId: string;
    hospitalId: string;
    hospitalName: string;
    commodityCategoryId: string;
    commodityCategoryName: string;
    outCommodityCategoryId: string;
    outCommodityCategoryName: string;
    inCommodityCategoryId: string;
    inCommodityCategoryName: string;
    accCommodityCategoryId: string;
    accCommodityCategoryName: string;
    fncCommodityCategoryId: string;
    fncCommodityCategoryName: string;
    mrCommodityCategoryId: string;
    mrCommodityCategoryName: string;
    enabledFlag: number;
    encounterTypeCodes: string[];
    commodityPurchasePrice: number;
    price: number;
    stockContrilStatusCode: string;
    stockContrilStatusDesc: string;
    outMedicineCalcTypeCode: string;
    outMedicineCalcTypeDesc: string;
    medicineUseSceneXUnitList: {
      medicineUseSceneXUnitId: string;
      medicineUseSceneCode: string;
      medicineUseSceneDesc: string;
      packUnitId: string;
      packUnitName: string;
      editable: boolean;
    }[];
  }

  interface MedicineListItem {
    // 药品商品
    commodityId: string;
    commodityNo: string;
    commodityName: string;
    commodity2ndName: string;
    commodityExtName: string;
    commodityNameDisplay: string;
    commoditySpec: string;
    unitId: string;
    unitName: string;
    spellNo: string;
    wbNo: string;
    memo: string;
    producedByOrgId: string;
    producedByOrgName: string;
    approvalNo: string;
    validPeriod: number;
    alreadyUseFlag: number;
    medicineSpecId: string;
    medicineSpec: string;
    miniUnitId: string;
    miniUnitName: string;
    doseFactor: number;
    doseUnitCode: string;
    doseUnitDesc: string;

    // 药品通用名表
    cadnId: string;
    cadn: string;
    cadnExt: string;
    cadn2nd: string;
    cadnEng: string;
    medicineTypeCode: string;
    medicineTypeDesc: string;
    dosageFormCode: string;
    dosageFormDesc: string;
    pharmacologyClassCode: string;
    pharmacologyClassDesc: string;
    specialManageMedicineCode: string;
    specialManageMedicineDesc: string;

    chargeItemLevelCode?: string;
    chargeItemLevelDesc?: string;
    upperPrice?: number;
    storageConditionCode?: string;
    storageConditionDesc?: string;
    storageDesc?: string;
    antibacterialLevelCode: string;
    antibacterialLevelDesc: string;
    antitumorDrugLevelCode: string;
    antitumorDrugLevelDesc: string;
    valuableFlag: string;
    existsApprovalChangeFlag: number;
    usageDesc?: string;
    dangerDrugLevelCode?: string;
    dangerDrugLevelDesc: string;
    highPriceFlag: number;
    selfPayFlag: number;
    fusionMediaDrugFlag: number;
    skinTestFlag: number;
    medicineXAdminRouteList?: {
      medicineXAdminRouteId: string;
      adminRouteId: string;
      adminRouteName: string;
    }[];
    commodityExtInfoList?: {
      commodityExtInfoId: string;
      otherAttributeCode: string;
      otherAttributeDesc: string;
      otherAttributeValue?: string;
    }[];
    medicinePackUnitList: MedicinePackUnitItem[];
    hospitalMedicineList?: HospitalMedicineItem[];
  }

  interface MedicineUpsertParams {
    approvalExecuteFlag: number;
    commodityId?: string;
    commodityNo?: string;
    commodityName?: string;
    commodity2ndName?: string;
    commodityExtName?: string;
    commoditySpec?: string;
    unitId?: string;
    spellNo?: string;
    wbNo?: string;
    memo?: string;
    producedByOrgId?: string;
    approvalNo?: string;
    validPeriod?: number;
    medicineSpecId?: string;
    chargeItemLevelCode?: string;
    upperPrice?: number;
    storageConditionCode?: string;
    storageDesc?: string;
    antibacterialLevelCode?: string;
    antitumorDrugLevelCode?: string;
    valuableFlag?: string;
    usageDesc?: string;
    dangerDrugLevelCode?: string;
    highPriceFlag?: number;
    selfPayFlag?: number;
    fusionMediaDrugFlag?: number;
    skinTestFlag?: number;
    medicineXAdminRouteList?: { adminRouteId: string }[];
    commodityExtInfoList?: {
      otherAttributeCode: string;
      otherAttributeValue?: string;
    }[];
    medicinePackUnitList?: {
      medicinePackUnitId?: string;
      packUnitId?: string;
      convertFactor?: number;
    }[];
    hospitalMedicineList?: {
      hospitalCommodityId?: string;
      hospitalId?: string;
      commodityCategoryId?: string;
      outCommodityCategoryId?: string;
      inCommodityCategoryId?: string;
      accCommodityCategoryId?: string;
      fncCommodityCategoryId?: string;
      mrCommodityCategoryId?: string;
      enabledFlag?: number;
      encounterTypeCodes?: string[];
      commodityPriceId?: string;
      commodityPurchasePrice?: number;
      price?: number;
      stockContrilStatusCode?: string;
      outMedicineCalcTypeCode?: string;
      medicineUseSceneXUnitList?: {
        medicineUseSceneCode?: string;
        medicineUseSceneXUnitId?: string;
        packUnitId?: string;
      }[];
    }[];
  }

  interface MedicineUseInfoUpsertParams {
    medicineUseInfoId?: string;
    commodityId?: string;
    defaultAdminRouteId?: string;
    defaultPresDoseQty?: number;
    doseUnitCode?: string;
    defaultAdminFreqId?: string;
    defaultDays?: number;
    defaultEntrust?: string;
  }

  interface MedicineUseInfoListItem {
    medicineUseInfoId: string;
    commodityId: string;
    defaultAdminRouteId?: string;
    defaultAdminRouteName?: string;
    defaultPresDoseQty?: number;
    doseUnitCode?: string;
    doseUnitDesc?: string;
    defaultAdminFreqId?: string;
    defaultAdminFreqName?: string;
    defaultDays?: number;
    defaultEntrust?: string;
  }
}
