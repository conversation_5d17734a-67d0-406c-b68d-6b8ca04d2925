declare namespace StorageRack {
  interface SearchStorageRackParams {
    hospitalId?: string;
    storageIdTypeCode?: string;
    commodityStorageId?: string;
    enabledFlag?: number;
  }

  interface StorageRackItem {
    storageRackId: string;
    storageIdTypeCode: string;
    commodityStorageId: string;
    storageRackName: string;
    fullStorageRackName: string;
    enabledFlag: number;
    subStorageRackList?: StorageRackItem[];
  }

  interface UpsertStorageRackParams {
    storageRackId?: string;
    storageRackName?: string;
    storageIdTypeCode?: string;
    commodityStorageId?: string;
    enabledFlag?: number;
    parentStorageRackList?: string;
  }
}
