declare namespace Cadn {
  interface CadnListQueryParams {
    keyWord?: string;
    cadnIds?: string[];
    medicineTypeCode?: string;
    dosageFormCode?: string;
    pharmacologyClassCode?: string;
    pageNumber: number;
    pageSize: number;
  }

  interface MedicineSpecDosageUnitItem {
    medicineSpecDosageUnitId: string;
    doseUnitCode: string;
    doseUnitDesc: string;
    convertFactorNumerator: number;
    convertFactorDenominator: number;
    enabledFlag: number;
    editable?: boolean;
  }

  interface MedicineSpecItem {
    medicineSpecId: string;
    medicineSpec: string;
    miniUnitId: string;
    miniUnitName: string;
    doseFactor: number;
    doseUnitCode: string;
    doseUnitDesc: string;
    enabledFlag: number;
    medicineSpecDosageUnitList: MedicineSpecDosageUnitItem[];
  }

  interface CadnListItem {
    cadnId: string;
    cadn: string;
    cadnExt: string;
    cadn2nd: string;
    cadnDisplay: string;
    cadnEng: string;
    spellNo: string;
    wbNo: string;
    medicineTypeCode: string;
    medicineTypeDesc: string;
    dosageFormCode: string;
    dosageFormDesc: string;
    pharmacologyClassCode: string;
    pharmacologyClassDesc: string;
    specialManageMedicineCode: string;
    specialManageMedicineDesc: string;
    alreadyUseFlag: number;
    medicineSpecList: MedicineSpecItem[];
  }

  interface MedicineServiceListQueryParams {
    keyWord?: string;
    enabledFlag?: number;
    medicineSpecIds?: string[];
    pageNumber?: number;
    pageSize?: number;
  }

  interface MedicineServiceItem {
    msId: string;
    msNo: string;
    msName: string;
    ms2ndName: string;
    msExtName: string;
    msNameDisplay: string;
    msTypeCode: string;
    msTypeDesc: string;
    wbNo: string;
    spellNo: string;
    enabledFlag: number;
    csTypeCode: string;
    csTypeDesc: string;
    medicineSpecId: string;
    commodityId: string;
    medicineSpec: string;
    miniUnitId: string;
    miniUnitName: string;
    doseFactor: number;
    doseUnitCode: string;
    doseUnitDesc: string;
    cadnId: string;
    cadn: string;
    cadnExt: string;
    cadn2nd: string;
    cadnEng: string;
    medicineTypeCode: string;
    medicineTypeDesc: string;
    dosageFormCode: string;
    dosageFormDesc: string;
    pharmacologyClassCode: string;
    pharmacologyClassDesc: string;
    specialManageMedicineCode: string;
    specialManageMedicineDesc: string;
  }

  interface CadnAndMedicineSpecListQueryParams {
    keyWord?: string;
    enabledFlag?: number;
    medicineSpecIds?: string[];
    pageNumber?: number;
    pageSize?: number;
  }

  interface CadnMedicineSpecItem {
    medicineSpecId: string;
    medicineSpec: string;
    miniUnitId: string;
    miniUnitName: string;
    doseFactor: number;
    doseUnitCode: string;
    doseUnitDesc: string;
    enabledFlag: number;
    cadnId: string;
    cadn: string;
    cadnExt: string;
    cadn2nd: string;
    cadnDisplay: string;
    cadnEng: string;
    spellNo: string;
    wbNo: string;
    medicineTypeCode: string;
    medicineTypeDesc: string;
    dosageFormCode: string;
    dosageFormDesc: string;
    pharmacologyClassCode: string;
    pharmacologyClassDesc: string;
    specialManageMedicineCode: string;
    specialManageMedicineDesc: string;
  }
}
