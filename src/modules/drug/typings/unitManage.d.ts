declare namespace UnitManage {
  export interface Unit {
    unitId?: string; // 单位标识
    unitNo?: string; // 单位编码
    unitName: string; // 单位名称
    unitTypeCode: string;
    unitTypeCodeDesc?: string;
    enabledFlag: number;
    editable?: boolean; // 正在编辑【自定义】
    saving?: boolean; // 正在保存【自定义】
  }

  export type SaveUnitReqParam = Partial<Unit> &
    Pick<Unit, 'unitNo' | 'unitName'>; // unitId: 为空时新增

  export interface ExportDMLReqParam {
    bizIdTypeCode: string; // 业务标识类型代码
    DataBaseTypeCode: string; // 数据库类型代码
    bisIds?: string[]; // 业务标识集合
  }

  export interface IOPerations {
    edit: (index: number) => void;
    cancel: (index: number) => void;
    save: (index: number) => void;
  }

  export interface IFetchListReqParam {
    pageNumber: number;
    pageSize: number;
    keyWord?: string;
    unitTypeCode?: string;
    enabledFlag?: number;
  }
}
