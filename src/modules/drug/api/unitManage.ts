import { dictRequest } from '@sun-toolkit/request';

/**
 * 1-10405-1
 * 保存计量单位
 */
export function saveUnit(params: UnitManage.SaveUnitReqParam) {
  return dictRequest<{ unitId: string }>('/unit/saveUnit', params);
}

/**
 * 1-10042-1
 * 根据条件查询单位列表
 */
export function queryUnitListByExample(params: UnitManage.IFetchListReqParam) {
  return dictRequest<UnitManage.Unit, UnitManage.IFetchListReqParam>(
    '/unit/queryUnitListByExample',
    params,
  );
}
