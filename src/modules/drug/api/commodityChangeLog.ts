import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10494-1] 根据条件查询商品变更记录列表
 * 是否分页	Y
 * @param params
 * @returns
 */
export const queryCommodityChangeLogListByExample = (
  params: CommodityChangeLog.CommodityChangeLogQueryParams,
) => {
  return dictRequest<
    CommodityChangeLog.CommodityChangeLogItem,
    CommodityChangeLog.CommodityChangeLogQueryParams
  >('/CommodityChangeLog/queryCommodityChangeLogListByExample', params);
};

/**
 * [1-10495-1] 根据标识集合审核变更记录
 * @param params
 * @returns
 */
export const approvalCommodityChangeByIds = (
  params: CommodityChangeLog.ApprovalCommodityChangeByIdsParams,
) => {
  return dictRequest(
    '/CommodityChangeLog/approvalCommodityChangeByIds',
    params,
    {
      successMsg: translation('global:save.success'),
    },
  );
};

/**
 * [1-10496-1] 根据标识集合执行变更记录
 * @param params
 * @returns
 */
export const executeCommodityChangeByIds = (params: {
  commodityChangeLogIds: string[];
}) => {
  return dictRequest(
    '/CommodityChangeLog/executeCommodityChangeByIds',
    params,
    {
      successMsg: translation('global:save.success'),
    },
  );
};
