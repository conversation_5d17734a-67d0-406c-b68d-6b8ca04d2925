import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10233-1] 根据条件查询药品商品列表
 * @param params
 * @returns
 */
export const queryMedicineListByExample = (
  params: Medicine.MedicineListQueryParams,
) => {
  return dictRequest<
    Medicine.MedicineListItem,
    Medicine.MedicineListQueryParams
  >('/medicine/queryMedicineListByExample', params);
};

/**
 * [1-10234-1] 新增药品商品
 * @param params
 * @returns
 */
export const addMedicine = (params: Medicine.MedicineUpsertParams) => {
  return dictRequest<{ commodityId: string }>('/medicine/addMedicine', params, {
    successMsg: translation('global:create.success'),
  });
};

/**
 * [1-10235-1] 根据标识修改药品商品
 * @param params
 * @returns
 */
export const updateMedicineById = (params: Medicine.MedicineUpsertParams) => {
  return dictRequest('/medicine/updateMedicineById', params, {
    successMsg: translation('global:modify.success'),
  });
};

/**
 * [1-10555-1] 新增药品默认用法用量
 * @param params
 * @returns
 */
export const addMedicineUseInfo = (
  params: Medicine.MedicineUseInfoUpsertParams,
) => {
  return dictRequest<{ medicineUseInfoId: string }>(
    '/medicine/addMedicineUseInfo',
    params,
    {
      successMsg: translation('global:create.success'),
    },
  );
};

/**
 * [1-10556-1] 新增药品默认用法用量
 * @param params
 * @returns
 */
export const updateMedicineUseInfoById = (
  params: Medicine.MedicineUseInfoUpsertParams,
) => {
  return dictRequest('/medicine/updateMedicineUseInfoById', params, {
    successMsg: translation('global:edit.success'),
  });
};

/**
 * [1-10557-1] 查询药品默认用法用量
 * @param params
 * @returns
 */
export const queryMedicineUseInfoByCommodityId = (params: {
  commodityIds: string[];
}) => {
  return dictRequest<Medicine.MedicineUseInfoListItem[]>(
    '/medicine/queryMedicineUseInfoByCommodityId',
    params,
  );
};
