import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10355-1]根据条件查询管理对象
 * @param params
 * @returns
 */
export const queryManageObjectByExample = (
  params: ManageObjectSetting.SearchManageObjectParams,
) => {
  return dictRequest<ManageObjectSetting.ManageObject[]>(
    '/manageRule/queryManageObjectByExample',
    params,
  );
};

/**
 * [1-10450-1] 新增管理对象
 * @param params
 * @returns
 */
export const addManageObject = (params: ManageObjectSetting.UpsertParams) => {
  return dictRequest<{ manageRuleId: string }>(
    '/manageRule/addManageObject',
    params,
  );
};

/**
 * [1-10451-1] 编辑管理对象
 * @param params
 * @returns
 */
export const editManageObject = (params: ManageObjectSetting.UpsertParams) => {
  return dictRequest('/manageRule/editManageObject', params);
};

/**
 * [1-10452-1] 根据条件查询病历类型
 * @param params
 * @returns
 */
export const queryMedicalRecordTypeByExample = (
  params: ManageObjectSetting.SearchManageObjectParams,
) => {
  return dictRequest<ManageObjectSetting.MedicalRecordType[]>(
    '/medicalRecordType/queryMedicalRecordTypeByExample',
    params,
  );
};
