import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10332-1]根据条件查询管理规则
 * @param params
 * @returns
 */
export const queryManageRuleByExample = (
  params: ManageRuleSetting.SearchManageRuleSettingParams,
) => {
  return dictRequest<ManageRuleSetting.RuleSettingItem[]>(
    '/manageRule/queryManageRuleByExample',
    params,
  );
};

/**
 * [1-10333-1] 新增管理规则
 * @param params
 * @returns
 */
export const addManageRule = (
  params: ManageRuleSetting.InsertRuleSettingParams,
) => {
  return dictRequest<{ manageRuleId: string }>(
    '/manageRule/addManageRule',
    params,
  );
};

/**
 * [1-10334-1] 编辑管理规则
 * @param params
 * @returns
 */
export const editManageRule = (
  params: ManageRuleSetting.UpdateRuleSettingParams,
) => {
  return dictRequest('/manageRule/editManageRule', params);
};

/**
 * [1-10335-1] 删除管理规则
 * @param params
 * @returns
 */
export const deleteManageRule = (ruleId: string) => {
  return dictRequest('/manageRule/deleteManageRule', { manageRuleId: ruleId });
};

/**
 * [1-10404-1] 保存质控规则的缺陷内容
 * @param params
 * @returns
 */
export const saveDeductRule = (
  ruleId: string,
  deductRules: ManageRuleSetting.DeductRuleItem[],
) => {
  return dictRequest('/manageRule/saveDeductRule', {
    manageRuleId: ruleId,
    deductRuleList: deductRules,
  });
};
