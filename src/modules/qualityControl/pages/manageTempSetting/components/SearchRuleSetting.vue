<script setup lang="tsx">
  import { queryManageRuleByExample } from '@/modules/qualityControl/api/manageRuleSetting';
  import { useManageObjectList } from '@/modules/qualityControl/hooks/useManageObjectList';
  import { FLAG } from '@/utils/constant';
  import { TableInstance } from 'element-sun';
  import { ProForm, ProTable, useFormConfig } from 'sun-biz';
  import { onMounted, reactive, ref, useTemplateRef, watch } from 'vue';
  import { useManageRuleSettingTableConfig } from '../config/useTableConfig';

  type Props = {
    manageObjectId?: string;
  };
  const props = defineProps<Props>();

  const state = reactive({
    loading: false,
    selectedRuleList: [] as ManageTemplate.ManageTempXRule[],
  });
  const searchParams = ref<ManageRuleSetting.SearchManageRuleSettingParams>({
    keyWord: '',
  });
  const ruleSettingList = ref<ManageTemplate.ManageTempXRule[]>([]);
  async function queryManageRuleList(
    data?: ManageRuleSetting.SearchManageRuleSettingParams,
  ) {
    state.loading = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryManageRuleByExample({
      ...searchParams.value,
      enabledFlag: FLAG.YES,
    });
    state.loading = false;
    if (res?.success) {
      const data = res.data || [];
      ruleSettingList.value = data.map(
        (item: ManageRuleSetting.RuleSettingItem) =>
          ({
            manageRuleId: item.manageRuleId,
            manageRuleNo: item.manageRuleNo,
            manageRuleContent: item.manageRuleContent,
            manageObjectId: item.manageObjectId,
            manageObjectName: item.manageObjectName,
            manageObjectTypeCode: item.manageObjectTypeCode,
            deductPoints: item.deductPoints,
          }) as unknown as ManageTemplate.ManageTempXRule,
      );
    }
  }

  const { manageObjectList, queryManageObjectList } = useManageObjectList();
  onMounted(() => {
    queryManageObjectList({ _t: Date.now() });
  });

  const searchConfig = useFormConfig({
    getData: (t) => [
      {
        label: t('manageRuleSetting.search.manageObject', '病历质控节点'),
        name: 'manageObjectId',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select'),
        extraProps: {
          options: manageObjectList.value,
          className: 'w-40',
          clearable: true,
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          stopPropagation: true,
          remoteMethod: (query: string) =>
            queryManageObjectList({ keyWord: query }),
          props: {
            label: 'manageObjectName',
            value: 'manageObjectId',
          },
          disabled: true,
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        triggerModelChange: true,
        extraProps: {
          style: { width: '220px' },
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryManageRuleList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryManageRuleList({
              keyWord: '',
            });
          },
        },
      },
    ],
  });

  function handleSelectChange(selectedRows: ManageTemplate.ManageTempXRule[]) {
    state.selectedRuleList = selectedRows;
  }

  const emit = defineEmits(['import']);
  const tableRef = useTemplateRef<{ eleTable: TableInstance }>('proTable');

  function handleImportClick() {
    const list = state.selectedRuleList;
    tableRef.value!.eleTable.clearSelection();
    emit('import', [...list]);
  }

  watch(
    () => props.manageObjectId,
    (val) => {
      searchParams.value = { manageObjectId: val, keyWord: '' };
      queryManageRuleList({ manageObjectId: val || '' });
    },
    { immediate: true },
  );

  const tableColumns = useManageRuleSettingTableConfig({
    canEdit: false,
    canSelect: true,
  });
</script>
<template>
  <div class="h-hull mx-4 flex flex-1 flex-col overflow-hidden">
    <div class="flex">
      <ProForm
        v-model="searchParams"
        layout-mode="inline"
        :data="searchConfig"
        @model-change="queryManageRuleList"
      />
      <el-button class="mr-3" type="primary" @click="handleImportClick">
        {{ $t('qualityControl.manageTemplate.import', '导入') }}
      </el-button>
    </div>

    <div>
      <pro-table
        ref="proTable"
        :row-key="
        (row: ManageTemplate.ManageTempXRule) =>
          `${row.manageRuleId}_${row.manageObjectId}`
      "
        :data="ruleSettingList"
        :columns="tableColumns"
        style="height: 260px"
        @selection-change="handleSelectChange"
      />
    </div>
  </div>
</template>
