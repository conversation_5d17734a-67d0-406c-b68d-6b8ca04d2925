<script setup lang="tsx">
  import { queryManageRuleByExample } from '@/modules/qualityControl/api/manageRuleSetting';
  import {
    addManageTemp,
    editManageTemp,
  } from '@/modules/qualityControl/api/manageTemplate';
  import { useManageObjectList } from '@/modules/qualityControl/hooks/useManageObjectList';
  import { FLAG } from '@sun-toolkit/enums';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { ProForm, ProTable } from 'sun-biz';
  import { computed, onMounted, onUnmounted, reactive, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { useManageTemplateRuleConfig } from '../config/useFormConfig';
  import { useManageRuleSettingTableConfig } from '../config/useTableConfig';
  import SearchRuleSetting from './SearchRuleSetting.vue';

  const { t } = useTranslation();
  type Props = {
    manageTemp: ManageTemplate.ManageTemplateItem;
  };
  const props = defineProps<Props>();
  const router = useRouter();
  const templateForm = computed(() => {
    const {
      manageTempId,
      manageTempName,
      enabledFlag,
      manageTempScopeList,
      manageObjectId,
    } = props.manageTemp;
    if (!manageTempId) {
      return {
        manageTempName: '',
        manageRuleScopeCodes: [] as string[],
        manageObjectId: '',
      } as unknown as ManageTemplate.ManageTemplateForm;
    }

    const form: ManageTemplate.ManageTemplateForm = {
      manageTempId,
      manageTempName,
      enabledFlag,
      manageRuleScopeCodes: manageTempScopeList.map(
        (item) => item.manageRuleScopeCode,
      ),
      manageObjectId,
      manageObject: {
        manageObjectId,
      },
    };
    return form;
  });
  const state = reactive({
    templateForm: {} as ManageTemplate.ManageTemplateForm,
    templateRuleForm: [{} as ManageTemplate.ManageTempXRule],
    isPopoverVisible: false,
    popoverReference: null as HTMLElement | null,
    currRowIndex: 0,
    selectedMrTempList: [] as ManageTemplate.MrTempItem[],
    selectedMrTemp: {} as ManageTemplate.MrTempItem,
    isInitialized: false, // 添加初始化标志
  });

  watch(
    () => props.manageTemp,
    () => {
      state.templateForm = templateForm.value;
      console.log(' templateForm>>>>', props.manageTemp);
      const ruleForm = props.manageTemp.manageTempXRuleList?.slice();
      if (!ruleForm || !ruleForm.length) {
        state.templateRuleForm = [{} as ManageTemplate.ManageTempXRule];
      } else {
        state.templateRuleForm = ruleForm;
      }
      // 标记组件已完成初始化
      state.isInitialized = true;
    },
    { immediate: true },
  );

  // 新增一条规则
  function handleAddClick(index: number) {
    state.templateRuleForm.splice(
      index + 1,
      0,
      {} as ManageTemplate.ManageTempXRule,
    );
  }

  // 删除一条规则
  function handleDeleteClick(index: number) {
    if (state.templateRuleForm.length === 1) {
      state.templateRuleForm = [{} as ManageTemplate.ManageTempXRule];
      return;
    }
    state.templateRuleForm.splice(index, 1);
  }

  // 批量选择导入规则
  function handleImport(list: ManageTemplate.ManageTempXRule[]) {
    state.templateRuleForm.splice(state.currRowIndex, 1, ...list);
    closePopover();
  }

  // 输入框获取焦点, 弹出规则选择框
  function handleInputFocus(e: Event, index: number) {
    state.currRowIndex = index;
    state.popoverReference = e.currentTarget as HTMLElement;
    state.isPopoverVisible = true;
  }

  // 点击外部关闭 popover
  function handleClickOutside(event: MouseEvent) {
    if (!state.isPopoverVisible) return;

    const target = event.target as HTMLElement;
    // 判断是否点击到了 input 内部
    if (state.popoverReference && state.popoverReference.contains(target)) {
      return;
    }
    const customPopover = document.querySelector('.custom-popover');
    // 判断点击的是不是 popover 内部
    if (customPopover && customPopover.contains(target)) {
      return;
    }

    // 关闭 popover 并清空状态
    closePopover();
  }

  // 关闭 popover 并清空状态
  function closePopover() {
    state.isPopoverVisible = false;
    state.popoverReference = null;
    state.currRowIndex = 0;
  }
  // 查询病历模板详情
  // async function getMrTempDetail(ids: string[]) {
  //   const [, res] = await queryMrTempById({
  //     mrTempIds: ids.map((item) => Number(item)),
  //   });
  //   if (res?.success) {
  //     const data = res.data || [];
  //     if (data.length) {
  //       state.selectedMrTempList = data;
  //       state.selectedMrTemp = data[0];
  //     }
  //   }
  // }
  // watch(
  //   () => state.templateForm.mrTempListId,
  //   (val) => {
  //     getMrTempDetail(val || []);
  //   },
  // );
  async function queryManageRules(manageObjectId: string) {
    const [, res] = await queryManageRuleByExample({
      manageObjectId,
      enabledFlag: FLAG.YES,
    });
    if (res?.success) {
      res.data?.sort(
        (
          a: ManageRuleSetting.RuleSettingItem,
          b: ManageRuleSetting.RuleSettingItem,
        ) => {
          return a.sort - b.sort;
        },
      );
      const data = res.data || [];
      return data;
    }
    return [];
  }

  async function autoImportManageObjectRules(
    val: ManageObjectSetting.ManageObject,
  ) {
    if (!val) return;
    // 自动引入规则
    const rulesCount = state.templateRuleForm.filter(
      (item) => item.manageRuleId,
    ).length;
    const tip = rulesCount
      ? `将自动引入质控节点【${val.manageObjectName}】对应的质控规则，并清空已有规则，请确认是否继续切换？`
      : `是否自动引入质控节点【${val.manageObjectName}】对应的质控规则`;
    try {
      await ElMessageBox.confirm(
        t('qualityControl.manageTemplate.select.ask.title', tip),
        t('global:tip', '提示'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      );
      // 查询质控项目对应的规则列表并引入
      const rules = await queryManageRules(val.manageObjectId + '');
      state.templateRuleForm =
        rules.length > 0
          ? rules.map(
              (item: ManageRuleSetting.RuleSettingItem) =>
                ({
                  manageRuleId: item.manageRuleId,
                  manageRuleNo: item.manageRuleNo,
                  manageRuleContent: item.manageRuleContent,
                  manageObjectId: item.manageObjectId,
                  manageObjectName: item.manageObjectName,
                  manageObjectTypeCode: item.manageObjectTypeCode,
                  deductPoints: item.deductPoints,
                }) as unknown as ManageTemplate.ManageTempXRule,
            )
          : [{} as ManageTemplate.ManageTempXRule];
      return true;
    } catch {
      return;
    }
  }

  // 监听病历质控节点下拉选择变化
  watch(
    () => state.templateForm.manageObject,
    async (val, oldVal) => {
      // 只有在组件初始化完成后且用户主动选择时才触发
      if (!state.isInitialized || !val || !oldVal) {
        return;
      }

      const res = await autoImportManageObjectRules(
        val as unknown as ManageObjectSetting.ManageObject,
      );
      if (!res) {
        state.templateForm.manageObject = oldVal;
      }
    },
  );

  // 搜索质控项目
  const { manageObjectList, queryManageObjectList } = useManageObjectList();
  onMounted(() => {
    queryManageObjectList();
  });

  const dialogForm = useManageTemplateRuleConfig(
    manageObjectList,
    queryManageObjectList,
  );
  const tableColumns = useManageRuleSettingTableConfig({
    canEdit: true,
    onAddClick: handleAddClick,
    onDeleteClick: handleDeleteClick,
    onInputFocus: handleInputFocus,
  });

  onMounted(() => {
    document.addEventListener('click', handleClickOutside);
  });

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
  });

  function getFormData() {
    const templateForm = state.templateForm;
    const ruleList = state.templateRuleForm.filter((item) => item.manageRuleId);
    const form: ManageTemplate.ManageTemplateItem = {
      ...props.manageTemp,
      manageTempName: templateForm.manageTempName,
      enabledFlag: templateForm.enabledFlag,
      manageTempScopeList: templateForm.manageRuleScopeCodes.map((item) => ({
        manageRuleScopeCode: item,
      })),
      manageTempXRuleList: ruleList,
      manageObjectId: templateForm.manageObject?.manageObjectId,
    };

    return form;
  }

  function handleCancelClick() {
    router.push('/');
  }

  const handleSaveClick = async () => {
    const data: ManageTemplate.ManageTemplateItem = getFormData();
    if (!validateForm(data)) {
      return;
    }
    let templateId = data.manageTempId;
    let isSuccess = false;
    if (!templateId) {
      const params: ManageTemplate.InsertManageTempParams = {
        ...data,
        manageRuleScopeCodes: data.manageTempScopeList.map(
          (item) => item.manageRuleScopeCode,
        ),
      };
      const [, res] = await addManageTemp(params);
      isSuccess = !!res?.success;
      templateId = res?.data?.manageTempId || 0;
    } else {
      const params: ManageTemplate.UpdateManageTempParams = {
        ...data,
      };
      const [, res] = await editManageTemp(params);
      isSuccess = !!res?.success;
    }
    if (isSuccess) {
      ElMessage.success(
        t(data.manageTempId ? 'global:edit.success' : 'global:add.success'),
      );
      router.push('/');
    }
  };

  // 校验表单
  function validateForm(form: ManageTemplate.ManageTemplateItem) {
    if (!form.manageTempName) {
      ElMessage.warning(
        t('global:placeholder.input.template', {
          content: t('qualityControl.manageTemplate.ruleForm.name', '模板名称'),
        }),
      );
      return false;
    }
    if (!form.manageTempScopeList || form.manageTempScopeList.length === 0) {
      ElMessage.warning(
        t('global:placeholder.select.template', {
          name: t('qualityControl.manageTemplate.ruleForm.scope', '质控范围'),
        }),
      );
      return false;
    }
    if (!form.manageObjectId) {
      ElMessage.warning(
        t('global:placeholder.select.template', {
          name: t(
            'qualityControl.manageTemplate.ruleForm.manageObject',
            '病历质控节点',
          ),
        }),
      );
      return false;
    }
    // 质控规则不能重复
    const manageRuleIds = form.manageTempXRuleList.map(
      (item) => item.manageRuleId,
    );
    const uniqueManageObjectIds = [...new Set(manageRuleIds)];
    if (manageRuleIds.length !== uniqueManageObjectIds.length) {
      ElMessage.warning(
        t(
          'qualityControl.manageTemplate.ruleForm.manageObjectRepeat',
          '质控规则不能重复',
        ),
      );
      return false;
    }
    return true;
  }
</script>
<template>
  <div class="h-hull flex flex-1 flex-col overflow-hidden">
    <div class="flex">
      <ProForm
        ref="formRef"
        v-model="state.templateForm"
        :column="4"
        :data="dialogForm"
        style="grid-template-columns: 1fr 1fr 150px 2fr"
      />
      <div class="flex w-64">
        <el-button type="primary" class="ml-2" @click="handleSaveClick">
          {{ $t('global:save') }}</el-button
        >
        <el-button type="default" class="ml-2" @click="handleCancelClick">
          {{ $t('global:cancel') }}</el-button
        >
      </div>
    </div>
    <div class="h-full">
      <!-- <div class="mb-2">
        {{ $t('qualityControl.manageTemplate.rule', '质控规则') }}
      </div> -->
      <el-row :gutter="10">
        <el-col :span="24">
          <pro-table
            ref="tableRef"
            row-key="manageTempXRuleId"
            :data="state.templateRuleForm"
            :columns="tableColumns"
          />
        </el-col>
        <!-- <el-col :span="12">
          <div class="h-[800px] border p-2">
            <div class="flex justify-end">
              <el-select
                class="w-56"
                v-model="state.selectedMrTemp"
                value-key="mrTempId"
              >
                <el-option
                  :value="item"
                  v-for="item in state.selectedMrTempList"
                  :key="item.mrTempId"
                  :label="item.mrTempName"
                >
                </el-option>
              </el-select>
            </div>
            <div class="mt-2 h-[700px] overflow-x-auto">
              <pre
                v-html="state.selectedMrTemp.mrTempContent"
                class="whitespace-pre-wrap"
              ></pre>
            </div>
          </div>
        </el-col> -->
      </el-row>

      <el-popover
        popper-class="custom-popover"
        width="800"
        placement="bottom-end"
        :visible="state.isPopoverVisible"
        virtual-triggering
        :virtual-ref="state.popoverReference"
      >
        <SearchRuleSetting
          :manage-object-id="state.templateForm.manageObject?.manageObjectId"
          @import="handleImport"
        ></SearchRuleSetting>
      </el-popover>
    </div>
  </div>
</template>
