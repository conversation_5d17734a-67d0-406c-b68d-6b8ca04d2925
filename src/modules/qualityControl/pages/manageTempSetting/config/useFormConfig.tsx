import { queryManageObjectByExample } from '@/modules/qualityControl/api/manageObject';
import { CodeSystemType } from '@/typings/codeManage';
import { ENABLED_FLAG } from '@/utils/constant';
import { useFormConfig } from 'sun-biz';
import { Ref } from 'vue';

// 查询质控模板表单配置
export function useManageTemplateSearchConfig(
  manageObjectList: Ref<ManageObjectSetting.ManageObject[]>,
  queryManageTemplateList: (
    data: ManageTemplate.SearchManageTemplateParams,
  ) => void,
) {
  const data = useFormConfig({
    dataSetCodes: [CodeSystemType.MANAGE_RULE_SCOPE_CODE],
    getData: (t, dataSet) => [
      {
        label: t('manageTemplate.search.tempeScope', '应用范围'),
        name: 'manageRuleScopeCode',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select'),
        extraProps: {
          clearable: true,
          filterable: true,
          className: 'w-40',
          options: dataSet?.value
            ? dataSet.value?.[CodeSystemType.MANAGE_RULE_SCOPE_CODE]
            : [],
          props: {
            value: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('manageTemplate.search.manageObject', '病历质控节点'),
        name: 'manageObjectId',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select'),
        extraProps: {
          options: manageObjectList.value,
          className: 'w-80',
          clearable: true,
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          stopPropagation: true,
          remoteMethod: (query: string) =>
            queryManageObjectByExample({
              keyWord: query,
              manageObjectTypeCode: '1',
            }),
          props: {
            label: 'manageObjectName',
            value: 'manageObjectId',
          },
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        triggerModelChange: true,
        extraProps: {
          style: { width: '220px' },
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryManageTemplateList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryManageTemplateList({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}

// 质控规则表单配置
export function useManageTemplateRuleConfig(
  manageObjectList: Ref<ManageObjectSetting.ManageObject[]>,
  queryManageObjectList: (
    params?: ManageObjectSetting.SearchManageObjectParams,
  ) => void,
  onManageObjectChange: (val: ManageObjectSetting.ManageObject) => void,
) {
  const data = useFormConfig({
    dataSetCodes: [CodeSystemType.MANAGE_RULE_SCOPE_CODE],
    getData: (t, dataSet) => [
      {
        label: t('manageTemplate.form.manageTempName', '质控模板'),
        name: 'manageTempName',
        component: 'input',
        triggerModelChange: true,
        placeholder: t('global:placeholder.input'),
        extraProps: {
          clearable: true,
        },
      },
      {
        label: t('manageTemplate.form.tempeScope', '质控范围'),
        name: 'manageRuleScopeCodes',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select'),
        extraProps: {
          clearable: true,
          filterable: true,
          multiple: true,
          options: dataSet?.value
            ? dataSet.value?.[CodeSystemType.MANAGE_RULE_SCOPE_CODE]
            : [],
          props: {
            value: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
          style: { width: '250px' },
        },
      },
      {
        label: '是否启用',
        name: 'enabledFlag',
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
      {
        label: t('manageTemplate.form.mrTemp', '病历质控节点'),
        name: 'manageObject',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select'),
        extraProps: {
          clearable: false,
          multiple: false,
          options: manageObjectList.value.map((item) => {
            return {
              value: item,
              label: item.manageObjectName,
            };
          }),
          valueKey: 'manageObjectId',
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          remoteMethod: (query: string) =>
            queryManageObjectList({
              keyWord: query,
            }),
          onChange: onManageObjectChange,
        },
      },
    ],
  });
  return data;
}

// 查询提示词表单配置
export function useManageTemplatePromptSearchConfig(
  promptList: Ref<ManageTemplate.ManageTemplatePromptItem[]>,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('manageTemplatePrompt.form.prompt', '提示词记录'),
        name: 'promptWordId',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select'),
        extraProps: {
          clearable: false,
          filterable: true,
          options: promptList.value || [],
          props: {
            value: 'promptWordId',
            label: 'promptWordTitle',
          },
        },
      },
    ],
  });
  return data;
}

// 新增+编辑提示词表单配置
export function useManageTemplatePromptUpsertConfig() {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('manageTemplatePrompt.form.promptTitle', '提示词标题'),
        name: 'promptWordTitle',
        component: 'input',
        triggerModelChange: true,
        placeholder: t('global:placeholder.input'),
        extraProps: {
          clearable: true,
          style: {
            width: '220px',
          },
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
    ],
  });
  return data;
}
