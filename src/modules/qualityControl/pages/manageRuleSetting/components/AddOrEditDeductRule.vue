<script setup lang="ts">
  import { saveDeductRule } from '@/modules/qualityControl/api/manageRuleSetting';
  import { SelectOptions } from '@/typings/common';
  import { FLAG } from '@/utils/constant';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { ProDialog, ProForm } from 'sun-biz';
  import { computed, ref, watch } from 'vue';

  const { t } = useTranslation();

  export type Props = {
    row?: Partial<ManageRuleSetting.DeductRuleItem>;
    title?: string;
    ruleTypeList: SelectOptions[];
    deductRuleTypeList: SelectOptions[];
    manageRuleId: string;
    maxDeductPoints: number;
    currentDeductRuleList: ManageRuleSetting.DeductRuleItem[];
  };

  const props = defineProps<Props>();
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: unknown };
  }>();
  const formModel = ref<ManageRuleSetting.DeductRuleItem>({
    deductRuleDesc: '',
    deductTypeCode: '',
    deductPoints: 0,
    enabledFlag: FLAG.YES,
    assessmentDesc: '',
    deductRuleTypeCode: '',
    singleVetoFlag: FLAG.NO,
  });

  const emits = defineEmits<{
    success: [];
  }>();
  const dialogRef = ref();

  // 表单配置
  const formConfig = computed(() => [
    {
      name: 'deductRuleDesc',
      label: t('qualityControl.deductRule.table.content', '缺陷内容'),
      component: 'input',
      type: 'textarea',
      placeholder: t('global:placeholder.input.template', {
        content: t('qualityControl.deductRule.table.content', '缺陷内容'),
      }),
      rules: [
        {
          required: true,
          message: t('global:placeholder.input.template', {
            content: t('qualityControl.deductRule.table.content', '缺陷内容'),
          }),
          trigger: 'blur',
        },
      ],
      isFullWidth: true,
      extraProps: {
        rows: 5,
      },
    },
    {
      name: 'deductRuleTypeCode',
      label: t(
        'qualityControl.deductRule.table.deductRuleType',
        '扣分规则类型',
      ),
      component: 'select',
      placeholder: t('global:placeholder.select.template', {
        name: t(
          'qualityControl.deductRule.table.deductRuleType',
          '扣分规则类型',
        ),
      }),
      options: props.deductRuleTypeList,
      rules: [
        {
          required: true,
          message: t('global:placeholder.select.template', {
            name: t(
              'qualityControl.deductRule.table.deductRuleType',
              '扣分规则类型',
            ),
          }),
          trigger: 'change',
        },
      ],
    },
    {
      name: 'deductTypeCode',
      label: t('qualityControl.deductRule.table.deductType', '扣分类型'),
      component: 'select',
      placeholder: t('global:placeholder.select.template', {
        name: t('qualityControl.deductRule.table.deductType', '扣分类型'),
      }),
      options: props.ruleTypeList,
      rules: [
        {
          required: true,
          message: t('global:placeholder.select.template', {
            name: t('qualityControl.deductRule.table.deductType', '扣分类型'),
          }),
          trigger: 'change',
        },
      ],
    },
    {
      name: 'deductPoints',
      label: t('qualityControl.deductRule.table.deductPoints', '扣减分数'),
      component: 'input-number',
      min: 0,
      max: props.maxDeductPoints,
      controlsPosition: 'right',
      style: { width: '200px' },
      rules: [
        {
          required: true,
          // message: t(
          //   'qualityControl.deductRule.table.deductPoints.required',
          //   '扣减分数必须大于0',
          // ),
          trigger: 'blur',
          validator: (
            rule: unknown,
            value: number,
            callback: (error?: Error) => void,
          ) => {
            if (value !== 0 && (!value || value < 0)) {
              callback(
                new Error(
                  t(
                    'qualityControl.deductRule.table.deductPoints.required',
                    '扣减分数必须不小于0',
                  ),
                ),
              );
            } else if (value > props.maxDeductPoints) {
              callback(
                new Error(
                  t(
                    'qualityControl.deductRuleSetting.deductPointsMax',
                    '扣减分数不能大于最大扣减分数',
                  ),
                ),
              );
            } else {
              callback();
            }
          },
        },
      ],
      extraProps: {
        precision: 1,
      },
    },
    {
      name: 'singleVetoFlag',
      label: t('qualityControl.form.singleVetoFlag', '单项否决项'),
      component: 'select',
      placeholder: t('global:placeholder.select'),
      extraProps: {
        options: [
          {
            value: FLAG.YES,
            label: t('global:yes'),
          },
          {
            value: FLAG.NO,
            label: t('global:no'),
          },
        ],
      },
    },
    {
      name: 'enabledFlag',
      label: t('qualityControl.form.enabledFlag', '启用标志'),
      component: 'switch',
      extraProps: {
        'active-value': FLAG.YES,
        'inactive-value': FLAG.NO,
        'inline-prompt': true,
        'active-text': t('global:enabled'),
        'inactive-text': t('global:disabled'),
      },
    },
    {
      name: 'assessmentDesc',
      label: t('qualityControl.deductRule.table.assessmentDesc', '评估说明'),
      component: 'input',
      type: 'textarea',
      placeholder: t('global:placeholder.input.template', {
        content: t(
          'qualityControl.deductRule.table.assessmentDesc',
          '评估说明',
        ),
      }),
      isFullWidth: true,
      extraProps: {
        rows: 5,
      },
    },
  ]);

  watch(
    () => props.row,
    () => {
      if (props.row && props.row.deductRuleId) {
        // 编辑模式，填充表单数据
        formModel.value = {
          deductRuleId: props.row.deductRuleId,
          deductRuleDesc: props.row.deductRuleDesc || '',
          deductTypeCode: props.row.deductTypeCode || '',
          deductPoints: props.row.deductPoints || 0,
          enabledFlag: props.row.enabledFlag ?? FLAG.NO,
          assessmentDesc: props.row.assessmentDesc || '',
          manageRuleId: props.row.manageRuleId,
          deductRuleTypeCode: props.row.deductRuleTypeCode || '',
          singleVetoFlag: props.row.singleVetoFlag ?? FLAG.NO,
        };
      } else {
        // 新增模式，重置表单
        formModel.value = {
          deductRuleDesc: '',
          deductTypeCode: '',
          deductPoints: 0,
          enabledFlag: FLAG.YES,
          assessmentDesc: '',
          deductRuleTypeCode: '',
          singleVetoFlag: FLAG.NO,
        };
      }
    },
    { immediate: true },
  );

  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid) => {
        if (valid) {
          const params = formModel.value;

          // 验证逻辑
          if (!params.deductRuleDesc) {
            reject([
              '',
              new Error(
                t('global:placeholder.input.template', {
                  content: t(
                    'qualityControl.deductRule.table.content',
                    '缺陷内容',
                  ),
                }),
              ),
            ]);
            return;
          }

          if (!params.deductRuleTypeCode) {
            reject([
              '',
              new Error(
                t('global:placeholder.select.template', {
                  name: t(
                    'qualityControl.deductRule.table.deductRuleTypeCode',
                    '扣分规则类型',
                  ),
                }),
              ),
            ]);
            return;
          }

          if (!params.deductTypeCode) {
            reject([
              '',
              new Error(
                t('global:placeholder.select.template', {
                  name: t(
                    'qualityControl.deductRule.table.deductType',
                    '扣分类型',
                  ),
                }),
              ),
            ]);
            return;
          }

          if (
            params.deductPoints !== 0 &&
            (!params.deductPoints || params.deductPoints < 0)
          ) {
            reject([
              '',
              new Error(
                t(
                  'qualityControl.deductRule.table.deductPoints.required',
                  '扣减分数必须不小于0',
                ),
              ),
            ]);
            return;
          }

          if (params.deductPoints > props.maxDeductPoints) {
            reject([
              '',
              new Error(
                t(
                  'qualityControl.deductRuleSetting.deductPointsMax',
                  '扣减分数不能大于最大扣减分数',
                ),
              ),
            ]);
            return;
          }

          // 构造保存参数 - 获取当前规则的所有缺陷内容，然后更新或添加当前项
          const deductRuleItem = {
            deductRuleId: params.deductRuleId,
            deductRuleDesc: params.deductRuleDesc,
            deductTypeCode: params.deductTypeCode,
            deductPoints: params.deductPoints,
            enabledFlag: params.enabledFlag,
            assessmentDesc: params.assessmentDesc,
            deductRuleTypeCode: params.deductRuleTypeCode,
            singleVetoFlag: params.singleVetoFlag,
          };

          // 复制当前所有缺陷内容
          let allDeductRules = [...props.currentDeductRuleList];

          if (props.row?.deductRuleId) {
            // 编辑模式：找到对应项并更新
            const index = allDeductRules.findIndex(
              (item) => item.deductRuleId === props.row?.deductRuleId,
            );
            if (index !== -1) {
              allDeductRules[index] = deductRuleItem;
            }
          } else {
            // 新增模式：添加到列表
            allDeductRules.push(deductRuleItem);
          }

          // 保存所有缺陷内容
          const [, result] = await saveDeductRule(
            props.manageRuleId,
            allDeductRules,
          );

          if (result?.success) {
            ElMessage({
              type: 'success',
              message: t(
                props.row?.deductRuleId
                  ? 'global:edit.success'
                  : 'global:add.success',
              ),
            });
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  }

  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :width="900"
    ref="dialogRef"
    :title="props.title"
    destroy-on-close
    @success="emits('success')"
  >
    <ProForm ref="formRef" v-model="formModel" :column="3" :data="formConfig" />
  </ProDialog>
</template>
