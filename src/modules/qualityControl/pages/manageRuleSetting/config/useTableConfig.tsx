import { editManageRule } from '@/modules/qualityControl/api/manageRuleSetting';
import { SelectOptions } from '@/typings/common';
import { FLAG } from '@/utils/constant';
import { deleteManageRule } from '@modules/qualityControl/api/manageRuleSetting';
import { ElMessage, ElMessageBox } from 'element-sun';
import { useTranslation } from 'i18next-vue';
import { useColumnConfig } from 'sun-biz';
import { Ref } from 'vue';
import { useRouter } from 'vue-router';

export function useRuleSettingTableConfig(options: {
  ruleTypeList: Ref<SelectOptions[]>;
  ruleExecuteWayList: Ref<SelectOptions[]>;
  objectTypeList: Ref<ManageObjectSetting.ManageObject[]>;
  ruleScopeList: Ref<SelectOptions[]>;
  queryData: (data?: ManageRuleSetting.SearchManageRuleSettingParams) => void;
  onEditRuleSettingClick: (item: ManageRuleSetting.RuleSettingItem) => void;
  queryManageObjectList: (
    params: ManageObjectSetting.SearchManageObjectParams,
  ) => Promise<void>;
}) {
  const { t } = useTranslation();
  const router = useRouter();
  const { queryData, onEditRuleSettingClick } = options;
  async function onEnabledFlagChange(row: ManageRuleSetting.RuleSettingItem) {
    const updateParams: ManageRuleSetting.UpdateRuleSettingParams = {
      ...row,
      manageRuleTypeCode: '1',
      manageRuleExecuteWayList: (row.manageRuleExecuteWayList || []).map(
        (item) => ({
          ruleExecuteWayCode: item.ruleExecuteWayCode,
        }),
      ),
      manageRuleScopeList: (row.manageRuleScopeList || []).map((item) => ({
        manageRuleScopeCode: item.manageRuleScopeCode,
      })),
    };
    const [, res] = await editManageRule(updateParams);
    if (res?.success) {
      ElMessage.success(
        t(
          row.enabledFlag
            ? 'global:enabled.success'
            : 'global:disabled.success',
        ),
      );
      queryData();
    }
  }

  function deleteItem(id: string) {
    ElMessageBox.confirm(
      t(
        'qualityControl.ruleSetting.delete.ask.title',
        '您确定要删除该项规则吗',
      ),
      t('global:tip', '提示'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        const [, result] = await deleteManageRule(id);
        if (result?.success) {
          ElMessage({
            type: 'success',
            message: t('global:delete.success'),
          });
          queryData();
        }
      })
      .catch(() => {});
  }

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('qualityControl.table.objectType', '病历质控节点'),
        prop: 'manageObjectName',
        minWidth: 120,
        render: (row: ManageRuleSetting.RuleSettingTableItem) => (
          <div>{row.manageObjectName || '--'}</div>
        ),
      },
      // {
      //   label: t('qualityControl.table.ruleNo', '质控规则编号'),
      //   prop: 'manageRuleNo',
      //   minWidth: 120,
      //   render: (row: ManageRuleSetting.RuleSettingItem) => (
      //     <div>{row.manageRuleNo || '--'}</div>
      //   ),
      // },
      {
        label: t('qualityControl.table.ruleName', '质控规则'),
        prop: 'manageRuleContent',
        minWidth: 250,
        emptyText: '--',
      },
      {
        label: t('qualityControl.table.ruleDes', '规则说明'),
        prop: 'manageRuleDesc',
        minWidth: 200,
        emptyText: '--',
      },
      // {
      //   label: t('qualityControl.table.ruleType', '质控规则类型'),
      //   prop: 'manageRuleTypeCode',
      //   minWidth: 160,
      //   render: (row: ManageRuleSetting.RuleSettingItem) => {
      //     return row.isEdit ? (
      //       <el-select
      //         v-model={row.form.manageRuleTypeCode}
      //         multiple={false}
      //         filterable={true}
      //         collapse-tags={true}
      //         collapse-tags-tooltip={true}
      //         placeholder={t('global:placeholder.select.template', {
      //           name: t('qualityControl.table.ruleType', '质控规则类型'),
      //         })}
      //       >
      //         {ruleTypeList.value?.map((item) => (
      //           <el-option
      //             key={item.value}
      //             label={item.label}
      //             value={item.value}
      //           />
      //         ))}
      //       </el-select>
      //     ) : (
      //       <div>{row.manageRuleTypeCodeDesc || '--'}</div>
      //     );
      //   },
      // },
      {
        label: t('qualityControl.table.ruleExecuteType', '质控方式'),
        prop: 'manageRuleExecuteWayList',
        minWidth: 100,
        render: (row: ManageRuleSetting.RuleSettingTableItem) => (
          <div>
            {row.manageRuleExecuteWayList?.length
              ? row.manageRuleExecuteWayList
                  .map(
                    (item: ManageRuleSetting.RuleExecuteWay) =>
                      item.ruleExecuteWayCodeDesc,
                  )
                  .join(' ')
              : '--'}
          </div>
        ),
      },
      {
        label: t('qualityControl.table.ruleScope', '应用范围'),
        prop: 'manageRuleScopeList',
        minWidth: 140,
        render: (row: ManageRuleSetting.RuleSettingTableItem) => (
          <div>
            {row.manageRuleScopeList?.length
              ? row.manageRuleScopeList
                  .map(
                    (item: ManageRuleSetting.RuleScope) =>
                      item.manageRuleScopeCodeDesc,
                  )
                  .join(' ')
              : '--'}
          </div>
        ),
      },
      {
        label: t('qualityControl.table.deductPoints', '最大扣减分数'),
        prop: 'deductPoints',
        minWidth: 110,
        render: (row: ManageRuleSetting.RuleSettingTableItem) => (
          <div>{row.deductPoints}</div>
        ),
      },
      {
        label: t('qualityControl.table.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 90,
        render: (row: ManageRuleSetting.RuleSettingItem) => (
          <el-switch
            v-model={row.enabledFlag}
            inline-prompt
            active-value={FLAG.YES}
            inactive-value={FLAG.NO}
            active-text={t('global:enabled')}
            inactive-text={t('global:disabled')}
            onChange={() => onEnabledFlagChange(row)}
          />
        ),
      },
      {
        label: t('qualityControl.deductRule.table.content', '扣分规则'),
        prop: 'deductRuleDesc',
        minWidth: 200,
        emptyText: '--',
      },
      // {
      //   label: t('qualityControl.deductRule.table.deductType', '扣分类型'),
      //   prop: 'deductTypeCode',
      //   minWidth: 120,
      //   render: (row: ManageRuleSetting.RuleSettingItem) => (
      //     <div>{row.deductTypeCodeDesc || '--'}</div>
      //   ),
      // },
      // {
      //   label: t('qualityControl.deductRule.table.deductPoints', '扣减分数'),
      //   prop: 'deductRulePoints',
      //   minWidth: 100,
      //   render: (row: ManageRuleSetting.RuleSettingItem) => (
      //     <div>{row.deductRulePoints || '--'}</div>
      //   ),
      // },
      {
        label: t('qualityControl.deductRule.table.assessmentDesc', '扣分说明'),
        prop: 'assessmentDesc',
        minWidth: 200,
        emptyText: '--',
      },

      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 200,
        fixed: 'right',
        render: (row: ManageRuleSetting.RuleSettingItem) => (
          <div class="flex justify-around">
            <el-button
              onClick={() => onEditRuleSettingClick(row)}
              link={true}
              type="primary"
            >
              {t('global:edit')}
            </el-button>
            <el-button
              onClick={() => deleteItem(row.manageRuleId)}
              link={true}
              type="danger"
            >
              {t('global:delete')}
            </el-button>
            <el-button
              onClick={() =>
                router.push({
                  name: 'manageDeductSetting',
                  query: {
                    manageRuleId: row.manageRuleId,
                  },
                })
              }
              link={true}
              type="primary"
            >
              {t('manageRuleSetting.table.addDeductContent', '缺陷内容')}
            </el-button>
          </div>
        ),
      },
    ],
  });
}

// 扣分规则表格
export function useDeductRuleSettingTableConfig(options: {
  ruleTypeList: Ref<SelectOptions[]>;
  onItemDeleteClick: (index: number) => void;
  onEditDeductRuleClick: (row: ManageRuleSetting.DeductRuleItem) => void;
}) {
  const { ruleTypeList, onItemDeleteClick, onEditDeductRuleClick } = options;

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('qualityControl.deductRule.table.content', '缺陷内容'),
        prop: 'deductRuleDesc',
        minWidth: 350,
        render: (row: ManageRuleSetting.DeductRuleItem) => (
          <div>{row.deductRuleDesc || '--'}</div>
        ),
      },
      {
        label: t(
          'qualityControl.deductRule.table.deductRuleType',
          '扣分规则类型',
        ),
        prop: 'deductRuleTypeCodeDesc',
        minWidth: 160,
      },
      {
        label: t('qualityControl.deductRule.table.deductType', '扣分类型'),
        prop: 'deductTypeCode',
        minWidth: 160,
        render: (row: ManageRuleSetting.DeductRuleItem) => {
          const typeItem = ruleTypeList.value?.find(
            (item) => item.value === row.deductTypeCode,
          );
          return <div>{typeItem?.label || '--'}</div>;
        },
      },
      {
        label: t('qualityControl.deductRule.table.deductPoints', '扣减分数'),
        prop: 'deductPoints',
        minWidth: 120,
        render: (row: ManageRuleSetting.DeductRuleItem) => (
          <div>{row.deductPoints}</div>
        ),
      },
      {
        label: t('qualityControl.deductRule.table.assessmentDesc', '评估说明'),
        prop: 'assessmentDesc',
        minWidth: 350,
        render: (row: ManageRuleSetting.DeductRuleItem) => (
          <div>{row.assessmentDesc || '--'}</div>
        ),
      },
      {
        label: t(
          'qualityControl.deductRule.table.singleVetoFlag',
          '单项否决项',
        ),
        prop: 'singleVetoFlag',
        minWidth: 120,
        render: (row: ManageRuleSetting.DeductRuleItem) => (
          <div>{row.singleVetoFlag === FLAG.YES ? '是' : '否'}</div>
        ),
      },
      {
        label: t('qualityControl.table.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: ManageRuleSetting.DeductRuleItem) => (
          <el-switch
            v-model={row.enabledFlag}
            inline-prompt
            active-value={FLAG.YES}
            inactive-value={FLAG.NO}
            active-text={t('global:enabled')}
            inactive-text={t('global:disabled')}
            disabled
          />
        ),
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 160,
        fixed: 'right',
        render: (row: ManageRuleSetting.DeductRuleItem, $index: number) => (
          <div class="flex justify-around">
            <el-button
              onClick={() => onEditDeductRuleClick(row)}
              link={true}
              type="primary"
            >
              {t('global:edit')}
            </el-button>
            <el-button
              onClick={() => onItemDeleteClick($index)}
              link={true}
              type="danger"
            >
              {t('global:delete')}
            </el-button>
          </div>
        ),
      },
    ],
  });
}
