import { SelectOptions } from '@/typings/common';
import { useFormConfig } from 'sun-biz';
import { Ref } from 'vue';

export function useRuleSettingFormConfig(
  options: {
    ruleWayList: Ref<SelectOptions[]>;
    objectTypeList: Ref<ManageObjectSetting.ManageObject[]>;
    ruleScopeList: Ref<SelectOptions[]>;
    queryManageObjectList: (
      params: ManageObjectSetting.SearchManageObjectParams,
    ) => Promise<void>;
  },
  queryManageRuleList: (
    data: ManageRuleSetting.SearchManageRuleSettingParams,
  ) => void,
) {
  const { ruleWayList, objectTypeList, ruleScopeList, queryManageObjectList } =
    options;
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('manageRuleSetting.search.ruleExecuteWay', '质控方式'),
        name: 'ruleExecuteWayCode',
        component: 'select',
        triggerModelChange: true,
        labelWidth: '70px',
        placeholder: t('global:placeholder.select'),
        extraProps: {
          clearable: true,
          filterable: true,
          className: 'w-24',
          options: ruleWayList.value,
        },
      },
      {
        label: t('manageRuleSetting.search.manageObject', '病历质控节点'),
        name: 'manageObjectId',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select'),
        extraProps: {
          options: objectTypeList.value,
          clearable: true,
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          remoteMethod: (query: string) =>
            queryManageObjectList({ keyWord: query }),
          props: {
            label: 'manageObjectName',
            value: 'manageObjectId',
          },
        },
      },
      {
        label: t('manageRuleSetting.search.ruleScope', '应用范围'),
        name: 'manageRuleScopeCode',
        component: 'select',
        triggerModelChange: true,
        labelWidth: '70px',
        placeholder: t('global:placeholder.select'),
        extraProps: {
          clearable: true,
          filterable: true,
          className: 'w-32',
          options: ruleScopeList.value,
        },
      },
      {
        label: t('manageRuleSetting.search.scoreScope', '分值范围'),
        name: 'pointLowerLimit',
        component: 'input-number',
        labelWidth: '68px',
        className: 'w-40',
        extraProps: {
          controlsPosition: 'right',
          min: 0,
        },
      },
      {
        label: '-',
        name: 'pointUppererLimit',
        component: 'input-number',
        labelWidth: '18px',
        className: 'w-28',
        extraProps: {
          controlsPosition: 'right',
          min: 0,
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        labelWidth: '70px',
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-24',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        triggerModelChange: true,
        extraProps: {
          style: { width: '160px' },
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryManageRuleList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryManageRuleList({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}

// 扣分规格维护表单
export function useDeductRuleSettingFormConfig(
  manageRuleList: Ref<ManageRuleSetting.RuleSettingItem[]>,
  queryManageRuleList: (
    data: ManageRuleSetting.SearchManageRuleSettingParams,
  ) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('deductRuleSetting.search.manageRule', '质控规则'),
        name: 'manageRuleId',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select'),
        extraProps: {
          options: manageRuleList.value,
          className: 'w-[300px]',
          clearable: false,
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          remoteMethod: (query: string) =>
            queryManageRuleList({ keyWord: query }),
          props: {
            label: 'manageRuleContent',
            value: 'manageRuleId',
          },
        },
      },
    ],
  });
  return data;
}
