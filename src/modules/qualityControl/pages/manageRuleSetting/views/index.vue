<script setup lang="ts" name="reasonManage">
  import { CodeSystemType } from '@/typings/codeManage';
  import { FLAG } from '@/utils/constant';
  import { queryManageRuleByExample } from '@modules/qualityControl/api/manageRuleSetting';
  import { useTranslation } from 'i18next-vue';
  import { ProForm, ProTable, useFetchDataset } from 'sun-biz';
  import { computed, ref } from 'vue';
  import { useManageObjectList } from '../../../hooks/useManageObjectList.ts';
  import AddOrEditRuleSetting from '../components/AddOrEditRuleSetting.vue';
  import { useRuleSettingFormConfig } from '../config/useFormConfig.tsx';
  import { useRuleSettingTableConfig } from '../config/useTableConfig.tsx';

  //isCloudEnv，true指云端，false其他是用户端（暂定）
  // const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();
  const searchParams = ref<ManageRuleSetting.SearchManageRuleSettingParams>({
    keyWord: '',
    enabledFlag: FLAG.ALL,
  });
  const tableRef = ref();
  const ruleSettingList = ref<ManageRuleSetting.RuleSettingTableItem[]>([]);
  const loading = ref(false);
  const addOrEditRuleSettingRef = ref();
  const dialogData = ref<{
    title: string;
    row?: Partial<ManageRuleSetting.RuleSettingItem>;
  }>({
    title: '',
    row: undefined,
  });

  // 数据转换函数：将质控规则数据转换为支持合并单元格的格式，按病历类型分组
  function transformDataForMergedTable(
    data: ManageRuleSetting.RuleSettingItem[],
  ) {
    const result: ManageRuleSetting.RuleSettingTableItem[] = [];

    // 按病历类型分组
    const groupedByObjectType = data.reduce(
      (groups, rule) => {
        // 使用病历类型ID作为主要分组键
        const objectTypeKey = `${rule.manageObjectId || 'unknown'}_${rule.manageObjectName || 'unknown'}`;
        if (!groups[objectTypeKey]) {
          groups[objectTypeKey] = [];
        }
        groups[objectTypeKey].push(rule);
        return groups;
      },
      {} as Record<string, ManageRuleSetting.RuleSettingItem[]>,
    );

    // 遍历每个病历类型分组
    Object.entries(groupedByObjectType).forEach(([, rules]) => {
      // 按质控规则ID排序，确保同一病历类型下的规则顺序稳定
      const sortedRules = rules.sort((a, b) => {
        return Number(a.manageRuleId || 0) - Number(b.manageRuleId || 0);
      });
      let objectTypeRowCount = 0;

      // 计算该病历类型的总行数
      sortedRules.forEach((rule) => {
        const deductRuleList = rule.deductRuleList || [];
        objectTypeRowCount += Math.max(1, deductRuleList.length);
      });

      let isFirstObjectTypeRow = true;

      // 处理该病历类型下的每个质控规则
      sortedRules.forEach((rule) => {
        const deductRuleList = rule.deductRuleList || [];

        if (deductRuleList.length === 0) {
          // 如果没有缺陷内容，仍然显示一行
          result.push({
            ...rule,
            deductRuleId: 0,
            deductRuleDesc: '--',
            assessmentDesc: '--',
            ruleRowSpan: 1,
            isFirstRuleRow: true,
            objectTypeRowSpan: isFirstObjectTypeRow ? objectTypeRowCount : 0,
            isFirstObjectTypeRow: isFirstObjectTypeRow,
          });
          isFirstObjectTypeRow = false;
        } else {
          // 有缺陷内容，每个缺陷内容一行
          deductRuleList.forEach((deductRule, index) => {
            result.push({
              ...rule,
              deductRuleId: deductRule.deductRuleId || 0,
              deductRuleDesc: deductRule.deductRuleDesc,
              assessmentDesc: deductRule.assessmentDesc || '--',
              ruleRowSpan: index === 0 ? deductRuleList.length : 0, // 质控规则的行合并数
              isFirstRuleRow: index === 0, // 标记是否为质控规则的第一行
              objectTypeRowSpan: isFirstObjectTypeRow ? objectTypeRowCount : 0, // 病历类型的行合并数
              isFirstObjectTypeRow: isFirstObjectTypeRow, // 标记是否为病历类型的第一行
            });

            if (index === 0) {
              isFirstObjectTypeRow = false;
            }
          });
        }
      });
    });

    return result;
  }

  // 合并单元格方法
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function spanMethod({ row, column }: { row: any; column: any }) {
    // 病历类型列需要按病历类型分组合并
    if (column.property === 'manageObjectName') {
      if (row.objectTypeRowSpan > 0) {
        return {
          rowspan: row.objectTypeRowSpan,
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }

    // 质控规则相关列需要按质控规则合并
    const ruleColumns = [
      'manageRuleDesc',
      'manageRuleContent',
      'manageRuleExecuteWayList',
      'manageRuleScopeList',
      'deductPoints',
      'enabledFlag',
      'operation',
    ];

    if (ruleColumns.includes(column.property)) {
      if (row.ruleRowSpan > 0) {
        return {
          rowspan: row.ruleRowSpan,
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }

    // 缺陷内容相关列不合并，每行独立显示
    return {
      rowspan: 1,
      colspan: 1,
    };
  }
  // 质控方式字典
  const ruleWayDataSetList = useFetchDataset([
    CodeSystemType.RULE_EXECUTE_WAY_CODE,
  ]);
  const ruleWayList = computed(() =>
    (
      ruleWayDataSetList?.value?.[CodeSystemType.RULE_EXECUTE_WAY_CODE] || []
    ).map((item) => ({
      ...item,
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  // 病历节点
  const { manageObjectList, queryManageObjectList } = useManageObjectList();
  // 应用范围字典
  const ruleScopeDataSetList = useFetchDataset([
    CodeSystemType.MANAGE_RULE_SCOPE_CODE,
  ]);
  const ruleScopeList = computed(() =>
    (
      ruleScopeDataSetList?.value?.[CodeSystemType.MANAGE_RULE_SCOPE_CODE] || []
    ).map((item) => ({
      ...item,
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  // 质控规则类型字典
  const ruleTypeDataSetList = useFetchDataset([
    CodeSystemType.MANAGE_RULE_TYPE_CODE,
  ]);
  const ruleTypeList = computed(() =>
    (
      ruleTypeDataSetList?.value?.[CodeSystemType.MANAGE_RULE_TYPE_CODE] || []
    ).map((item) => ({
      ...item,
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  async function queryData(
    data?: ManageRuleSetting.SearchManageRuleSettingParams,
  ) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryManageRuleByExample({
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    });
    loading.value = false;
    if (res?.success) {
      res.data?.sort(
        (
          a: ManageRuleSetting.RuleSettingItem,
          b: ManageRuleSetting.RuleSettingItem,
        ) => {
          return a.sort - b.sort;
        },
      );
      const data = res.data || [];
      ruleSettingList.value = transformDataForMergedTable(data);
    }
  }

  function onAddRuleSettingClick() {
    dialogData.value = {
      title: t('add.manageRule.dialog.title', '新增质控规则'),
      row: {},
    };
    addOrEditRuleSettingRef.value?.dialogRef?.open();
  }

  function onEditRuleSettingClick(row: ManageRuleSetting.RuleSettingItem) {
    dialogData.value = {
      title: t('edit.manageRule.dialog.title', '编辑质控规则'),
      row: row,
    };
    addOrEditRuleSettingRef.value?.dialogRef?.open();
  }

  queryData();
  queryManageObjectList();
  const searchConfig = useRuleSettingFormConfig(
    {
      ruleWayList,
      objectTypeList: manageObjectList,
      ruleScopeList,
      queryManageObjectList,
    },
    queryData,
  );
  const columns = useRuleSettingTableConfig({
    ruleTypeList,
    ruleScopeList,
    ruleExecuteWayList: ruleWayList,
    objectTypeList: manageObjectList,
    queryData,
    onEditRuleSettingClick,
    queryManageObjectList,
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          :show-search-button="true"
          @model-change="queryData"
        />
      </div>

      <el-button class="mr-3" type="primary" @click="onAddRuleSettingClick">
        {{ $t('global:add') }}
      </el-button>
    </div>
    <pro-table
      component-no="28"
      ref="tableRef"
      :row-key="
        (row: ManageRuleSetting.RuleSettingTableItem) =>
          `${row.manageRuleId}_${row.manageObjectId}_${row.deductRuleId || 'main'}`
      "
      :data="ruleSettingList"
      :columns="columns"
      :loading="loading"
      :span-method="spanMethod"
    />

    <AddOrEditRuleSetting
      ref="addOrEditRuleSettingRef"
      v-bind="dialogData"
      :rule-way-list="ruleWayList"
      :object-type-list="manageObjectList"
      :rule-scope-list="ruleScopeList"
      :query-manage-object-list="queryManageObjectList"
      @success="queryData"
    />
  </div>
</template>
