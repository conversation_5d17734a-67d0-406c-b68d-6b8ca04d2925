<script setup lang="ts" name="manageObject">
  import { CodeSystemType } from '@/typings/codeManage';
  import { BIZ_ID_TYPE_CODE, FLAG } from '@/utils/constant';
  import { queryManageObjectByExample } from '@modules/qualityControl/api/manageObject';
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    useAppConfigData,
    useFetchDataset,
  } from 'sun-biz';
  import { computed, ref } from 'vue';
  import { queryMedicalRecordTypeByExample } from '../../api/manageObject.ts';
  import { useManageObjectConfig } from './config/useFormConfig.tsx';
  import { useManageObjectTableConfig } from './config/useTableConfig.tsx';

  //isCloudEnv，true指云端，false其他是用户端
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const searchParams = ref<ManageObjectSetting.SearchManageObjectParams>({
    keyWord: '',
    enabledFlag: FLAG.ALL,
  });
  const tableRef = ref();
  const manageObjectList = ref<ManageObjectSetting.ManageObject[]>([]);
  const loading = ref(false);
  // 管理对象类型字典
  const objectTypeDataSetList = useFetchDataset([
    CodeSystemType.MANAGE_OBJECT_TYPE_CODE,
  ]);
  const objectTypeList = computed(() =>
    (
      objectTypeDataSetList?.value?.[CodeSystemType.MANAGE_OBJECT_TYPE_CODE] ||
      []
    ).map((item) => ({
      ...item,
      value: item?.dataValueNo,
      label: item?.dataValueCnName,
    })),
  );
  // 外部质控节点
  const medicalRecordTypeList = ref<ManageObjectSetting.MedicalRecordType[]>(
    [],
  );
  async function queryMedicalRecordType(
    params?: ManageObjectSetting.SearchManageObjectParams,
  ) {
    const [, res] = await queryMedicalRecordTypeByExample(params || {});
    if (res?.success) {
      medicalRecordTypeList.value = res.data || [];
    }
  }
  queryMedicalRecordType();
  // 选中的项目集合
  const selections = ref<ManageObjectSetting.ManageObject[]>([]);
  async function queryData(
    data?: ManageObjectSetting.SearchManageObjectParams,
  ) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const [, res] = await queryManageObjectByExample({
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    });
    loading.value = false;
    if (res?.success) {
      const data = res.data || [];
      manageObjectList.value = data.map(
        (item: ManageObjectSetting.ManageObject) => ({
          ...item,
          isEdit: false,
          form: {
            manageObjectId: item.manageObjectId,
            manageObjectName: item.manageObjectName,
            manageObjectTypeCode: item.manageObjectTypeCode,
            medicalRecordTypeList: item.medicalRecordTypeList,
            deductPoints: item.deductPoints,
            enabledFlag: item.enabledFlag,
          },
        }),
      );
    }
  }

  function onAddBtnClick() {
    manageObjectList.value.push({
      isEdit: true,
      form: {
        manageObjectName: '',
        manageObjectTypeCode: '',
        medicalRecordTypeList: [],
        enabledFlag: 1,
      },
    } as unknown as ManageObjectSetting.ManageObject);
  }

  function onItemCancelClick(
    item: ManageObjectSetting.ManageObject,
    index: number,
  ) {
    const data = { ...item };
    if (data.manageObjectId) {
      data.isEdit = false;
      data.form = {
        manageObjectId: data.manageObjectId,
        manageObjectName: data.manageObjectName,
        manageObjectTypeCode: data.manageObjectTypeCode,
        medicalRecordTypeList: data.medicalRecordTypeList,
        enabledFlag: data.enabledFlag,
        deductPoints: data.deductPoints,
      };

      manageObjectList.value.splice(index, 1, data);
    } else {
      manageObjectList.value.splice(index, 1);
    }
  }
  queryData();
  const searchConfig = useManageObjectConfig(objectTypeList, queryData);
  const columns = useManageObjectTableConfig({
    manageObjectTypeList: objectTypeList,
    medicalRecordTypeList,
    queryMedicalRecordType,
    queryData,
    onItemCancelClick,
    isCloudEnv,
  });

  function handleSelectChange(value: ManageObjectSetting.ManageObject[]) {
    selections.value = value;
  }

  const bizData = computed(() => {
    return selections.value.map((item) => {
      return item.manageObjectId || '';
    });
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          layout-mode="inline"
          :data="searchConfig"
          :show-search-button="true"
          @model-change="queryData"
        />
      </div>
      <div>
        <el-button
          class="mr-3"
          type="primary"
          @click="onAddBtnClick"
          :disabled="!isCloudEnv"
        >
          {{ $t('global:add') }}
        </el-button>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_MANAGE_OBJECT"
          @success="
            () => {
              tableRef?.proTableRef.clearSelection();
              selections = [];
            }
          "
        />
      </div>
    </div>
    <pro-table
      ref="tableRef"
      row-key="tempVariableId"
      :data="manageObjectList"
      :columns="columns"
      :loading="loading"
      @selection-change="handleSelectChange"
    />
  </div>
</template>
