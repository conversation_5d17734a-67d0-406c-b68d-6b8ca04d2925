import {
  deleteMrqaSystem,
  enabledMrqaSystem,
} from '@/modules/qualityControl/api/mrqaSystem';
import { FLAG } from '@/utils/constant';
import { ElMessage, ElMessageBox } from 'element-sun';
import { useTranslation } from 'i18next-vue';
import { useColumnConfig } from 'sun-biz';

export function useMrQaSystemTableConfig(
  editRow: (row: MrQaSystem.MrQaSystemItem) => void,
  queryData: (data?: MrQaSystem.SearchMrQaSystemParams) => void,
) {
  const { t } = useTranslation();

  async function changeEnabled(row: MrQaSystem.MrQaSystemItem) {
    const [, res] = await enabledMrqaSystem({
      mrqaSystemId: row.mrqaSystemId,
      enabledFlag: row.enabledFlag === 0 ? 1 : 0,
    });
    const isSuccess = !!res?.success;
    if (isSuccess) {
      ElMessage.success(t('global:edit.success'));
      queryData();
    }
  }

  // 启停
  async function handleSwitchEnabled(row: MrQaSystem.MrQaSystemItem) {
    return new Promise((resolve, reject) => {
      if (!row.enabledFlag) {
        ElMessageBox.confirm(
          t(
            'qualityControl.mrQaSystem.enabled.ask.title',
            `即将启用${row.mrqaSystemName}，同时将停用其余标准，是否继续？`,
          ),
          t('global:tip', '提示'),
          {
            confirmButtonText: t('global:confirm'),
            cancelButtonText: t('global:cancel'),
            type: 'warning',
          },
        )
          .then(async () => {
            changeEnabled(row);
            resolve(true);
          })
          .catch(() => {
            reject();
          });
      } else {
        changeEnabled(row);
        resolve(true);
      }
    });
  }

  // 删除
  async function handleDelete(id: string) {
    ElMessageBox.confirm(
      t('qualityControl.mrQaSystem.delete.ask.title', '您确定要删除该项标准吗'),
      t('global:tip', '提示'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        const [, result] = await deleteMrqaSystem({
          mrqaSystemId: id,
        });
        if (result?.success) {
          ElMessage({
            type: 'success',
            message: t('global:delete.success'),
          });
          queryData();
        }
      })
      .catch(() => {});
  }

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('mrQaSystem.table.mrqaSystemName', '标准名称'),
        prop: 'mrqaSystemName',
        minWidth: 120,
        render: (row: MrQaSystem.MrQaSystemItem) => {
          return (
            <div>
              <div>{row.mrqaSystemName}</div>
              {row.enabledFlag === FLAG.YES && (
                <div class="absolute right-0 top-0 rotate-12 rounded-full border border-green-500 p-[3px] text-sm text-green-500">
                  <div class="h-11 w-11 rounded-full border border-green-500 text-center leading-[2.75rem]">
                    {t('mrQaSystem.tips.inuse', '使用中')}
                  </div>
                </div>
              )}
            </div>
          );
        },
      },
      // {
      //   label: t('mrQaSystem.table.enabledFlag', '启用标志'),
      //   prop: 'enabledFlag',
      //   minWidth: 120,
      //   render: (row: MrQaSystem.MrQaSystemItem) => {
      //     return (
      //       <el-switch
      //         v-model={row.enabledFlag}
      //         inline-prompt
      //         active-value={FLAG.YES}
      //         inactive-value={FLAG.NO}
      //         active-text={t('global:enabled')}
      //         inactive-text={t('global:disabled')}
      //         beforeChange={() => {
      //           handleSwitchEnabled(row);
      //         }}
      //       />
      //     );
      //   },
      // },
      {
        label: t('mrQaSystem.table.totalScore', '病历总分'),
        prop: 'totalScore',
        minWidth: 100,
      },

      {
        label: t('mrQaSystem.table.mrqaResultCodeDesc', '病历质控结果'),
        prop: 'mrqaResultCodeDesc',
        minWidth: 250,
      },

      {
        label: t('mrQaSystem.table.lowerPoints', '区间下限（含）'),
        prop: 'lowerPoints',
        minWidth: 160,
      },
      {
        label: t('mrQaSystem.table.upperPoints', '区间上限（不含）'),
        prop: 'upperPoints',
        minWidth: 160,
      },

      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 120,
        fixed: 'right',
        render: (row: MrQaSystem.MrQaSystemItem) => {
          return (
            <div class="flex justify-around">
              <el-button
                onClick={() => editRow(row)}
                link={true}
                type="primary"
              >
                {t('global:edit')}
              </el-button>
              <el-button
                onClick={() => handleDelete(row.mrqaSystemId)}
                link={true}
                type="danger"
              >
                {t('global:delete')}
              </el-button>
              <el-button
                onClick={() => handleSwitchEnabled(row)}
                link={true}
                type="primary"
              >
                {row.enabledFlag === FLAG.YES
                  ? t('global:disabled')
                  : t('global:enabled')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
}
