import { useFormConfig } from 'sun-biz';

export function useSearchConfig(
  queryList: (data: MrQaSystem.SearchMrQaSystemParams) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        triggerModelChange: true,
        extraProps: {
          style: { width: '260px' },
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              queryList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryList({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}
