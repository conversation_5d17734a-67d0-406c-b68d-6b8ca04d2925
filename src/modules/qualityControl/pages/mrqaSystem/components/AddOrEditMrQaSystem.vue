<script lang="tsx" setup>
  import { CodeSystemType } from '@/typings/codeManage';
  import {
    addMrqaSystem,
    saveMrqaSystem,
  } from '@modules/qualityControl/api/mrqaSystem';
  import { ElMessage, type FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import {
    ProDialog,
    ProForm,
    ProTable,
    useColumnConfig,
    useFetchDataset,
    useFormConfig,
  } from 'sun-biz';
  import { computed, ref, watch } from 'vue';

  const { t } = useTranslation();
  // 评分结果
  const resultDataSetList = useFetchDataset([CodeSystemType.MRQA_RESULT_CODE]);
  const resultList = computed(() =>
    (resultDataSetList?.value?.[CodeSystemType.MRQA_RESULT_CODE] || []).map(
      (item) => ({
        ...item,
        value: item?.dataValueNo,
        label: item?.dataValueCnName,
      }),
    ),
  );
  export type Props = {
    row?: Partial<MrQaSystem.MrQaSystemItem>;
    title?: string;
  };

  const props = defineProps<Props>();
  const formRef = ref<{
    ref: FormInstance;
    model: { [key: string]: unknown };
  }>();
  const formModel = ref({
    mrqaSystemName: '',
    totalScore: 100,
    mrqaSystemDefList: [
      {
        mrqaResultCode: '',
        lowerPoints: 0,
        upperPoints: 0,
      },
    ],
  } as MrQaSystem.UpsertParams);
  const emits = defineEmits<{
    success: [];
  }>();
  const dialogRef = ref();

  watch(
    () => props.row,
    () => {
      if (props.row?.mrqaSystemId) {
        formModel.value = {
          mrqaSystemId: props.row.mrqaSystemId,
          mrqaSystemName: props.row.mrqaSystemName || '',
          totalScore: props.row.totalScore || 100,
          mrqaSystemDefList: (props.row.mrqaSystemDefList || []).map(
            (item) => ({ ...item }),
          ),
        };
      } else {
        formModel.value = {
          mrqaSystemName: '',
          totalScore: 100,
          mrqaSystemDefList: [
            {
              mrqaResultCode: '',
              lowerPoints: 0,
              upperPoints: 0,
            },
          ],
        };
      }
    },
    { immediate: true },
  );

  function validateTable() {
    const list = formModel.value.mrqaSystemDefList;
    // 是否所有区间下限都有值
    const isAllLowerPoints = list.every(
      (item: MrQaSystem.MrQaSystemDef) => item.lowerPoints,
    );
    if (!isAllLowerPoints) {
      ElMessage.warning(
        t('global:placeholder.select.template', {
          name: t('qualityControl.mrQaSystem.lowerPoints', '区间下限'),
        }),
      );
      return false;
    }
    // 是否所有区间上限都有值
    const isAllUpperPoints = list.every(
      (item: MrQaSystem.MrQaSystemDef) => item.upperPoints,
    );
    if (!isAllUpperPoints) {
      ElMessage.warning(
        t('global:placeholder.select.template', {
          name: t('qualityControl.mrQaSystem.upperPoints', '区间上限'),
        }),
      );
      return false;
    }
    // 是否所有评分结果都有值
    const isAllMrqaResult = list.every(
      (item: MrQaSystem.MrQaSystemDef) => item.mrqaResultCode,
    );
    if (!isAllMrqaResult) {
      ElMessage.warning(
        t('global:placeholder.select.template', {
          name: t('qualityControl.mrQaSystem.mrqaResult', '评分结果'),
        }),
      );
      return false;
    }

    // 检查区间是否重合
    for (let i = 0; i < list.length; i++) {
      for (let j = i + 1; j < list.length; j++) {
        const range1 = list[i];
        const range2 = list[j];
        // 检查两个区间是否有重叠
        if (
          (range1.lowerPoints < range2.upperPoints &&
            range1.upperPoints > range2.lowerPoints) ||
          (range2.lowerPoints < range1.upperPoints &&
            range2.upperPoints > range1.lowerPoints)
        ) {
          ElMessage.warning(
            t('mrQaSystem.form.rangeOverlap', '评分区间不能重合'),
          );
          return false;
        }
      }
    }

    return true;
  }

  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (result) => {
        const valid = result && validateTable();
        if (valid) {
          try {
            const formData = {
              ...formModel.value,
            };
            let result;
            if (props.row?.mrqaSystemId) {
              // 编辑
              const [, res] = await saveMrqaSystem({
                ...formData,
                mrqaSystemId: props.row.mrqaSystemId,
              } as MrQaSystem.UpsertParams);
              result = res;
            } else {
              // 新增
              const [, res] = await addMrqaSystem(
                formData as MrQaSystem.UpsertParams,
              );
              result = res;
            }

            if (result?.success) {
              ElMessage({
                type: 'success',
                message: t(
                  props.row?.mrqaSystemId
                    ? 'global:modify.success'
                    : 'global:create.success',
                ),
              });
              resolve([] as unknown as [never, unknown]);
            } else {
              reject(['', new Error('接口错误')]);
            }
          } catch (error) {
            console.error('保存失败:', error);
            reject(['', new Error('保存失败')]);
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  }

  function onAddBtnClick() {
    const list = formModel.value.mrqaSystemDefList || [];
    list.push({
      mrqaResultCode: '',
      lowerPoints: 0,
      upperPoints: 0,
    });
    formModel.value.mrqaSystemDefList = list;
  }

  function onItemDeleteClick(index: number) {
    const list = formModel.value.mrqaSystemDefList;
    formModel.value.mrqaSystemDefList.splice(index, 1);
    if (list.length === 0) {
      onAddBtnClick();
    }
  }

  /**
   * 判断两个区间是否重叠
   * @param range1 第一个区间
   * @param range2 第二个区间
   * @returns 是否重叠
   */
  const isRangesOverlap = (
    range1: { lowerPoints: number; upperPoints: number },
    range2: { lowerPoints: number; upperPoints: number },
  ): boolean => {
    return (
      (range1.lowerPoints < range2.upperPoints &&
        range1.upperPoints > range2.lowerPoints) ||
      (range2.lowerPoints < range1.upperPoints &&
        range2.upperPoints > range1.lowerPoints)
    );
  };

  /**
   * 检查区间是否重叠
   * @param row 当前修改的行
   * @param upperPoints 修改后的上限值
   * @returns 是否重叠
   */
  const checkRangeOverlap = (
    row: MrQaSystem.MrQaSystemDef,
    upperPoints: number,
  ): boolean => {
    const currentIndex = formModel.value.mrqaSystemDefList.findIndex(
      (item) => item === row,
    );
    // 遍历所有区间，找到第一个重叠的区间
    for (let i = 0; i < formModel.value.mrqaSystemDefList.length; i++) {
      if (i === currentIndex) continue;
      const item = formModel.value.mrqaSystemDefList[i];
      if (
        isRangesOverlap(
          { lowerPoints: row.lowerPoints, upperPoints },
          { lowerPoints: item.lowerPoints, upperPoints: item.upperPoints },
        )
      ) {
        // 清空当前行的上限和下限
        row.upperPoints = 0;
        row.lowerPoints = 0;
        return true;
      }
    }
    return false;
  };

  const validateAndAdjustUpperPoints = (
    row: MrQaSystem.MrQaSystemDef,
    upperPoints: number,
  ) => {
    // 确保上限不小于下限
    if (upperPoints < row.lowerPoints) {
      row.upperPoints = row.lowerPoints;
      return;
    }
    // 确保上限不大于总分+1
    if (
      formModel.value.totalScore &&
      upperPoints > formModel.value.totalScore + 1
    ) {
      ElMessage.error(
        t('mrQaSystem.form.upperPointsWarning', '区间上限必须小于等于总分+1'),
      );
      row.upperPoints = formModel.value.totalScore + 1;
      return;
    }

    // 检查区间是否重叠
    if (checkRangeOverlap(row, upperPoints)) {
      ElMessage.error(t('mrQaSystem.form.rangeOverlap', '评分区间不能重叠'));
      return;
    }
  };

  defineExpose({
    dialogRef,
  });

  const formConfig = useFormConfig({
    getData: () => [
      {
        label: t('mrQaSystem.form.mrqaSystemName', '评分标准名称'),
        name: 'mrqaSystemName',
        component: 'input',
        placeholder: t('global:placeholder.input'),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'addMrQaSystem.mrqaSystemName.placeholder',
                '评分标准名称',
              ),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          style: { width: '360px' },
        },
      },
      {
        label: t('mrQaSystem.form.mrqaSystemName', '病历总分'),
        name: 'totalScore',
        component: 'input-number',
        placeholder: t('global:placeholder.input.template', {
          content: t('addMrQaSystem.totalScore.placeholder', '病历总分'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('addMrQaSystem.totalScore.placeholder', '病历总分'),
            }),
            trigger: 'change',
          },
          {
            validator: (
              rule: unknown,
              value: number,
              callback: (error?: Error) => void,
            ) => {
              if (!value) {
                callback();
                return;
              }
              const maxUpperPoints = Math.max(
                ...formModel.value.mrqaSystemDefList.map(
                  (item) => item.upperPoints,
                ),
              );
              if (value + 1 < maxUpperPoints) {
                callback(new Error('请输入一个有效的值'));
              } else {
                callback();
              }
            },
            trigger: ['blur', 'change'],
          },
        ],
        extraProps: {
          controlsPosition: 'right',
          style: { width: '200px' },
          min: 0.1,
          precision: 1,
          autocomplete: 'off',
          title: '',
        },
      },
    ],
  });
  const columns = useColumnConfig({
    getData: () => [
      {
        label: t('mrQaSystem.table.lowerPoints', '区间下限（含）'),
        prop: 'lowerPoints',
        minWidth: 220,
        component: 'input-number',
        render: (row: MrQaSystem.MrQaSystemDef) => {
          return (
            <el-input-number
              onChange={() => {
                if (
                  row.upperPoints !== 0 &&
                  row.lowerPoints > row.upperPoints
                ) {
                  row.lowerPoints = row.upperPoints;
                }
                if (
                  formModel.value.totalScore &&
                  row.upperPoints > formModel.value.totalScore + 1
                ) {
                  ElMessage.error(
                    t(
                      'mrQaSystem.form.lowerPointsWarning',
                      '区间下限不能大于病历总分',
                    ),
                  );
                  row.lowerPoints = formModel.value.totalScore;
                }
              }}
              v-model={row.lowerPoints}
              precision={1}
              min={0}
              controls-position="right"
            />
          );
        },
      },
      {
        label: t('mrQaSystem.table.upperPoints', '区间上限（不含）'),
        prop: 'upperPoints',
        minWidth: 220,
        component: 'input-number',
        render: (row: MrQaSystem.MrQaSystemDef) => {
          return (
            <el-input-number
              onChange={(value: number) => {
                validateAndAdjustUpperPoints(row, value);
              }}
              v-model={row.upperPoints}
              precision={1}
              min={0}
              controls-position="right"
            />
          );
        },
      },
      {
        label: t('mrQaSystem.table.mrqaResult', '评分结果'),
        prop: 'mrqaResultCode',
        minWidth: 160,
        render: (row: MrQaSystem.MrQaSystemDef) => {
          return (
            <el-select
              v-model={row.mrqaResultCode}
              multiple={false}
              placeholder={t('global:placeholder.select')}
            >
              {resultList.value?.map((item) => (
                <el-option
                  key={item.value}
                  label={item.label}
                  value={item.value}
                />
              ))}
            </el-select>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 120,
        fixed: 'right',
        render: (row: MrQaSystem.MrQaSystemDef, $index: number) => {
          return (
            <div class="flex justify-around">
              {
                <>
                  <el-button
                    onClick={() => onItemDeleteClick($index)}
                    link={true}
                    type="danger"
                  >
                    {t('global:delete')}
                  </el-button>
                </>
              }
            </div>
          );
        },
      },
    ],
  });
</script>

<template>
  <ProDialog
    ref="dialogRef"
    :confirm-fn="submit"
    :title="props.title"
    :width="900"
    destroy-on-close
    @success="emits('success')"
  >
    <div class="mb-4 mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          ref="formRef"
          v-model="formModel"
          :data="formConfig"
          layout-mode="inline"
        />
      </div>
      <div>
        <el-button class="mr-3" type="primary" @click="onAddBtnClick">
          {{ $t('global:add') }}
        </el-button>
      </div>
    </div>
    <pro-table
      ref="tableRef"
      :columns="columns"
      :data="formModel.mrqaSystemDefList"
      row-key="mrqaSystemDefId"
    />
  </ProDialog>
</template>
