<script lang="ts" name="mrqaStandardManage" setup>
  import { FLAG } from '@/utils/constant';
  import { queryMrqaSystemByExample } from '@modules/qualityControl/api/mrqaSystem';
  import { useTranslation } from 'i18next-vue';
  import { ProForm, ProTable } from 'sun-biz';
  import { reactive, ref } from 'vue';
  import AddOrEditMrQaSystem from './components/AddOrEditMrQaSystem.vue';
  import { useSearchConfig } from './config/useFormConfig';
  import { useMrQaSystemTableConfig } from './config/useTableConfig';

  const { t } = useTranslation();
  // 搜索参数
  const searchParams = ref<MrQaSystem.SearchMrQaSystemParams>({
    enabledFlag: FLAG.ALL,
  });

  // 表格相关
  const tableRef = ref();
  const mrqaSystemList = ref<MrQaSystem.MrQaSystemItem[]>([]);
  const loading = ref(false);

  // 查询质控评分标准数据
  async function queryData(data?: MrQaSystem.SearchMrQaSystemParams) {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }

    const [, res] = await queryMrqaSystemByExample({
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    });
    if (res?.success) {
      mrqaSystemList.value = transformDataForMergedTable(res.data || []);
      loading.value = false;
    }
  }

  interface DialogData {
    title: string;
    row: Partial<MrQaSystem.MrQaSystemItem>;
  }

  const groupData = reactive<DialogData>({
    title: '',
    row: {},
  });
  const dialogRef = ref();

  function openDialog(data: DialogData) {
    groupData.title = data.title;
    groupData.row = data.row;
    dialogRef.value?.dialogRef?.open(data);
  }

  // 新增评分标准
  function onAddStandardClick() {
    openDialog({
      title: t('add.mrQaSystem.dialog.title', '新增质控评分标准'),
      row: {},
    });
  }

  // 编辑评分标准
  function onEditStandardClick(item: MrQaSystem.MrQaSystemItem) {
    openDialog({
      title: t('edit.mrQaSystem.dialog.title', '编辑质控评分标准'),
      row: { ...item },
    });
  }

  // 数据转换函数：按id分组
  function transformDataForMergedTable(data: MrQaSystem.MrQaSystemItem[]) {
    const result: MrQaSystem.MrQaSystemTableItem[] = [];
    data.forEach((item) => {
      const defList = item.mrqaSystemDefList;
      const count = defList.length;
      defList.forEach((defItem, index) => {
        result.push({
          ...item,
          ...defItem,
          count: index === 0 ? count : 0,
        });
      });
    });
    return result;
  }

  // 合并单元格方法
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function spanMethod({ row, column }: { row: any; column: any }) {
    if (
      column.property === 'mrqaSystemName' ||
      column.property === 'totalScore' ||
      column.property === 'operation' ||
      column.property === 'enabledFlag'
    ) {
      if (row.count > 0) {
        return {
          rowspan: row.count,
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }
  }

  const searchConfig = useSearchConfig(queryData);
  // 表格列配置
  const columns = useMrQaSystemTableConfig(onEditStandardClick, queryData);

  // 初始化查询
  queryData();
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          :data="searchConfig"
          :show-search-button="true"
          layout-mode="inline"
        />
      </div>

      <el-button class="mr-3" type="primary" @click="onAddStandardClick">
        新增
      </el-button>
    </div>
    <pro-table
      ref="tableRef"
      :columns="columns"
      :data="mrqaSystemList"
      :loading="loading"
      :span-method="spanMethod"
      row-key="mrqaSystemDefId"
    />
    <AddOrEditMrQaSystem
      ref="dialogRef"
      v-bind="groupData"
      @success="queryData"
    />
  </div>
</template>
