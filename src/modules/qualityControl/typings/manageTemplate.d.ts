declare namespace ManageTemplate {
  interface SearchManageTemplateParams {
    manageRuleScopeCode?: string;
    keyWord?: string;
    manageTempIds?: string[];
    manageObjectId?: string;
  }

  interface ManageTemplateItem {
    manageTempId: number;
    manageTempName: string;
    enabledFlag: number;
    manageObjectId: string;
    manageObjectName: string;
    manageObjectTypeCode: string;
    manageObjectTypeCodeDesc: string;
    manageTempScopeList: TempScopeScope[];
    manageTempXRuleList: ManageTempXRule[];
    // manageTempMrTempList: ManageTempMrTemp[];
  }

  interface TempScopeScope {
    manageTempScopeId?: number;
    manageRuleScopeCode: string;
    manageRuleScopeCodeDesc?: string;
  }

  interface ManageTempXRule {
    manageTempXRuleId: number;
    manageRuleId: number;
    manageRuleNo: string;
    manageRuleContent: string;
    manageObjectId: string;
    manageObjectName: string;
    manageObjectTypeCode: string;
    manageObjectTypeCodeDesc: string;
    deductPoints: number;
  }

  interface ManageTempMrTemp {
    manageTempMrTempId?: number;
    mrTempId: number;
    mrTempName: string;
  }

  interface ManageTemplateForm {
    manageTempId?: number;
    manageTempName: string;
    enabledFlag: number;
    manageRuleScopeCodes: string[];
    mrTempList?: {
      mrTempName: string;
      mrTempFid: number;
    }[];
    mrTempListId?: string[];
    manageObjectId: string;
    manageObject: {
      manageObjectId: string;
      manageObjectName?: string;
    };
  }

  interface UpsertBaseParams {
    manageTempId?: number;
    manageTempName: string;
    enabledFlag: number;
    manageTempXRuleList: ManageTempXRule[];
    manageObjectId: manageObjectId;
  }

  interface InsertManageTempParams extends UpsertBaseParams {
    manageRuleScopeCodes: string[];
    mrTempList?: {
      mrTempName: string;
      mrTempFid: number;
    }[];
  }

  interface UpdateManageTempParams extends UpsertBaseParams {
    manageTempScopeList: {
      manageTempScopeId?: number;
      manageRuleScopeCode: string;
    }[];
    manageTempMrTempList?: {
      mrTempName: string;
      mrTempFid: number;
    }[];
  }

  // 以下是管理的病历模板对应的实体类
  interface SearchMrTempParams {
    medicalRecordTypeId?: string;
    keyWord?: string;
    mrTempScopeCodes?: string[];
  }

  interface MrTempItem {
    mrTempId: string;
    mrTempName: string;
    medicalRecordTypeId: string;
    medicalRecordTypeName: string;
    mrTempContent: string;
    mrTempScopeList?: MrTempScope[];
  }

  interface MrTempScope {
    mrTempScopeId?: string;
    mrTempScopeCode?: string;
    mrTempScopeCodeDesc?: string;
  }

  // 以下是模板对应的提示词实体类
  interface SearchManageTemplatePromptParams {
    bizId: number;
    // 值默认为：DICT_MANAGE_TEMP
    bizIdTypeCode: string;
    getPromptWordFlag: number;
  }

  interface ManageTemplatePromptItem {
    promptWordId: number;
    promptWordTitle: string;
    promptWord: string;
    enabledFlag: number;
    promptXTempVarList: PromptXTempVar[];
  }

  interface PromptXTempVar {
    promptXTempVarId?: number;
    tempVariableId: number;
    tempVariableName?: string;
  }

  interface ManageTemplatePromptInsertParams {
    promptWordTitle: string;
    promptWord: string;
    enabledFlag: number;
    bizId: number;
    bizIdTypeCode: string;
    tempVariableIds: number[];
  }

  interface ManageTemplatePromptUpdateParams {
    promptWordId?: number;
    promptWordTitle: string;
    promptWord: string;
    enabledFlag: number;
    promptXTempVarList: PromptXTempVar[];
  }

  interface ManageTemplatePromptDeleteParams {
    promptWordId: number;
    bizId: number;
    bizIdTypeCode: string;
  }
}
