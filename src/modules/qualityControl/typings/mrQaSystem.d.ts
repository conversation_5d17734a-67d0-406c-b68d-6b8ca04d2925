declare namespace MrQaSystem {
  import { type FLAG } from '@/utils/constant';

  interface SearchMrQaSystemParams {
    keyWord?: string;
    enabledFlag?: FLAG;
  }

  interface MrQaSystemItem {
    mrqaSystemId: string;
    mrqaSystemName: string;
    enabledFlag: number;
    totalScore: number | null;
    mrqaSystemDefList: MrQaSystemDef[];
  }

  interface MrQaSystemTableItem extends MrQaSystemItem, MrQaSystemDef {
    count: number;
  }

  interface MrQaSystemDef {
    mrqaSystemDefId?: string;
    mrqaResultCode: string;
    mrqaResultCodeDesc?: string;
    lowerPoints: number;
    upperPoints: number;
  }

  interface UpsertParams {
    mrqaSystemId?: string;
    mrqaSystemName: string;
    totalScore: number | null;
    mrqaSystemDefList: MrQaSystemDef[];
  }
}
