declare namespace ManageObjectSetting {
  import { type FLAG } from '@/utils/constant';
  interface SearchManageObjectParams {
    manageObjectTypeCode?: string;
    keyWord?: string;
    enabledFlag?: FLAG;
    _t?: number;
  }

  interface ManageObject {
    manageObjectId: number;
    manageObjectName: string;
    manageObjectTypeCode: string;
    manageObjectTypeCodeDesc: string;
    manageRuleTypeCodeDesc: string;
    deductPoints: number;
    enabledFlag: number;
    medicalRecordTypeList: MedicalRecordType[];
    isEdit: boolean;
    form: UpsertParams;
  }

  interface UpsertParams {
    manageObjectId?: number;
    manageObjectName: string;
    manageObjectTypeCode: string;
    medicalRecordTypeList?: MedicalRecordType[];
    enabledFlag: number;
    deductPoints: number;
  }

  interface MedicalRecordType {
    medicalRecordTypeId: number;
    medicalRecordTypeName: string;
    medicalRecordTypeFid?: string;
    enabledFlag?: number;
  }
}
