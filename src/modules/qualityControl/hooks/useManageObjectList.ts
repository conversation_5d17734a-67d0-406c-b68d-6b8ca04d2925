import { queryManageObjectByExample } from '@/modules/qualityControl/api/manageObject';
import { ENABLED_FLAG } from '@sun-toolkit/enums';
import { ref } from 'vue';

// 获取规则管理对象列表
export function useManageObjectList() {
  const loading = ref(false);
  const manageObjectList = ref<ManageObjectSetting.ManageObject[]>([]);

  const queryManageObjectList = async (
    params?: ManageObjectSetting.SearchManageObjectParams,
  ) => {
    loading.value = true;
    const [, res] = await queryManageObjectByExample({
      keyWord: '',
      manageObjectTypeCode: '1',
      enabledFlag: ENABLED_FLAG.YES,
      ...params,
    });
    loading.value = false;
    if (res?.success) {
      manageObjectList.value = res.data ?? [];
    }
  };
  return {
    loading,
    manageObjectList,
    queryManageObjectList,
  };
}
