/** [1-10503-1] 根据条件查询生命体征字典 入参 */
export type VitalSignReqParams = {
  enabledFlag?: number;
  vitalSignIds?: string[];
};

/** [1-10503-1] 根据条件查询生命体征字典 出参 */
export type VitalSignResItem = {
  vitalSignId?: string;
  vitalSignName: string;
  unitId: string;
  unitName: string;
  yStepSize: number;
  yStart: number;
  enabledFlag: number;
};

/** [1-10504-1] 新增生命体征 入参 */
export type AddVitalSignReqParams = {
  vitalSignName: string;
  unitId: string;
  yStepSize: number;
  yStart: number;
  enabledFlag: number;
};

/** [1-10504-1] 新增生命体征 出参 */
export type AddVitalSignResItem = {
  vitalSignId: string;
};

/** [1-10505-1] 编辑生命体征 入参 */
export type EditVitalSignReqParams = {
  vitalSignId: string;
  vitalSignName: string;
  unitId: string;
  yStepSize: number;
  enabledFlag: number;
  yStart: number;
};

// /** [1-10505-1] 编辑生命体征 出参 */
// export type EditVitalSignResItem = {
//   enabledFlag?: number;
//   vitalSignIds?: string;
// };

/** [1-10506-1] 删除生命体征 入参 */
export type DeleteVitalSignReqParams = {
  vitalSignId: string;
};

/** [1-10506-1] 删除生命体征 出参 */
// export type DeleteVitalSignResItem = {
//   enabledFlag?: number;
//   vitalSignIds?: string;
// };
