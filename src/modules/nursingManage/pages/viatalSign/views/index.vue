<script setup lang="ts" name="vitalSign">
  import { Ref, ref, computed, onMounted } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { FLAG } from '@sun-toolkit/enums';
  import { useTranslation } from 'i18next-vue';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant';
  import { useTableConfig } from '../config/useTableConfig';
  import { useSearchFormConfig } from '../config/useSearchFormConfig';
  import {
    queryVitalSignByExample,
    addVitalSign,
    editVitalSign,
    deleteVitalSign,
  } from '@/modules/nursingManage/api/vitalSign';
  import { useGetUnitList } from '../hooks/useGetUnitList';
  import { VitalSignResItem } from '@/modules/nursingManage/typings/vitalSign';
  import {
    Title,
    ProForm,
    ProTable,
    DmlButton,
    useEditableTable,
    useAppConfigData,
    MAIN_APP_CONFIG,
  } from 'sun-biz';

  type FormModelType = {
    enabledFlag?: number;
  };

  const { t } = useTranslation();

  const { loading: unitLoading, unitList, getUnitList } = useGetUnitList();
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);

  const formRef = ref();
  const tableRef = ref();

  const loading = ref<boolean>(false);
  const tableData = ref<VitalSignResItem[]>([]);
  const formModel = ref<FormModelType>({
    enabledFlag: FLAG.ALL,
  });
  const selections = ref<VitalSignResItem[]>([]);

  const { cancelEdit, toggleEdit, addItem } = useEditableTable({
    tableRef,
    data: tableData as unknown as Ref<
      (VitalSignResItem & { editable: boolean })[]
    >,
    id: 'vitalSignId',
  });

  const bizData = computed(() => {
    const list = selections.value
      .filter((item) => item.vitalSignId) // 过滤掉 vitalSignId 为 null 或 undefined 的项
      .map((item) => item.vitalSignId);
    return list ?? [];
  });

  /** 初始化 */
  const initData = async () => {
    await fetchData();
    await getUnitList();
  };

  /** modelChange */
  const modelChange = async (data: FormModelType) => {
    formModel.value = {
      ...formModel.value,
      ...data,
    };

    await fetchData();
  };

  /** 新增 */
  const handleAdd = async () => {
    addItem({
      editable: true,
      enabledFlag: FLAG.YES,
      yStart: 0,
    } as VitalSignResItem & { editable: boolean });
  };

  /** 获取数据 */
  const fetchData = async () => {
    const params = {
      enabledFlag:
        formModel.value.enabledFlag === FLAG.ALL
          ? undefined
          : formModel.value.enabledFlag,
    };
    loading.value = true;

    const [, res] = await queryVitalSignByExample(params);

    loading.value = false;
    if (res?.success) {
      tableData.value = res?.data ?? [];
    }
  };

  /** dml导出 */
  const dmlExportFn = async () => {
    tableRef.value?.proTableRef.clearSelection();
    selections.value = [];
    // ElMessage.success(t('export.success', '导出成功'));
  };

  /** 保存 */
  const handleSave = async (
    row: VitalSignResItem & {
      editable: boolean;
    },
    index: number,
  ) => {
    /** 校验 */
    const isValid = await tableRef?.value?.validateRow(index);

    if (!isValid) return;
    const params = {
      vitalSignId: row?.vitalSignId as string,
      vitalSignName: row?.vitalSignName as string,
      unitId: row?.unitId as string,
      yStepSize: row?.yStepSize as number,
      yStart: row?.yStart as number,
      enabledFlag: row?.enabledFlag as number,
    };
    let result;
    if (row?.vitalSignId) {
      result = await editVitalSign(params);
    } else {
      result = await addVitalSign(params);
    }
    const [, res] = result;
    if (res?.success) {
      row.editable = false;
      ElMessage.success(
        row?.vitalSignId ? t('global:edit.success') : t('global:add.success'),
      );
      await fetchData();
    }
  };

  /** 移除 */
  const handleDelete = async (row: VitalSignResItem) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要{{action}} “{{name}}” 吗？', {
        action: t('global:delete'),
        name: row.vitalSignName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await deleteVitalSign({
        vitalSignId: row?.vitalSignId as string,
      });
      if (res?.success) {
        ElMessage.success(t('global:delete.success'));
        await fetchData();
      }
    });
  };

  /** 停启用 */
  const handleEnableSwitch = async (
    row: VitalSignResItem & {
      editable: boolean;
    },
  ) => {
    if (row?.editable === true) {
      row.enabledFlag = row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES;
      return;
    }

    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要{{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.vitalSignName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        vitalSignId: row?.vitalSignId as string,
        vitalSignName: row?.vitalSignName as string,
        unitId: row?.unitId as string,
        yStepSize: row?.yStepSize as number,
        yStart: row?.yStart as number,
        enabledFlag: row?.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES,
      };
      const [, res] = await editVitalSign(params);
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        await fetchData();
      }
    });
  };

  const handleSelectChange = (value: VitalSignResItem[]) => {
    selections.value = value;
  };

  const { data: formConfig } = useSearchFormConfig();
  const { data: tableConfig } = useTableConfig({
    isCloudEnv: isCloudEnv as boolean,
    unitLoading,
    unitList,
    getUnitList,
    cancelEdit,
    toggleEdit,
    handleSave,
    handleDelete,
    handleEnableSwitch,
  });

  onMounted(() => {
    initData();
  });
</script>

<template>
  <div class="p-box flex size-full flex-col">
    <Title :title="$t('vitalSign.title', '生命体征列表')" class="mb-3" />

    <div class="mb-2.5">
      <ProForm
        ref="formRef"
        class="ml-4 flex flex-1 flex-wrap"
        v-model="formModel"
        :data="formConfig"
        layout-mode="inline"
        @model-change="modelChange"
      >
        <div class="flex flex-1 justify-end gap-2.5">
          <el-button type="primary" @click="fetchData">
            {{ $t('global:query') }}
          </el-button>
          <el-button
            type="primary"
            @click="handleAdd"
            class="!ml-0"
            v-if="isCloudEnv"
          >
            {{ $t('global:add') }}
          </el-button>
          <DmlButton
            :biz-data="bizData"
            :code="BIZ_ID_TYPE_CODE.DICT_VITAL_SIGN"
            @success="dmlExportFn"
          />
        </div>
      </ProForm>
    </div>

    <ProTable
      ref="tableRef"
      :data="tableData"
      :columns="tableConfig"
      :loading="loading"
      :editable="true"
      row-key="vitalSignId"
      @selection-change="handleSelectChange"
    />
  </div>
</template>
