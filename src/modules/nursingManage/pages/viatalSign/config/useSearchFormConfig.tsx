import { useFormConfig } from 'sun-biz';

export function useSearchFormConfig() {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        className: 'mb-0',
        extraProps: {
          clearable: false,
          className: 'w-32',
        },
      },
    ],
  });

  return { data };
}
