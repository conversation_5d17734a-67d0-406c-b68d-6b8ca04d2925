import { useColumnConfig, formatDecimalNumber } from 'sun-biz';
import { Ref } from 'vue';
import { UnitResItem } from '@/api/types';
import { FLAG } from '@/utils/constant';
import { VitalSignResItem } from '@/modules/nursingManage/typings/vitalSign';

export function useTableConfig(options: {
  isCloudEnv: boolean;
  unitLoading: Ref<boolean>;
  unitList: Ref<UnitResItem[]>;
  getUnitList: (keyWord?: string) => Promise<void>;
  cancelEdit: (
    row: VitalSignResItem & {
      editable: boolean;
    },
    index: number,
  ) => void;
  toggleEdit: (
    row: VitalSignResItem & {
      editable: boolean;
    },
  ) => Promise<void>;
  handleSave: (
    row: VitalSignResItem & {
      editable: boolean;
    },
    index: number,
  ) => Promise<void>;
  handleDelete: (row: VitalSignResItem) => Promise<void>;
  handleEnableSwitch: (
    row: VitalSignResItem & {
      editable: boolean;
    },
  ) => Promise<void>;
}) {
  const {
    isCloudEnv,
    unitLoading,
    unitList,
    getUnitList,
    cancelEdit,
    toggleEdit,
    handleSave,
    handleDelete,
    handleEnableSwitch,
  } = options;

  const data = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t('vitalSign.vitalSignId', '生命体征标识'),
        prop: 'vitalSignId',
        minWidth: 150,
      },
      {
        label: t('vitalSign.vitalSignName', '生命体征名称'),
        prop: 'vitalSignName',
        minWidth: 150,
        editable: true,
        require: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('vitalSign.vitalSignName', '生命体征名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: VitalSignResItem & {
            editable: boolean;
          },
        ) => {
          if (row.editable && isCloudEnv) {
            return (
              <el-input
                v-model={row.vitalSignName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('vitalSign.vitalSignName', '生命体征名称'),
                })}
              />
            );
          } else {
            return (
              <div class="w-full truncate text-center">{row.vitalSignName}</div>
            );
          }
        },
      },
      {
        label: t('vitalSign.unitId', '单位'),
        prop: 'unitId',
        minWidth: 150,
        editable: true,
        require: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('vitalSign.unitId', '单位'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: VitalSignResItem & {
            editable: boolean;
          },
        ) => {
          if (row.editable && isCloudEnv) {
            return (
              <div class={'w-full'}>
                <el-select
                  class="mx-auto w-3/5"
                  remote={true}
                  filterable={true}
                  v-model={row.unitId}
                  remote-show-suffix={true}
                  loading={unitLoading.value}
                  remote-method={async (keyWord: string) => {
                    await getUnitList(keyWord);
                  }}
                  placeholder={t('global:placeholder.select.template', {
                    name: t('vitalSign.unitId', '单位'),
                  })}
                  onChange={(val: string) => {
                    const findObj = unitList.value.find(
                      (item) => item.unitId === val,
                    );
                    row.unitName = findObj?.unitName || '';
                  }}
                >
                  {(unitList.value ?? []).map((item) => {
                    return (
                      <el-option
                        key={item.unitId}
                        label={item.unitName}
                        value={item.unitId}
                      ></el-option>
                    );
                  })}
                </el-select>
              </div>
            );
          } else {
            return (
              <div class="w-full truncate text-center">{row.unitName}</div>
            );
          }
        },
      },
      {
        label: t('vitalSign.yStepSize', 'Y轴步长'),
        prop: 'yStepSize',
        minWidth: 150,
        editable: true,
        require: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('vitalSign.yStepSize', 'Y轴步长'),
            }),
            trigger: ['change', 'blur'],
          },
          {
            validator: (
              rule: unknown,
              value: number,
              callback: (error?: Error) => void,
            ) => {
              if (value < 0) {
                callback(
                  new Error(
                    t('vitalSign.yStepSize.positive', 'Y轴步长不小于0'),
                  ),
                );
              } else {
                callback();
              }
            },
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: VitalSignResItem & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <div class={'w-full'}>
                <el-input-number
                  class={'mx-auto w-3/5'}
                  v-model={row.yStepSize}
                  controls-position="right"
                  placeholder={t('global:placeholder.input.template', {
                    content: t('vitalSign.yStepSize', 'Y轴步长'),
                  })}
                />
              </div>
            );
          } else {
            return (
              <div class="w-full truncate text-center">
                {row.yStepSize || row.yStepSize === 0
                  ? formatDecimalNumber(row.yStepSize)
                  : '--'}
              </div>
            );
          }
        },
      },
      {
        label: t('vitalSign.yStart', 'Y轴起点'),
        prop: 'yStart',
        minWidth: 150,
        editable: true,
        require: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('vitalSign.yStart', 'Y轴起点'),
            }),
            trigger: ['change', 'blur'],
          },
          {
            validator: (
              rule: unknown,
              value: number,
              callback: (error?: Error) => void,
            ) => {
              if (value < 0) {
                callback(
                  new Error(t('vitalSign.yStart.positive', 'Y轴起点不小于0')),
                );
              } else {
                callback();
              }
            },
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: VitalSignResItem & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <div class={'w-full'}>
                <el-input-number
                  class={'mx-auto w-3/5'}
                  v-model={row.yStart}
                  controls-position="right"
                  placeholder={t('global:placeholder.input.template', {
                    content: t('vitalSign.yStart', 'Y轴起点'),
                  })}
                />
              </div>
            );
          } else {
            return (
              <div class="w-full truncate text-center">
                {row.yStart || row.yStart === 0
                  ? formatDecimalNumber(row.yStart)
                  : '--'}
              </div>
            );
          }
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        render: (
          row: VitalSignResItem & {
            editable: boolean;
          },
        ) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              disabled={!isCloudEnv}
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              onChange={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 150,
        render: (
          row: VitalSignResItem & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return row.editable ? (
            <div class="flex items-center justify-around" key="operation">
              <el-button
                key="cancel"
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                type="primary"
                key="save"
                link={true}
                onClick={() => handleSave(row, $index)}
              >
                {t('global:save')}
              </el-button>
            </div>
          ) : (
            <div class="flex items-center justify-around" key="operationEdit">
              <el-button
                key="edit"
                link={true}
                type={'primary'}
                onClick={() => toggleEdit(row)}
              >
                {t('global:edit')}
              </el-button>
              {isCloudEnv && (
                <el-button
                  key="delete"
                  link={true}
                  type={'danger'}
                  onClick={() => handleDelete(row)}
                >
                  {t('global:delete')}
                </el-button>
              )}
            </div>
          );
        },
      },
    ],
  });
  return { data };
}
