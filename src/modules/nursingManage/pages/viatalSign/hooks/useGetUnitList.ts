import { ref } from 'vue';
import { FIRST_PAGE_NUMBER, ONE_PAGE_SIZE } from '@sun-toolkit/enums';
import { queryUnitListByExample } from '@/api/common';
import { UnitResItem } from '@/api/types';

export function useGetUnitList() {
  const loading = ref<boolean>(false);
  const unitList = ref<UnitResItem[]>([]);

  const getUnitList = async (keyWord?: string) => {
    const defaultParams = {
      pageNumber: FIRST_PAGE_NUMBER,
      pageSize: ONE_PAGE_SIZE,
    };

    loading.value = true;
    const [, res] = await queryUnitListByExample({
      ...defaultParams,
      keyWord: keyWord,
    });
    loading.value = false;
    if (res?.success) {
      unitList.value = res?.data ?? [];
    }
  };

  return {
    loading,
    unitList,
    getUnitList,
  };
}
