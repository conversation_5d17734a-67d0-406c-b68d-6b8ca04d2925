import { dictRequest } from '@sun-toolkit/request';
import {
  VitalSignReqParams,
  VitalSignResItem,
  AddVitalSignReqParams,
  AddVitalSignResItem,
  EditVitalSignReqParams,
  // EditVitalSignResItem,
  DeleteVitalSignReqParams,
  // DeleteVitalSignResItem,
} from '../typings/vitalSign';

/**
 * [1-10503-1] 根据条件查询生命体征字典
 * @param
 * @returns
 */
export const queryVitalSignByExample = (params: VitalSignReqParams) => {
  return dictRequest<VitalSignResItem[]>(
    '/VitalSign/queryVitalSignByExample',
    params,
  );
};

/**
 * [1-10504-1] 新增生命体征
 * @param
 * @returns
 */
export const addVitalSign = (params: AddVitalSignReqParams) => {
  return dictRequest<AddVitalSignResItem>('/VitalSign/addVitalSign', params);
};

/**
 * [1-10505-1] 编辑生命体征
 * @param
 * @returns
 */
export const editVitalSign = (params: EditVitalSignReqParams) => {
  return dictRequest('/VitalSign/editVitalSign', params);
};

/**
 * [1-10506-1] 删除生命体征
 * @param
 * @returns
 */
export const deleteVitalSign = (params: DeleteVitalSignReqParams) => {
  return dictRequest('/VitalSign/deleteVitalSign', params);
};
