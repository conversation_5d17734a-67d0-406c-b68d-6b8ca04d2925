import { useColumnConfig } from 'sun-biz';
import { FLAG } from '@/utils/constant';
import { AdminRouteResItem } from '@/modules/cisOutp/typing/adminRouteFreq.ts';

export function useTableConfig(options: {
  isCloudEnv: boolean;
  handleEnableSwitch: (row: AdminRouteResItem) => Promise<void>;
  handleEdit: (row: AdminRouteResItem) => Promise<void>;
  handleDelete: (row: AdminRouteResItem) => Promise<void>;
}) {
  const {
    // isCloudEnv,
    handleEnableSwitch,
    handleEdit,
    handleDelete,
  } = options;

  const data = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t('global:indexNo'),
        prop: 'indexNumber',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('global:name'),
        prop: 'adminRouteName',
        minWidth: 100,
      },
      {
        label: t('cisOut.adminRoute.adminRouteDisplayName', '显示名称'),
        prop: 'adminRouteDisplayName',
        minWidth: 100,
      },
      {
        label: t('global:secondName'),
        prop: 'adminRoute2ndName',
        minWidth: 100,
      },
      {
        label: t('global:thirdName'),
        prop: 'adminRouteExtName',
        minWidth: 100,
      },
      {
        label: t('cisOut.adminRoute.adminRouteCategoryCode', '用法分类'),
        prop: 'adminRouteCategoryCodeDesc',
        minWidth: 100,
      },
      {
        label: t('cisOut.adminRoute.adminRouteBizScenarioCode', '业务场景'),
        prop: 'adminRouteBizScenarioList',
        minWidth: 150,
        render: (row: AdminRouteResItem) => {
          return (
            <div class={'w-full truncate text-center'}>
              {row?.adminRouteBizScenarioList
                ?.map((item) => item.adminRouteBizScenarioCodeDesc)
                .join('，')}
            </div>
          );
        },
      },
      {
        label: t('cisOut.adminRoute.encounterTypeCode', '适用范围'),
        prop: 'adminRouteEncTypeList',
        minWidth: 150,
        render: (row: AdminRouteResItem) => {
          return (
            <div class={'w-full truncate text-center'}>
              {row?.adminRouteEncTypeList
                ?.map((item) => item.encounterTypeCodeDesc)
                .join('，')}
            </div>
          );
        },
      },
      {
        label: t('global:spellNo'),
        prop: 'spellNo',
        minWidth: 100,
      },
      {
        label: t('global:wbNo'),
        prop: 'wbNo',
        minWidth: 100,
      },
      {
        label: t('cisOut.adminRoute.allowEdit', '允许编辑'),
        prop: 'editableFlag',
        minWidth: 150,
        render: (row: AdminRouteResItem) => {
          return (
            <el-checkbox modelValue={!!row.editableFlag} disabled={true} />
          );
        },
      },
      {
        label: t('global:status'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: AdminRouteResItem) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              // disabled={!isCloudEnv}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 150,
        render: (row: AdminRouteResItem) => {
          return (
            <div class="flex justify-around">
              <el-button
                key="edit"
                type={'primary'}
                link={true}
                onClick={() => handleEdit(row)}
              >
                {t('global:edit')}
              </el-button>
              <el-button
                key="edit"
                type={'danger'}
                link={true}
                onClick={() => handleDelete(row)}
              >
                {t('global:delete')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return data;
}
