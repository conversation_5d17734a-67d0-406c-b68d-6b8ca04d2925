import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import {
  FLAG,
  ENCOUNTER_TYPE_CODE_NAME,
  ADMIN_ROUTE_CATEGORY_CODE_NAME,
  ADMIN_ROUTE_BIZ_SCENARIO_CODE_NAME,
} from '@/utils/constant';
import { AdminRouteResItem } from '@/modules/cisOutp/typing/adminRouteFreq.ts';

export function useDetailFormConfig(options: {
  isCloudEnv: boolean;
  isAdd: Ref<boolean>;
  currentRow: Ref<AdminRouteResItem | undefined>;
  handleNameBlur: (value: string) => void;
}) {
  const { isCloudEnv, isAdd, currentRow, handleNameBlur } = options;

  const data = useFormConfig({
    dataSetCodes: [
      ENCOUNTER_TYPE_CODE_NAME,
      ADMIN_ROUTE_CATEGORY_CODE_NAME,
      ADMIN_ROUTE_BIZ_SCENARIO_CODE_NAME,
    ],
    getData: (t, dataSet) => [
      {
        label: t('global:name'),
        name: 'adminRouteName',
        component: 'input',
        autoConvertSpellNoAndWbNo: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('global:name'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:name'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          clearable: true,
          disabled: !isCloudEnv && !isAdd.value,
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              handleNameBlur((e.target as HTMLInputElement)?.value);
            }
          },
          onBlur: (e: Event) => {
            handleNameBlur((e.target as HTMLInputElement)?.value);
          },
        },
      },
      {
        label: t('displayName', '显示名称'),
        name: 'adminRouteDisplayName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('displayName', '显示名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('displayName', '显示名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          clearable: true,
          disabled:
            !isCloudEnv &&
            !isAdd.value &&
            currentRow.value?.editableFlag === FLAG.NO,
        },
      },
      {
        label: t('global:secondName'),
        name: 'adminRoute2ndName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:secondName'),
        }),
        extraProps: {
          clearable: true,
          disabled:
            !isCloudEnv &&
            !isAdd.value &&
            currentRow.value?.editableFlag === FLAG.NO,
        },
      },
      {
        label: t('global:thirdName'),
        name: 'adminRouteExtName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:thirdName'),
        }),
        extraProps: {
          clearable: true,
          disabled:
            !isCloudEnv &&
            !isAdd.value &&
            currentRow.value?.editableFlag === FLAG.NO,
        },
      },
      {
        label: t('global:spellNo'),
        name: 'spellNo',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:spellNo'),
        }),
        extraProps: {
          clearable: true,
          disabled:
            !isCloudEnv &&
            !isAdd.value &&
            currentRow.value?.editableFlag === FLAG.NO,
        },
      },
      {
        label: t('global:wbNo'),
        name: 'wbNo',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:wbNo'),
        }),
        extraProps: {
          clearable: true,
          disabled:
            !isCloudEnv &&
            !isAdd.value &&
            currentRow.value?.editableFlag === FLAG.NO,
        },
      },
      {
        label: t('adminRouteCategoryCode', '分类'),
        name: 'adminRouteCategoryCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('adminRouteCategoryCode', '分类'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('adminRouteCategoryCode', '分类'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          clearable: true,
          filterable: true,
          disabled: !isCloudEnv && !isAdd.value,
          options: dataSet?.value
            ? dataSet.value[ADMIN_ROUTE_CATEGORY_CODE_NAME]
            : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
      {
        label: t('adminRouteEncTypeList', '适用范围'),
        name: 'adminRouteEncTypeList',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('adminRouteEncTypeList', '适用范围'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('adminRouteEncTypeList', '适用范围'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          clearable: true,
          multiple: true,
          filterable: true,
          collapseTags: true,
          collapseTagsTooltip: true,
          maxCollapseTags: 2,
          disabled:
            !isCloudEnv &&
            !isAdd.value &&
            currentRow.value?.editableFlag === FLAG.NO,
          options: dataSet?.value
            ? dataSet.value[ENCOUNTER_TYPE_CODE_NAME]
            : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
      {
        label: t('adminRouteBizScenarioList', '业务场景'),
        name: 'adminRouteBizScenarioList',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('adminRouteBizScenarioList', '业务场景'),
        }),
        extraProps: {
          clearable: true,
          multiple: true,
          filterable: true,
          collapseTags: true,
          collapseTagsTooltip: true,
          maxCollapseTags: 2,
          disabled:
            !isCloudEnv &&
            !isAdd.value &&
            currentRow.value?.editableFlag === FLAG.NO,
          options: dataSet?.value
            ? dataSet.value[ADMIN_ROUTE_BIZ_SCENARIO_CODE_NAME]
            : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          // disabled: !isCloudEnv && !isAdd.value,
        },
      },
      {
        name: 'editableFlag',
        label: t('allowEdit', '允许编辑'),
        component: 'checkbox',
        isHidden: !isCloudEnv,
        extraProps: {
          disabled: !isCloudEnv && !isAdd.value,
          'true-value': FLAG.YES,
          'false-value': FLAG.NO,
        },
      },
    ],
  });
  return data;
}
