<script setup lang="ts" name="AddAndEditDialog">
  import { ElMessage } from 'element-sun';
  import { ref, computed, nextTick } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { FLAG } from '@/utils/constant';
  import { saveAdminRoute } from '@/modules/cisOutp/api/adminRouteFreq';
  import { AdminRouteResItem } from '@/modules/cisOutp/typing/adminRouteFreq.ts';
  import { useDetailFormConfig } from '../config/useDialogFormConfig';
  import {
    ProForm,
    ProDialog,
    useAppConfigData,
    MAIN_APP_CONFIG,
  } from 'sun-biz';

  const emits = defineEmits(['success']);
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();

  /** formModel类型 */
  export type FromModelType = {
    adminRouteName?: string;
    adminRouteDisplayName?: string;
    adminRoute2ndName?: string;
    adminRouteExtName?: string;
    wbNo?: string;
    spellNo?: string;
    adminRouteCategoryCode?: string;
    adminRouteEncTypeList?: string[];
    adminRouteBizScenarioList?: string[];
    enabledFlag?: number;
    editableFlag?: number;
  };

  const dialogRef = ref();
  const formRef = ref();
  const currentRow = ref<AdminRouteResItem>();
  const formModel = ref<FromModelType>({
    adminRouteName: undefined,
    adminRouteDisplayName: undefined,
    adminRoute2ndName: undefined,
    adminRouteExtName: undefined,
    wbNo: undefined,
    spellNo: undefined,
    adminRouteCategoryCode: undefined,
    adminRouteEncTypeList: undefined,
    adminRouteBizScenarioList: undefined,
    enabledFlag: FLAG.YES,
    editableFlag: FLAG.YES,
  });

  /** 是否新增 */
  const isAdd = computed(() => {
    if (currentRow.value) return false;
    return true;
  });

  /** 弹窗title */
  const dialogTitle = computed(() => {
    let title;
    if (currentRow.value?.adminRouteId) {
      title =
        t('edit.adminRoute', '编辑给药途径') +
        ' ' +
        (currentRow.value?.adminRouteName ?? '');
    } else {
      title = t('add.adminRoute', '新增给药途径');
    }
    return title;
  });

  /** 初始化数据 */
  const initData = async () => {
    formModel.value = {
      adminRouteName: currentRow.value?.adminRouteName ?? undefined,
      adminRouteDisplayName:
        currentRow.value?.adminRouteDisplayName ?? undefined,
      adminRoute2ndName: currentRow.value?.adminRoute2ndName ?? undefined,
      adminRouteExtName: currentRow.value?.adminRouteExtName ?? undefined,
      wbNo: currentRow.value?.wbNo ?? undefined,
      spellNo: currentRow.value?.spellNo ?? undefined,
      editableFlag: currentRow.value?.editableFlag ?? FLAG.YES,
      enabledFlag: currentRow.value?.enabledFlag ?? FLAG.YES,
      adminRouteCategoryCode:
        currentRow.value?.adminRouteCategoryCode ?? undefined,
      adminRouteEncTypeList:
        (currentRow.value?.adminRouteEncTypeList ?? []).map(
          (item) => item.encounterTypeCode,
        ) ?? undefined,
      adminRouteBizScenarioList:
        (currentRow.value?.adminRouteBizScenarioList ?? []).map(
          (item) => item.adminRouteBizScenarioCode,
        ) ?? undefined,
    };
  };

  /** 打开弹窗 */
  const openDialog = async (row: AdminRouteResItem) => {
    currentRow.value = row;
    dialogRef.value?.open();
    nextTick(async () => {
      await initData();
    });
  };

  /** 关闭弹窗 */
  const handleClose = async () => {
    formRef.value.ref?.resetFields();
    formRef.value?.ref.clearValidate();
  };

  /** 输入框失去焦点 */
  const handleNameBlur = (value: string) => {
    if (value && !formModel.value.adminRouteDisplayName) {
      formModel.value = {
        ...formModel.value,
        adminRouteDisplayName: value,
      };
    }
  };

  /** 保存 */
  const handleSubmit = async () => {
    const isValid = await formRef.value?.ref?.validate();
    if (!isValid) return;

    const params = {
      adminRouteId: isAdd.value ? undefined : currentRow.value?.adminRouteId,
      adminRouteName: formModel.value?.adminRouteName as string,
      adminRouteDisplayName: formModel.value?.adminRouteDisplayName as string,
      adminRoute2ndName: formModel.value?.adminRoute2ndName,
      adminRouteExtName: formModel.value?.adminRouteExtName,
      adminRouteCategoryCode: formModel.value?.adminRouteCategoryCode as string,
      wbNo: formModel.value?.wbNo,
      spellNo: formModel.value?.spellNo,
      enabledFlag: formModel.value?.enabledFlag as number,
      editableFlag: formModel.value?.editableFlag as number,
      sort: isAdd.value ? undefined : currentRow.value?.sort,

      adminRouteEncTypeList: formModel.value?.adminRouteEncTypeList?.map(
        (encounterTypeCode: string) => {
          const existingItem = currentRow.value?.adminRouteEncTypeList?.find(
            (item) => item.encounterTypeCode === encounterTypeCode,
          );
          return existingItem
            ? {
                encounterTypeCode: encounterTypeCode,
                adminRouteEncTypeId: existingItem.adminRouteEncTypeId,
              }
            : { encounterTypeCode: encounterTypeCode };
        },
      ) as {
        encounterTypeCode: string;
      }[],
      adminRouteBizScenarioList:
        formModel.value?.adminRouteBizScenarioList?.map(
          (adminRouteBizScenarioCode: string) => {
            const existingItem =
              currentRow.value?.adminRouteBizScenarioList?.find(
                (item) =>
                  item.adminRouteBizScenarioCode === adminRouteBizScenarioCode,
              );
            return existingItem
              ? {
                  adminRouteBizScenarioCode: adminRouteBizScenarioCode,
                  adminRouteBizScenarioId: existingItem.adminRouteBizScenarioId,
                }
              : { adminRouteBizScenarioCode: adminRouteBizScenarioCode };
          },
        ) as {
          adminRouteBizScenarioCode: string;
        }[],
    };

    return await saveAdminRoute(params);
  };

  defineExpose({
    open: openDialog,
  });

  const formConfig = useDetailFormConfig({
    isCloudEnv: isCloudEnv as boolean,
    isAdd,
    currentRow,
    handleNameBlur,
  });
</script>

<template>
  <ProDialog
    ref="dialogRef"
    :title="dialogTitle"
    :confirm-fn="handleSubmit"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @success="
      () => {
        ElMessage.success(
          isAdd ? t('global:add.success') : t('global:modify.success'),
        );
        handleClose();
        emits('success');
      }
    "
    @close="handleClose"
    @cancel="handleClose"
  >
    <ProForm ref="formRef" v-model="formModel" :data="formConfig" :column="2" />
  </ProDialog>
</template>
