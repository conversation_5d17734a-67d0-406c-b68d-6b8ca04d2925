<script setup lang="ts" name="adminRoute">
  import { ref, computed, onMounted } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { commonSort } from '@/api/common.ts';
  import { FLAG, BIZ_ID_TYPE_CODE } from '@/utils/constant';
  import { AdminRouteResItem } from '@/modules/cisOutp/typing/adminRouteFreq.ts';
  import {
    saveAdminRoute,
    deleteAdminRouteById,
    queryAdminRouteByExample,
  } from '@/modules/cisOutp/api/adminRouteFreq.ts';
  import { useSearchFormConfig } from '../config/useSearchFormConfig.tsx';
  import { useTableConfig } from '../config/useTableConfig.tsx';
  import {
    Title,
    ProForm,
    ProTable,
    DmlButton,
    useAppConfigData,
    MAIN_APP_CONFIG,
  } from 'sun-biz';
  import AddAndEditDialog from '../components/AddAndEditDialog.vue';

  type FormModelType = {
    enabledFlag: number;
    encounterTypeCodes?: string[];
    keyWord?: string;
  };

  const { t } = useTranslation();
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);

  const formModel = ref<FormModelType>({
    enabledFlag: FLAG.ALL,
    encounterTypeCodes: undefined,
    keyWord: undefined,
  });

  const formRef = ref();
  const tableRef = ref();
  const addAndEditDialogRef = ref();

  const loading = ref(false);
  const tableData = ref<AdminRouteResItem[]>([]);
  const selections = ref<AdminRouteResItem[]>([]);

  const bizData = computed(() => {
    const list = selections.value.map((item) => {
      return item.adminRouteId;
    });
    return list ?? [];
  });

  const draggableFlag = computed(() => {
    return (
      formModel.value.enabledFlag === FLAG.ALL &&
      (!formModel.value?.encounterTypeCodes ||
        formModel.value?.encounterTypeCodes?.length === 0) &&
      !formModel.value.keyWord
    );
  });

  /** dml导出 */
  const dmlExportFn = async () => {
    tableRef.value?.proTableRef?.clearSelection();
    selections.value = [];
  };

  /** 获取数据 */
  const fetchData = async () => {
    loading.value = true;
    const params = {
      enabledFlag:
        formModel.value?.enabledFlag === FLAG.ALL
          ? undefined
          : formModel.value?.enabledFlag,
      encounterTypeCodes: formModel.value?.encounterTypeCodes,
      keyWord: formModel.value?.keyWord,
    };

    const [, res] = await queryAdminRouteByExample(params);

    loading.value = false;
    if (res?.success) {
      tableData.value = res?.data ?? [];
    }
  };

  /** modelChange */
  const modelChange = async (data: FormModelType) => {
    formModel.value = {
      ...formModel.value,
      ...data,
    };

    await fetchData();
  };

  /** 排序 */
  const handleSortEnd = async (data: AdminRouteResItem[]) => {
    tableData.value = data;

    const bizIdList = (data || []).map((item, index) => ({
      bizId: item.adminRouteId,
      sort: index + 1,
    }));
    const [, res] = await commonSort({
      bizIdTypeCode: BIZ_ID_TYPE_CODE.DICT_ADMIN_ROUTE,
      bizIdList,
    });
    if (res?.success) {
      ElMessage.success(t('global:modify.sort.success'));
    }
    await fetchData();
  };

  /** input失去焦点 */
  const blurFn = async (keyWord?: string) => {
    const keyWordRef = formRef.value?.getItemRef('keyWord');

    formModel.value = {
      ...formModel.value,
      keyWord,
    };

    keyWordRef?.blur();
    await fetchData();
  };

  /** 停启用切换 */
  const handleEnableSwitch = async (row: AdminRouteResItem) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} "{{name}}" 吗？', {
        action:
          row.enabledFlag === FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.adminRouteName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        ...row,
        enabledFlag: row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES,
      };

      const [, res] = await saveAdminRoute(params);

      if (res?.success) {
        row.enabledFlag = row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES;
        ElMessage.success(
          row.enabledFlag === FLAG.YES
            ? t('global:enabled.success')
            : t('global:disabled.success'),
        );
      }
    });
  };

  /** 编辑方法 */
  const handleEdit = async (row?: AdminRouteResItem) => {
    addAndEditDialogRef.value.open(row);
  };

  /** 删除方法 */
  const handleDelete = async (row: AdminRouteResItem) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} "{{name}}" 吗？', {
        action: t('global:delete'),
        name: row.adminRouteName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await deleteAdminRouteById({
        adminRouteId: row?.adminRouteId,
      });

      if (res?.success) {
        ElMessage.success(t('global:delete.success'));
        await fetchData();
      }
    });
  };

  /** dml勾选 */
  const handleSelectChange = (value: AdminRouteResItem[]) => {
    selections.value = value;
  };

  /** 输入框输入的值为null */
  const handleInput = async (value: string) => {
    if (!value) {
      formModel.value = {
        ...formModel.value,
        keyWord: undefined,
      };
      await fetchData();
    }
  };

  const formConfig = useSearchFormConfig({ blurFn, handleInput });
  const tableConfig = useTableConfig({
    isCloudEnv: isCloudEnv as boolean,
    handleEnableSwitch,
    handleEdit,
    handleDelete,
  });

  onMounted(() => {
    fetchData();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('adminRoute.title', '给药途径设置')" class="mb-3" />
    <div class="mb-2.5">
      <ProForm
        ref="formRef"
        class="ml-4 flex flex-1 flex-wrap"
        v-model="formModel"
        :data="formConfig"
        layout-mode="inline"
        @model-change="modelChange"
      >
        <div class="flex flex-1 justify-end">
          <el-button type="primary" @click="fetchData">
            {{ $t('global:query') }}
          </el-button>
          <el-button type="primary" @click="() => handleEdit()" class="mr-2.5">
            {{ $t('global:add') }}
          </el-button>
          <DmlButton
            :biz-data="bizData"
            :code="BIZ_ID_TYPE_CODE.DICT_ADMIN_ROUTE"
            @success="dmlExportFn"
          />
        </div>
      </ProForm>
    </div>

    <proTable
      ref="tableRef"
      :data="tableData"
      :columns="tableConfig"
      :loading="loading"
      :draggable="draggableFlag"
      row-key="adminRouteId"
      @drag-end="handleSortEnd"
      @selection-change="handleSelectChange"
    />
  </div>

  <AddAndEditDialog ref="addAndEditDialogRef" @success="fetchData" />
</template>
