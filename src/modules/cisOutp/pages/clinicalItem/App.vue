<script setup lang="ts">
  import { provide, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import ClinicalCategoryTree from './components/ClinicalCategoryTree.vue';

  const router = useRouter();
  const curCsCategory = ref<ClinicalItem.CsCategoryItem>();
  const csCategories = ref<ClinicalItem.CsCategoryListResponse>([]);

  function handleNodeClick(csCategory: ClinicalItem.CsCategoryItem) {
    curCsCategory.value = csCategory;
    router.push({
      name: 'projectList',
      params: {
        /** 类别标识 */
        csCategoryId: csCategory.csCategoryId,
        /** 临床服务类型代码 */
        csTypeCode: csCategory.csTypeCode,
      },
    });
  }

  function handleFetchTreeSuccess(
    categories: ClinicalItem.CsCategoryListResponse,
  ) {
    csCategories.value = categories;
  }

  provide('csCategories', csCategories);
</script>

<template>
  <div class="flex size-full">
    <!-- 服务分类 -->
    <ClinicalCategoryTree
      class="p-box h-full w-[278px] flex-shrink-0 flex-grow-0 overflow-y-hidden shadow-lg"
      @node-click="handleNodeClick"
      @fetch-tree-success="handleFetchTreeSuccess"
    />
    <!-- 临床项目 -->
    <div
      class="p-box ml-4 h-full flex-auto flex-col overflow-auto pb-0 shadow-lg"
    >
      <router-view v-slot="{ Component, route }">
        <component
          :is="Component"
          :route="route"
          :key="route.params.csCategoryId"
        />
      </router-view>
    </div>
  </div>
</template>
