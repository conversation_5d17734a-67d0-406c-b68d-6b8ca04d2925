import { Search } from '@element-sun/icons-vue';
import { useFormConfig, FormDescItem } from 'sun-biz';

export function useSearchProFormConfig() {
  const data = useFormConfig({
    getData: (t) => {
      return [
        {
          name: 'keyWord',
          component: 'input',
          triggerModelChange: true,
          placeholder: t(
            'global:placeholder.input.template', // '请输入搜索内容'
            { content: t('global:search') + t('global:content') },
          ),
          extraProps: {
            clearable: true,
            suffixIcon: Search,
          },
        },
      ] as FormDescItem[];
    },
  });
  return data;
}
