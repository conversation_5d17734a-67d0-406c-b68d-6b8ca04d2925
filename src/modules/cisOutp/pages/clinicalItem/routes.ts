import ProjectList from './views/ProjectList.vue';

export const routes = [
  {
    path: '/',
    redirect: 'ProjectList',
  },
  {
    path: '/list/:csCategoryId/:csTypeCode',
    name: 'projectList',
    component: ProjectList,
  },
  {
    path: '/:csCategoryId/:csTypeCode/add',
    name: 'add',
    component: () => import('./views/ProjectDetail.vue'),
  },
  {
    path: '/:csCategoryId/:csTypeCode/:ciId/edit',
    name: 'edit',
    component: () => import('./views/ProjectDetail.vue'),
  },
  {
    path: '/:csCategoryId/:csTypeCode/:ciId/detail',
    name: 'view',
    component: () => import('./views/ProjectDetail.vue'),
  },
];

export default routes;
