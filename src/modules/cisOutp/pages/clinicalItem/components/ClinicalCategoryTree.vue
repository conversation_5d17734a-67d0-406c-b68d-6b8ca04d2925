<script setup lang="ts">
  import { onBeforeMount, ref } from 'vue';
  import { queryCliServiceCategory } from '../../../api/clinicalItem';
  import { useTranslation } from 'i18next-vue';
  import { FULL_NODE_ID } from '../constants';

  const emit = defineEmits<{
    (event: 'node-click', csCategory: ClinicalItem.CsCategoryItem): void;
    (
      event: 'fetch-tree-success',
      categories: ClinicalItem.CsCategoryListResponse,
    ): void;
  }>();

  const { t } = useTranslation();
  const allNodeName = t('global:all');
  const clinicalCategories = ref<ClinicalItem.CsCategoryListResponse>([]);
  const defaultProps = {
    children: 'childCliServiceCategory',
    label: 'csCategoryNameDisplay',
  };
  const currentNodeKey = ref<string>();

  async function fetchClinicalCategories() {
    const [, res] = await queryCliServiceCategory();
    if (res?.success) {
      emit('fetch-tree-success', res.data || []);
      clinicalCategories.value = [
        {
          csCategoryId: FULL_NODE_ID,
          csCategoryNameDisplay: allNodeName || '全部 ',
          childCliServiceCategory: res.data || [],
        },
      ];
    }
    // Set default current node key
    if (clinicalCategories.value.length > 0) {
      const isAllNode = clinicalCategories.value[0];

      if (
        isAllNode.childCliServiceCategory &&
        isAllNode.childCliServiceCategory.length > 0
      ) {
        const firstNode = isAllNode
          .childCliServiceCategory[0] as ClinicalItem.CsCategoryItem;
        currentNodeKey.value = firstNode.csCategoryId;
        emit('node-click', firstNode);
      }
    }
  }

  function handleNodeClick(node: ClinicalItem.CsCategoryItem) {
    // if (node.csCategoryId === FULL_NODE_ID) return;
    currentNodeKey.value = node.csCategoryId;
    emit('node-click', node);
  }

  onBeforeMount(() => {
    fetchClinicalCategories();
  });
</script>

<template>
  <div class="h-full">
    <el-scrollbar>
      <el-tree
        highlight-current
        default-expand-all
        :indent="10"
        :data="clinicalCategories"
        :props="defaultProps"
        :current-node-key="currentNodeKey"
        :expand-on-click-node="false"
        node-key="csCategoryId"
        @node-click="handleNodeClick"
      />
    </el-scrollbar>
  </div>
</template>
