<script setup lang="ts">
  import { ProTable } from 'sun-biz';
  import { computed, onBeforeMount, ref, toRef, useTemplateRef } from 'vue';
  import { useCiCommodityEditableTable } from '../config/useCiCommodityEditableTable';
  import {
    queryHospitalChargeItemListByExample,
    queryHospitalMedicineListByExample,
  } from '../../../api/clinicalItem';
  import {
    FIRST_PAGE_NUMBER,
    DEFAULT_PAGE_SIZE,
    FLAG,
  } from '@sun-toolkit/enums';
  import { dayjs, TableInstance } from 'element-sun';
  import { COMMODITY_TYPE_CODE } from '../constants';

  /** 自定义保存不需要参数*/
  export interface CUSTOME_EXTRA_PROP {
    unitName?: string;
    commodityNo?: string;
    commoditySpec?: string;
    commodityName?: string;
  }

  const model =
    defineModel<
      (ClinicalItem.CiCommodityItemFormValue & CUSTOME_EXTRA_PROP)[]
    >();
  const props = defineProps<{
    curLimitHospital: ClinicalItem.OrgAndHospitall['orgId'];
    chargeFlag: ClinicalItem.ClinicalItemFormValues['chargeFlag'];
    disabled: boolean;
  }>();
  const proTableRef = useTemplateRef<{
    validateRow: (index: string, callback?: (valid: boolean) => void) => void;
    proTableRef: TableInstance;
  }>('proTable');
  const drugOptions = ref<ClinicalItem.MedicineInfo[]>([]);
  const category = ref<COMMODITY_TYPE_CODE>(COMMODITY_TYPE_CODE.CHARGE_ITEM);
  const actions = {
    edit,
    remove,
    confirm,
    cancel,
    radioChange,
    disabled: props.disabled,
  };
  const remoteMethods = {
    [COMMODITY_TYPE_CODE.CHARGE_ITEM]: fetchHospitalChargeItemListByExample,
    [COMMODITY_TYPE_CODE.MEDICINE]: fetchHospitalMedicineListByExample,
  };
  const tableData = ref<ClinicalItem.CiCommodityItem[]>([]);
  const tableDataCommodityIds = computed(() =>
    tableData.value
      .map((item) => item.commodityId as string)
      .filter((id) => id !== ''),
  );
  const tableConfig = useCiCommodityEditableTable(
    remoteMethods,
    drugOptions,
    tableDataCommodityIds,
    actions,
    toRef(props.chargeFlag),
    category,
  );

  function edit(index: number) {
    tableData.value[index].editable = true;
  }

  function remove(index: number) {
    tableData.value.splice(index, 1);
    model.value?.splice(index, 1);
  }

  function cancel(index: number, isAddRow: boolean = false) {
    if (isAddRow) {
      /** 新增行 */
      tableData.value.splice(index, 1);
      return;
    }
    tableData.value[index].editable = false;
    tableData.value[index].num = model.value![index].num;
  }

  function confirm(index: number) {
    proTableRef.value?.validateRow(index.toString(), (valid) => {
      if (valid) {
        tableData.value[index].editable = false;
        tableData.value[index].isAddRow = false;
        transferToModel();
      }
    });
  }

  function radioChange(cat: COMMODITY_TYPE_CODE) {
    category.value = cat;
    remoteMethods[cat]();
  }

  function add() {
    tableData.value.push({
      editable: true,
      isAddRow: true,
      ciCommodityId: '',
      hospitalCommodityId: '',
      commodityId: '',
      commodityNo: '',
      commodityTypeCode: '',
      commodityName: '',
      commoditySpec: '',
      num: FLAG.YES,
      unitId: '',
      unitName: '',
    });
  }

  function transferToModel() {
    if (tableData.value?.length) {
      model.value = tableData.value.map((item) => ({
        ciCommodityId: item.ciCommodityId ?? '',
        hospitalCommodityId: item.hospitalCommodityId ?? '',
        commodityId: item.commodityId ?? '',
        commodityTypeCode: item.commodityTypeCode ?? '',
        num: item.num ?? FLAG.YES,
        unitId: item.unitId ?? '',
        /** 自定义添加，保存需要删除字段 */
        unitName: item.unitName,
        commodityName: item.commodityName,
        commodityNo: item.commodityNo,
        commoditySpec: item.commoditySpec,
      }));
    }
  }

  function transferToTableData() {
    if (model.value?.length) {
      tableData.value = model.value.map((item) => {
        return {
          editable: false,
          isAddRow: false,
          ciCommodityId: item.ciCommodityId,
          hospitalCommodityId: item.hospitalCommodityId,
          commodityId: item.commodityId,
          commodityTypeCode: item.commodityTypeCode,
          num: item.num,
          unitId: item.unitId,
          /** 更具自定义表单字段渲染 */
          unitName: item.unitName,
          commodityNo: item.commodityNo,
          commoditySpec: item.commoditySpec,
          commodityName: item.commodityName,
        };
      });
    }
  }

  /** 根据条件查询医院的收费项目列表(业务态) */
  async function fetchHospitalChargeItemListByExample(keyWord?: string) {
    const [, res] = await queryHospitalChargeItemListByExample({
      keyWord,
      pageNumber: FIRST_PAGE_NUMBER,
      pageSize: DEFAULT_PAGE_SIZE,
      /** 所属医院标识 */
      hospitalId: props.curLimitHospital,
      /** 使用范围代码 */
      // encounterTypeCode: '',
      /** 价格日期时间 */
      priceAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    });
    if (res?.success) {
      drugOptions.value = (res.data || []).map((item) => ({
        ...item,
        commodityTypeCode: COMMODITY_TYPE_CODE.CHARGE_ITEM,
      }));
    }
  }

  /** 根据条件查询医院的药品商品列表--update */
  async function fetchHospitalMedicineListByExample(keyWord?: string) {
    const [, res] = await queryHospitalMedicineListByExample({
      keyWord,
      pageNumber: FIRST_PAGE_NUMBER,
      pageSize: DEFAULT_PAGE_SIZE,
      hospitalId: props.curLimitHospital,
    });
    if (res?.success) {
      drugOptions.value = (res.data || []).map((item) => ({
        ...item,
        commodityTypeCode: COMMODITY_TYPE_CODE.MEDICINE,
      }));
    }
  }

  onBeforeMount(() => {
    fetchHospitalChargeItemListByExample();
    transferToTableData();
  });
</script>

<template>
  <div>
    <div class="mb-3 text-right">
      <el-button
        type="primary"
        :disabled="disabled || !chargeFlag"
        @click="add"
        >{{ $t('global:add') }}</el-button
      >
    </div>
    <ProTable
      ref="proTable"
      :columns="tableConfig"
      :data="tableData"
      :editable="true"
    ></ProTable>
  </div>
</template>
