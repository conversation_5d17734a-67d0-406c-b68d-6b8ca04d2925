<script setup lang="ts">
  import { type CodeSystem, ProForm } from 'sun-biz';
  import { SunApiPageData } from '@sun-toolkit/request';
  import { FLAG } from '@sun-toolkit/enums';
  import { onBeforeMount, ref } from 'vue';
  import { useVerfifyLabItemAttributeFormConfig } from '@/modules/cisOutp/config/useLabItemAttributeFormConfig';
  import { queryDataSetListByExample } from '@/modules/cisOutp/api/clinicalItem';
  import { SPECIMEN_NAME, GB_T_2261_2003_NAME } from '../constants';

  interface Specimen {
    /** 检验项目标本标识 */
    clSpecimenId?: string;
    /** 检验项目标本代码 */
    clSpecimenCode: string;
    /** 默认标志 */
    defaultFlag: number;
  }

  type CHECK_FLAG = 0 | 1;

  interface FormProps {
    /** 限用性别 */
    limitSexCode: string;
    /** 允许单选 */
    allowSingleCheckFlag: CHECK_FLAG;
    /** 允许加急 */
    allowUrgentFlag: CHECK_FLAG;
    /** 检验项目标本列表 */
    labItemSpecimenList?: Specimen['clSpecimenCode'][];
    /** 默认标本 */
    defaultSpecimen?: Specimen['clSpecimenCode'];
  }

  const props = defineProps<{
    curLimitHospital: ClinicalItem.OrgAndHospitall['orgId'];
    disabled: boolean;
  }>();
  const model = defineModel<ClinicalItem.LabItemAttributeItemFormValue>({
    default: {
      ciLabitemId: '',
      limitSexCode: '',
      allowUrgentFlag: FLAG.NO,
      allowSingleCheckFlag: FLAG.NO,
      labItemSpecimenList: [],
    },
  });
  const specimenList = ref<CodeSystem[]>([]);
  const limitSexCodes = ref<CodeSystem[]>([]);
  const defaultSpecimenList = ref<CodeSystem[]>([]);
  const form = ref<FormProps>({
    limitSexCode: '',
    allowSingleCheckFlag: FLAG.NO,
    allowUrgentFlag: FLAG.NO,
    labItemSpecimenList: [],
    /** 默认标本 */
    defaultSpecimen: undefined,
  });
  const formConfig = useVerfifyLabItemAttributeFormConfig(
    onSpecimenListChange,
    onSpecimenListBlur,
    specimenList,
    limitSexCodes,
    defaultSpecimenList,
  );

  function onSpecimenListChange(val: string[]) {
    // 默认标本范围取自标本集合
    defaultSpecimenList.value = specimenList.value.filter((item: CodeSystem) =>
      val.includes(item.dataValueNo),
    );
  }

  function onSpecimenListBlur() {
    const defaultSpecimen = form.value.defaultSpecimen;
    const specimenList = form.value.labItemSpecimenList;
    if (defaultSpecimen && !specimenList?.includes(defaultSpecimen)) {
      /** 清空默认标本 */
      form.value.defaultSpecimen = undefined;
    } else if (specimenList?.length && specimenList.length === 1) {
      /** 标本集合只有1项时，设置该项为默认标本 */
      form.value.defaultSpecimen = specimenList[0];
    }
  }

  function handleModelChange(form: FormProps) {
    /** 转换为保存的数据结构 */
    transferFormToModel(form);
  }

  function transferFormToModel(form: FormProps) {
    const labItemSpecimenList = form.labItemSpecimenList?.map((code) => {
      const item = {
        clSpecimenCode: code,
        defaultFlag: FLAG.NO,
      } as Specimen;

      if (form.defaultSpecimen && code === form.defaultSpecimen) {
        item.defaultFlag = FLAG.YES;
      }

      return item;
    });
    model.value.limitSexCode = form.limitSexCode;
    model.value.allowSingleCheckFlag = form.allowSingleCheckFlag;
    model.value.allowUrgentFlag = form.allowUrgentFlag;
    model.value.labItemSpecimenList = labItemSpecimenList;
  }

  function transferModelToForm() {
    const labItemSpecimenList =
      model.value.labItemSpecimenList?.map((item) => item.clSpecimenCode) ?? [];
    form.value.limitSexCode = model.value.limitSexCode as string;
    form.value.allowSingleCheckFlag = model.value
      .allowSingleCheckFlag as CHECK_FLAG;
    form.value.allowUrgentFlag = model.value.allowUrgentFlag as CHECK_FLAG;
    form.value.defaultSpecimen = model.value.labItemSpecimenList?.find(
      (item) => item.defaultFlag === FLAG.YES,
    )?.clSpecimenCode;
    form.value.labItemSpecimenList = labItemSpecimenList;
    defaultSpecimenList.value = specimenList.value.filter((item: CodeSystem) =>
      labItemSpecimenList.includes(item.dataValueNo),
    );
  }

  /** 获取标本集合/ 限用性别集合 */
  async function fetchDataSetListByExample() {
    const [specimenRes, sexCodeRes] = (await Promise.all([
      queryDataSetListByExample({
        hospitalId: props.curLimitHospital,
        codeSystemNos: [SPECIMEN_NAME],
        enabledFlag: FLAG.YES,
      }),
      queryDataSetListByExample({
        hospitalId: props.curLimitHospital,
        codeSystemNos: [GB_T_2261_2003_NAME],
        enabledFlag: FLAG.YES,
      }),
    ])) as [undefined, SunApiPageData<CodeSystem>][];

    specimenList.value = specimenRes[1].data ?? [];
    limitSexCodes.value = sexCodeRes[1].data ?? [];
  }

  onBeforeMount(async () => {
    await fetchDataSetListByExample();
    transferModelToForm();
  });
</script>

<template>
  <div>
    <ProForm
      :data="formConfig"
      :column="3"
      :disabled="disabled"
      v-model="form"
      @model-change="handleModelChange"
    ></ProForm>
  </div>
</template>
