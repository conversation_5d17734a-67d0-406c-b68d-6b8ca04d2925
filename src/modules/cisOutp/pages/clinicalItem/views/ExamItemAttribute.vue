<script setup lang="ts">
  import { ProForm, type CodeSystem } from 'sun-biz';
  import { FLAG } from '@sun-toolkit/enums';
  import { onBeforeMount, ref } from 'vue';
  import { queryDataSetListByExample } from '@/modules/cisOutp/api/clinicalItem';
  import { useExamineLabItemAttributeFormConfig } from '@/modules/cisOutp/config/useLabItemAttributeFormConfig';
  import { GB_T_2261_2003_NAME } from '../constants';

  const props = defineProps<{
    curLimitHospital: ClinicalItem.OrgAndHospitall['orgId'];
    disabled: boolean;
  }>();
  const form = defineModel<ClinicalItem.ExamItemAttributeItemFormValue>();
  const limitSexCodes = ref<CodeSystem[]>([]);
  const formConfig = useExamineLabItemAttributeFormConfig(limitSexCodes);

  /** 获取标本集合/ 限用性别集合 */
  async function fetchDataSetListByExample() {
    const [, res] = await queryDataSetListByExample({
      hospitalId: props.curLimitHospital,
      codeSystemNos: [GB_T_2261_2003_NAME],
      enabledFlag: FLAG.YES,
    });
    if (res?.success) {
      limitSexCodes.value = res.data ?? [];
    }
  }

  onBeforeMount(() => {
    fetchDataSetListByExample();
  });
</script>

<template>
  <div>
    <ProForm
      layout-mode="inline"
      :data="formConfig"
      :disabled="disabled"
      v-model="form"
    ></ProForm>
  </div>
</template>
