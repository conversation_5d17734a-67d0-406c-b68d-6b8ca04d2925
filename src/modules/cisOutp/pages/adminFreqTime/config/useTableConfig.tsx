import { Ref } from 'vue';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';
import { AdminFreqTimeResItem } from '@/modules/cisOutp/typing/adminRouteFreq';

export function useTableConfig(options: {
  tableRef: Ref<TableRef>;
  tableData: Ref<AdminFreqTimeResItem[]>;
  saveFn: (row: AdminFreqTimeResItem, index: number) => Promise<void>;
  deleteFn: (row: AdminFreqTimeResItem, index: number) => Promise<void>;
}) {
  const { tableRef, tableData, saveFn, deleteFn } = options;

  const { toggleEdit, addItem, cancelEdit, delItem } = useEditableTable({
    tableRef,
    data: tableData as Ref<(AdminFreqTimeResItem & { editable: boolean })[]>,
    id: 'execTimeId',
  });

  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNumber',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('adminFreqTime.execTime', '执行时间'),
        prop: 'execTime',
        editable: true,
        required: true,
        minWidth: 120,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('adminFreqTime.time', '执行时间'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: AdminFreqTimeResItem & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <div class={'w-full'}>
                <el-time-picker
                  v-model={row.execTime}
                  placeholder={t('global:placeholder.select.template', {
                    name: t('adminFreqTime.time', '执行时间'),
                  })}
                  format="HH:mm:ss"
                  value-format="HH:mm:ss"
                />
              </div>
            );
          } else {
            return <>{row.execTime}</>;
          }
        },
      },
      {
        label: t('adminFreqTime.execTimeDesc', '执行时间描述'),
        prop: 'execTimeDesc',
        minWidth: 220,
        required: true,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('adminFreqTime.execTimeDesc', '执行时间描述'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: AdminFreqTimeResItem & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.execTimeDesc}
                placeholder={t('global:placeholder.input.template', {
                  content: t('adminFreqTime.execTimeDesc', '执行时间描述'),
                })}
              />
            );
          } else {
            return <>{row.execTimeDesc}</>;
          }
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        width: 150,
        render: (
          row: AdminFreqTimeResItem & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return row.editable ? (
            <div class="flex items-center justify-around">
              <el-button
                key="cancel"
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel', '取消')}
              </el-button>
              <el-button
                key="save"
                type="primary"
                link={true}
                onClick={async () => {
                  saveFn(row, $index);
                }}
              >
                {t('global:save')}
              </el-button>
            </div>
          ) : (
            <div class="flex items-center justify-around">
              <el-button
                link={true}
                key="edit"
                type="primary"
                onClick={() => toggleEdit(row)}
              >
                {t('global:edit')}
              </el-button>
              <el-button
                link={true}
                key="delete"
                type="danger"
                onClick={() => {
                  deleteFn(row, $index);
                }}
              >
                {t('global:delete')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return { tableColumns, addItem, toggleEdit, delItem };
}
