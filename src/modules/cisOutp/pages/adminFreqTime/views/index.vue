<script setup lang="ts" name="adminFreqTime">
  import { ref, computed, onMounted } from 'vue';
  import { ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant';
  import { Title, ProTable, DmlButton } from 'sun-biz';
  import {
    queryAdminFreqTimes,
    deleteAdminFreqTimesById,
    saveAdminFreqTimes,
  } from '@/modules/cisOutp/api/adminRouteFreq.ts';
  import { AdminFreqTimeResItem } from '@/modules/cisOutp/typing/adminRouteFreq.ts';
  import { useTableConfig } from '../config/useTableConfig.tsx';

  const { t } = useTranslation();

  const tableRef = ref();
  const loading = ref(false);
  const tableData = ref<AdminFreqTimeResItem[]>([]);
  const selections = ref<AdminFreqTimeResItem[]>([]);

  const bizData = computed(() => {
    const list = selections.value
      .filter((item) => item.execTimeId) // 过滤掉 execTimeId 为 null 或 undefined 的项
      .map((item) => item.execTimeId);
    return list ?? [];
  });

  /** 获取数据 */
  const fetchData = async () => {
    loading.value = true;

    const [, res] = await queryAdminFreqTimes();

    loading.value = false;
    if (res?.success) {
      tableData.value = res?.data ?? [];
    }
  };

  /** 新增方法 */
  const addFn = async () => {
    addItem({
      editable: true,
    } as AdminFreqTimeResItem & { editable: boolean });
  };

  /** dml导出方法 */
  const dmlExportFn = async () => {
    tableRef.value?.proTableRef?.clearSelection();
    selections.value = [];
  };

  /** 移除方法 */
  const deleteFn = async (row: AdminFreqTimeResItem, index: number) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action: t('global:remove'),
        name: row.execTimeDesc,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const [, res] = await deleteAdminFreqTimesById({
        execTimeId: row?.execTimeId,
      });
      if (res?.success) {
        delItem(index);
        fetchData();
      }
    });
  };

  /** 保存方法 */
  const saveFn = async (row: AdminFreqTimeResItem, index: number) => {
    const isValid = await tableRef?.value?.validateRow(index);
    if (!isValid) return;

    const [, res] = await saveAdminFreqTimes({
      execTimeId: row?.execTimeId ?? undefined,
      execTime: row?.execTime,
      execTimeDesc: row?.execTimeDesc,
    });
    if (res?.success) {
      toggleEdit(
        row as AdminFreqTimeResItem & {
          editable: boolean;
        },
      );
      fetchData();
    }
  };

  // /** 拖拽排序方法 */
  // const handleSortEnd = async (list: AdminFreqTimeResItem[]) => {
  //   if (!validSave()) {
  //     ElMessage.warning(
  //       t('validSave.tips', '当前存在未保存的给药频次时间，请先保存'),
  //     );
  //   }
  // };

  /** 校验是否保存 */
  // const validSave = async () => {
  //   const editableData = tableData.value?.filter(
  //     (item) => (item as AdminFreqTimeResItem & { editable: boolean }).editable,
  //   );
  //   // 2. 明确处理空数组和未定义的情况
  //   if (!editableData || editableData.length === 0) {
  //     return true; // 无未保存数据，校验通过
  //   }
  //   return false; // 存在未保存数据，校验失败
  // };

  /** dml勾选 */
  const handleSelectChange = (value: AdminFreqTimeResItem[]) => {
    selections.value = value;
  };

  const { tableColumns, addItem, toggleEdit, delItem } = useTableConfig({
    tableRef,
    tableData,
    saveFn,
    deleteFn,
  });

  onMounted(() => {
    fetchData();
  });
</script>

<template>
  <!-- 给药频次时间 -->
  <div class="p-box flex h-full flex-col">
    <!-- 标题 -->
    <Title
      class="mb-3"
      :title="$t('adminFreqTime.setting', '给药频次时间设置')"
    >
      <div>
        <el-button type="primary" @click="addFn" class="mr-2.5">
          {{ $t('global:add') }}
        </el-button>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_EXEC_TIME"
          @success="dmlExportFn"
        />
      </div>
    </Title>

    <!-- 展示table -->
    <ProTable
      ref="tableRef"
      row-key="execTimeId"
      :editable="true"
      :data="tableData"
      :loading="loading"
      :columns="tableColumns"
      @selection-change="handleSelectChange"
    />
  </div>
</template>
