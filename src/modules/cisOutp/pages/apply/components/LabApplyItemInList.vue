<script setup lang="ts">
  import { ProTable, Title } from 'sun-biz';
  import { computed, ref, useTemplateRef, watchEffect } from 'vue';
  import {
    DEFAULT_PAGE_SIZE,
    FIRST_PAGE_NUMBER,
    FLAG,
  } from '@sun-toolkit/enums';
  import { useRoute } from 'vue-router';
  import { useTranslation } from 'i18next-vue';
  import { omit } from 'es-toolkit';
  import { useLabApplyItemList } from '../config/useLabApplyItemList';
  import { queryClinicalItem } from '../../../api/apply';
  import LabApplyItemAttribute from './LabApplyItemAttribute.vue';
  import LabApplyItemGroupList from './LabApplyItemGroupList.vue';
  import { CS_TYPE_CODE } from '../constant';
  import {
    ITEM_MODEL,
    transferToLabApplyItem,
  } from '../config/useTransferTableBetweenModel';

  export type ICLI_ITEM_OPTION = ClinicalItem.ClinicalProjectItem & {
    /** 组合明细 */
    applySubItemOutList: Apply.ApplySubItemOutItem[];
  };

  enum ISETTINGVALUE {
    ATTRIBUTE = 'ATTRIBUTE',
    GROUP_LIST = 'GROUP_LIST',
  }

  const { t } = useTranslation();
  const route = useRoute();
  const model = defineModel<ITEM_MODEL[]>();
  const props = defineProps<{
    applyItemId: string;
    curLimitHospitalId: string;
  }>();
  const csTypeCode = computed(() => route.params.csTypeCode as string);
  const isExamAttribute = computed(
    () => csTypeCode.value === CS_TYPE_CODE.EXAMINE,
  );
  const isVerifyAttribute = computed(
    () => csTypeCode.value === CS_TYPE_CODE.VERIFY,
  );
  const proTableRef = useTemplateRef<{
    validateRow: (index: string, callback?: (valid: boolean) => void) => void;
  }>('proTable');
  const tableData = ref<Partial<Apply.LabApplyItemOutItem>[]>([]);
  const cliItemOptions = ref<ICLI_ITEM_OPTION[]>([]);
  const cliItemSelected = computed(() =>
    tableData.value.map((item) => item.ciId),
  );
  const actions = { confirm, cancel, remove };
  const tableConfig = useLabApplyItemList(
    fetchCliItemOptions,
    cliItemOptions,
    cliItemSelected,
    actions,
  );

  const curSetting = ref<ISETTINGVALUE>(ISETTINGVALUE.ATTRIBUTE);
  const cliItemSettings = computed(() => {
    return [
      {
        label: t('cisOutp.apply.itemAttribute', '项目属性') || '项目属性',
        value: ISETTINGVALUE.ATTRIBUTE,
        show: true,
        disabled: false,
      },
      {
        label: t('cisOutp.apply.groupList', '组合明细') || '组合明细',
        value: ISETTINGVALUE.GROUP_LIST,
        show: csTypeCode.value === CS_TYPE_CODE.VERIFY,
        disabled: !curRowClicked.value?.groupFlag,
      },
    ].filter((item) => item.show);
  });
  const curRowClicked = ref<Apply.LabApplyItemOutItem | null>();
  const curRowClickedIndex = ref<number>(0);
  const ownsAttributeTypes = [CS_TYPE_CODE.EXAMINE, CS_TYPE_CODE.VERIFY];
  const itemSettingShow = computed(
    () =>
      ownsAttributeTypes.includes(csTypeCode.value as CS_TYPE_CODE) &&
      curRowClicked.value?.ciId &&
      !curRowClicked.value.editable,
  );

  function removeHighlight() {
    curRowClicked.value = null;
  }

  function add() {
    removeHighlight();
    tableData.value.push({
      editable: true,
      isAddRow: true,
      applyItemId: '',
      ciId: '',
      ciName: '',
      ciNo: '',
      csTypeCode: '',
      groupFlag: FLAG.NO,
    });
  }

  function confirm(index: number) {
    removeHighlight();
    proTableRef.value?.validateRow(index.toString(), (valid) => {
      if (valid) {
        tableData.value[index].editable = false;
        tableData.value[index].isAddRow = false;
        model.value![index] = transferToLabApplyItem(
          tableData.value[index],
          props.applyItemId,
          csTypeCode.value as CS_TYPE_CODE,
        );
      }
    });
  }

  function cancel(index: number) {
    tableData.value.splice(index, 1);
    removeHighlight();
  }

  function remove(index: number) {
    tableData.value.splice(index, 1);
    model.value?.splice(index, 1);
    removeHighlight();
  }

  async function fetchCliItemOptions(keyWord?: string) {
    const [, res] = await queryClinicalItem({
      hospitalId: props.curLimitHospitalId,
      csTypeCode: csTypeCode.value,
      pageNumber: FIRST_PAGE_NUMBER,
      pageSize: DEFAULT_PAGE_SIZE,
      keyWord,
    });

    if (res?.success) {
      cliItemOptions.value = (res?.data ?? []).map((item) => {
        const hospital = item.limitHospitalList![0];
        return {
          ...item,
          /** 提取项目组合明细 */
          applySubItemOutList:
            hospital.subItemList?.map((item) => {
              return {
                ciId: item.subCiId,
                ciNo: item.subCiNo,
                ciName: item.subCiName,
                num: item.num,
                appyLabItemSpecimenOutList:
                  hospital.labItemAttribute?.labItemSpecimenList?.map(
                    (specimenItem) => {
                      return {
                        clSpecimenCode: specimenItem.clSpecimenCode,
                        clSpecimenId: specimenItem.clSpecimenId,
                        clSpecimenCodeDesc: specimenItem.clSpecimenCodeDesc,
                        defaultFlag: specimenItem.defaultFlag,
                      } as Apply.AppyLabItemSpecimenOutItem;
                    },
                  ) ?? [],
              } as Apply.ApplySubItemOutItem;
            }) ?? [],
        };
      });
    }
  }

  function handleRowClick(row: Apply.LabApplyItemOutItem) {
    // 展示项目的 项目属性/组合明细
    curRowClicked.value = row;
    curRowClickedIndex.value = tableData.value.findIndex(
      (item) => item.ciId === row.ciId,
    );
  }

  function transferToTableData(model: ITEM_MODEL[]) {
    if (model.length) {
      tableData.value = model.map((item) => {
        const baseItem = {
          editable: false,
          isAddRow: false,
          applyItemId: item.applyItemId,
          ciId: item.ciId,
          ciName: item.ciName,
          ciNo: item.ciNo,
          groupFlag: item.groupFlag,
          csTypeCode: item.csTypeCode,
        } as Partial<Apply.LabApplyItemOutItem>;

        if (isExamAttribute.value) {
          /**
           * 检查项目属性
           */
          baseItem.applyExamAttributeOut = {
            ciExamitemId: item.applyExamAttributeIn?.ciExamitemId ?? '',
            allowUrgentFlag:
              item.applyExamAttributeIn?.allowUrgentFlag ?? FLAG.NO,
            limitSexCode: item.applyExamAttributeIn?.limitSexCode ?? '',
            reservationFlag:
              item.applyExamAttributeIn?.reservationFlag ?? FLAG.NO,
            limitSexCodeDesc: item.applyExamAttributeIn?.limitSexCodeDesc ?? '',
          };
        } else if (isVerifyAttribute.value) {
          /**
           * 检验项目属性
           */
          baseItem.applyLabItemAttributeOut = {
            ciLabitemId: item.applyLabItemAttributeIn?.ciLabitemId ?? '',
            limitSexCode: item.applyLabItemAttributeIn?.limitSexCode ?? '',
            limitSexCodeDesc:
              item.applyLabItemAttributeIn?.limitSexCodeDesc ?? '',
            allowUrgentFlag:
              item.applyLabItemAttributeIn?.allowUrgentFlag ?? FLAG.NO,
            allowSingleCheckFlag:
              item.applyLabItemAttributeIn?.allowSingleCheckFlag ?? FLAG.NO,
            appyLabItemSpecimenOutList: (item.applyLabItemAttributeIn
              ?.applyLabItemSpecimenInList ??
              []) as unknown as Apply.AppyLabItemSpecimenOutItem[],
          };

          /**
           * 检验组合项目明细列表
           */
          if (item.groupFlag) {
            baseItem.applySubItemOutList = item.applySubItemOutList;
          }
        }

        return baseItem;
      });
    }
  }

  function resolveItemGroupList(list: Apply.ApplySubItemOutItem[]) {
    return list.map((item) => {
      const specimenList = item.appyLabItemSpecimenOutList.map(
        (item) => item.clSpecimenCodeDesc,
      );
      const defaultSpecimen = item.appyLabItemSpecimenOutList.find(
        (item) => item.defaultFlag,
      )?.clSpecimenCodeDesc;
      return {
        ...omit(item, ['appyLabItemSpecimenOutList', 'ciId']),
        specimenList,
        defaultSpecimen,
      };
    });
  }

  watchEffect(() => {
    transferToTableData(model.value!);
  });
</script>

<template>
  <div>
    <div class="mb-3 text-right">
      <el-button type="primary" @click="add">{{ $t('global:add') }}</el-button>
    </div>
    <!-- 项目列表 -->
    <ProTable
      ref="proTable"
      highlight-current-row
      current-row-key="ciId"
      :columns="tableConfig"
      :data="tableData"
      :editable="true"
      :row-class-name="'cursor-pointer'"
      @row-click="handleRowClick"
    ></ProTable>
    <!-- 项目属性 / 组合明细 配置 -->
    <div class="mt-3" v-if="itemSettingShow">
      <Title :title="curRowClicked!.ciName"></Title>
      <el-radio-group v-model="curSetting" class="mt-3">
        <el-radio-button
          v-for="setting in cliItemSettings"
          :key="setting.value"
          :label="setting.label"
          :value="setting.value"
          :disabled="setting.disabled"
        />
      </el-radio-group>
      <LabApplyItemAttribute
        class="mt-3"
        v-if="curSetting === ISETTINGVALUE.ATTRIBUTE"
        :cur-limit-hospital="curLimitHospitalId"
        :cs-type-code="csTypeCode"
        :key="curRowClickedIndex"
        v-model="model![curRowClickedIndex]"
      ></LabApplyItemAttribute>
      <LabApplyItemGroupList
        class="mt-3"
        v-if="curSetting === ISETTINGVALUE.GROUP_LIST"
        :key="curRowClickedIndex"
        :table-data="
          resolveItemGroupList(
            model![curRowClickedIndex].applySubItemOutList ?? [],
          )
        "
      ></LabApplyItemGroupList>
    </div>
  </div>
</template>
