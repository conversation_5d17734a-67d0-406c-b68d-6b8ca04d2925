<script setup lang="ts">
  import { ProTable, type CodeSystem } from 'sun-biz';
  import { useAppScopeInList } from '../config/useAppScopeInList';
  import { computed, nextTick, onBeforeMount, ref, useTemplateRef } from 'vue';
  import { queryDataSetListByExample } from '@/modules/cisOutp/api/apply';
  import { ENCOUNTER_TYPE_CODE_NAME, FLAG } from '@sun-toolkit/enums';
  import { Action, ElMessageBox, TableInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';

  const { t } = useTranslation();
  const model = defineModel<Apply.ApplyScopeInItemSaved[]>();
  const props = withDefaults(
    defineProps<{
      curLimitHospitalId: string;
      disabled?: boolean;
    }>(),
    {
      curLimitHospitalId: '',
      disabled: false,
    },
  );
  const emits = defineEmits<{
    remove: [code: Apply.ApplyScopeOutItem['encounterTypeCode']];
  }>();
  const proTableRef = useTemplateRef<{
    validateRow: (index: string, callback?: (valid: boolean) => void) => void;
    proTableRef: TableInstance;
  }>('proTable');
  const csTypeOptions = ref<CodeSystem[]>([]);
  const alreadyExists = computed<CodeSystem['dataValueNo']>(() =>
    tableData.value.map((item) => item.encounterTypeCode),
  );
  const actions = { remove, confirm, cancel, disabled: props.disabled };
  const tableConfig = useAppScopeInList(alreadyExists, csTypeOptions, actions);
  const tableData = ref<Apply.ApplyScopeOutItem[]>([]);

  function remove(index: number) {
    /** 删除提示：该操作将停用该就诊类型下的【执行科室】、【开单科室】不可用，是否继续？ */
    ElMessageBox.alert(
      t(
        'cisOutp.clinicalItem.delEncTypeTip',
        '该操作将停用该就诊类型下的【执行科室】、【开单科室】不可用，是否继续？',
      ),
      t('global:tip'),
      {
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: t('global:yes'),
        cancelButtonText: t('global:no'),
        callback: (action: Action) => {
          if (action === 'confirm') {
            emits('remove', tableData.value[index].encounterTypeCode);
            tableData.value.splice(index, 1);
            model.value?.splice(index, 1);
          }
        },
      },
    );
  }

  function confirm(index: number) {
    proTableRef.value?.validateRow(index.toString(), (valid) => {
      if (valid) {
        tableData.value[index].editable = false;
        tableData.value[index].isAddRow = false;
        transferToModel();
      }
    });
  }

  function cancel(index: number) {
    tableData.value.splice(index, 1);
  }

  function add() {
    tableData.value.push({
      editable: true,
      isAddRow: true,
      applyScopeId: '',
      encounterTypeCode: '',
    });

    nextTick(() => {
      const btELe = document.querySelector('.block-end') as HTMLElement;
      btELe.scrollIntoView({
        block: 'start',
      });
    });
  }

  function transferToTableData() {
    if (model.value?.length) {
      tableData.value = model.value.map(
        (item) =>
          ({
            editable: false,
            isAddRow: false,
            applyScopeId: item.applyScopeId,
            encounterTypeCode: item.encounterTypeCode,
          }) as Apply.ApplyScopeOutItem,
      );
    }
  }

  function transferToModel() {
    if (tableData.value?.length) {
      model.value = tableData.value.map((item) => ({
        applyScopeId: item.applyScopeId,
        encounterTypeCode: item.encounterTypeCode,
      }));
    }
  }

  /** 获取就诊类型 */
  async function fetchDataSetListByExample() {
    const [, res] = await queryDataSetListByExample({
      hospitalId: props.curLimitHospitalId,
      codeSystemNos: [ENCOUNTER_TYPE_CODE_NAME],
      enabledFlag: FLAG.YES,
    });
    if (res?.success) {
      csTypeOptions.value = res.data ?? [];
    }
  }

  onBeforeMount(async () => {
    await fetchDataSetListByExample();
    transferToTableData();
  });
</script>

<template>
  <div>
    <div class="mb-3 text-right">
      <el-button type="primary" @click="add" :disabled="disabled">{{
        $t('global:add')
      }}</el-button>
    </div>
    <ProTable
      ref="proTable"
      highlight-current-row
      :columns="tableConfig"
      :data="tableData"
      :editable="true"
    ></ProTable>
    <div class="block-end"></div>
  </div>
</template>
