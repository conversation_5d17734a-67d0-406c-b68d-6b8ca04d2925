<script setup lang="ts">
  import {
    Action,
    DialogInstance,
    ElMessage,
    ElMessageBox,
    FormInstance,
  } from 'element-sun';
  import { ProDialog, ProForm } from 'sun-biz';
  import {
    computed,
    onBeforeMount,
    ref,
    Ref,
    useTemplateRef,
    watchEffect,
  } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { omit } from 'es-toolkit';
  import {
    useApplyTemplateProFormConfig,
    type ILimitHospitalOption,
  } from '../config/applyTemplateProFormConfig';
  import { FLAG } from '@sun-toolkit/enums';
  import { APPLY_TEMPLATE_TYPE, HOSPITAL_SETTING_ITEMS } from '../constant';
  import {
    queryCliApplyItemByExample,
    queryOrgAndHospitalList,
    saveApplyTemplate,
  } from '@/modules/cisOutp/api/apply';

  const { t } = useTranslation();
  const formModel = defineModel<Apply.IFORM_MODEL>({
    default: {
      applyTempId: '',
      applyTempNo: '',
      applyTempName: '',
      applyTemp2ndName: '',
      applyTempExtName: '',
      applyTempPnId: '',
      csTypeCode: '',
      enabledFlag: FLAG.YES,
      applyTempTypeCode: APPLY_TEMPLATE_TYPE.DIR,
      /** 自定义 限用医院, 保存需要删除 */
      applyTempLimitHospital: null,
      applyTempLimitHospitalInList: [],
    },
  });
  const props = defineProps<{
    /** 是否编辑 */
    isEdit: boolean;
    /** 拥有子集 */
    hasChildren?: boolean;
    /** 类型是否可以编辑 */
    typeDisabled: boolean;
    /** 模板类型编码 */
    applyTempTypeCode?: string;
  }>();
  const proDialogRef = useTemplateRef<{
    ref: DialogInstance;
    visible: Ref<boolean>;
    open: () => void;
    close: () => void;
  }>('proDialog');
  const proFormRef = useTemplateRef<{
    ref: FormInstance;
  }>('proForm');
  const limitHospitalList = ref<ILimitHospitalOption[]>([]);
  const csTypeCodeDisabled = computed(
    () => props.isEdit || !!formModel.value.applyTempPnId,
  );
  const tempTypeCodeDisabled = computed(() => {
    /**
     * 编辑：模板无维护项目，则可以修改 类型
     */
    if (props.isEdit) {
      if (props.applyTempTypeCode === APPLY_TEMPLATE_TYPE.TEMP) {
        return props.typeDisabled && projectMaintained.value;
      }
    } else {
      /**
       * 新增：顶级目录 类型 不可修改，默认为目录
       */
      return formModel.value.applyTempPnId ? false : true;
    }
    return false;
  });
  const projectMaintained = ref(false);
  const formConfig = useApplyTemplateProFormConfig(
    limitHospitalList,
    csTypeCodeDisabled,
    tempTypeCodeDisabled,
  );
  const saveLoading = ref(false);

  watchEffect(() => {
    if (props.isEdit && formModel.value.applyTempId) {
      fetchCliApplyItemByExample();
    }
  });

  function limitHospitalMaintainProject(
    limitHospitalSettings: Apply.ApplyTempLimitHospitalOutResponseItem[],
  ) {
    return limitHospitalSettings
      .map((limitHospitalSetting) => {
        return [
          HOSPITAL_SETTING_ITEMS.ITEM_LIST,
          HOSPITAL_SETTING_ITEMS.SCOPE_LIST,
          HOSPITAL_SETTING_ITEMS.DEPT_LIST,
          HOSPITAL_SETTING_ITEMS.EXEC_DEPT_LIST,
        ]
          .map((key) => key.replace('In', 'Out'))
          .map(
            (key) =>
              limitHospitalSetting[
                key as keyof Apply.ApplyTempLimitHospitalOutResponseItem
              ],
          )
          .some((item) => !!item);
      })
      .some((item) => !!item);
  }

  async function fetchCliApplyItemByExample() {
    const [, res] = await queryCliApplyItemByExample({
      applyTempId: formModel.value.applyTempId!,
    });
    if (res?.success) {
      projectMaintained.value = limitHospitalMaintainProject(
        res.data?.applyLimitHospitalOutList ?? [],
      );
    }
  }

  async function fetchOrgAndHospitalList() {
    const [, res] = await queryOrgAndHospitalList();
    if (res?.success) {
      limitHospitalList.value =
        res?.data.map((item) => ({
          label: item.orgName,
          value: { hospitalId: item.orgId, applyHospitalId: '' },
        })) || [];
    }
  }

  function handleModelChange(
    form: Apply.IFORM_MODEL,
    key: keyof Apply.IFORM_MODEL,
  ) {
    if (key === 'applyTempLimitHospital') {
      /** tips 限用医院变化时，提示 【限用医院】的变化将影响该【项目】【限用就诊类型】【开单科室】【执行科室】的设置，是否继续？ */
      if (props.isEdit) {
        ElMessageBox.alert(
          t(
            'cisOutp.apply.limitHospitalReduceTip',
            '【限用医院】的变化将影响该【项目】【限用就诊类型】【开单科室】【执行科室】的设置，是否继续？',
          ),
          t('global:tip'),
          {
            type: 'warning',
            showCancelButton: true,
            confirmButtonText: t('global:yes'),
            cancelButtonText: t('global:no'),
            callback: (action: Action) => {
              if (action === 'cancel' || action === 'close') {
                form[key] = formModel.value['applyTempLimitHospitalInList'][0];
              } else {
                form['applyTempLimitHospitalInList'] = form[key]
                  ? [form[key]]
                  : [];
              }
            },
          },
        );
      } else {
        form['applyTempLimitHospitalInList'] = form[key] ? [form[key]] : [];
      }
    }
  }

  function confirm() {
    proFormRef.value?.ref.validate(async (valid) => {
      if (valid) {
        saveLoading.value = true;
        const [, res] = await saveApplyTemplate(
          omit(formModel.value, ['applyTempLimitHospital', 'applyTempPnName']),
        );
        if (res?.success) {
          ElMessage.success(t('global:save.success'));
          proDialogRef.value?.close();
        }
        saveLoading.value = false;
      }
    });
  }

  function cancel() {
    proDialogRef.value?.close();
  }

  onBeforeMount(async () => {
    await fetchOrgAndHospitalList();
    formModel.value['applyTempLimitHospital'] =
      formModel.value['applyTempLimitHospitalInList'][0];
  });

  defineExpose({
    ref: proDialogRef,
  });
</script>

<template>
  <ProDialog
    ref="proDialog"
    destroy-on-close
    :align-center="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :width="800"
    :title="$t('cisOutp.apply.applyTemplate', '申请单模板')"
  >
    <ProForm
      ref="proForm"
      :data="formConfig"
      :column="2"
      v-model="formModel"
      @model-change="handleModelChange"
    >
    </ProForm>
    <template #footer>
      <div>
        <el-button @click="cancel">{{ $t('global:cancel') }}</el-button>
        <el-button type="primary" @click="confirm">{{
          $t('global:confirm')
        }}</el-button>
      </div>
    </template>
  </ProDialog>
</template>
