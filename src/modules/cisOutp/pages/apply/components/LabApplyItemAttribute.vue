<script setup lang="ts">
  import LabItemAttribute from './LabItemAttribute.vue';
  import ExamItemAttribute from './ExamItemAttribute.vue';
  import { COMPONENT_SPECIMEN_LIST_PROP, CS_TYPE_CODE } from '../constant';
  import { type ITEM_MODEL } from '../config/useTransferTableBetweenModel';
  import { watch } from 'vue';
  const props = defineProps<{
    curLimitHospital: string;
    csTypeCode: string;
  }>();

  const model = defineModel<ITEM_MODEL>({ required: true });

  watch(model.value, () => {
    if (props.csTypeCode === CS_TYPE_CODE.VERIFY) {
      if (model.value.applyLabItemAttributeIn) {
        model.value.applyLabItemAttributeIn.applyLabItemSpecimenInList =
          model.value.applyLabItemAttributeIn[COMPONENT_SPECIMEN_LIST_PROP];
      }
    }
  });
</script>
<template>
  <div>
    <LabItemAttribute
      v-if="csTypeCode === CS_TYPE_CODE.VERIFY"
      v-model="model.applyLabItemAttributeIn!"
      :cur-limit-hospital="curLimitHospital"
      :disabled="false"
    ></LabItemAttribute>
    <ExamItemAttribute
      v-if="csTypeCode === CS_TYPE_CODE.EXAMINE"
      v-model="model.applyExamAttributeIn!"
      :cur-limit-hospital="curLimitHospital"
      :disabled="false"
    ></ExamItemAttribute>
  </div>
</template>
