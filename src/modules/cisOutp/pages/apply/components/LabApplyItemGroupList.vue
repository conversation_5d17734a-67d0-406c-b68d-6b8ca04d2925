<script setup lang="ts">
  import { ProTable } from 'sun-biz';
  import { useLabApplyItemGroupList } from '../config/useLabApplyItemGroupList';

  export interface ITABLE_PROP {
    ciName: string;
    ciNo: string;
    num: number;
    specimenList?: string[];
    defaultSpecimen?: string;
  }

  defineProps<{
    tableData: ITABLE_PROP[];
  }>();

  const tableConfig = useLabApplyItemGroupList();
</script>

<template>
  <div>
    <ProTable :columns="tableConfig" :data="tableData"></ProTable>
  </div>
</template>
