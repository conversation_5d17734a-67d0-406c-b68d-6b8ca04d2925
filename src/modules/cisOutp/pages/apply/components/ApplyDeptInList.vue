<script setup lang="ts">
  import { ProTable, type CodeSystem } from 'sun-biz';
  import {
    nextTick,
    onBeforeMount,
    ref,
    useTemplateRef,
    watchEffect,
  } from 'vue';
  import {
    ENABLED_FLAG,
    ENCOUNTER_TYPE_CODE_NAME,
    FIRST_PAGE_NUMBER,
    FLAG,
    ONE_PAGE_SIZE,
    ORG_TYPE_CODE,
  } from '@sun-toolkit/enums';
  import { useApplyDeptInList } from '../config/useApplyDeptInList';
  import {
    queryDataSetListByExample,
    queryOrgListByExampleFlat,
  } from '@/modules/cisOutp/api/clinicalItem';
  import { TableInstance } from 'element-sun';
  import { useDeptEncounterConfig } from '@/modules/cisOutp/config/useDeptEncounterConfig';

  interface EXTRA_DEPT_PROP {
    /**
     * 科室编码
     */
    applyOrgNo?: string;
    /**
     * 科室名称
     */
    applyOrgName?: string;
  }

  export type IDEPT_MODEL_PROP = Apply.ApplyDeptInItemSaved & EXTRA_DEPT_PROP;

  const model = defineModel<IDEPT_MODEL_PROP[]>();
  const props = defineProps<{
    curLimitHospitalId: string;
    encTypeList?: ClinicalItem.EncTypeItemFormValue[];
    disabled?: boolean;
  }>();
  const proTableRef = useTemplateRef<{
    validateRow: (index: string, callback?: (valid: boolean) => void) => void;
    proTableRef: TableInstance;
  }>('proTable');
  const depOptions = ref<
    {
      label: string;
      value: ClinicalItem.OrganizationItem;
    }[]
  >([]);
  const enctypeOptions = ref<CodeSystem[]>([]);

  watchEffect(() => {
    if (props.encTypeList?.length) {
      const encounterTypeCodes = props.encTypeList.map(
        (item) => item.encounterTypeCode,
      );
      enctypeOptions.value = enctypeOptions.value.filter((item: CodeSystem) =>
        encounterTypeCodes.includes(item.dataValueNo),
      );
    }
  });
  const actions = {
    confirm,
    edit,
    remove,
    cancel,
    disabled: props.disabled,
  };
  const tableData = ref<Apply.ApplyDeptOutItem[]>([]);
  const { execOrgIds, encTypeMap, resolveOrgIdDisabled } =
    useDeptEncounterConfig<Apply.ApplyDeptOutItem>(
      props.encTypeList!,
      tableData,
    );
  const tableConfig = useApplyDeptInList(
    fetchOrgListByExampleFlat,
    resolveOrgIdDisabled,
    depOptions,
    enctypeOptions,
    actions,
    execOrgIds,
    encTypeMap,
  );

  function confirm(index: number) {
    proTableRef.value?.validateRow(index.toString(), (valid) => {
      if (valid) {
        tableData.value[index].editable = false;
        tableData.value[index].isAddRow = false;
        transferToModel();
      }
    });
  }

  function edit(index: number) {
    tableData.value[index].editable = true;
  }

  function remove(index: number) {
    tableData.value.splice(index, 1);
    model.value?.splice(index, 1);
  }

  function cancel(index: number, isAddRow: boolean = false) {
    if (isAddRow) {
      /** 新增行 */
      tableData.value.splice(index, 1);
      return;
    }
    tableData.value[index].editable = false;
    tableData.value[index].orgId = model.value![index].orgId;
    tableData.value[index].applyOrgNo = model.value![index].applyOrgNo!;
    tableData.value[index].applyOrgName = model.value![index].applyOrgName!;
    tableData.value[index].encounterTypeCode =
      model.value![index].encounterTypeCode!;
  }

  function add() {
    tableData.value.push({
      editable: true,
      isAddRow: true,
      applyDeptId: '',
      orgId: '',
      applyOrgNo: '',
      applyOrgName: '',
      encounterTypeCode: '',
    });

    nextTick(() => {
      const btELe = document.querySelector('.block-end') as HTMLElement;
      btELe.scrollIntoView({
        block: 'start',
      });
    });
  }

  async function fetchOrgListByExampleFlat(keyWord: string = '') {
    // 需要添加组织表示等参数，即是哪个医院下的科室选项
    const params = {
      keyWord,
      parentOrgId: props.curLimitHospitalId,
      enabledFlag: ENABLED_FLAG.YES,
      orgTypeCodes: [ORG_TYPE_CODE.DEPARTMENT, ORG_TYPE_CODE.AREA],
      pageSize: ONE_PAGE_SIZE,
      pageNumber: FIRST_PAGE_NUMBER,
    } as ClinicalItem.QueryOrgListByExampleFlatParam;
    const [, res] = await queryOrgListByExampleFlat(params);
    if (res?.success) {
      depOptions.value = (res?.data || []).map((item) => ({
        label: item.orgNameDisplay,
        value: item,
      }));
    }
  }

  function transferToModel() {
    if (tableData.value?.length) {
      model.value = tableData.value
        .filter((item) => !item.isAddRow)
        .map((item) => ({
          applyDeptId: item.applyDeptId ?? '',
          orgId: item.orgId ?? '',
          applyOrgNo: item.applyOrgNo ?? '',
          applyOrgName: item.applyOrgName ?? '',
          encounterTypeCode: item.encounterTypeCode ?? '',
        }));
    }
  }
  async function transferToTableData() {
    if (model.value?.length) {
      eliminateEnctypeNotExisted();
      await nextTick();
      tableData.value = model.value.map((item) => ({
        editable: false,
        isAddRow: false,
        applyDeptId: item.applyDeptId ?? '',
        orgId: item.orgId ?? '',
        applyOrgNo: item.applyOrgNo ?? '',
        applyOrgName: item.applyOrgName ?? '',
        encounterTypeCode: item.encounterTypeCode ?? '',
      }));
    }
  }

  /** 删除 就诊类型 不存在数据 */
  function eliminateEnctypeNotExisted() {
    if (model.value?.length) {
      const encTypeCodes = props.encTypeList!.map(
        (item) => item.encounterTypeCode,
      );

      if (encTypeCodes?.length) {
        model.value = model.value.filter(
          (item) =>
            item.encounterTypeCode !== undefined &&
            encTypeCodes.includes(item.encounterTypeCode as string),
        );
      }
    }
  }

  /** 获取就诊类型 */
  async function fetchDataSetListByExample() {
    const [, res] = await queryDataSetListByExample({
      hospitalId: props.curLimitHospitalId,
      codeSystemNos: [ENCOUNTER_TYPE_CODE_NAME],
      enabledFlag: FLAG.YES,
    });
    if (res?.success) {
      enctypeOptions.value = res.data ?? [];
    }
  }

  onBeforeMount(async () => {
    await fetchOrgListByExampleFlat();
    await fetchDataSetListByExample();
    transferToTableData();
  });
</script>

<template>
  <div>
    <div class="mb-3 text-right">
      <el-button type="primary" @click="add" :disabled="disabled">{{
        $t('global:add')
      }}</el-button>
    </div>
    <ProTable
      ref="proTable"
      :columns="tableConfig"
      :data="tableData"
      :editable="true"
    ></ProTable>
    <div class="block-end"></div>
  </div>
</template>
