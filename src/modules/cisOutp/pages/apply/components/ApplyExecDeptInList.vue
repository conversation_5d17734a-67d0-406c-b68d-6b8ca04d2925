<script setup lang="ts">
  import { ProTable, type CodeSystem } from 'sun-biz';
  import {
    nextTick,
    onBeforeMount,
    ref,
    useTemplateRef,
    watchEffect,
  } from 'vue';
  import {
    ENABLED_FLAG,
    ENCOUNTER_TYPE_CODE_NAME,
    FIRST_PAGE_NUMBER,
    FLAG,
    ONE_PAGE_SIZE,
    ORG_TYPE_CODE,
  } from '@sun-toolkit/enums';
  import { useApplyExecDeptInList } from '../config/useApplyExecDeptInList';
  import {
    queryDataSetListByExample,
    queryOrgListByExampleFlat,
  } from '@/modules/cisOutp/api/clinicalItem';
  import { TableInstance } from 'element-sun';
  import { useDeptEncounterConfig } from '@/modules/cisOutp/config/useDeptEncounterConfig';

  interface EXTRA_EXEC_DEPT_PROP {
    /**
     * 科室编码
     */
    ciExecOrgNo?: string;
    /**
     * 科室名称
     */
    ciExecOrgName?: string;
  }

  export type IEXEC_DEPT_MODEL_PROP = Apply.ApplyExecDeptInItemSaved &
    EXTRA_EXEC_DEPT_PROP;

  const model = defineModel<IEXEC_DEPT_MODEL_PROP[]>();
  const props = defineProps<{
    curLimitHospitalId: string;
    encTypeList: ClinicalItem.EncTypeItemFormValue[];
    disabled?: boolean;
  }>();
  const proTableRef = useTemplateRef<{
    validateRow: (index: string, callback?: (valid: boolean) => void) => void;
    proTableRef: TableInstance;
  }>('proTable');
  const depOptions = ref<
    {
      label: string;
      value: ClinicalItem.OrganizationItem;
    }[]
  >([]);
  const enctypeOptions = ref<CodeSystem[]>([]);

  watchEffect(() => {
    if (props.encTypeList?.length) {
      const encounterTypeCodes = props.encTypeList.map(
        (item) => item.encounterTypeCode,
      );
      enctypeOptions.value = enctypeOptions.value.filter((item: CodeSystem) =>
        encounterTypeCodes.includes(item.dataValueNo),
      );
    }
  });
  const actions = {
    confirm,
    edit,
    remove,
    cancel,
    disabled: props.disabled,
  };
  const tableData = ref<Apply.ApplyExecDeptOutItem[]>([]);
  const { execOrgIds, encTypeMap, resolveOrgIdDisabled } =
    useDeptEncounterConfig<Apply.ApplyExecDeptOutItem>(
      props.encTypeList,
      tableData,
    );
  const tableConfig = useApplyExecDeptInList(
    fetchOrgListByExampleFlat,
    resolveOrgIdDisabled,
    depOptions,
    enctypeOptions,
    actions,
    execOrgIds,
    encTypeMap,
  );

  function confirm(index: number) {
    proTableRef.value?.validateRow(index.toString(), (valid) => {
      if (valid) {
        tableData.value[index].editable = false;
        tableData.value[index].isAddRow = false;
        transferToModel();
      }
    });
  }

  function edit(index: number) {
    tableData.value[index].editable = true;
  }

  function remove(index: number) {
    tableData.value.splice(index, 1);
    model.value?.splice(index, 1);
  }

  function cancel(index: number, isAddRow: boolean = false) {
    if (isAddRow) {
      /** 新增行 */
      tableData.value.splice(index, 1);
      return;
    }
    tableData.value[index].editable = false;
    tableData.value[index].orgId = model.value![index].orgId;
    tableData.value[index].ciExecOrgNo = model.value![index].ciExecOrgNo!;
    tableData.value[index].ciExecOrgName = model.value![index].ciExecOrgName!;
    tableData.value[index].encounterTypeCode =
      model.value![index].encounterTypeCode!;
    tableData.value[index].defaultFlag = model.value![index].defaultFlag;
  }

  function add() {
    tableData.value.push({
      editable: true,
      isAddRow: true,
      applyExecDeptId: '',
      orgId: '',
      ciExecOrgNo: '',
      ciExecOrgName: '',
      encounterTypeCode: '',
      defaultFlag: ENABLED_FLAG.NO,
    });

    nextTick(() => {
      const btELe = document.querySelector('.block-end') as HTMLElement;
      btELe.scrollIntoView({
        block: 'start',
      });
    });
  }

  async function fetchOrgListByExampleFlat(keyWord: string = '') {
    // 需要添加组织表示等参数，即是哪个医院下的科室选项
    const params = {
      keyWord,
      parentOrgId: props.curLimitHospitalId,
      enabledFlag: ENABLED_FLAG.YES,
      orgTypeCodes: [ORG_TYPE_CODE.DEPARTMENT, ORG_TYPE_CODE.AREA],
      pageSize: ONE_PAGE_SIZE,
      pageNumber: FIRST_PAGE_NUMBER,
    } as ClinicalItem.QueryOrgListByExampleFlatParam;
    const [, res] = await queryOrgListByExampleFlat(params);
    if (res?.success) {
      depOptions.value = (res?.data || []).map((item) => ({
        label: item.orgNameDisplay,
        value: item,
      }));
    }
  }

  function transferToModel() {
    if (tableData.value?.length) {
      model.value = tableData.value
        .filter((item) => !item.isAddRow)
        .map((item) => ({
          applyExecDeptId: item.applyExecDeptId ?? '',
          orgId: item.orgId ?? '',
          ciExecOrgNo: item.ciExecOrgNo ?? '',
          ciExecOrgName: item.ciExecOrgName ?? '',
          encounterTypeCode: item.encounterTypeCode ?? '',
          defaultFlag: Number(item.defaultFlag),
        }));
    }
  }
  async function transferToTableData() {
    if (model.value?.length) {
      eliminateEnctypeNotExisted();
      await nextTick();
      tableData.value = model.value.map((item) => ({
        editable: false,
        isAddRow: false,
        applyExecDeptId: item.applyExecDeptId ?? '',
        orgId: item.orgId ?? '',
        ciExecOrgNo: item.ciExecOrgNo ?? '',
        ciExecOrgName: item.ciExecOrgName ?? '',
        encounterTypeCode: item.encounterTypeCode ?? '',
        defaultFlag: item.defaultFlag ?? 0,
      }));
    }
  }

  /** 删除 就诊类型 不存在数据 */
  function eliminateEnctypeNotExisted() {
    if (model.value?.length) {
      const encTypeCodes = props.encTypeList.map(
        (item) => item.encounterTypeCode,
      );

      if (encTypeCodes?.length) {
        model.value = model.value.filter(
          (item) =>
            item.encounterTypeCode !== undefined &&
            encTypeCodes.includes(item.encounterTypeCode as string),
        );
      }
    }
  }

  /** 获取就诊类型 */
  async function fetchDataSetListByExample() {
    const [, res] = await queryDataSetListByExample({
      hospitalId: props.curLimitHospitalId,
      codeSystemNos: [ENCOUNTER_TYPE_CODE_NAME],
      enabledFlag: FLAG.YES,
    });
    if (res?.success) {
      enctypeOptions.value = res.data ?? [];
    }
  }

  onBeforeMount(async () => {
    await fetchOrgListByExampleFlat();
    await fetchDataSetListByExample();
    transferToTableData();
  });
</script>

<template>
  <div>
    <div class="mb-3 text-right">
      <el-button type="primary" @click="add" :disabled="disabled">{{
        $t('global:add')
      }}</el-button>
    </div>
    <ProTable
      ref="proTable"
      :columns="tableConfig"
      :data="tableData"
      :editable="true"
    ></ProTable>
    <div class="block-end"></div>
  </div>
</template>
