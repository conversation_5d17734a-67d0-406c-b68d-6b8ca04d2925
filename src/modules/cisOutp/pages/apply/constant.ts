export const APPLY_TEMPLATE_TYPE_NAME = 'APPLY_TEMPLATE_TYPE';

export const CS_TYPE_CODE_NAME = 'CS_TYPE_CODE';

export enum APPLY_TEMPLATE_TYPE {
  /**
   * 目录
   */
  DIR = '1',
  /**
   * 模板
   */
  TEMP = '2',
}

/** 限用医院设置属性 */
export enum HOSPITAL_SETTING_ITEMS {
  /** 项目 */
  ITEM_LIST = 'labApplyItemInList',
  /** 限用就诊类型 */
  SCOPE_LIST = 'applyScopeInList',
  /** 开单科室 */
  DEPT_LIST = 'applyDeptInList',
  /** 执行科室 */
  EXEC_DEPT_LIST = 'applyExecDeptInList',
}

/** 项目类型 */
export enum CS_TYPE_CODE {
  /** 检查 */
  EXAMINE = '4',
  /** 检验 */
  VERIFY = '5',
  /** 其他：需要在 @sun-toolkit/enums 中添加类型定义文件 */
}

export const COMPONENT_SPECIMEN_LIST_PROP = 'labItemSpecimenList';
