import { ColumnProps, useColumnConfig, type CodeSystem } from 'sun-biz';
import { ComputedRef, Ref } from 'vue';

export function useAppScopeInList(
  alreadyExists: ComputedRef<CodeSystem['dataValueNo'][]>,
  /** 就诊类型选项 */
  csTypeOptions: Ref<CodeSystem[]>,
  /** 操作 */
  actions: {
    /** 删除 */
    remove: (index: number) => void;
    /** 确定 */
    confirm: (index: number) => void;
    cancel: (index: number) => void;
    disabled: boolean;
  },
) {
  return useColumnConfig({
    getData: (t) => {
      return [
        {
          label: t('global:indexNo'),
          type: 'index',
          width: 65,
        },
        {
          label: t('cisOutp.apply.encounterTypeCode', '就诊类型'),
          prop: 'encounterTypeCode',
          align: 'left',
          editable: true,
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('cisOutp.apply.encounterTypeCode', '就诊类型'),
              }),
              trigger: 'blur',
            },
          ],
          render(row: Apply.ApplyScopeOutItem) {
            if (row.editable) {
              return (
                <>
                  <el-select
                    v-model={row.encounterTypeCode}
                    placeholder={t('global:placeholder.select')}
                  >
                    {csTypeOptions.value.map((item: CodeSystem) => (
                      <el-option
                        key={item.dataValueNo}
                        label={item.dataValueNameDisplay}
                        value={item.dataValueNo}
                        disabled={alreadyExists.value.includes(
                          item.dataValueNo,
                        )}
                      />
                    ))}
                  </el-select>
                </>
              );
            } else {
              return (
                csTypeOptions.value.find(
                  (item) => item.dataValueNo === row.encounterTypeCode,
                )?.dataValueNameDisplay || '--'
              );
            }
          },
        },
        {
          label: t('cisOutp.apply.encounterTypeCode', '编码'),
          prop: 'encounterTypeCode',
          width: 120,
        },
        {
          label: t('global:operation'),
          width: 120,
          render(row: Apply.ApplyScopeOutItem, index: number) {
            if (row.isAddRow) {
              return (
                <>
                  <el-button
                    type="danger"
                    link
                    onClick={() => actions.cancel(index)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    onClick={() => actions.confirm(index)}
                  >
                    {t('global:confirm')}
                  </el-button>
                </>
              );
            } else {
              return (
                <>
                  <el-button
                    type="danger"
                    link
                    disabled={actions.disabled}
                    onClick={() => actions.remove(index)}
                  >
                    {t('global:delete')}
                  </el-button>
                </>
              );
            }
          },
        },
      ] as ColumnProps[];
    },
  });
}
