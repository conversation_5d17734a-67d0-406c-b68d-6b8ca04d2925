import { FLAG } from '@sun-toolkit/enums';
import { FormDescItem, useFormConfig } from 'sun-biz';
import { APPLY_TEMPLATE_TYPE_NAME, CS_TYPE_CODE_NAME } from '../constant';
import { ComputedRef, Ref } from 'vue';

export interface ILimitHospitalOption {
  label: string;
  value: Apply.ApplyTempLimitHospitalInItem;
}

export function useApplyTemplateProFormConfig(
  /** 限用医院选项 */
  limitHospitalList: Ref<ILimitHospitalOption[]>,
  /** 临床服务类型 禁用状态 */
  csTypeCodeDisabled: ComputedRef<boolean>,
  /** 类型 禁用状态 */
  tempTypeCodeDisabled: ComputedRef<boolean>,
) {
  return useFormConfig({
    dataSetCodes: [APPLY_TEMPLATE_TYPE_NAME, CS_TYPE_CODE_NAME],
    getData: (t, dataSets) => {
      return [
        {
          label: t('cisOutp.apply.applyTempPnName', '上级模板'),
          name: 'applyTempPnName',
          triggerModelChange: true,
          component: 'input',
          isFullWidth: true,
          extraProps: {
            disabled: true,
          },
        },
        {
          label: t('cisOutp.apply.applyTempNo', '模板编码'),
          name: 'applyTempNo',
          triggerModelChange: true,
          component: 'input',
          rules: [
            {
              required: true,
              trigger: 'blur',
              message: t('global:placeholder.input.template', {
                content: t('cisOutp.apply.applyTempNo', '模板编码'),
              }),
            },
          ],
          placeholder: t('global:placeholder.input.template', {
            content: t('cisOutp.apply.applyTempNo', '模板编码'),
          }),
        },
        {
          label: t('cisOutp.apply.applyTempName', '模板名称'),
          name: 'applyTempName',
          triggerModelChange: true,
          component: 'input',
          rules: [
            {
              required: true,
              trigger: 'blur',
              message: t('global:placeholder.input.template', {
                content: t('cisOutp.apply.applyTempName', '模板名称'),
              }),
            },
          ],
          placeholder: t('global:placeholder.input.template', {
            content: t('cisOutp.apply.applyTempName', '模板名称'),
          }),
        },
        {
          label: t('cisOutp.apply.applyTemp2ndName', '辅助名称'),
          name: 'applyTemp2ndName',
          triggerModelChange: true,
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('cisOutp.apply.applyTemp2ndName', '辅助名称'),
          }),
        },
        {
          label: t('cisOutp.apply.applyTempExtName', '扩展名称'),
          name: 'applyTempExtName',
          triggerModelChange: true,
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('cisOutp.apply.applyTempExtName', '扩展名称'),
          }),
        },
        /** 临床服务类型 */
        {
          label: t('cisOutp.apply.csTypeCode', '临床服务类型'),
          name: 'csTypeCode',
          triggerModelChange: true,
          component: 'select',
          placeholder: t('global:placeholder.select.template', {
            name: t('cisOutp.apply.csTypeCode', '临床服务类型'),
          }),
          options: dataSets?.value ? dataSets.value[CS_TYPE_CODE_NAME] : [],
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('cisOutp.apply.csTypeCode', '临床服务类型'),
              }),
              trigger: 'blur',
            },
          ],
          extraProps: {
            disabled: csTypeCodeDisabled.value,
          },
        },
        /** 限用医院 */
        {
          label: t('cisOutp.apply.applyTempLimitHospital', '限用医院'),
          name: 'applyTempLimitHospital',
          triggerModelChange: true,
          component: 'select',
          placeholder: t('global:placeholder.select.template', {
            name: t('cisOutp.apply.applyTempLimitHospital', '限用医院'),
          }),
          options: limitHospitalList.value,
          extraProps: {
            'value-key': 'hospitalId',
          },
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t(
                  'cisOutp.apply.applyTempLimitHospitalInList',
                  '限用医院',
                ),
              }),
              trigger: 'blur',
            },
          ],
        },
        // 类型
        {
          label: t('cisOutp.apply.applyTempTypeCode', '类型'),
          name: 'applyTempTypeCode',
          triggerModelChange: true,
          component: 'radio-group',
          options: dataSets?.value
            ? dataSets.value[APPLY_TEMPLATE_TYPE_NAME].map((item) => ({
                label: item.dataValueNameDisplay,
                value: item.dataValueNo,
              }))
            : [],
          rules: [
            {
              required: true,
              trigger: 'blur',
              message: t('global:placeholder.select.template', {
                name: t('cisOutp.apply.applyTempTypeCode', '类型'),
              }),
            },
          ],
          extraProps: {
            disabled: tempTypeCodeDisabled.value,
          },
        },
        // 启用标志
        {
          label: t('cisOutp.apply.enabledFlag', '启用标志'),
          name: 'enabledFlag',
          component: 'switch',
          triggerModelChange: true,
          extraProps: {
            'active-value': FLAG.YES,
            'inactive-value': FLAG.NO,
            'active-text': t('global:enabled'),
            'inactive-text': t('global:disabled'),
            'inline-prompt': true,
          },
        },
      ] as FormDescItem[];
    },
  });
}
