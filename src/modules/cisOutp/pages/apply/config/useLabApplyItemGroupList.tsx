import { ColumnProps, useColumnConfig } from 'sun-biz';
import { type ITABLE_PROP } from '../components/LabApplyItemGroupList.vue';

export function useLabApplyItemGroupList() {
  return useColumnConfig({
    getData: (t) => {
      return [
        // 序号
        {
          label: t('global:indexNo'),
          type: 'index',
          width: 65,
        },
        // 项目名称
        {
          label: t('cisOutp.apply.ciName', '项目名称'),
          prop: 'ciName',
        },
        // 项目编码
        {
          label: t('cisOutp.apply.ciNo', '项目编码'),
          prop: 'ciNo',
        },
        // 数量
        {
          label: t('cisOutp.apply.num', '数量'),
          prop: 'num',
        },
        // 标本集合
        {
          label: t('cisOutp.apply.specimenList', '标本集合'),
          prop: 'specimenList',
          render(row: ITABLE_PROP) {
            return row.specimenList?.length
              ? row.specimenList.join(', ')
              : '--';
          },
        },
        // 默认标本
        {
          label: t('cisOutp.apply.defaultSpecimen', '默认标本'),
          prop: 'defaultSpecimen',
        },
      ] as ColumnProps[];
    },
  });
}
