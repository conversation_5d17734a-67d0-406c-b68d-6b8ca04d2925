import { ColumnProps, useColumnConfig } from 'sun-biz';
import { ComputedRef, Ref } from 'vue';
import { type ICLI_ITEM_OPTION } from '../components/LabApplyItemInList.vue';

export function useLabApplyItemList(
  /** 获取项目名称选项 */
  fetchCliItemOptions: (keyWord?: string) => void,
  /** 项目名称选项 */
  cliItemOptions: Ref<ICLI_ITEM_OPTION[]>,
  /** 已选项目 ciId 集合 */
  cliItemSelected: ComputedRef<ClinicalItem.ClinicalProjectItem['ciId'][]>,
  /** 操作 */
  actions: {
    cancel: (index: number) => void;
    confirm: (index: number) => void;
    remove: (index: number) => void;
  },
) {
  return useColumnConfig({
    getData: (t) => {
      return [
        {
          label: t('global:indexNo'),
          type: 'index',
          width: 65,
        },
        /** 项目名称 */
        {
          label: t('cisOutp.apply.ciName', '项目名称'),
          prop: 'ciName',
          editable: true,
          rules: [
            {
              required: true,
              trigger: 'blur',
              message: t('global:placeholder.select.template', {
                name: t('cisOutp.apply.ciName', '项目名称'),
              }),
            },
          ],
          render(row: Apply.LabApplyItemOutItem) {
            if (row.isAddRow) {
              return (
                <>
                  <el-select
                    remote
                    filterable
                    v-model={row.ciName}
                    value-key="ciId"
                    placeholder={t('global:placeholder.select.template', {
                      name: t('cisOutp.apply.ciName', '项目名称'),
                    })}
                    remote-method={fetchCliItemOptions}
                    onChange={(val: ICLI_ITEM_OPTION) => {
                      row.ciId = val.ciId!;
                      row.ciName = val.ciName!;
                      row.ciNo = val.ciNo!;
                      row.groupFlag = val.groupFlag!;
                      // 处理组合明细
                      row.applySubItemOutList = val.applySubItemOutList;
                    }}
                  >
                    {cliItemOptions.value.map((cliItem) => (
                      <el-option
                        key={cliItem.ciId}
                        label={cliItem.ciName}
                        value={cliItem}
                        disabled={cliItemSelected.value.includes(cliItem.ciId!)}
                      ></el-option>
                    ))}
                  </el-select>
                </>
              );
            } else {
              return row.ciName || '--';
            }
          },
        },
        /** 项目编码 */
        {
          label: t('cisOutp.apply.ciNo', '项目编码'),
          prop: 'ciNo',
        },
        /** 项目编码 */
        {
          label: t('cisOutp.apply.groupFlag', '组合标志'),
          prop: 'groupFlag',
          width: 120,
          render(row: Apply.LabApplyItemOutItem) {
            return row.groupFlag ? t('global:yes') : t('global:no');
          },
        },
        {
          label: t('global:operation'),
          width: 120,
          render(row: Apply.LabApplyItemOutItem, index: number) {
            if (row.isAddRow) {
              return (
                <>
                  <el-button
                    type="danger"
                    link
                    onClick={(e: MouseEvent) => {
                      e.stopPropagation();
                      actions.cancel(index);
                    }}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    onClick={(e: MouseEvent) => {
                      e.stopPropagation();
                      actions.confirm(index);
                    }}
                  >
                    {t('global:confirm')}
                  </el-button>
                </>
              );
            }

            return (
              <el-button
                type="danger"
                link
                onClick={(e: MouseEvent) => {
                  e.stopPropagation();
                  actions.remove(index);
                }}
              >
                {t('global:delete')}
              </el-button>
            );
          },
        },
      ] as ColumnProps[];
    },
  });
}
