import { ColumnProps, useColumnConfig } from 'sun-biz';
import { ComputedRef, Ref } from 'vue';
import {
  IRESOLVE_ORGID_DISABLE,
  ITYPE_MAP,
} from '../../../config/useDeptEncounterConfig';

export function useApplyExecDeptInList(
  fetchOrgListByExampleFlat: (keyWord?: string) => void,
  resolveOrgIdDisabled: IRESOLVE_ORGID_DISABLE,
  /** 科室名称选项 */
  deptOptions: Ref<
    {
      label: string;
      value: ClinicalItem.OrganizationItem;
    }[]
  >,
  /** 就诊类型选项 */
  enctypeOptions: Ref<
    {
      dataValueNo: string;
      dataValueNameDisplay: string;
    }[]
  >,
  actions: {
    /** 删除 */
    remove: (index: number) => void;
    /** 确定 */
    confirm: (index: number) => void;
    /** 确定 */
    edit: (index: number) => void;
    /** 取消 */
    cancel: (index: number, isAddRow?: boolean) => void;
    disabled: boolean;
  },
  /** 已选执行科室 orgId 集合 */
  execOrgIds: ComputedRef<ClinicalItem.OrganizationItem['orgId'][]>,
  encTypeMap: ComputedRef<ITYPE_MAP>,
) {
  return useColumnConfig({
    getData: (t) => {
      return [
        {
          label: t('global:indexNo'),
          type: 'index',
          width: 65,
        },
        {
          label: t('cisOutp.clinicalItem.ciExecOrgName', '科室名称'),
          prop: 'ciExecOrgName',
          align: 'left',
          editable: true,
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('cisOutp.clinicalItem.ciExecOrgName', '科室名称'),
              }),
              trigger: 'blur',
            },
          ],
          render(row: Apply.ApplyExecDeptOutItem) {
            if (row.isAddRow && row.editable) {
              return (
                <>
                  <el-select
                    filterable
                    remote
                    value-key="orgId"
                    v-model={row.ciExecOrgName}
                    placeholder={t('global:placeholder.select')}
                    remote-method={fetchOrgListByExampleFlat}
                    onChange={(val: ClinicalItem.OrganizationItem) => {
                      row.orgId = val.orgId;
                      row.ciExecOrgNo = val.orgNo;
                      row.ciExecOrgName = val.orgNameDisplay;
                    }}
                  >
                    {deptOptions.value.map((item) => (
                      <el-option
                        key={item.value}
                        label={item.label}
                        value={item.value}
                        /** 禁止重复 */
                        disabled={resolveOrgIdDisabled(
                          row,
                          item,
                          execOrgIds,
                          encTypeMap,
                        )}
                      />
                    ))}
                  </el-select>
                </>
              );
            } else {
              return row.ciExecOrgName ?? '--';
            }
          },
        },
        {
          label: t('cisOutp.clinicalItem.ciExecOrgNo', '科室编码'),
          prop: 'ciExecOrgNo',
          width: 120,
        },
        {
          label: t('cisOutp.clinicalItem.encounterTypeCode', '就诊类型'),
          prop: 'encounterTypeCode',
          align: 'left',
          editable: true,
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('cisOutp.clinicalItem.encounterTypeCode', '就诊类型'),
              }),
              trigger: 'blur',
            },
          ],
          render(row: Apply.ApplyExecDeptOutItem) {
            if (row.editable) {
              return (
                <>
                  <el-select
                    v-model={row.encounterTypeCode}
                    placeholder={t('global:placeholder.select')}
                  >
                    {enctypeOptions.value.map(
                      (item: {
                        dataValueNo: string;
                        dataValueNameDisplay: string;
                      }) => (
                        <el-option
                          key={item.dataValueNo}
                          label={item.dataValueNameDisplay}
                          value={item.dataValueNo}
                          disabled={encTypeMap.value[item.dataValueNo].includes(
                            row.orgId!,
                          )}
                        />
                      ),
                    )}
                  </el-select>
                </>
              );
            } else {
              const encType = enctypeOptions.value.find(
                (item) => item.dataValueNo === row.encounterTypeCode,
              );
              return encType?.dataValueNameDisplay || '--';
            }
          },
        },
        {
          label: t('cisOutp.clinicalItem.defaultFlag', '默认标志'),
          width: 100,
          prop: 'defaultFlag',
          render(row: ClinicalItem.ExecDeptItem) {
            if (row.editable) {
              return (
                <>
                  <el-checkbox v-model={row.defaultFlag}></el-checkbox>
                </>
              );
            } else {
              return row.defaultFlag ? t('global:yes') : t('global:no');
            }
          },
        },
        {
          label: t('global:operation'),
          width: 120,
          render(row: ClinicalItem.ExecDeptItem, index: number) {
            if (row.isAddRow) {
              return (
                <>
                  <el-button
                    type="danger"
                    link
                    onClick={() => actions.cancel(index, row.isAddRow!)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    onClick={() => actions.confirm(index)}
                  >
                    {t('global:confirm')}
                  </el-button>
                </>
              );
            } else {
              if (!row.editable) {
                return (
                  <>
                    <el-button
                      type="primary"
                      link
                      disabled={actions.disabled}
                      onClick={() => {
                        actions.edit(index);
                      }}
                    >
                      {t('global:edit')}
                    </el-button>
                    <el-button
                      type="danger"
                      link
                      disabled={actions.disabled}
                      onClick={() => actions.remove(index)}
                    >
                      {t('global:delete')}
                    </el-button>
                  </>
                );
              } else {
                return (
                  <>
                    <el-button
                      type="danger"
                      link
                      onClick={() => actions.cancel(index)}
                    >
                      {t('global:cancel')}
                    </el-button>
                    <el-button
                      type="primary"
                      link
                      onClick={() => actions.confirm(index)}
                    >
                      {t('global:save')}
                    </el-button>
                  </>
                );
              }
            }
          },
        },
      ] as ColumnProps[];
    },
  });
}
