import { FLAG } from '@sun-toolkit/enums';
import { COMPONENT_SPECIMEN_LIST_PROP, CS_TYPE_CODE } from '../constant';

export interface EXTRA_ITEM_MODEL_PROP {
  ciName?: string;
  ciNo?: string;
  groupFlag?: 0 | 1;
  csTypeCode?: string;
  applySubItemOutList?: Apply.ApplySubItemOutItem[];
}

export type ITEM_MODEL = Apply.LabApplyItemInItemSaved & EXTRA_ITEM_MODEL_PROP;

/**
 * 转换项目-项目列表为 model
 * @param tableData
 * @param applyItemId
 * @param csTypeCode
 * @returns
 */
export function transferToLabApplyItemListModel(
  tableData: Partial<Apply.LabApplyItemOutItem>[],
  applyItemId: string,
  csTypeCode: CS_TYPE_CODE,
) {
  if (tableData?.length) {
    return tableData
      .filter((item) => !item.editable)
      .map((item) =>
        transferToLabApplyItem(item, applyItemId, csTypeCode),
      ) as ITEM_MODEL[];
  }

  return [];
}

/**
 * 项目-项目属性 转换 为 model
 * @param {Partial<Apply.LabApplyItemOutItem>} item 项目
 * @param {String} applyItemId 项目Id
 * @param {String} csTypeCode 临床项目类型
 * @returns {ITEM_MODEL}
 */
export function transferToLabApplyItem(
  item: Partial<Apply.LabApplyItemOutItem>,
  applyItemId: string,
  csTypeCode: CS_TYPE_CODE,
) {
  const extraModelProp = {
    ciName: item.ciName,
    ciNo: item.ciNo,
    groupFlag: item.groupFlag,
    csTypeCode: item.csTypeCode,
    applySubItemOutList: item.applySubItemOutList,
  } as EXTRA_ITEM_MODEL_PROP;

  const baseItem = {
    applyItemId: item.applyItemId ?? applyItemId,
    ciId: item.ciId,
  } as Apply.LabApplyItemInItemSaved;

  /** 检验 项目属性 */
  const applyLabItemAttributeIn = {
    ciLabitemId: item.applyLabItemAttributeOut?.ciLabitemId ?? '',
    limitSexCode: item.applyLabItemAttributeOut?.limitSexCode ?? '',
    allowUrgentFlag: item.applyLabItemAttributeOut?.allowUrgentFlag ?? FLAG.NO,
    allowSingleCheckFlag:
      item.applyLabItemAttributeOut?.allowSingleCheckFlag ?? FLAG.NO,
    /** 检验项目标本列表 */
    applyLabItemSpecimenInList:
      item.applyLabItemAttributeOut?.appyLabItemSpecimenOutList ?? [],
    [COMPONENT_SPECIMEN_LIST_PROP]:
      item.applyLabItemAttributeOut?.appyLabItemSpecimenOutList ?? [],
  } as Apply.ApplyLabItemAttributeInModel;

  /** 检查 项目属性 */
  const applyExamAttributeIn = {
    ciExamitemId: item.applyExamAttributeOut?.ciExamitemId ?? '',
    allowUrgentFlag: item.applyExamAttributeOut?.allowUrgentFlag ?? FLAG.NO,
    limitSexCode: item.applyExamAttributeOut?.limitSexCode ?? '',
    reservationFlag: item.applyExamAttributeOut?.reservationFlag ?? FLAG.NO,
    limitSexCodeDesc: item.applyExamAttributeOut?.limitSexCodeDesc ?? '',
  } as Apply.ApplyExamAttributeInModel;

  if (csTypeCode === CS_TYPE_CODE.EXAMINE) {
    baseItem.applyExamAttributeIn = applyExamAttributeIn;
  } else if (csTypeCode === CS_TYPE_CODE.VERIFY) {
    baseItem.applyLabItemAttributeIn = applyLabItemAttributeIn;
  } else if (item.groupFlag) {
    /** 组合明细 */
    baseItem.applySubItemOutList = item.applySubItemOutList;
  }

  return {
    ...extraModelProp,
    ...baseItem,
  } as ITEM_MODEL;
}
