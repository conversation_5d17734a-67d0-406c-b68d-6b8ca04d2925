<script setup lang="ts">
  import { computed, ref, watchEffect } from 'vue';
  import {
    NavigationGuardNext,
    onBeforeRouteUpdate,
    RouteLocationNormalized,
    useRoute,
  } from 'vue-router';
  import { useTranslation } from 'i18next-vue';
  import { cloneDeep, omit } from 'es-toolkit';
  import { ENCOUNTER_TYPE_CODE_NAME, FLAG } from '@sun-toolkit/enums';
  import { CodeSystem } from 'sun-biz';
  import {
    queryCliApplyItemByExample,
    saveCliApplyItem,
  } from '@/modules/cisOutp/api/apply';
  import { CS_TYPE_CODE, HOSPITAL_SETTING_ITEMS } from '../constant';
  import LabApplyItemInList from '../components/LabApplyItemInList.vue';
  import ApplyDeptInList, {
    type IDEPT_MODEL_PROP,
  } from '../components/ApplyDeptInList.vue';
  import ApplyExecDeptInList, {
    type IEXEC_DEPT_MODEL_PROP,
  } from '../components/ApplyExecDeptInList.vue';
  import ApplyScopeInList from '../components/ApplyScopeInList.vue';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { transferToLabApplyItemListModel } from '../config/useTransferTableBetweenModel';
  import { useDataChangeDetector } from 'sun-biz';
  import { useApplyStore } from '../store';
  import { queryDataSetListByExample } from '@/modules/cisOutp/api/clinicalItem';

  const { t } = useTranslation();
  const route = useRoute();
  const applyStore = useApplyStore();
  const applyTempItem = ref<Apply.QueryCliApplyItemByExampleResponse>();
  const curLimitHospitalId = ref('');
  const applyLimitHospitalNames = computed(() => {
    const options = (applyTempItem.value?.applyLimitHospitalOutList ?? []).map(
      (item) => ({
        label: item.hospitalName,
        value: item.hospitalId,
      }),
    );

    return options;
  });

  const curLimitHospitalSettingProp = ref<HOSPITAL_SETTING_ITEMS>(
    HOSPITAL_SETTING_ITEMS.ITEM_LIST,
  );
  const hospitalSettings = [
    {
      label: t('cisOutp.apply.labApplyItemInList', '项目') || '项目',
      value: HOSPITAL_SETTING_ITEMS.ITEM_LIST,
    },
    {
      label:
        t('cisOutp.apply.applyScopeInList', '限用就诊类型') || '限用就诊类型',
      value: HOSPITAL_SETTING_ITEMS.SCOPE_LIST,
    },
    {
      label: t('cisOutp.apply.applyDeptInList', '开单科室') || '开单科室',
      value: HOSPITAL_SETTING_ITEMS.DEPT_LIST,
    },
    {
      label: t('cisOutp.apply.applyExecDeptInList', '执行科室') || '执行科室',
      value: HOSPITAL_SETTING_ITEMS.EXEC_DEPT_LIST,
    },
  ];

  const applyTempId = computed(() => route.params.applyTempId as string);

  const formModel = ref<Apply.SaveCliApplyItemParam>({
    applyTempId: route.params.applyTempId as string,
    applyLimitHospitalInList: [],
  });
  const { resetDetector, checkChange } = useDataChangeDetector([formModel], {
    cancelFn,
  });
  const curLimitHospitalIndex = ref(FLAG.NO);
  const enctypeOptions = ref<ClinicalItem.EncTypeItemFormValue[]>();

  function cancelFn() {
    applyStore.setCancelSave(true);
  }

  function handleLimitHospitalTabChange(val: string) {
    curLimitHospitalIndex.value = applyLimitHospitalNames.value.findIndex(
      (item) => item.value === val,
    );
  }

  function transferToModel() {
    if (applyTempItem.value) {
      if (applyTempItem.value.applyTempId) {
        formModel.value.applyTempId = applyTempItem.value.applyTempId;
      }

      if (applyTempItem.value.applyLimitHospitalOutList?.length) {
        formModel.value.applyLimitHospitalInList =
          applyTempItem.value.applyLimitHospitalOutList.map((item) => {
            return {
              applyHospitalId: item.applyHospitalId,
              hospitalId: item.hospitalId,
              applyDeptInList: item.applyDeptOutList ?? [],
              applyScopeInList: item.applyScopeOutList ?? [],
              applyExecDeptInList: item.applyExecDeptOutList ?? [],
              labApplyItemInList: transferToLabApplyItemListModel(
                item.labApplyItemOutList ?? [],
                applyTempId.value,
                route.params.csTypeCode as CS_TYPE_CODE,
              ),
            };
          });
      }
    }
    resetDetector();
  }

  async function fetchCliApplyItemByExample(applyTempId: string) {
    const [, res] = await queryCliApplyItemByExample({
      applyTempId,
    });
    if (res?.success) {
      applyTempItem.value = res.data ?? {};
      curLimitHospitalId.value =
        res.data?.applyLimitHospitalOutList[0].hospitalId || '';
      transferToModel();
    }
  }

  async function save() {
    const resModel = cloneDeep(formModel.value);
    resModel.applyLimitHospitalInList = resModel.applyLimitHospitalInList.map(
      (hospital) => {
        return {
          applyHospitalId: hospital.applyHospitalId,
          hospitalId: hospital.hospitalId,
          /** TODO  开单科室列表 数据处理 去除 applyOrgNo,  applyOrgName*/
          applyDeptInList: resolveApplyDeptInList(hospital.applyDeptInList),
          /** 申请单模板应用范围列表 done*/
          applyScopeInList: hospital.applyScopeInList,
          /** TODO  执行科室列表 数据处理 去除 ciExecOrgName 、 ciExecOrgNo */
          applyExecDeptInList: resolveApplyExecDeptInList(
            hospital.applyExecDeptInList,
          ),
          /** 申请单项目列表：done */
          labApplyItemInList: resoveLabApplyItemInList(
            hospital.labApplyItemInList,
          ),
        } as Apply.ApplyTempLimitHospitalInItemSaved;
      },
    );

    const [, res] = await saveCliApplyItem(resModel);
    if (res?.success) {
      ElMessage.success(t('global:save.success'));
      resetDetector();
      curLimitHospitalSettingProp.value = HOSPITAL_SETTING_ITEMS.ITEM_LIST;
    }
  }

  /**
   * resolve 申请单项目列表
   * prop: labApplyItemInList
   */
  function resoveLabApplyItemInList(
    list: Apply.LabApplyItemInItemSaved[] = [],
  ) {
    const resolved = list.map((item) => {
      return {
        applyItemId: item.applyItemId,
        ciId: item.ciId,
        applyLabItemAttributeIn: item.applyLabItemAttributeIn
          ? omit(item.applyLabItemAttributeIn, [
              'limitSexCodeDesc',
              'labItemSpecimenList',
            ])
          : null,
        applyExamAttributeIn: item.applyExamAttributeIn,
      } as Apply.LabApplyItemInItemSaved;
    }) as Apply.LabApplyItemInItemSaved[];

    return resolved;
  }

  /**
   * resolve 开单科室列表
   * prop: applyDeptInList
   */
  function resolveApplyDeptInList(list: IDEPT_MODEL_PROP[] = []) {
    return list.map((item) => omit(item, ['applyOrgNo', 'applyOrgName']));
  }

  /**
   * resolve 执行科室
   * prop: applyExecDeptInList
   */
  function resolveApplyExecDeptInList(list: IEXEC_DEPT_MODEL_PROP[] = []) {
    return list.map((item) => omit(item, ['ciExecOrgName', 'ciExecOrgNo']));
  }

  /** resolve 执行科室选项 */
  function resolveEncTypeList(list: { encounterTypeCode: string }[]) {
    if (list?.length) {
      return list;
    } else {
      return enctypeOptions.value ?? [];
    }
  }

  /** 限用医院 就诊类型数据 删除 */
  function handleLimitHospitalEncounterTypeRemove(
    code: Apply.ApplyScopeOutItem['encounterTypeCode'],
  ) {
    // 开单科室 数据处理
    formModel.value.applyLimitHospitalInList[curLimitHospitalIndex.value][
      HOSPITAL_SETTING_ITEMS.DEPT_LIST
    ] = formModel.value.applyLimitHospitalInList[curLimitHospitalIndex.value][
      HOSPITAL_SETTING_ITEMS.DEPT_LIST
    ]?.filter((item) => item.encounterTypeCode !== code);
    // 执行科室 数据处理
    formModel.value.applyLimitHospitalInList[curLimitHospitalIndex.value][
      HOSPITAL_SETTING_ITEMS.EXEC_DEPT_LIST
    ] = formModel.value.applyLimitHospitalInList[curLimitHospitalIndex.value][
      HOSPITAL_SETTING_ITEMS.EXEC_DEPT_LIST
    ]?.filter((item) => item.encounterTypeCode !== code);
  }

  /** 获取就诊类型 */
  async function fetchDataSetListByExample() {
    const [, res] = await queryDataSetListByExample({
      hospitalId: curLimitHospitalId.value,
      codeSystemNos: [ENCOUNTER_TYPE_CODE_NAME],
      enabledFlag: FLAG.YES,
    });
    if (res?.success) {
      enctypeOptions.value = (res.data ?? []).map(
        (item: CodeSystem) =>
          ({
            ciEncTypeId: '',
            encounterTypeCode: item.dataValueNo,
          }) as ClinicalItem.EncTypeItemFormValue,
      );
    }
  }

  watchEffect(async () => {
    await fetchCliApplyItemByExample(route.params.applyTempId as string);
    fetchDataSetListByExample();
  });

  onBeforeRouteUpdate(
    (
      to: RouteLocationNormalized,
      from: RouteLocationNormalized,
      next: NavigationGuardNext,
    ) => {
      if (checkChange()) {
        ElMessageBox.confirm(
          t('cisOutp.apply.cancelSaveTip', '您有未保存的更改，确定要离开吗？'),
          t('global:tip'),
          {
            confirmButtonText: t('global:confirm'),
            cancelButtonText: t('global:cancel'),
            type: 'warning',
          },
        )
          .then(() => {
            next();
          })
          .catch(() => {
            applyStore.setCancelSave(true);
            next(false);
          });
        return;
      } else {
        next();
      }
    },
  );
</script>

<template>
  <div>
    <!-- 保存按钮行 -->
    <div class="text-right">
      <el-button type="primary" @click="save">{{
        $t('global:save')
      }}</el-button>
    </div>
    <!-- 限用医院 Tabs -->
    <el-radio-group
      class="mt-3"
      v-if="applyLimitHospitalNames.length > 1"
      v-model="curLimitHospitalId"
      @change="handleLimitHospitalTabChange"
    >
      <el-radio-button
        v-for="hospital in applyLimitHospitalNames"
        :label="hospital.label"
        :value="hospital.value"
        :key="hospital.value"
      />
    </el-radio-group>
    <!-- 配置项 Tabs: 项目、限用就诊类型、开单科室、就诊科室 -->
    <div class="mt-3" v-if="applyLimitHospitalNames.length">
      <el-radio-group v-model="curLimitHospitalSettingProp">
        <el-radio-button
          v-for="setting in hospitalSettings"
          :label="setting.label"
          :value="setting.value"
          :key="setting.value"
        />
      </el-radio-group>
      <LabApplyItemInList
        v-if="curLimitHospitalSettingProp === HOSPITAL_SETTING_ITEMS.ITEM_LIST"
        v-model="
          formModel.applyLimitHospitalInList[curLimitHospitalIndex][
            curLimitHospitalSettingProp
          ]
        "
        :apply-item-id="applyTempId"
        :cur-limit-hospital-id="curLimitHospitalId"
      ></LabApplyItemInList>
      <ApplyScopeInList
        v-if="curLimitHospitalSettingProp === HOSPITAL_SETTING_ITEMS.SCOPE_LIST"
        v-model="
          formModel.applyLimitHospitalInList[curLimitHospitalIndex][
            HOSPITAL_SETTING_ITEMS.SCOPE_LIST
          ]
        "
        :apply-item-id="applyTempId"
        :cur-limit-hospital-id="curLimitHospitalId"
        @remove="handleLimitHospitalEncounterTypeRemove"
      ></ApplyScopeInList>
      <ApplyDeptInList
        v-if="curLimitHospitalSettingProp === HOSPITAL_SETTING_ITEMS.DEPT_LIST"
        v-model="
          formModel.applyLimitHospitalInList[curLimitHospitalIndex][
            HOSPITAL_SETTING_ITEMS.DEPT_LIST
          ]
        "
        :apply-item-id="applyTempId"
        :cur-limit-hospital-id="curLimitHospitalId"
        :enc-type-list="
          resolveEncTypeList(
            formModel.applyLimitHospitalInList[curLimitHospitalIndex][
              HOSPITAL_SETTING_ITEMS.SCOPE_LIST
            ]!,
          )
        "
      ></ApplyDeptInList>
      <ApplyExecDeptInList
        v-if="
          curLimitHospitalSettingProp === HOSPITAL_SETTING_ITEMS.EXEC_DEPT_LIST
        "
        v-model="
          formModel.applyLimitHospitalInList[curLimitHospitalIndex][
            HOSPITAL_SETTING_ITEMS.EXEC_DEPT_LIST
          ]
        "
        :apply-item-id="applyTempId"
        :cur-limit-hospital-id="curLimitHospitalId"
        :enc-type-list="
          resolveEncTypeList(
            formModel.applyLimitHospitalInList[curLimitHospitalIndex][
              HOSPITAL_SETTING_ITEMS.SCOPE_LIST
            ]!,
          )
        "
      ></ApplyExecDeptInList>
    </div>
  </div>
</template>
