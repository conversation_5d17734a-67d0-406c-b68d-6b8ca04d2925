import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useApplyStore = defineStore('apply', () => {
  const cancelSave = ref(false);
  const currentNodeKey = ref('');

  function setCancelSave(status: boolean) {
    cancelSave.value = status;
  }

  function setCurrentNodeKey(key: string) {
    currentNodeKey.value = key;
  }

  return { cancelSave, currentNodeKey, setCancelSave, setCurrentNodeKey };
});
