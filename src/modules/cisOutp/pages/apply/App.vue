<script setup lang="ts">
  import TemplateTree from './components/TemplateTree.vue';
</script>

<template>
  <div class="flex size-full">
    <TemplateTree
      class="p-box h-full w-[278px] flex-shrink-0 flex-grow-0 overflow-y-hidden shadow-lg"
    >
    </TemplateTree>
    <div class="p-box ml-4 h-full flex-auto flex-col overflow-auto pb-0">
      <router-view v-slot="{ Component, route }">
        <component :is="Component" :route="route" :key="route.name" />
      </router-view>
    </div>
  </div>
</template>
