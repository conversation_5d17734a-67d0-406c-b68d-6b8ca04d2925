<script setup lang="ts">
  import { Title, DmlButton, ProTable, useEditableTable } from 'sun-biz';
  import { onBeforeMount, ref } from 'vue';
  import type { TableRef } from 'sun-biz';
  import { ENABLED_FLAG } from '@sun-toolkit/enums';
  import { pick } from 'es-toolkit';
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { useRouter } from 'vue-router';
  import { useDmlConfig } from '../config/useDmlConfig';
  import { DICT_CI_SEARCH_COMPONENT_NAME } from '../constants';
  import { useSearchComponentListConfig } from '../config/useSearchComponentListConfig';
  import {
    addCliItemSearchComponent,
    queryCliItemSearchComponent,
    updateCliItemSearchComponentById,
  } from '@/modules/cisOutp/api/ciSearchComponent';

  const { bizData, bizIdTypeCode } = useDmlConfig(
    DICT_CI_SEARCH_COMPONENT_NAME,
  );
  const router = useRouter();
  const { t } = useTranslation();
  const proTable = ref<TableRef>();
  const tableData = ref<CiSearchComponent.IQueryCliItemByExampleRespItem[]>([]);
  const { addItem, updateItem, toggleEdit, cancelEdit, validateItem } =
    useEditableTable<CiSearchComponent.IQueryCliItemByExampleRespItem>({
      tableRef: proTable,
      data: tableData,
      id: 'componentId',
    });
  const actions = {
    confirm,
    save,
    setGroup,
    toggleEnableFlag,
    cancel: cancelEdit,
    edit: toggleEdit,
  };
  const tableConfig = useSearchComponentListConfig(actions);

  function handleSelectionChange(
    selection: CiSearchComponent.IQueryCliItemByExampleRespItem[],
  ) {
    bizData.value = selection
      .map((item) => item.componentId)
      .filter((item) => !!item);
  }

  /** 切换 启用/停用 状态 */
  async function toggleEnableFlag(
    row: CiSearchComponent.IQueryCliItemByExampleRespItem,
  ) {
    let tip = '';
    if (row.enabledFlag) {
      tip = t('global:enabled.success');
    } else {
      tip = t('global:disabled.success');
    }
    update(row, tip);
  }

  function setGroup(row: CiSearchComponent.IQueryCliItemByExampleRespItem) {
    router.push({ path: `/set-group/${row.componentId}/${row.componentNo}` });
  }

  /** 编辑保存 */
  async function save(
    row: CiSearchComponent.IQueryCliItemByExampleRespItem,
    index?: number,
  ) {
    const valid = await validateItem(row);
    if (valid) {
      updateItem(row, index);
      toggleEdit(row);
      update(row);
    }
  }

  /**
   *
   * @param row 行数据
   * @param successTip 调用成功提示
   */
  async function update(
    row: CiSearchComponent.IQueryCliItemByExampleRespItem,
    successTip = t('global:save.success'),
  ) {
    const params = pick(row, ['componentId', 'componentDesc', 'enabledFlag']);
    const [, res] = await updateCliItemSearchComponentById(params);
    if (res?.success) {
      ElMessage.success(successTip);
      fetchCliItemByExample();
    }
  }

  /** 新增确定 */
  async function confirm(
    row: CiSearchComponent.IQueryCliItemByExampleRespItem,
  ) {
    const valid = await validateItem(row);
    if (valid) {
      toggleEdit(row);
      row.isAddRow = false;
      const params = pick(row, ['componentNo', 'componentDesc', 'enabledFlag']);
      const [, res] = await addCliItemSearchComponent(params);
      if (res?.success) {
        ElMessage.success(t('global:save.success'));
        fetchCliItemByExample();
      }
    }
  }

  /** 新增行 */
  function add() {
    addItem({
      editable: true,
      isAddRow: true,
      componentId: '',
      componentDesc: '',
      componentNo: '',
      enabledFlag: ENABLED_FLAG.YES,
    });
  }

  async function fetchCliItemByExample() {
    const [, res] = await queryCliItemSearchComponent({});

    if (res?.success) {
      tableData.value = res.data ?? [];
    }
  }

  onBeforeMount(() => {
    fetchCliItemByExample();
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title
      :title="
        $t(
          'cisOutp.ciSearchComponent.searchComponentListTitle',
          '临床项目检索组件',
        )
      "
    ></Title>
    <div class="mb-3 text-right">
      <el-button type="primary" class="mr-3" @click="add">{{
        $t('global:add')
      }}</el-button>
      <DmlButton :biz-data="bizData" :code="bizIdTypeCode"></DmlButton>
    </div>
    <ProTable
      ref="proTable"
      class="flex-1"
      row-key="componentId"
      :columns="tableConfig"
      :data="tableData"
      :editable="true"
      @selection-change="handleSelectionChange"
    ></ProTable>
  </div>
</template>
