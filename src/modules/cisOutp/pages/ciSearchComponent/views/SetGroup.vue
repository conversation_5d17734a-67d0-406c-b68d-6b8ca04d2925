<script setup lang="ts">
  import { Back } from '@element-sun/icons-vue';
  import { Title, ProTable, TableRef, useEditableTable } from 'sun-biz';
  import { computed, onBeforeMount, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ENABLED_FLAG } from '@sun-toolkit/enums';
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import {
    addCliItemSearchSetting,
    deleteCliItemSearchSettingById,
    queryCliItemSearchSettingByExample,
    updateCliItemSearchSettingById,
  } from '@/modules/cisOutp/api/ciSearchComponent';
  import { useSetGroupConfig } from '../config/useSetGroup';
  import { DICT_DATA_SETS } from '../constants';

  const { t } = useTranslation();
  const route = useRoute();
  const router = useRouter();
  const componentId = computed(() => route.params.componentId as string);
  const componentNo = computed(() => route.params.componentNo as string);
  const proTable = ref<TableRef>();
  const tableData = ref<
    CiSearchComponent.IQueryCliItemSearchSettingByExampleRespItem[]
  >([]);
  const { addItem, updateItem, toggleEdit, cancelEdit, validateItem } =
    useEditableTable<CiSearchComponent.IQueryCliItemSearchSettingByExampleRespItem>(
      {
        tableRef: proTable,
        data: tableData,
        id: 'ciSearchGroupId',
      },
    );
  const actions = {
    toggleEnableFlag,
    toggleEdit,
    confirm,
    save,
    remove,
    cancel: cancelEdit,
  };
  const tableConfig = useSetGroupConfig(actions);

  function add() {
    addItem({
      editable: true,
      isAddRow: true,
      ciSearchGroupId: '',
      ciSearchGroupName: '',
      ciSearchGroup2ndName: '',
      ciSearchGroupExtName: '',
      ciSearchSourceCodeDesc: '',
      cisGroupSettingList: [],
      cisGroupEncTypeList: [],
      enabledFlag: ENABLED_FLAG.YES,
      ciSearchSourceCode: '',
      bizIdTypeCode: '',
      ciSearchGroupNameDisplay: '',
    });
  }

  /**
   * 新增确定
   */
  async function confirm(
    row: CiSearchComponent.IQueryCliItemSearchSettingByExampleRespItem,
  ) {
    const valid = await validateItem(row);
    if (valid) {
      toggleEdit(row);
      row.isAddRow = false;
      const params = handleSaveParams(row);
      const [, res] = await addCliItemSearchSetting(params);
      if (res?.success) {
        ElMessage.success(t('global:save.success'));
        fetchCliItemSearchSettingByExample();
      }
    }
  }

  function handleSaveParams(
    row: CiSearchComponent.IQueryCliItemSearchSettingByExampleRespItem,
  ) {
    return {
      componentId: componentId.value,
      ciSearchGroupName: row.ciSearchGroupName,
      ciSearchGroup2ndName: row.ciSearchGroup2ndName,
      ciSearchGroupExtName: row.ciSearchGroupExtName,
      ciSearchSourceCode: row.ciSearchSourceCode,
      enabledFlag: row.enabledFlag,
      bizIdTypeCode: DICT_DATA_SETS,
      bizIds: row.cisGroupSettingList.map((item) => item.bizId),
      encTypeCodes: row.cisGroupEncTypeList.map(
        (item) => item.encounterTypeCode,
      ),
    } as CiSearchComponent.IAddCliItemSearchSettingParam;
  }

  /** 切换 启用/停用 状态 */
  async function toggleEnableFlag(
    row: CiSearchComponent.IQueryCliItemSearchSettingByExampleRespItem,
  ) {
    let tip = '';
    if (row.enabledFlag) {
      tip = t('global:enabled.success');
    } else {
      tip = t('global:disabled.success');
    }
    update(row, tip);
  }

  /**
   *
   * @param row 行数据
   * @param successTip 调用成功提示
   */
  async function update(
    row: CiSearchComponent.IQueryCliItemSearchSettingByExampleRespItem,
    successTip = t('global:save.success'),
  ) {
    const params = handleUpdateParam(row);
    const [, res] = await updateCliItemSearchSettingById(params);
    if (res?.success) {
      ElMessage.success(successTip);
      fetchCliItemSearchSettingByExample();
    }
  }

  function handleUpdateParam(
    row: CiSearchComponent.IQueryCliItemSearchSettingByExampleRespItem,
  ) {
    return {
      ciSearchGroupId: row.ciSearchGroupId,
      ciSearchGroup2ndName: row.ciSearchGroup2ndName,
      ciSearchGroupExtName: row.ciSearchGroupExtName,
      bizIdTypeCode: row.bizIdTypeCode,
      enabledFlag: row.enabledFlag,
      cisGroupEncTypeList: row.cisGroupEncTypeList.map((item) => ({
        encounterTypeCode: item.encounterTypeCode,
      })),
      cisGroupSettingList: row.cisGroupSettingList.map((item) => ({
        bizId: item.bizId,
      })),
    } as CiSearchComponent.IUpdateCliItemSearchSettingByIdParam;
  }

  /**
   * 编辑保存
   */
  async function save(
    row: CiSearchComponent.IQueryCliItemSearchSettingByExampleRespItem,
    index?: number,
  ) {
    const valid = await validateItem(row);
    if (valid) {
      updateItem(row, index);
      toggleEdit(row);
      update(row);
    }
  }

  /**
   * 删除
   */
  async function remove(ciSearchGroupId: string) {
    const [, res] = await deleteCliItemSearchSettingById({ ciSearchGroupId });
    if (res?.success) {
      ElMessage.success(t('global:delete.success'));
      fetchCliItemSearchSettingByExample();
    }
  }

  async function fetchCliItemSearchSettingByExample() {
    const [, res] = await queryCliItemSearchSettingByExample({
      componentId: componentId.value,
      componentNo: componentNo.value,
    });

    if (res?.success) {
      tableData.value = res.data ?? [];
    }
  }

  function back() {
    router.push('/');
  }

  onBeforeMount(() => {
    fetchCliItemSearchSettingByExample();
  });
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <div class="mb-2">
      <el-button link @click="back" class="align-center inline-block">
        <el-icon :size="20"> <Back /></el-icon>
        <span>{{ $t('global:back') }}</span>
      </el-button>
      <span class="mx-2 border-l border-[var(--el-border-color)]"></span>
      <span class="align-center">{{
        $t('cisOutp.ciSearchComponent.setGroup', '配置分组')
      }}</span>
    </div>
    <Title
      class="mb-3"
      :title="$t('cisOutp.ciSearchComponent.groupList', '分组列表')"
    >
      <el-button type="primary" @click="add">{{ $t('global:add') }}</el-button>
    </Title>
    <ProTable
      ref="proTable"
      :editable="true"
      :data="tableData"
      :columns="tableConfig"
    ></ProTable>
  </div>
</template>
