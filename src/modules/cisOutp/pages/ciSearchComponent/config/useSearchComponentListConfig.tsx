import { ENABLED_FLAG } from '@sun-toolkit/enums';
import { ColumnProps, useColumnConfig } from 'sun-biz';

export function useSearchComponentListConfig(actions: {
  /** 取消 */
  cancel: (
    row: CiSearchComponent.IQueryCliItemByExampleRespItem,
    index: number,
    deleteWithoutId?: boolean,
  ) => void;
  /** 确定 */
  confirm: (row: CiSearchComponent.IQueryCliItemByExampleRespItem) => void;
  /** 保存 */
  save: (
    row: CiSearchComponent.IQueryCliItemByExampleRespItem,
    index?: number,
  ) => void;
  /** 编辑 */
  edit: (
    row: CiSearchComponent.IQueryCliItemByExampleRespItem,
  ) => Promise<void>;
  /** enableFlag 转换 */
  toggleEnableFlag: (
    row: CiSearchComponent.IQueryCliItemByExampleRespItem,
  ) => void;
  /** 设置分组 */
  setGroup: (row: CiSearchComponent.IQueryCliItemByExampleRespItem) => void;
}) {
  return useColumnConfig({
    getData: (t) => {
      return [
        // 序号
        {
          type: 'selection',
          width: 65,
        },
        // 序号
        {
          label: t('global:indexNo'),
          type: 'index',
          width: 65,
        },
        // 组件名称
        {
          label: t('cisOutp.ciSearchComponent.componentDesc', '组件名称'),
          prop: 'componentDesc',
          editable: true,
          rules: [
            {
              required: true,
              trigger: 'blur',
              message: t('global:placeholder.input.template', {
                content: t(
                  'cisOutp.ciSearchComponent.componentDesc',
                  '组件名称',
                ),
              }),
            },
          ],
          render(row: CiSearchComponent.IQueryCliItemByExampleRespItem) {
            if (row.editable) {
              return (
                <>
                  <el-input
                    placeholder={t('global:placeholder.input.template', {
                      content: t(
                        'cisOutp.ciSearchComponent.componentDesc',
                        '组件名称',
                      ),
                    })}
                    v-model={row.componentDesc}
                  ></el-input>
                </>
              );
            } else {
              return row.componentDesc ?? '--';
            }
          },
        },
        // 组件编码
        {
          label: t('cisOutp.ciSearchComponent.componentNo', '组件编码'),
          prop: 'componentNo',
          editable: true,
          rules: [
            {
              required: true,
              trigger: 'blur',
              message: t('global:placeholder.input.template', {
                content: t('cisOutp.ciSearchComponent.componentNo', '组件编码'),
              }),
            },
          ],
          render(row: CiSearchComponent.IQueryCliItemByExampleRespItem) {
            if (row.editable) {
              return (
                <>
                  <el-input
                    placeholder={t('global:placeholder.input.template', {
                      content: t(
                        'cisOutp.ciSearchComponent.componentNo',
                        '组件编码',
                      ),
                    })}
                    v-model={row.componentNo}
                  ></el-input>
                </>
              );
            } else {
              return row.componentNo ?? '--';
            }
          },
        },
        // 启用标志
        {
          label: t('cisOutp.ciSearchComponent.enabledFlag', '启用标志'),
          prop: 'enabledFlag',
          width: 180,
          editable: true,
          render(row: CiSearchComponent.IQueryCliItemByExampleRespItem) {
            return (
              <div class="flex-1 justify-center">
                <el-switch
                  inline-prompt
                  v-model={row.enabledFlag}
                  active-value={ENABLED_FLAG.YES}
                  inactive-value={ENABLED_FLAG.NO}
                  active-text={t('global:enabled')}
                  inactive-text={t('global:disabled')}
                  onChange={() => {
                    if (row.componentId && !row.editable) {
                      actions.toggleEnableFlag(row);
                    }
                  }}
                />
              </div>
            );
          },
        },
        // 操作
        {
          label: t('global:operation'),
          width: 180,
          render(
            row: CiSearchComponent.IQueryCliItemByExampleRespItem,
            index: number,
          ) {
            if (row.isAddRow) {
              /** 新增 */
              return (
                <>
                  <el-button
                    type="danger"
                    link
                    onClick={() => actions.cancel(row, index)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    onClick={() => actions.confirm(row)}
                  >
                    {t('global:confirm')}
                  </el-button>
                </>
              );
            } else if (row.editable) {
              /** 编辑 */
              return (
                <>
                  <el-button
                    type="danger"
                    link
                    onClick={() => actions.cancel(row, index, false)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    onClick={() => actions.save(row, index)}
                  >
                    {t('global:save')}
                  </el-button>
                </>
              );
            } else {
              return (
                <>
                  <el-button
                    type="primary"
                    link
                    onClick={() => actions.edit(row)}
                  >
                    {t('global:edit')}
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    onClick={() => actions.setGroup(row)}
                  >
                    {t('cisOutp.ciSearchComponent.setGroup', '配置分组')}
                  </el-button>
                </>
              );
            }
          },
        },
      ] as ColumnProps[];
    },
  });
}
