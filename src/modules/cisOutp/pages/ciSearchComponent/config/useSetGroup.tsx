import {
  ENABLED_FLAG,
  ClinicalItem_Search_Source_Code_NAME,
  ClinicalItem_Search_Source_Code,
  ENCOUNTER_TYPE_CODE_NAME,
  CS_TYPE_CODE,
  CS_TYPE_CODE_NAME,
} from '@sun-toolkit/enums';
import { ColumnProps, useColumnConfig, CodeSystem } from 'sun-biz';

type ROW_TYPE = CiSearchComponent.IQueryCliItemSearchSettingByExampleRespItem;

export function useSetGroupConfig(actions: {
  toggleEnableFlag: (row: ROW_TYPE) => void;
  toggleEdit: (row: ROW_TYPE) => Promise<void>;
  confirm: (row: ROW_TYPE) => void;
  save: (row: ROW_TYPE, index?: number) => void;
  remove: (ciSearchGroupId: string) => void;
  cancel: (row: ROW_TYPE, index: number, deleteWithoutId?: boolean) => void;
}) {
  return useColumnConfig({
    dataSetCodes: [
      ClinicalItem_Search_Source_Code_NAME,
      ENCOUNTER_TYPE_CODE_NAME,
      CS_TYPE_CODE_NAME,
    ],
    getData: (t, dataSets) => {
      let searchSourceOptions: CodeSystem[] = [];
      let typeOptions: CodeSystem[] = [];
      let csTypeOptions: CodeSystem[] = [];
      let csTypeOptionsClone: CodeSystem[] = [];

      if (dataSets?.value) {
        if (dataSets.value[ClinicalItem_Search_Source_Code_NAME]) {
          searchSourceOptions =
            dataSets.value[ClinicalItem_Search_Source_Code_NAME];
        }
        if (dataSets.value[ENCOUNTER_TYPE_CODE_NAME]) {
          typeOptions = dataSets.value[ENCOUNTER_TYPE_CODE_NAME].map(
            (item) => ({
              cisGroupEncTypeId: '',
              encounterTypeCode: item.dataValueNo,
              encounterTypeCodeDesc: item.dataValueNameDisplay,
            }),
          );
        }
        if (dataSets.value[CS_TYPE_CODE_NAME]) {
          csTypeOptions = handleCsTypeOptions(
            dataSets.value[CS_TYPE_CODE_NAME],
          );
          csTypeOptionsClone = dataSets.value[CS_TYPE_CODE_NAME];
        }
      }

      function handleCsTypeOptions(list: CodeSystem[]) {
        return list.map((item) => ({
          cisGroupSettingId: '',
          bizId: item.dataValueId,
          bizName: item.dataValueNameDisplay,
        }));
      }

      /**
       * 处理选项名称集合
       * @param list 选项列表
       * @param nameProp label展示名称
       * @returns 展示名称集合
       */
      function resoveListNames<T>(list: T[], nameProp: keyof T) {
        return list.map((item) => item[nameProp]).join(', ');
      }

      /**
       * 来源类型变化，业务内容选项变化
       * @param code 来源类型
       */
      function handleSourceChange(
        code: ClinicalItem_Search_Source_Code,
        row: ROW_TYPE,
      ) {
        if (code === ClinicalItem_Search_Source_Code.APPLY_TEMPLATE) {
          // 【用血、治疗、检验、检查】
          const keys = [
            CS_TYPE_CODE.BLOOD,
            CS_TYPE_CODE.TREATMENT,
            CS_TYPE_CODE.TEST,
            CS_TYPE_CODE.EXAMINE,
          ];
          const filtered = csTypeOptionsClone.filter((item) =>
            keys.includes(item.dataValueNo),
          );
          csTypeOptions = handleCsTypeOptions(filtered);
        } else {
          csTypeOptions = handleCsTypeOptions(csTypeOptionsClone);
        }
        // 清空业务内容
        row.cisGroupSettingList = [];
      }

      return [
        // 序号
        {
          label: t('global:indexNo'),
          type: 'index',
          width: 65,
        },
        /** 分组名称 */
        {
          label: t('cisOutp.ciSearchComponent.ciSearchGroupName', '分组名称'),
          prop: 'ciSearchGroupName',
          editable: true,
          rules: [
            {
              required: true,
              trigger: 'blur',
              message: t('global:placeholder.input.template', {
                content: t(
                  'cisOutp.ciSearchComponent.ciSearchGroupName',
                  '分组名称',
                ),
              }),
            },
          ],
          render(row: ROW_TYPE) {
            if (row.editable) {
              return (
                <>
                  <el-input
                    clearable
                    placeholder={t('global:placeholder.input.template', {
                      content: t(
                        'cisOutp.ciSearchComponent.ciSearchGroupName',
                        '分组名称',
                      ),
                    })}
                    disabled={!row.isAddRow}
                    v-model={row.ciSearchGroupName}
                  ></el-input>
                </>
              );
            } else {
              return row.ciSearchGroupName ?? '--';
            }
          },
        },
        /** 辅助名称 */
        {
          label: t('global:secondName', '辅助名称'),
          prop: 'ciSearchGroup2ndName',
          editable: true,
          render(row: ROW_TYPE) {
            if (row.editable) {
              return (
                <>
                  <el-input
                    clearable
                    placeholder={t('global:placeholder.input.template', {
                      content: t('global:secondName', '辅助名称'),
                    })}
                    v-model={row.ciSearchGroup2ndName}
                  ></el-input>
                </>
              );
            } else {
              return row.ciSearchGroup2ndName ?? '--';
            }
          },
        },
        /** 扩展名称 */
        {
          label: t('global:thirdDesc', '扩展名称'),
          prop: 'ciSearchGroupExtName',
          editable: true,
          render(row: ROW_TYPE) {
            if (row.editable) {
              return (
                <>
                  <el-input
                    clearable
                    placeholder={t('global:placeholder.input.template', {
                      content: t('global:thirdDesc', '扩展名称'),
                    })}
                    v-model={row.ciSearchGroupExtName}
                  ></el-input>
                </>
              );
            } else {
              return row.ciSearchGroupExtName ?? '--';
            }
          },
        },
        /** 来源类型 */
        {
          label: t(
            'cisOutp.ciSearchComponent.ciSearchSourceCodeDesc',
            '来源类型',
          ),
          prop: 'ciSearchSourceCodeDesc',
          editable: true,
          rules: [
            {
              required: true,
              trigger: 'blur',
              message: t('global:placeholder.select.template', {
                name: t(
                  'cisOutp.ciSearchComponent.ciSearchSourceCodeDesc',
                  '来源类型',
                ),
              }),
            },
          ],
          render(row: ROW_TYPE) {
            if (row.editable) {
              return (
                <>
                  <el-select
                    clearable
                    value-key="dataValueNo"
                    placeholder={t('global:placeholder.select.template', {
                      name: t(
                        'cisOutp.ciSearchComponent.ciSearchSourceCodeDesc',
                        '来源类型',
                      ),
                    })}
                    disabled={!row.isAddRow}
                    v-model={row.ciSearchSourceCodeDesc}
                    onChange={(val: CodeSystem) => {
                      row.ciSearchSourceCodeDesc = val.dataValueNameDisplay;
                      row.ciSearchSourceCode = val.dataValueNo;
                      handleSourceChange(val.dataValueNo, row);
                    }}
                  >
                    {searchSourceOptions.map((item) => (
                      <>
                        <el-option
                          label={item.dataValueNameDisplay}
                          value={item}
                          key={item.dataValueNo}
                        ></el-option>
                      </>
                    ))}
                  </el-select>
                </>
              );
            } else {
              return row.ciSearchSourceCodeDesc ?? '--';
            }
          },
        },
        /** 业务内容 */
        {
          label: t('cisOutp.ciSearchComponent.cisGroupSettingList', '业务内容'),
          prop: 'cisGroupSettingList',
          editable: true,
          render(row: ROW_TYPE) {
            if (row.editable) {
              return (
                <>
                  <el-select
                    clearable
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                    value-key="bizId"
                    placeholder={t('global:placeholder.select.template', {
                      name: t(
                        'cisOutp.ciSearchComponent.cisGroupSettingList',
                        '业务内容',
                      ),
                    })}
                    v-model={row.cisGroupSettingList}
                  >
                    {csTypeOptions.map((item) => (
                      <>
                        <el-option
                          label={item.bizName}
                          value={item}
                          key={item.bizId}
                        ></el-option>
                      </>
                    ))}
                  </el-select>
                </>
              );
            } else {
              if (row.cisGroupSettingList?.length) {
                return resoveListNames(row.cisGroupSettingList, 'bizName');
              } else {
                return '--';
              }
            }
          },
        },
        /** 限用就诊类型 */
        {
          label: t(
            'cisOutp.ciSearchComponent.cisGroupEncTypeList',
            '限用就诊类型',
          ),
          prop: 'cisGroupEncTypeList',
          editable: true,
          rules: [
            {
              required: true,
              trigger: 'blur',
              message: t('global:placeholder.select.template', {
                name: t(
                  'cisOutp.ciSearchComponent.cisGroupEncTypeList',
                  '限用就诊类型',
                ),
              }),
            },
          ],
          render(row: ROW_TYPE) {
            if (row.editable) {
              return (
                <>
                  <el-select
                    clearable
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                    value-key="encounterTypeCode"
                    placeholder={t('global:placeholder.select.template', {
                      name: t(
                        'cisOutp.ciSearchComponent.cisGroupEncTypeList',
                        '限用就诊类型',
                      ),
                    })}
                    v-model={row.cisGroupEncTypeList}
                  >
                    {typeOptions.map((item) => (
                      <>
                        <el-option
                          label={item.encounterTypeCodeDesc}
                          value={item}
                          key={item.encounterTypeCode}
                        ></el-option>
                      </>
                    ))}
                  </el-select>
                </>
              );
            } else {
              if (row.cisGroupEncTypeList?.length) {
                return resoveListNames(
                  row.cisGroupEncTypeList,
                  'encounterTypeCodeDesc',
                );
              } else {
                return '--';
              }
            }
          },
        },
        /** 状态 */
        {
          label: t('global:status'),
          prop: 'enabledFlag',
          render(row: ROW_TYPE) {
            return (
              <>
                <div class="flex-1 justify-center">
                  <el-switch
                    inline-prompt
                    v-model={row.enabledFlag}
                    active-value={ENABLED_FLAG.YES}
                    inactive-value={ENABLED_FLAG.NO}
                    active-text={t('global:enabled')}
                    inactive-text={t('global:disabled')}
                    onChange={() => {
                      if (row.ciSearchGroupId && !row.editable) {
                        actions.toggleEnableFlag(row);
                      }
                    }}
                  />
                </div>
              </>
            );
          },
        },
        /** 操作 */
        {
          label: t('global:operation'),
          width: 120,
          render(row: ROW_TYPE, index: number) {
            if (row.isAddRow) {
              /** 新增 */
              return (
                <>
                  <el-button
                    type="danger"
                    link
                    onClick={() => actions.cancel(row, index, false)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    onClick={() => actions.confirm(row)}
                  >
                    {t('global:confirm')}
                  </el-button>
                </>
              );
            } else if (row.editable) {
              /** 编辑 */
              return (
                <>
                  <el-button
                    type="danger"
                    link
                    onClick={() => actions.cancel(row, index, false)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    onClick={() => actions.save(row)}
                  >
                    {t('global:save')}
                  </el-button>
                </>
              );
            } else {
              return (
                <>
                  <el-button
                    type="danger"
                    link
                    onClick={() => actions.remove(row.ciSearchGroupId)}
                  >
                    {t('global:delete')}
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    onClick={() => {
                      actions.toggleEdit(row);
                    }}
                  >
                    {t('global:edit')}
                  </el-button>
                </>
              );
            }
          },
        },
      ] as ColumnProps[];
    },
  });
}
