<script setup lang="ts">
  import { useResizable } from '@/hooks/useResizable';
  import ClinicalCategoryTree from './components/ClinicalCategoryTree.vue';

  const { width, onMouseDown } = useResizable(280, 270, 420);
</script>

<template>
  <div class="flex size-full">
    <!-- 服务分类 -->
    <ClinicalCategoryTree
      class="p-box h-full w-[278px] flex-shrink-0 flex-grow-0 overflow-y-hidden"
      :style="{ width: width + 'px' }"
    />
    <div
      @mousedown="onMouseDown"
      style="width: 2px"
      class="box-border cursor-ew-resize border-r border-solid border-slate-300 hover:border-r-2"
    ></div>
    <!-- 临床项目 -->
    <div class="p-box ml-4 h-full flex-auto flex-col overflow-auto pb-0">
      <router-view v-slot="{ Component, route }">
        <component
          :is="Component"
          :route="route"
          :key="route.params.csCategoryId"
        />
      </router-view>
    </div>
  </div>
</template>
