<script setup lang="ts">
  import {
    Title,
    ProForm,
    ProTable,
    DmlButton,
    type ProTableInstance,
  } from 'sun-biz';
  import { debounce } from '@sun-toolkit/shared';
  import { useSearchProForm } from './config/useSearchProForm';
  import { onUnmounted, reactive, ref, useTemplateRef, watch } from 'vue';
  import {
    usePermissionListConfig,
    useValueRangeConfig,
  } from './config/useProTableConfig';
  import { DEFAULT_PAGE_SIZE, FLAG } from '@sun-toolkit/enums';
  import { useDMLconfig } from './config/useDMLconfig';
  import { queryCliPermissionList } from '../../api/cliPermission';
  import PermissionDialog from './components/permissionDialog.vue';
  import valueRangeDialog from './components/valueRangeDialog.vue';
  import { FORM_OPERATION } from '../diagnosis/constant';
  import {
    typeOptions,
    CLI_PERMISSION_ROW_KEY,
    CLI_PERMISSION_VALUE_ROW_KEY,
  } from '../../constant';

  //  DML下载
  const {
    permissionCode,
    valueRangeCode,
    permissionBizData,
    valueRangeBizData,
  } = useDMLconfig();
  // 检索
  const searchProFormConfig = useSearchProForm();
  const searchModelOfPermisson =
    reactive<CliPermission.SearchCliPermissionsReqParam>({
      keyWord: '',
      pageSize: DEFAULT_PAGE_SIZE,
      pageNumber: FLAG.YES,
    });
  const showPermissionDialog = ref(false);
  const showValueRangeDialog = ref(false);
  const mode = ref(FORM_OPERATION.ADD);
  const isCloseDialog = ref(false);
  // 权限列表
  const permissionListConfig = usePermissionListConfig(editPermissionRow);
  const list = ref<CliPermission.Permission[]>([]);
  const permissionTableRef = useTemplateRef<ProTableInstance>(
    'cliPermissionListTable',
  );
  const permissionLoading = ref(false);
  const total = ref(0);
  const permissionEdited = ref<CliPermission.Permission>();
  const valueRangeEdited = ref<CliPermission.ValuesOfPermissionRange>();
  // 当前选中权限行
  const currentPermission = ref<CliPermission.Permission>();
  // 值域列表
  const valueRangeListConfig = useValueRangeConfig(editValueRangeRow);
  const valueRangeListLoading = ref(false);
  const valueRangeList = ref<CliPermission.ValuesOfPermissionRange[]>([]);
  // 获取权限列表
  const fetchListDebounceFn = debounce(async () => {
    permissionLoading.value = true;
    const [, res] = await queryCliPermissionList(searchModelOfPermisson);
    permissionLoading.value = false;
    if (res?.success) {
      const { data = [], total: pageTotal } = res;
      list.value = data;
      total.value = Number(pageTotal);
    }
    if (!isCloseDialog.value && list.value?.length) {
      // 默认选中第一项
      const firstItem = list.value[0];
      permissionTableRef.value?.setCurrentRow(firstItem);
      // 设置域值列表
      setCurrentPermissionValueRange(firstItem);
    } else {
      // 编辑关闭数据刷新
      const preSelectPermission = currentPermission.value; // 获取值域对应的权限行
      const filtered = list.value.filter(
        ({ cliPermissionId }) =>
          preSelectPermission?.cliPermissionId === cliPermissionId,
      );
      const editedPermissionRow = filtered[0];
      if (editedPermissionRow) {
        permissionTableRef.value?.setCurrentRow(editedPermissionRow);
        // 设置域值列表
        setCurrentPermissionValueRange(editedPermissionRow);
      }
      isCloseDialog.value = false;
    }
  }, 500);

  function setCurrentPermissionValueRange(row: CliPermission.Permission) {
    valueRangeListLoading.value = true;
    currentPermission.value = row;
    const { cliPermissionValueList = [] } = row;
    valueRangeList.value = cliPermissionValueList;
    valueRangeListLoading.value = false;
  }

  function addCliPermission() {
    showPermissionDialog.value = true;
    mode.value = FORM_OPERATION.ADD;
  }

  function handleSearchChange(form: { keyWord: string }) {
    searchModelOfPermisson.keyWord = form.keyWord;
    searchModelOfPermisson.pageNumber = FLAG.YES;
  }

  function handleRowClick(row: CliPermission.Permission) {
    setCurrentPermissionValueRange(row);
  }

  // 设置列表选中事件
  function handleSelectionChange<T, P extends keyof T>(
    selected: T[],
    rowKey: P,
  ) {
    return selected.map((item) => item[rowKey]);
  }

  function handlePermissionListSelection(selected: CliPermission.Permission[]) {
    permissionBizData.value = handleSelectionChange(
      selected,
      CLI_PERMISSION_ROW_KEY,
    );
  }

  function handleValueRangeListSelection(
    selected: CliPermission.ValuesOfPermissionRange[],
  ) {
    valueRangeBizData.value = handleSelectionChange(
      selected,
      CLI_PERMISSION_VALUE_ROW_KEY,
    );
  }

  async function editPermissionRow(row: CliPermission.Permission) {
    mode.value = FORM_OPERATION.EDIT;
    currentPermission.value = row;
    permissionEdited.value = row;
    showPermissionDialog.value = true;
  }

  function editValueRangeRow(row: CliPermission.ValuesOfPermissionRange) {
    // TODO: 编辑 域值
    mode.value = FORM_OPERATION.EDIT;
    valueRangeEdited.value = row;
    showValueRangeDialog.value = true;
  }

  // 权限/值域 表单弹窗
  function closePermissionDialog() {
    fetchListDebounceFn();
  }

  async function closeValueRangeDialog() {
    isCloseDialog.value = true;
    // 刷新列表
    fetchListDebounceFn();
  }

  watch(
    searchModelOfPermisson,
    () => {
      fetchListDebounceFn();
    },
    {
      immediate: true,
    },
  );

  onUnmounted(() => {
    // 取消防抖，防止内存泄露
    fetchListDebounceFn.cancel();
  });
</script>

<template>
  <el-row class="p-box h-full">
    <el-col :span="12" class="flex h-full flex-col border-r pr-5">
      <div class="flex justify-between">
        <Title
          :title="
            $t('baseConfig.cliPermission.permission', '权限') +
            $t('global:list')
          "
          class="mb-5 flex-1"
        >
        </Title>
        <ProForm
          @submit.prevent
          layout-mode="inline"
          v-model="searchModelOfPermisson"
          :data="searchProFormConfig"
          @model-change="handleSearchChange"
          class="relative flex-1"
        >
          <template #customButton>
            <div class="absolute right-0 top-0">
              <el-button
                class="mr-5"
                type="primary"
                @click="addCliPermission"
                >{{ $t('global:add') }}</el-button
              >
              <DmlButton
                :biz-data="permissionBizData"
                :code="permissionCode"
              ></DmlButton>
            </div>
          </template>
        </ProForm>
      </div>
      <!-- 列表栏 -->
      <ProTable
        ref="cliPermissionListTable"
        highlight-current-row
        :data="list"
        :pagination="true"
        :loading="permissionLoading"
        :columns="permissionListConfig"
        :row-key="CLI_PERMISSION_ROW_KEY"
        :page-info="{
          total,
          pageNumber: searchModelOfPermisson.pageNumber,
          pageSize: searchModelOfPermisson.pageSize,
        }"
        @current-page-change="
          (pageNumber: number) => {
            searchModelOfPermisson.pageNumber = pageNumber;
          }
        "
        @size-page-change="
          (pageSize: number) => {
            searchModelOfPermisson.pageSize = pageSize;
          }
        "
        @selection-change="handlePermissionListSelection"
        @row-click="handleRowClick"
      ></ProTable>
    </el-col>
    <el-col class="flex h-full flex-col overflow-hidden pl-5" :span="12">
      <Title
        :title="
          $t('baseConfig.cliPermission.range', '值域') + $t('global:list')
        "
        class="mb-5"
      >
      </Title>
      <div class="absolute right-2 top-2">
        <DmlButton
          :biz-data="valueRangeBizData"
          :code="valueRangeCode"
        ></DmlButton>
      </div>
      <!-- 值域列表 -->
      <ProTable
        :data="valueRangeList"
        :loading="valueRangeListLoading"
        :columns="valueRangeListConfig"
        :row-key="CLI_PERMISSION_VALUE_ROW_KEY"
        @selection-change="handleValueRangeListSelection"
      ></ProTable>
    </el-col>
  </el-row>
  <PermissionDialog
    v-if="showPermissionDialog"
    v-model:show="showPermissionDialog"
    v-model:mode="mode"
    :row-edited="permissionEdited!"
    :type-options="typeOptions"
    @close-dialog="closePermissionDialog"
  ></PermissionDialog>
  <valueRangeDialog
    v-if="showValueRangeDialog"
    v-model:show="showValueRangeDialog"
    v-model:mode="mode"
    :row-edited="valueRangeEdited!"
    @close-dialog="closeValueRangeDialog"
  ></valueRangeDialog>
</template>
<style>
  micro-app-body {
    height: 100%;
  }
</style>
