<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('Dictionary of physical models', '物理模型字典')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          v-model="searchParams"
          :column="5"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="queryComputerIndexList"
        />
        <el-button
          class="mr-1"
          type="primary"
          @click="
            () => {
              queryComputerIndexList();
            }
          "
        >
          {{ $t('global:search', '查询') }}
        </el-button>
        <el-button
          :disabled="!isCloudEnv"
          type="primary"
          @click="onOpenComputerIndexDialog()"
        >
          {{ $t('global:add') }}
        </el-button>
      </div>
      <div>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_PHYSICAL_MODEL"
          @success="
            () => {
              computerIndexTableRef?.proTableRef.clearSelection();
              selections = [];
            }
          "
        />
      </div>
    </div>
    <ProTable
      ref="computerIndexTableRef"
      :columns="tableColumns"
      :data="computerIndexList"
      :editable="true"
      :loading="loading"
      :page-info="{
        pageNumber: searchParams.pageNumber,
        pageSize: searchParams.pageSize,
        total: total,
      }"
      :pagination="true"
      row-key="physicalModelId"
      @selection-change="handleSelectChange"
      @current-page-change="
        (val: number) => {
          searchParams.pageNumber = val;
          queryComputerIndexList({ pageNumber: val });
        }
      "
      @size-page-change="
        (val: number) => {
          searchParams.pageSize = val;
          searchParams.pageNumber = 1;
          queryComputerIndexList({ pageSize: val, pageNumber: 1 });
        }
      "
    />
  </div>
</template>

<script lang="ts" name="computerIndex" setup>
  import { computed, nextTick, onMounted, ref } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { BIZ_ID_TYPE_CODE, ENABLED_FLAG } from '@/utils/constant';
  import { UsephysicalSearchConfig } from './config/usephysicalSearchFormConfig.tsx';
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    Title,
    useAppConfigData,
  } from 'sun-biz';

  import {
    PhysicalModelByExampleParams,
    PhysicalModelFormReq,
  } from '@/modules/cisOutp/typing/physicalmode';
  import {
    deletePhysicalModel,
    queryPhysicalModelByExample,
  } from '@/modules/cisOutp/api/physicalModelDict.ts';
  import { usephytonTableConfig } from '@/modules/cisOutp/pages/physicalModelDict/config/usephytonTableConfig.tsx';
  import { DEFAULT_PAGE_SIZE } from '@sun-toolkit/enums';

  const { t } = useTranslation();

  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const searchParams = ref<PhysicalModelByExampleParams>({
    keyWord: '',
    enabledFlag: ENABLED_FLAG.ALL,
    syncFlag: ENABLED_FLAG.ALL,
    pageNumber: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    tagId: '',
    clearTabTypeCodeInit: '',
    clearTabTypeCodeOnline: '',
  });
  const loading = ref(false);
  const computerIndexList = ref<PhysicalModelFormReq[]>([]);
  const total = ref(0);
  const computerIndexTableRef = ref();
  const selections = ref<PhysicalModelFormReq[]>([]);

  const bizData = computed(() => {
    return selections.value.map((item) => {
      return item.physicalModelId || '';
    });
  });

  const queryComputerIndexList = async (
    data?: PhysicalModelByExampleParams,
  ) => {
    loading.value = true;
    if (data) {
      searchParams.value = {
        ...searchParams.value,
        ...data,
      };
    }
    const params = {
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
      syncFlag:
        searchParams.value.syncFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.syncFlag,
      tagId: Array.isArray(searchParams.value.tagId)
        ? searchParams.value.tagId.join(',')
        : (searchParams.value.tagId ?? undefined),
    };

    const [, res] = await queryPhysicalModelByExample(params);
    loading.value = false;
    if (res?.success) {
      computerIndexList.value = res.data ?? [];
      computerIndexList.value.forEach((el) => {
        el.tagId = Array.isArray(el.bizTagId)
          ? el.bizTagId.map((item) => item.tagId)
          : [];
      });
      total.value = res?.total;
    }
  };

  const onOpenComputerIndexDialog = async () => {
    const isEditing = computerIndexList.value.some((item) => !!item.editable);

    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    } else {
      // 生成临时 key
      const tempKey = Symbol('newRow');
      const maybeRow = await addItem({
        editable: true,
        enabledFlag: ENABLED_FLAG.YES,
        syncFlag: ENABLED_FLAG.NO,
        __tempKey: tempKey,
      });
      // fallback: 找 editable 行
      let key = maybeRow?.physicalModelId || maybeRow?.__tempKey;
      if (!key) {
        const editableRow = computerIndexList.value.find(
          (item) => item.editable,
        );
        key = editableRow?.physicalModelId || editableRow?.__tempKey;
      }
      nextTick(() => {
        if (key) focusRowInput(key);
      });
    }
  };

  const handleSelectChange = (value: PhysicalModelFormReq[]) => {
    selections.value = value || [];
  };

  const searchConfig = UsephysicalSearchConfig(queryComputerIndexList);

  // 删除
  const deleteAppEnvByID = async (row: PhysicalModelFormReq): Promise<void> => {
    return new Promise<void>((resolve) => {
      ElMessageBox.confirm(
        t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
          action: t('global:delete'),
          name: row.physicalModelName,
        }),
        t('global:tip'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      )
        .then(async () => {
          const [, res] = await deletePhysicalModel({
            physicalModelId: row.physicalModelId || '',
          });
          if (res?.success) {
            ElMessage.success(t('The deletion is successful', '删除成功'));
            selections.value = [];
            queryComputerIndexList();
          }
          resolve();
        })
        .catch(() => {
          resolve();
        });
    });
  };
  const { tableColumns, addItem, focusRowInput } = usephytonTableConfig(
    isCloudEnv,
    computerIndexTableRef,
    computerIndexList,
    deleteAppEnvByID,
    queryComputerIndexList,
  );

  onMounted(async () => {
    await queryComputerIndexList();
  });
</script>
