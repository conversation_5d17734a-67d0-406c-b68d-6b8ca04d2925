import { ref } from 'vue';
import { AdminFreqTimeResItem } from '@/modules/cisOutp/typing/adminRouteFreq';
import { queryAdminFreqTimes } from '@/modules/cisOutp/api/adminRouteFreq';

export function useGetAdminFreqTimes() {
  const loading = ref<boolean>(false);
  const adminFreqTimesList = ref<AdminFreqTimeResItem[]>([]);
  const adminFreqTimesOptionsList = ref<
    (AdminFreqTimeResItem & {
      label: string;
      value: string;
    })[]
  >([]);

  const getAdminFreqTimes = async () => {
    loading.value = true;
    const [, res] = await queryAdminFreqTimes();
    loading.value = false;
    if (res?.success) {
      adminFreqTimesList.value = res?.data ?? [];
      adminFreqTimesOptionsList.value = (res?.data ?? [])?.map((item) => {
        return {
          ...item,
          label: item.execTimeDesc,
          value: item.execTimeId,
        };
      });
    }
  };

  return {
    loading,
    adminFreqTimesList,
    adminFreqTimesOptionsList,
    getAdminFreqTimes,
  };
}
