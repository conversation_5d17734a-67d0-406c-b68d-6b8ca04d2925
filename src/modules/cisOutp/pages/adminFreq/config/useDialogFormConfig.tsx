import { Ref } from 'vue';
import { useFormConfig } from 'sun-biz';
import {
  FLAG,
  ENCOUNTER_TYPE_CODE_NAME,
  ORDER_FREQ_TYPE_CODE_NAME,
  ADMIN_FREQ_TYPE_CODE_NAME,
} from '@/utils/constant';
import {
  AdminFreqResItem,
  AdminFreqTimeResItem,
} from '@/modules/cisOutp/typing/adminRouteFreq.ts';
import { FromModelType } from '../components/AddAndEditDialog.vue';
import CycleType from '../components/cycleType.vue';

export function useDetailFormConfig(options: {
  formModel: Ref<FromModelType>;
  isCloudEnv: boolean;
  isAdd: Ref<boolean>;
  currentRow: Ref<AdminFreqResItem | undefined>;
  adminFreqTimesOptionsList: Ref<
    (AdminFreqTimeResItem & {
      label: string;
      value: string;
    })[]
  >;
  handleNameBlur: (value: string) => void;
}) {
  const {
    formModel,
    isCloudEnv,
    isAdd,
    currentRow,
    adminFreqTimesOptionsList,
    handleNameBlur,
  } = options;

  const data = useFormConfig({
    dataSetCodes: [
      ENCOUNTER_TYPE_CODE_NAME,
      ORDER_FREQ_TYPE_CODE_NAME,
      ADMIN_FREQ_TYPE_CODE_NAME,
    ],
    getData: (t, dataSet) => [
      {
        label: t('global:name'),
        name: 'adminFreqName',
        component: 'input',
        autoConvertSpellNoAndWbNo: true,
        placeholder: t('global:placeholder.input.template', {
          content: t('global:name'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('global:name'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          clearable: true,
          disabled: !isCloudEnv && !isAdd.value,
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              handleNameBlur((e.target as HTMLInputElement)?.value);
            }
          },
          onBlur: (e: Event) => {
            handleNameBlur((e.target as HTMLInputElement)?.value);
          },
        },
      },
      {
        label: t('displayName', '显示名称'),
        name: 'adminFreqDisplayName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('displayName', '显示名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('displayName', '显示名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          clearable: true,
          disabled:
            !isCloudEnv &&
            !isAdd.value &&
            currentRow.value?.editableFlag === FLAG.NO,
        },
      },
      {
        label: t('global:secondName'),
        name: 'adminFreq2ndName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:secondName'),
        }),
        extraProps: {
          clearable: true,
          disabled:
            !isCloudEnv &&
            !isAdd.value &&
            currentRow.value?.editableFlag === FLAG.NO,
        },
      },
      {
        label: t('global:thirdName'),
        name: 'adminFreqExtName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:thirdName'),
        }),
        extraProps: {
          clearable: true,
          disabled:
            !isCloudEnv &&
            !isAdd.value &&
            currentRow.value?.editableFlag === FLAG.NO,
        },
      },
      {
        label: t('global:wbNo'),
        name: 'wbNo',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:wbNo'),
        }),
        extraProps: {
          clearable: true,
          disabled:
            !isCloudEnv &&
            !isAdd.value &&
            currentRow.value?.editableFlag === FLAG.NO,
        },
      },
      {
        label: t('global:spellNo'),
        name: 'spellNo',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:spellNo'),
        }),
        extraProps: {
          clearable: true,
          disabled:
            !isCloudEnv &&
            !isAdd.value &&
            currentRow.value?.editableFlag === FLAG.NO,
        },
      },
      {
        label: t('adminFreqTypeCode', '周期类型'),
        name: 'adminFreqTypeCode',
        isFullWidth: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('adminFreqTypeCode', '周期类型'),
            }),
            trigger: ['change', 'blur'],
          },
          {
            validator: (
              _: unknown,
              __: unknown,
              callback: (error?: Error) => void,
            ) => {
              const {
                adminFreqTypeCode,
                adminFreqExecTimes,
                adminFreqCycle,
                adminFreqWeekdayList,
              } = formModel.value;
              if (!adminFreqTypeCode) {
                callback(
                  new Error(
                    t('global:placeholder.select.template', {
                      name: t('adminFreqTypeCode', '周期类型'),
                    }),
                  ),
                );
              } else if (!adminFreqCycle) {
                callback(
                  new Error(
                    t('global:placeholder.input.template', {
                      content: t('adminFreqCycle', '执行周期'),
                    }),
                  ),
                );
              } else if (!adminFreqExecTimes) {
                callback(
                  new Error(
                    t('global:placeholder.input.template', {
                      content: t('adminFreqExecTimes', '执行次数'),
                    }),
                  ),
                );
              } else if (
                adminFreqTypeCode === 'WEEK' &&
                adminFreqWeekdayList &&
                adminFreqWeekdayList.length > Number(adminFreqExecTimes)
              ) {
                callback(
                  new Error(
                    t(
                      'adminFreqWeekdayList.valid',
                      `选择星期天数不能大于${adminFreqExecTimes}天`,
                    ),
                  ),
                );
              } else {
                callback();
              }
            },
            trigger: ['change', 'blur'],
          },
        ],
        placeholder: t('global:placeholder.select.template', {
          name: t('adminFreqTypeCode', '周期类型'),
        }),
        render: () => {
          return (
            <CycleType
              codeList={
                dataSet?.value ? dataSet.value[ADMIN_FREQ_TYPE_CODE_NAME] : []
              }
              disabled={!isCloudEnv && !isAdd.value}
              weekDisabled={
                !isCloudEnv &&
                !isAdd.value &&
                currentRow.value?.editableFlag === FLAG.NO
              }
              currentRow={currentRow.value}
              onCode-change={(data: string) => {
                formModel.value = {
                  ...formModel.value,
                  adminFreqTypeCode: data,
                };
              }}
              onTimes-change={(data: number) => {
                formModel.value = {
                  ...formModel.value,
                  adminFreqExecTimes: data,
                };
              }}
              onCycle-change={(data: number) => {
                formModel.value = {
                  ...formModel.value,
                  adminFreqCycle: data,
                };
              }}
              onWeek-change={(data: number[]) => {
                formModel.value = {
                  ...formModel.value,
                  adminFreqWeekdayList: data,
                };
              }}
            ></CycleType>
          );
        },
      },
      {
        label: t('adminFreqEncTypeList', '适用范围'),
        name: 'adminFreqEncTypeList',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('adminFreqEncTypeList', '适用范围'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('adminFreqEncTypeList', '适用范围'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          clearable: true,
          filterable: true,
          multiple: true,
          collapseTags: true,
          collapseTagsTooltip: true,
          maxCollapseTags: 2,
          disabled:
            !isCloudEnv &&
            !isAdd.value &&
            currentRow.value?.editableFlag === FLAG.NO,
          options: dataSet?.value
            ? dataSet.value[ENCOUNTER_TYPE_CODE_NAME]
            : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
      {
        label: t('adminFreqOrderTypeList', '医嘱时效类型'),
        name: 'adminFreqOrderTypeList',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('adminFreqOrderTypeList', '医嘱时效类型'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('adminFreqOrderTypeList', '医嘱时效类型'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          clearable: true,
          multiple: true,
          filterable: true,
          collapseTags: true,
          collapseTagsTooltip: true,
          maxCollapseTags: 2,
          disabled: !isCloudEnv && !isAdd.value,
          options: dataSet?.value
            ? dataSet.value[ORDER_FREQ_TYPE_CODE_NAME]
            : [],
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
          // disabled: !isCloudEnv && !isAdd.value,
        },
      },
      {
        name: 'editableFlag',
        label: t('allowEdit', '允许编辑'),
        component: 'checkbox',
        isHidden: !isCloudEnv,
        extraProps: {
          disabled: !isCloudEnv && !isAdd.value,
          'true-value': FLAG.YES,
          'false-value': FLAG.NO,
        },
      },
      {
        name: 'adminFreqTimeList',
        label: t('adminFreqTimeList', '执行时间'),
        component: 'checkbox-group',
        isFullWidth: true,
        rules: [
          {
            required: !(formModel.value?.adminFreqOrderTypeList ?? [])?.every(
              (type: string) => ['ASNEED', 'PRN', 'SOS'].includes(type),
            ),
            message: t('global:placeholder.select.template', {
              name: t('adminFreqTimeList', '执行时间'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          className: 'grid grid-cols-6 gap-2 w-full',
          disabled:
            !isCloudEnv &&
            !isAdd.value &&
            currentRow.value?.editableFlag === FLAG.NO,
          options: adminFreqTimesOptionsList.value,
        },
      },
    ],
  });
  return data;
}
