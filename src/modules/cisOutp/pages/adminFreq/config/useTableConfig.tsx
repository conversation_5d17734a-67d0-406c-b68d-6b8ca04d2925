import { useColumnConfig } from 'sun-biz';
import { FLAG, ADMIN_FREQ_TYPE_CODE } from '@/utils/constant';
import { AdminFreqResItem } from '@/modules/cisOutp/typing/adminRouteFreq.ts';

export function useTableConfig(options: {
  isCloudEnv: boolean;
  handleEnableSwitch: (row: AdminFreqResItem) => Promise<void>;
  handleEdit: (row: AdminFreqResItem) => Promise<void>;
  handleDelete: (row: AdminFreqResItem) => Promise<void>;
}) {
  const {
    //  isCloudEnv,
    handleEnableSwitch,
    handleEdit,
    handleDelete,
  } = options;

  const data = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t('global:indexNo'),
        prop: 'indexNumber',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('global:name'),
        prop: 'adminFreqName',
        minWidth: 100,
      },
      {
        label: t('cisOut.adminFreq.adminFreqDisplayName', '显示名称'),
        prop: 'adminFreqDisplayName',
        minWidth: 100,
      },
      {
        label: t('global:secondName'),
        prop: 'adminFreq2ndName',
        minWidth: 100,
      },
      {
        label: t('global:thirdName'),
        prop: 'adminFreqExtName',
        minWidth: 100,
      },
      {
        label: t('cisOut.adminFreq.adminFreqExecTimes', '周期/执行数'),
        prop: 'timesAndTypeCode',
        minWidth: 120,
        render: (row: AdminFreqResItem) => {
          return (
            <div class={'w-full truncate text-center'}>
              {`${row.adminFreqCycle || '--'}${
                (row.adminFreqTypeCode === ADMIN_FREQ_TYPE_CODE.DAY
                  ? t('day', '天')
                  : row.adminFreqTypeCode === ADMIN_FREQ_TYPE_CODE.WEEK
                    ? t('week', '周')
                    : row.adminFreqTypeCode === ADMIN_FREQ_TYPE_CODE.HOUR
                      ? t('hour', '小时')
                      : '') || ''
              }/${row.adminFreqExecTimes || '--'}${t('per', '次')}`}
            </div>
          );
        },
      },
      {
        label: t('cisOut.adminFreq.weekDay', '星期'),
        prop: 'adminFreqWeekdayList',
        minWidth: 150,
        render: (row: AdminFreqResItem) => {
          return (
            <div class={'w-full truncate text-center'}>
              {row?.adminFreqWeekdayList
                ?.map((item) => item.weekDayDesc)
                .join('，')}
            </div>
          );
        },
      },
      {
        label: t('cisOut.adminFreq.execTime', '执行时间'),
        prop: 'adminFreqTimeList',
        minWidth: 150,
        render: (row: AdminFreqResItem) => {
          return (
            <div class={'w-full truncate text-center'}>
              {row?.adminFreqTimeList
                ?.map((item) => item.execTimeDesc)
                .join('，')}
            </div>
          );
        },
      },
      {
        label: t('cisOut.adminFreq.encounterTypeCode', '适用范围'),
        prop: 'adminFreqEncTypeList',
        minWidth: 150,
        render: (row: AdminFreqResItem) => {
          return (
            <div class={'w-full truncate text-center'}>
              {row?.adminFreqEncTypeList
                ?.map((item) => item.encounterTypeCodeDesc)
                .join('，')}
            </div>
          );
        },
      },
      {
        label: t('cisOut.adminFreq.adminFreqTypeCode', '医嘱时效类型'),
        prop: 'adminFreqOrderTypeList',
        minWidth: 150,
        render: (row: AdminFreqResItem) => {
          return (
            <div class={'w-full truncate text-center'}>
              {row?.adminFreqOrderTypeList
                ?.map((item) => item.orderFreqTypeCodeDesc)
                .join('，')}
            </div>
          );
        },
      },
      {
        label: t('global:spellNo'),
        prop: 'spellNo',
        minWidth: 100,
      },
      {
        label: t('global:wbNo'),
        prop: 'wbNo',
        minWidth: 100,
      },
      {
        label: t('cisOut.adminFreq.allowEdit', '允许编辑'),
        prop: 'editableFlag',
        minWidth: 150,
        render: (row: AdminFreqResItem) => {
          return (
            <el-checkbox modelValue={!!row.editableFlag} disabled={true} />
          );
        },
      },
      {
        label: t('global:status'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: AdminFreqResItem) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              // disabled={!isCloudEnv}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 150,
        render: (row: AdminFreqResItem) => {
          return (
            <div class="flex justify-around">
              <el-button
                key="edit"
                type={'primary'}
                link={true}
                onClick={() => handleEdit(row)}
              >
                {t('global:edit')}
              </el-button>
              <el-button
                key="edit"
                type={'danger'}
                link={true}
                onClick={() => handleDelete(row)}
              >
                {t('global:delete')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return data;
}
