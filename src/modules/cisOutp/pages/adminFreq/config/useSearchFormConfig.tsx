import { useFormConfig } from 'sun-biz';
import { ENCOUNTER_TYPE_CODE_NAME } from '@/utils/constant';

export function useSearchFormConfig(options: {
  blurFn: (keyWord?: string) => Promise<void>;
  handleInput: (value: string) => Promise<void>;
}) {
  const { blurFn, handleInput } = options;

  const data = useFormConfig({
    dataSetCodes: [ENCOUNTER_TYPE_CODE_NAME],
    getData: (t, dataSet) => [
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        className: 'mb-0',
        extraProps: {
          clearable: false,
          className: 'w-32',
        },
      },
      {
        label: t('cisOutp.adminFreq.encounterTypeCodes', '适用范围'),
        name: 'encounterTypeCodes',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('cisOutp.adminFreq.encounterTypeCodes', '适用范围'),
        }),
        className: 'mb-0',
        triggerModelChange: true,
        extraProps: {
          className: 'w-60',
          filterable: true,
          clearable: true,
          multiple: true,
          collapseTags: true,
          maxCollapseTags: 2,
          collapseTagsTooltip: true,
          options: dataSet?.value
            ? dataSet.value[ENCOUNTER_TYPE_CODE_NAME]
            : [],
          props: {
            value: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-52 mb-0',
        extraProps: {
          suffixIcon: 'Search',
          onkeydown: async (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              (e.target as HTMLInputElement)?.blur();
              await blurFn((e.target as HTMLInputElement)?.value);
            }
          },
          onInput: async (value: string) => handleInput(value),
          onClear: async () => {
            await blurFn();
          },
        },
      },
    ],
  });
  return data;
}
