<script setup lang="ts" name="cycleType">
  import { ref, computed } from 'vue';
  import { CodeSystem } from '@/typings/codeManage';
  import { useTranslation } from 'i18next-vue';
  import { ADMIN_FREQ_TYPE_CODE } from '@/utils/constant';
  import { AdminFreqResItem } from '@/modules/cisOutp/typing/adminRouteFreq.ts';

  const emits = defineEmits([
    'code-change',
    'times-change',
    'cycle-change',
    'week-change',
  ]);

  const weekList = [
    {
      label: '周一',
      value: 1,
    },
    {
      label: '周二',
      value: 2,
    },
    {
      label: '周三',
      value: 3,
    },
    {
      label: '周四',
      value: 4,
    },
    {
      label: '周五',
      value: 5,
    },
    {
      label: '周六',
      value: 6,
    },
    {
      label: '周日',
      value: 7,
    },
  ];
  const {
    codeList,
    currentRow,
    disabled = false,
    weekDisabled = false,
  } = defineProps<{
    disabled: boolean;
    weekDisabled?: boolean;
    codeList: CodeSystem[];
    currentRow?: AdminFreqResItem;
  }>();

  const { t } = useTranslation();

  const codeValue = ref(
    currentRow?.adminFreqTypeCode ?? ADMIN_FREQ_TYPE_CODE.DAY,
  );
  const cycleValue = ref(currentRow?.adminFreqCycle);
  const timesValue = ref(currentRow?.adminFreqExecTimes);
  const weekValue = ref(
    currentRow?.adminFreqWeekdayList?.map((item) => item.weekDay) || [],
  );

  const cycleUnitName = computed(() => {
    switch (codeValue.value) {
      case ADMIN_FREQ_TYPE_CODE.DAY:
        return t('day', '天');
      case ADMIN_FREQ_TYPE_CODE.WEEK:
        return t('week', '周');
      case ADMIN_FREQ_TYPE_CODE.HOUR:
        return t('hour', '小时');
      default:
        return '';
    }
  });
</script>
<template>
  <div class="flex w-full items-center gap-2">
    <el-select
      class="w-1/4"
      filterable
      clearable
      :disabled="disabled"
      @change="emits('code-change', codeValue)"
      v-model="codeValue"
      :placeholder="
        $t('global:placeholder.select.template', {
          name: $t('adminFreqTypeCode', '周期类型'),
        })
      "
    >
      <el-option
        v-for="item in codeList"
        :key="item.dataValueNo"
        :label="item.dataValueNameDisplay"
        :value="item.dataValueNo"
      ></el-option>
    </el-select>
    <el-input-number
      class="w-1/6"
      :disabled="disabled"
      v-model="cycleValue"
      controls-position="right"
      @change="emits('cycle-change', cycleValue)"
    >
      <template #suffix>
        <span>{{ cycleUnitName }}</span>
      </template>
    </el-input-number>
    <el-input-number
      class="w-1/6"
      :disabled="disabled"
      v-model="timesValue"
      controls-position="right"
      @change="emits('times-change', timesValue)"
    >
      <template #suffix>
        <span>{{ $t('freq', '次') }}</span>
      </template>
    </el-input-number>
    <div
      class="flex w-1/3 flex-1 items-center gap-2"
      v-if="codeValue === ADMIN_FREQ_TYPE_CODE.WEEK"
    >
      <div class="whitespace-nowrap">{{ $t('week', '星期') }}</div>
      <el-select
        :disabled="weekDisabled"
        @change="emits('week-change', weekValue)"
        class="flex-1"
        v-model="weekValue"
        filterable
        multiple
        collapse-tags
        collapse-tags-tooltip
        :max-collapse-tags="2"
        clearable
        :placeholder="
          $t('global:placeholder.select.template', {
            name: $t('adminFreqTypeCode', '星期'),
          })
        "
      >
        <el-option
          v-for="item in weekList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </div>
  </div>
</template>
