<script setup lang="ts" name="AddAndEditDialog">
  import { ElMessage } from 'element-sun';
  import { ref, computed, nextTick } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { FLAG, ADMIN_FREQ_TYPE_CODE } from '@/utils/constant';
  import { saveAdminFreq } from '@/modules/cisOutp/api/adminRouteFreq';
  import { useGetAdminFreqTimes } from '../hooks/useGetAdminFreqTimes';
  import { AdminFreqResItem } from '@/modules/cisOutp/typing/adminRouteFreq.ts';
  import { useDetailFormConfig } from '../config/useDialogFormConfig';
  import {
    ProForm,
    ProDialog,
    useAppConfigData,
    MAIN_APP_CONFIG,
  } from 'sun-biz';

  const emits = defineEmits(['success']);
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { adminFreqTimesOptionsList, getAdminFreqTimes } =
    useGetAdminFreqTimes();
  const { t } = useTranslation();

  /** formModel类型 */
  export type FromModelType = {
    adminFreqName?: string;
    adminFreqDisplayName?: string;
    adminFreq2ndName?: string;
    adminFreqExtName?: string;
    wbNo?: string;
    spellNo?: string;
    editableFlag?: number;
    enabledFlag?: number;
    adminFreqTypeCode?: string;
    adminFreqCycle?: number;
    adminFreqExecTimes?: number;
    adminFreqWeekdayList?: number[];
    adminFreqOrderTypeList?: string[];
    adminFreqEncTypeList?: string[];
    adminFreqTimeList?: string[];
  };

  const dialogRef = ref();
  const formRef = ref();
  const currentRow = ref<AdminFreqResItem>();
  const formModel = ref<FromModelType>({
    adminFreqName: undefined,
    adminFreqDisplayName: undefined,
    adminFreq2ndName: undefined,
    adminFreqExtName: undefined,
    wbNo: undefined,
    spellNo: undefined,
    editableFlag: FLAG.YES,
    enabledFlag: FLAG.YES,
    adminFreqTypeCode: ADMIN_FREQ_TYPE_CODE.DAY,
    adminFreqCycle: undefined,
    adminFreqExecTimes: undefined,
    adminFreqWeekdayList: undefined,
    adminFreqOrderTypeList: undefined,
    adminFreqEncTypeList: undefined,
    adminFreqTimeList: undefined,
  });

  /** 是否新增 */
  const isAdd = computed(() => {
    if (currentRow.value) return false;
    return true;
  });

  /** 弹窗title */
  const dialogTitle = computed(() => {
    let title;
    if (currentRow.value?.adminFreqId) {
      title =
        t('edit.adminFreq', '编辑给药频次') +
        ' ' +
        (currentRow.value?.adminFreqName ?? '');
    } else {
      title = t('add.adminFreq', '新增给药频次');
    }
    return title;
  });

  /** 初始化数据 */
  const initData = async () => {
    formModel.value = {
      adminFreqName: currentRow.value?.adminFreqName ?? undefined,
      adminFreqDisplayName: currentRow.value?.adminFreqDisplayName ?? undefined,
      adminFreq2ndName: currentRow.value?.adminFreq2ndName ?? undefined,
      adminFreqExtName: currentRow.value?.adminFreqExtName ?? undefined,
      wbNo: currentRow.value?.wbNo ?? undefined,
      spellNo: currentRow.value?.spellNo ?? undefined,
      editableFlag: currentRow.value?.editableFlag ?? FLAG.YES,
      enabledFlag: currentRow.value?.enabledFlag ?? FLAG.YES,
      adminFreqTypeCode:
        currentRow.value?.adminFreqTypeCode ?? ADMIN_FREQ_TYPE_CODE.DAY,
      adminFreqCycle: currentRow.value?.adminFreqCycle ?? undefined,
      adminFreqExecTimes: currentRow.value?.adminFreqExecTimes ?? undefined,
      adminFreqWeekdayList:
        (currentRow.value?.adminFreqWeekdayList ?? []).map(
          (item) => item.weekDay,
        ) ?? undefined,
      adminFreqOrderTypeList:
        (currentRow.value?.adminFreqOrderTypeList ?? []).map(
          (item) => item.adminFreqTypeCode,
        ) ?? undefined,
      adminFreqEncTypeList:
        (currentRow.value?.adminFreqEncTypeList ?? []).map(
          (item) => item.encounterTypeCode,
        ) ?? undefined,
      adminFreqTimeList:
        (currentRow.value?.adminFreqTimeList ?? []).map(
          (item) => item.execTimeId,
        ) ?? undefined,
    };
  };

  /** 打开弹窗 */
  const openDialog = async (row: AdminFreqResItem) => {
    currentRow.value = row;
    dialogRef.value?.open();
    nextTick(async () => {
      await initData();
      await getAdminFreqTimes();
    });
  };

  /** 关闭弹窗 */
  const handleClose = async () => {
    formRef.value.ref?.resetFields();
    formRef.value?.ref.clearValidate();
  };

  /** 输入框失去焦点 */
  const handleNameBlur = (value: string) => {
    if (value && !formModel.value.adminFreqDisplayName) {
      formModel.value = {
        ...formModel.value,
        adminFreqDisplayName: value,
      };
    }
  };

  /** 保存 */
  const handleSubmit = async () => {
    const isValid = await formRef.value?.ref?.validate();
    if (!isValid) return;

    const params = {
      adminFreqId: isAdd.value ? undefined : currentRow.value?.adminFreqId,
      adminFreqName: formModel.value?.adminFreqName as string,
      adminFreqDisplayName: formModel.value?.adminFreqDisplayName as string,
      adminFreq2ndName: formModel.value?.adminFreq2ndName,
      adminFreqExtName: formModel.value?.adminFreqExtName,
      adminFreqTypeCode: formModel.value?.adminFreqTypeCode as string,
      adminFreqExecTimes: formModel.value?.adminFreqExecTimes as number,
      adminFreqCycle: formModel.value?.adminFreqCycle as number,
      wbNo: formModel.value?.wbNo,
      spellNo: formModel.value?.spellNo,
      enabledFlag: formModel.value?.enabledFlag as number,
      editableFlag: formModel.value?.editableFlag as number,
      sort: isAdd.value ? undefined : currentRow.value?.sort,
      adminFreqWeekdayList: (formModel.value?.adminFreqTypeCode ===
      ADMIN_FREQ_TYPE_CODE.WEEK
        ? formModel.value?.adminFreqWeekdayList?.map((weekDay: number) => {
            const existingItem = currentRow.value?.adminFreqWeekdayList?.find(
              (item) => item.weekDay === weekDay,
            );
            return existingItem
              ? {
                  weekDay: weekDay,
                  adminFreqWeekdayId: existingItem.adminFreqWeekdayId,
                }
              : { weekDay: weekDay };
          })
        : undefined) as {
        weekDay: number;
      }[],
      adminFreqOrderTypeList: formModel.value?.adminFreqOrderTypeList?.map(
        (adminFreqTypeCode: string) => {
          const existingItem = currentRow.value?.adminFreqOrderTypeList?.find(
            (item) => item.adminFreqTypeCode === adminFreqTypeCode,
          );
          return existingItem
            ? {
                adminFreqTypeCode: adminFreqTypeCode,
                adminFreqWeekdayId: existingItem.adminFreqOrderTypeId,
              }
            : { adminFreqTypeCode: adminFreqTypeCode };
        },
      ) as {
        adminFreqTypeCode: string;
      }[],
      adminFreqEncTypeList: formModel.value?.adminFreqEncTypeList?.map(
        (encounterTypeCode: string) => {
          // 检查当前 weekDay 是否存在于 currentRow 的原始数据中
          const existingItem = currentRow.value?.adminFreqEncTypeList?.find(
            (item) => item.encounterTypeCode === encounterTypeCode,
          );
          return existingItem
            ? {
                encounterTypeCode: encounterTypeCode,
                adminFreqEncTypeId: existingItem.adminFreqEncTypeId,
              }
            : { encounterTypeCode: encounterTypeCode };
        },
      ) as {
        encounterTypeCode: string;
      }[],
      adminFreqTimeList: formModel.value?.adminFreqTimeList?.map(
        (execTimeId: string) => {
          // 检查当前 weekDay 是否存在于 currentRow 的原始数据中
          const existingItem = currentRow.value?.adminFreqTimeList?.find(
            (item) => item.execTimeId === execTimeId,
          );
          return existingItem
            ? {
                execTimeId: execTimeId,
                adminFreqTimeId: existingItem.adminFreqTimeId,
                execTime: existingItem.execTime,
                execTimeDesc: existingItem.execTimeDesc,
              }
            : { execTimeId: execTimeId };
        },
      ) as {
        execTimeId: string;
      }[],
    };

    return await saveAdminFreq(params);
  };

  defineExpose({
    open: openDialog,
  });

  const formConfig = useDetailFormConfig({
    formModel,
    isCloudEnv: isCloudEnv as boolean,
    isAdd,
    currentRow,
    adminFreqTimesOptionsList,
    handleNameBlur,
  });
</script>

<template>
  <ProDialog
    ref="dialogRef"
    :title="dialogTitle"
    :confirm-fn="handleSubmit"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @success="
      () => {
        ElMessage.success(
          isAdd ? t('global:add.success') : t('global:modify.success'),
        );
        handleClose();
        emits('success');
      }
    "
    @close="handleClose"
    @cancel="handleClose"
  >
    <ProForm ref="formRef" v-model="formModel" :data="formConfig" :column="2" />
  </ProDialog>
</template>
