import { Ref } from 'vue';
import { useFormConfig, FormDescItem, type CodeSystem } from 'sun-biz';
import { FLAG } from '@sun-toolkit/enums';

/** 检查项目属性 */
export function useExamineLabItemAttributeFormConfig(
  /** 限用性别集合 */
  limitSexCodes: Ref<CodeSystem[]>,
) {
  return useFormConfig({
    getData: (t) =>
      [
        {
          label: t('cisOutp.clinicalItem.limitSexCode', '限用性别'),
          name: 'limitSexCode',
          component: 'select',
          triggerModelChange: true,
          options: limitSexCodes.value,
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
          placeholder: t('global:placeholder.select.template', {
            name: t('cisOutp.clinicalItem.limitSexCode', '限用性别'),
          }),
          className: 'w-80',
        },
        {
          label: t('cisOutp.clinicalItem.reservationFlag', '需要预约'),
          name: 'reservationFlag',
          component: 'checkbox',
          triggerModelChange: true,
          extraProps: {
            trueValue: FLAG.YES,
            falseValue: FLAG.NO,
          },
        },
        {
          label: t('cisOutp.clinicalItem.allowUrgentFlag', '允许加急'),
          name: 'allowUrgentFlag',
          component: 'checkbox',
          triggerModelChange: true,
          extraProps: {
            trueValue: FLAG.YES,
            falseValue: FLAG.NO,
          },
        },
      ] as FormDescItem[],
  });
}
/** 检验项目属性 */
export function useVerfifyLabItemAttributeFormConfig(
  /** 标本集合 onChange 事件 */
  onSpecimenListChange: (val: string[]) => void,
  /** 标本集合 onBlur 事件 */
  onSpecimenListBlur: () => void,
  /** 标本集合 */
  specimenList: Ref<CodeSystem[]>,
  /** 限用性别集合 */
  limitSexCodes: Ref<CodeSystem[]>,
  /** 默认标本集合 */
  defaultSpecimenList: Ref<CodeSystem[]>,
) {
  return useFormConfig({
    getData: (t) =>
      [
        {
          label: t('cisOutp.clinicalItem.limitSexCode', '限用性别'),
          name: 'limitSexCode',
          component: 'select',
          triggerModelChange: true,
          options: limitSexCodes.value,
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
        {
          label: t('cisOutp.clinicalItem.labItemSpecimenList', '标本集合'),
          name: 'labItemSpecimenList',
          component: 'select',
          triggerModelChange: true,
          options: specimenList.value,
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
          extraProps: {
            multiple: true,
            'collapse-tags': true,
            'collapse-tags-tooltip': true,
            onChange: onSpecimenListChange,
            onBlur: onSpecimenListBlur,
          },
        },
        {
          label: t('cisOutp.clinicalItem.defaultSpecimen', '默认标本'),
          name: 'defaultSpecimen',
          component: 'select',
          triggerModelChange: true,
          options: defaultSpecimenList.value,
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
        {
          label: t('cisOutp.clinicalItem.allowUrgentFlag', '允许加急'),
          name: 'allowUrgentFlag',
          component: 'checkbox',
          triggerModelChange: true,
          extraProps: {
            trueValue: FLAG.YES,
            falseValue: FLAG.NO,
          },
        },
        {
          label: t('cisOutp.clinicalItem.allowSingleCheckFlag', '允许单选'),
          name: 'allowSingleCheckFlag',
          component: 'checkbox',
          triggerModelChange: true,
          extraProps: {
            trueValue: FLAG.YES,
            falseValue: FLAG.NO,
          },
        },
      ] as FormDescItem[],
  });
}
