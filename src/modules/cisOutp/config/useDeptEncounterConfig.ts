import { pickBy } from 'es-toolkit';
import { computed, ComputedRef, Ref } from 'vue';

export interface IENCOUNTER_TYPE_CODE {
  encounterTypeCode: string;
}

export interface IMODEL {
  encounterTypeCode?: string;
  orgId: string;
}

export interface IORGID_COUNT {
  orgId: string;
  count: number;
}

export interface ITYPE_MAP {
  [key: string]: string[];
}

export interface IRESOLVE_ORGID_DISABLE {
  (
    row: IENCOUNTER_TYPE_CODE,
    item: {
      label: string;
      value: ClinicalItem.OrganizationItem;
    },
    /** 已选执行科室(设置了所有就诊类型) orgId 集合 */
    execOrgIds: ComputedRef<ClinicalItem.OrganizationItem['orgId'][]>,
    /** 就诊类型对象 */
    encTypeMap: ComputedRef<ITYPE_MAP>,
  ): void;
}

/**
 * 执行科室 同 就诊类型 关系处理
 * @param encTypeList
 * @param model
 * @returns
 */
export function useDeptEncounterConfig<T extends IMODEL>(
  encTypeList: IENCOUNTER_TYPE_CODE[],
  tableData: Ref<T[]>,
) {
  // 科室集合（包含了所有执行科室）
  const execOrgIds = computed(() => {
    if (tableData.value?.length) {
      const limit = encTypeList.length;
      const res = tableData.value.reduce(
        (pre: Record<string, IORGID_COUNT>, item) => {
          if (item.orgId && item.encounterTypeCode) {
            let count = 0;
            if (pre[item.orgId]) {
              count = pre[item.orgId].count;
            }

            pre[item.orgId] = {
              orgId: item.orgId,
              count: count + 1,
            };
          }

          return pre;
        },
        {},
      );
      const reachUpperLimit = pickBy(res, (item) => item.count >= limit);
      return Object.keys(reachUpperLimit);
    } else {
      return [];
    }
  });

  /**
   * 就诊类型对应 执行科室集合
   * { enctypeCode: ['orgId', ....], ...}
   */
  const encTypeMap = computed(() => {
    const typeObj: ITYPE_MAP = {};
    encTypeList.forEach((item) => {
      typeObj[item.encounterTypeCode] = [];
    });

    if (tableData.value?.length) {
      tableData.value.reduce((pre, item) => {
        if (item.orgId && item.encounterTypeCode) {
          pre[item.encounterTypeCode!].push(item.orgId);
        }
        return pre;
      }, typeObj);
    }

    return typeObj;
  });

  /**
   * 科室选项禁用状态处理
   * @param row
   * @param item
   * @param execOrgIds
   * @param encTypeMap
   * @returns
   */
  function resolveOrgIdDisabled(
    row: IENCOUNTER_TYPE_CODE,
    item: {
      label: string;
      value: ClinicalItem.OrganizationItem;
    },
    /** 已选执行科室(设置了所有就诊类型) orgId 集合 */
    execOrgIds: ComputedRef<ClinicalItem.OrganizationItem['orgId'][]>,
    /** 就诊类型对象 */
    encTypeMap: ComputedRef<ITYPE_MAP>,
  ) {
    let disabled = execOrgIds.value.includes(item.value.orgId!);
    if (row.encounterTypeCode) {
      disabled ||= encTypeMap.value[row.encounterTypeCode].includes(
        item.value.orgId,
      );
    }
    return disabled;
  }

  return {
    execOrgIds,
    encTypeMap,
    resolveOrgIdDisabled,
  };
}
