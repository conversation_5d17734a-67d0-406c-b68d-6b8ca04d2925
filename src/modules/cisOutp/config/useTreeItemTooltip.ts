import { nextTick } from 'vue';

export function useTreeItemTooltip<T extends { visible: boolean }>(
  indent: number = 10,
) {
  const paddingLeft = 20;

  async function mouseOverHandler(e: MouseEvent, data: T) {
    const firstChild = (e.currentTarget as HTMLElement)
      .firstChild as HTMLElement;
    await nextTick();
    const { scrollWidth } = firstChild;
    const { width } = firstChild.getBoundingClientRect();

    if (scrollWidth > width) {
      data.visible = true;
    }
  }

  function mouseLeaveHandler(data: T) {
    data.visible = false;
  }

  return {
    indent,
    paddingLeft,
    mouseOverHandler,
    mouseLeaveHandler,
  };
}
