declare namespace CiSearchComponent {
  export interface IQueryCliItemByExampleRespItem {
    /**
     * 自定义UUID 【自定义】
     */
    key?: string;
    /**
     * 是否编辑状态【自定义】
     */
    editable: boolean;
    /**
     * 是否为新增行【自添加】
     */
    isAddRow?: boolean;
    /**
     * 组件标识
     */
    componentId: string;
    /**
     * 组件编码
     */
    componentNo: string;
    /**
     * 组件描述，组件名称
     */
    componentDesc: string;
    /**
     * 启用标志
     */
    enabledFlag: number;
  }

  export interface IUpdateCliItemSearchComponentByIdParam {
    /**
     * 组件标识
     */
    componentId: string;
    /**
     * 组件描述，组件名称
     */
    componentDesc: string;
    /**
     * 启用标志
     */
    enabledFlag: number;
  }

  export interface IAddCliItemSearchComponentParam {
    /**
     * 组件编码
     */
    componentNo: string;
    /**
     * 组件描述，组件名称
     */
    componentDesc: string;
    /**
     * 启用标志
     */
    enabledFlag: number;
  }

  export interface IQueryCliItemSearchSettingByExampleParam {
    /**
     * 组件标识
     */
    componentId?: string;
    /**
     * 组件编码
     */
    componentNo?: string;
    /**
     * 启用标志
     */
    enabledFlag?: number;
  }

  export interface IQueryCliItemSearchSettingByExampleRespItem {
    editable: boolean;
    isAddRow?: boolean;
    /**
     * 临床项目检索分组标识
     */
    ciSearchGroupId: string;
    /**
     * 临床项目检索分组名称
     */
    ciSearchGroupName: string;
    /**
     * 临床项目检索辅助名称
     */
    ciSearchGroup2ndName: string;
    /**
     * 临床项目检索扩展名称
     */
    ciSearchGroupExtName: string;
    /**
     * 临床项目检索来源代码
     */
    ciSearchSourceCode: string;
    /**
     * 临床项目检索来源代码描述
     */
    ciSearchSourceCodeDesc: string;
    /**
     * 临床项目检索分组名称(语言环境)
     */
    ciSearchGroupNameDisplay: string;
    /**
     * 业务标识类型代码
     */
    bizIdTypeCode: string;
    /**
     * 启用标志
     */
    enabledFlag: number;
    /**
     * 限用就诊类型列表
     */
    cisGroupEncTypeList: ICisGroupEncTypeItem[];
    /**
     * 临床项目检索设置列表
     */
    cisGroupSettingList: ICisGroupSettingItem[];
  }

  export interface ICisGroupEncTypeItem {
    /**
     * 临床项目检索分组设置对应就诊类型标识
     */
    cisGroupEncTypeId: string;
    /**
     * 就诊类型代码
     */
    encounterTypeCode: string;
    /**
     * 就诊类型代码描述
     */
    encounterTypeCodeDesc: string;
  }

  export interface ICisGroupSettingItem {
    /**
     * 临床项目检索分组设置对应内容标识
     */
    cisGroupSettingId: string;
    /**
     * 业务标识
     */
    bizId: string;
    /**
     * 业务名称
     */
    bizName: string;
  }

  export interface IUpdateCliItemSearchSettingByIdParam {
    /**
     * 临床项目检索分组标识
     */
    ciSearchGroupId: string;
    /**
     * 临床项目检索辅助名称
     */
    ciSearchGroup2ndName?: string;
    /**
     * 临床项目检索扩展名称
     */
    ciSearchGroupExtName?: string;
    /**
     * 业务标识类型代码
     */
    bizIdTypeCode: string;
    /**
     * 启用标志
     */
    enabledFlag: number;
    /**
     * 限用就诊类型列表
     */
    cisGroupEncTypeList: ICisGroupEncTypeItemIn[];
    /**
     * 临床项目检索设置列表
     */
    cisGroupSettingList: ICisGroupSettingItemIn[];
  }

  export interface ICisGroupEncTypeItemIn {
    /**
     * 临床项目检索分组设置对应就诊类型标识
     */
    cisGroupEncTypeId?: string;
    /**
     * 就诊类型代码
     */
    encounterTypeCode: string;
  }

  export interface ICisGroupSettingItemIn {
    /**
     * 临床项目检索分组设置对应内容标识
     */
    cisGroupSettingId?: string;
    /**
     * 业务标识
     */
    bizId: string;
  }

  export interface IAddCliItemSearchSettingParam {
    /**
     * 组件标识
     */
    componentId: string;
    /**
     * 临床项目检索分组名称
     */
    ciSearchGroupName: string;
    /**
     * 临床项目检索分组辅助名称
     */
    ciSearchGroup2ndName?: string;
    /**
     * 临床项目检索分组扩展名称
     */
    ciSearchGroupExtName?: string;
    /**
     * 临床项目检索来源代码
     */
    ciSearchSourceCode: string;
    /**
     * 启用标志
     */
    enabledFlag: number;
    /**
     * 就诊类型代码集合
     */
    encTypeCodes: string[];
    /**
     * 业务标识类型代码 : 常量 DICT_DATA_SETS
     */
    bizIdTypeCode: string;
    /**
     * 业务标识集合
     */
    bizIds: string[];
  }
}
