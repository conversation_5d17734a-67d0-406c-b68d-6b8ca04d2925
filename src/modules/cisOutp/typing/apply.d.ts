declare namespace Apply {
  export interface QueryApplyTemplateParam {
    /**
     * 临床服务类型代码
     */
    csTypeCode?: string;
    /**
     * 启用标志
     */
    enabledFlag?: number;
    /**
     * 所属医院标识集合
     */
    hospitalIds?: string[];
  }

  export type QueryApplyTemplateResponse = ApplyTemplateItem[];

  export interface ApplyTemplateItem {
    /** 【自定义】 */
    visible: boolean;
    /**
     * 模板标识
     */
    applyTempId: string;
    /**
     * 模板编码
     */
    applyTempNo: string;
    /**
     * 模板名称
     */
    applyTempName: string;
    /**
     * 模板辅助名称
     */
    applyTemp2ndName: string;
    /**
     * 模板扩展名称
     */
    applyTempExtName: string;
    /**
     * 上级模板标识
     */
    applyTempPnId: string;
    /**
     * 上级模板名称（语言环境)
     */
    applyTempPnNameDisplay: string;
    /**
     * 临床服务类型代码
     */
    csTypeCode: string;
    /**
     * 临床服务类型代码描述
     */
    csTypeCodeDesc: string;
    /**
     * 启用标志
     */
    enabledFlag: number;
    /**
     * 模板类型代码
     */
    applyTempTypeCode: string;
    /**
     * 模板类型代码描述
     */
    applyTempTypeCodeDesc: string;
    /**
     * 限用医院列表
     */
    applyTempLimitHospitalOutList: ApplyTempLimitHospitalOutItem[];
    /** 子模版 */
    children?: ApplyTemplateItem[];
  }

  export interface ApplyTempLimitHospitalOutItem {
    /**
     * 模板对应医院标识
     */
    applyHospitalId: string;
    /**
     * 所属医院标识
     */
    hospitalId: string;
    /**
     * 医院名称（语言环境）
     */
    hospitalName: string;
  }

  export interface SaveApplyTemplateParam {
    /**
     * 模板标识
     */
    applyTempId?: string;
    /**
     * 模板编码
     */
    applyTempNo: string;
    /**
     * 模板名称
     */
    applyTempName: string;
    /**
     * 模板辅助名称
     */
    applyTemp2ndName?: string;
    /**
     * 模板扩展名称
     */
    applyTempExtName?: string;
    /**
     * 上级模板标识
     */
    applyTempPnId?: string;
    /**
     * 临床服务类型代码
     */
    csTypeCode: string;
    /**
     * 启用标志
     */
    enabledFlag: number;
    /**
     * 模板类型代码
     */
    applyTempTypeCode: string;
    /**
     * 限用医院列表
     */
    applyTempLimitHospitalInList: ApplyTempLimitHospitalInItem[];
  }

  export interface ApplyTempLimitHospitalInItem {
    /**
     * 模板对应医院标识
     */
    applyHospitalId?: string;
    /**
     * 所属医院标识
     */
    hospitalId: string;
  }

  export interface IFORM_MODEL extends SaveApplyTemplateParam {
    /** 限用医院 */
    applyTempLimitHospital?: Apply.ApplyTempLimitHospitalInItem | null;
    /** 父目录名称 */
    applyTempPnName?: string;
  }

  export interface QueryCliApplyItemByExampleParam {
    /**
     * 模板标识
     */
    applyTempId: string;
    /**
     * 所属医院标识集合
     */
    hospitalIds?: string[];
  }

  export interface QueryCliApplyItemByExampleResponse {
    /**
     * 模板标识
     */
    applyTempId: string;
    /**
     * 限用医院列表
     */
    applyLimitHospitalOutList: ApplyTempLimitHospitalOutResponseItem[];
  }

  export interface ApplyTempLimitHospitalOutResponseItem {
    /**
     * 模板对应医院标识
     */
    applyHospitalId: string;
    /**
     * 所属医院标识
     */
    hospitalId: string;
    /**
     * 医院名称（语言环境）
     */
    hospitalName: string;
    /**
     * 执行科室列表
     */
    applyExecDeptOutList: ApplyExecDeptOutItem[];
    /**
     * 申请单模板应用范围列表
     */
    applyScopeOutList: ApplyScopeOutItem[];
    /**
     * 开单科室列表
     */
    applyDeptOutList: ApplyDeptOutItem[];
    /**
     * 申请单项目列表
     */
    labApplyItemOutList: LabApplyItemOutItem[];
  }

  export interface ApplyExecDeptOutItem {
    /** 是否编辑【自定义】 */
    editable?: boolean;
    /** 是否添加行【自定义】 */
    isAddRow?: boolean;
    /**
     * 执行科室编码
     */
    ciExecOrgNo: string;
    /**
     * 申请单模板执行科室标识
     */
    applyExecDeptId: string;
    /**
     * 执行科室标识
     */
    orgId: string;
    /**
     * 执行科室名称（语言环境）
     */
    ciExecOrgName: string;
    /**
     * 就诊类型代码
     */
    encounterTypeCode: string;
    /**
     * 就诊类型代码描述
     */
    encounterTypeCodeDesc?: string;
    /**
     * 默认标志
     */
    defaultFlag: number;
  }

  export interface ApplyScopeOutItem {
    /** 是否编辑【自定义】 */
    editable?: boolean;
    /** 是否添加行【自定义】 */
    isAddRow?: boolean;
    /**
     *
     * 申请单模板应用范围标识
     */
    applyScopeId: string;
    /**
     * 就诊类型代码
     */
    encounterTypeCode: string;
    /**
     * 就诊类型代码描述
     */
    encounterTypeCodeDesc?: string;
  }

  export interface ApplyDeptOutItem {
    /** 是否编辑【自定义】 */
    editable?: boolean;
    /** 是否添加行【自定义】 */
    isAddRow?: boolean;
    /**
     * 申请单模板开单科室标识
     */
    applyDeptId: string;
    /**
     * 开单科室标识
     */
    orgId: string;
    /**
     * 开单科室编码
     */
    applyOrgNo: string;
    /**
     * 开单科室名称（语言环境）
     */
    applyOrgName: string;
    /**
     * 就诊类型代码
     */
    encounterTypeCode: string;
    /**
     * 就诊类型代码描述
     */
    encounterTypeCodeDesc?: string;
  }

  export interface LabApplyItemOutItem {
    /**
     * 是否可编辑【自定义】
     */
    editable?: boolean;
    /**
     * 是否为新增行【自定义】
     */
    isAddRow?: boolean;
    /**
     * 项目类型
     */
    csTypeCode?: string;
    /**
     * 申请单项目标识
     */
    applyItemId: string;
    /**
     * 临床项目标识
     */
    ciId: string;
    /**
     * 临床项目编码
     */
    ciNo: string;
    /**
     * 临床项目名称（语言环境）
     */
    ciName: string;
    /**
     * 组合标志
     */
    groupFlag: number;
    /**
     * 检验组合项目明细列表
     */
    applySubItemOutList: ApplySubItemOutItem[];
    /**
     * 检验项目属性
     */
    applyLabItemAttributeOut: ApplyLabItemAttributeOutModel;
    /**
     * 检查项目属性
     */
    applyExamAttributeOut: ApplyExamAttributeOutModel;
  }

  export interface ApplySubItemOutItem {
    /**
     * 临床项目标识
     */
    ciId: string;
    /**
     * 临床项目编码
     */
    ciNo: string;
    /**
     * 临床项目名称（语言环境）
     */
    ciName: string;
    /**
     * 数量
     */
    num: number;
    /**
     * 检验项目标本列表
     */
    appyLabItemSpecimenOutList: AppyLabItemSpecimenOutItem[];
  }

  export interface AppyLabItemSpecimenOutItem {
    /**
     * 检验项目标本标识
     */
    clSpecimenId: string;
    /**
     * 检验项目标本代码
     */
    clSpecimenCode: string;
    /**
     * 默认标志
     */
    defaultFlag: number;
    /**
     * 检验项目标本代码描述
     */
    clSpecimenCodeDesc: string;
  }

  export interface ApplyLabItemAttributeOutModel {
    /**
     * 检验项目标识
     */
    ciLabitemId: string;
    /**
     * 限用性别代码
     */
    limitSexCode: string;
    /**
     * 限用性别代码描述
     */
    limitSexCodeDesc: string;
    /**
     * 允许加急
     */
    allowUrgentFlag: number;
    /**
     * 允许单选
     */
    allowSingleCheckFlag: number;
    /**
     * 检验项目标本列表
     */
    appyLabItemSpecimenOutList: AppyLabItemSpecimenOutItem[];
  }

  export interface ApplyExamAttributeOutModel {
    /**
     * 检查项目标识
     */
    ciExamitemId: string;
    /**
     * 允许加急
     */
    allowUrgentFlag: number;
    /**
     * 限用性别代码
     */
    limitSexCode: string;
    /**
     * 限用性别代码描述
     */
    limitSexCodeDesc: string;
    /**
     * 需要预约
     */
    reservationFlag: number;
  }

  export interface SaveCliApplyItemParam {
    /**
     * 模板标识
     */
    applyTempId: string;
    /**
     * 限用医院列表
     */
    applyLimitHospitalInList: ApplyTempLimitHospitalInItemSaved[];
  }

  export interface ApplyTempLimitHospitalInItemSaved {
    /**
     * 模板对应医院标识
     */
    applyHospitalId: string;
    /**
     * 所属医院标识
     */
    hospitalId: string;
    /**
     * 执行科室列表
     */
    applyExecDeptInList?: ApplyExecDeptInItemSaved[];
    /**
     * 申请单模板应用范围列表
     */
    applyScopeInList?: ApplyScopeInItemSaved[];
    /**
     * 开单科室列表
     */
    applyDeptInList?: ApplyDeptInItemSaved[];
    /**
     * 申请单项目列表
     */
    labApplyItemInList?: LabApplyItemInItemSaved[];
  }

  export interface ApplyExecDeptInItemSaved {
    /**
     * 申请单模板执行科室标识
     */
    applyExecDeptId?: string;
    /**
     * 执行科室标识
     */
    orgId: string;
    /**
     * 就诊类型代码
     */
    encounterTypeCode?: string;
    /**
     * 默认标志
     */
    defaultFlag: number;
  }

  export interface ApplyScopeInItemSaved {
    /**
     * 申请单模板应用范围标识
     */
    applyScopeId?: string;
    /**
     * 就诊类型代码
     */
    encounterTypeCode: string;
  }

  export interface ApplyDeptInItemSaved {
    /**
     * 申请单模板开单科室标识
     */
    applyDeptId?: string;
    /**
     * 执行科室标识
     */
    orgId: string;
    /**
     * 就诊类型代码
     */
    encounterTypeCode?: string;
  }

  export interface LabApplyItemInItemSaved {
    /**
     * 申请单项目标识
     */
    applyItemId?: string;
    /**
     * 临床项目标识
     */
    ciId: string;
    /**
     * 检验项目属性
     */
    applyLabItemAttributeIn?: ApplyLabItemAttributeInModel | null;
    /**
     * 检查项目属性
     */
    applyExamAttributeIn?: ApplyExamAttributeInModel | null;
    /**
     * 检验组合项目明细列表【自添加】
     */
    applySubItemOutList?: ApplySubItemOutItem[];
  }

  export interface ApplyLabItemAttributeInModel {
    /**
     * 检验项目标识
     */
    ciLabitemId?: string;
    /**
     * 限用性别代码
     */
    limitSexCode?: string;
    /**
     * 限用性别代码描述【自添加】
     */
    limitSexCodeDesc?: string;
    /**
     * 允许加急
     */
    allowUrgentFlag: number;
    /**
     * 允许单选
     */
    allowSingleCheckFlag: number;
    /**
     * 检验项目标本列表
     */
    applyLabItemSpecimenInList?: ApplyLabItemSpecimenInItem[];
    /**
     * 检验项目标本列表【公共组件属性, 自填加】
     */
    labItemSpecimenList?: ApplyLabItemSpecimenInItem[];
  }

  export interface ApplyLabItemSpecimenInItem {
    /**
     * 检验项目标本标识
     */
    clSpecimenId?: string;
    /**
     * 检验项目标本代码
     */
    clSpecimenCode: string;
    /**
     * 检验项目标本代码描述
     */
    clSpecimenCodeDesc?: string;
    /**
     * 默认标志
     */
    defaultFlag: number;
  }

  export interface ApplyExamAttributeInModel {
    /**
     * 检查项目标识
     */
    ciExamitemId?: string;
    /**
     * 允许加急
     */
    allowUrgentFlag: number;
    /**
     * 限用性别代码
     */
    limitSexCode?: string;
    /**
     * 限用性别代码描述【自填加】
     */
    limitSexCodeDesc?: string;
    /**
     * 需要预约
     */
    reservationFlag: number;
  }

  export interface QueryDataSetListParam {
    /** 编码体系标识 */
    codeSystemIds?: string[];
    /** 编码体系NO */
    codeSystemNos?: string[];
    /** 关键字 */
    keyWord?: string;
    /** 启用状态 */
    enabledFlag?: number;
    /** 值标识 */
    dataValueIds?: number[];
    /** 值编码 */
    dataValueNos?: string[];
    /** 医院标识 */
    hospitalId: string;
  }
}
