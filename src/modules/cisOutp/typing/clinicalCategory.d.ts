declare namespace ClinicalCategory {
  export interface IQueryCliServiceCategoryResItem {
    /** 自定义【保存需删除】 */
    visible: boolean;
    /** 类别标识 */
    csCategoryId: string;
    /** 类别编码 */
    csCategoryNo: string;
    /** 类别名称（语言环境） */
    csCategoryNameDisplay: string;
    /** 类别名称 */
    csCategoryName: string;
    /** 类别辅助名称 */
    csCategory2ndName: string;
    /** 类别扩展名称 */
    csCategoryExtName: string;
    /** 上级类别标识 */
    csPnCategoryId: string;
    /** 临床服务类型代码 */
    csTypeCode: string;
    /** 临床服务类型描述 */
    csTypeDesc: string;
    /** children */
    childCliServiceCategory: IQueryCliServiceCategoryResItem[] | null;
  }

  export interface IAddCliServiceCategoryParam {
    /** 类别编码 */
    csCategoryNo: string;
    /** 类别名称 */
    csCategoryName: string;
    /** 类别辅助名称 */
    csCategory2ndName?: string;
    /** 类别扩展名称 */
    csCategoryExtName?: string;
    /** 上级类别标识 */
    csPnCategoryId?: string;
    /** 临床服务类型代码 */
    csTypeCode?: string;
  }

  export interface IUpdateCliServiceCategoryByIdParam {
    /** 类别标识 */
    csCategoryId: string;
    /** 类别编码 */
    csCategoryNo: string;
    /** 类别名称 */
    csCategoryName: string;
    /** 类别辅助名称 */
    csCategory2ndName?: string;
    /** 类别扩展名称 */
    csCategoryExtName?: string;
    /** 临床服务类型代码 */
    csTypeCode?: string;
  }

  export interface IModel {
    /** 自定义【需删除】 */
    visible?: boolean;
    /** 类别标识 */
    csCategoryId: string;
    /** 类别编码 */
    csCategoryNo: string;
    /** 类别名称 */
    csCategoryName: string;
    /** 类别辅助名称 */
    csCategory2ndName?: string;
    /** 类别扩展名称 */
    csCategoryExtName?: string;
    /** 临床服务类型代码 */
    csTypeCode?: string;
    /** 上级类别标识 */
    csPnCategoryId?: string;
  }
}
