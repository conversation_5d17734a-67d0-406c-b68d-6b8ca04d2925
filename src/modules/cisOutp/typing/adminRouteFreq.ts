/** 查询给药频次时间 入参 */
// export type AdminFreqTimeReqParams = {};

/** 查询给药频次时间 出参 */
export type AdminFreqTimeResItem = {
  execTimeId: string;
  /** 格式 HH:mm:ss */
  execTime: string;
  execTimeDesc: string;
};

/** 保存给药频次时间 入参 */
export type SaveAdminFreqTimeReqParams = {
  execTimeId?: string;
  execTime: string;
  execTimeDesc: string;
};

/** 保存给药频次时间 出参 */
// export type SaveAdminFreqTimeResItem = {};

/** 根据标识删除给药频次时间 入参 */
export type DeleteAdminFreqTimeReqParams = {
  execTimeId: string;
};

/** 根据标识删除给药频次时间 出参 */
// export type DeleteAdminFreqTimeResItem = {};

/** 根据条件查询给药频次 入参 */
export type AdminFreqReqParams = {
  encounterTypeCodes?: string[];
  enabledFlag?: number;
  keyWord?: string;
};

/** 根据条件查询给药频次 出参 */
export type AdminFreqResItem = {
  adminFreqId: string;
  adminFreqName: string;
  adminFreqDisplayName: string;
  adminFreq2ndName: string;
  adminFreqExtName: string;
  adminFreqTypeCode: string;
  adminFreqTypeCodeDesc: string;
  adminFreqExecTimes: number;
  adminFreqCycle: number;
  wbNo: string;
  spellNo: string;
  enabledFlag: number;
  editableFlag: number;
  sort: number;
  adminFreqWeekdayList: AdminFreqWeekdayResItem[];
  adminFreqOrderTypeList: AdminFreqOrderTypeResItem[];
  adminFreqEncTypeList: AdminFreqEncTypeResItem[];
  adminFreqTimeList: AdminFreqTimesResItem[];
};

/** 给药频次对应星期列表 */
export type AdminFreqWeekdayResItem = {
  adminFreqWeekdayId: string;
  weekDay: number;
  weekDayDesc: string;
};

/** 给药频次对应医嘱时效类型列表 */
export type AdminFreqOrderTypeResItem = {
  adminFreqOrderTypeId: string;
  adminFreqTypeCode: string;
  orderFreqTypeCodeDesc: string;
};

/** 就诊类型代码列表  */
export type AdminFreqEncTypeResItem = {
  adminFreqEncTypeId: string;
  encounterTypeCode: string;
  encounterTypeCodeDesc: string;
};

/** 就诊时间列表 */
export type AdminFreqTimesResItem = {
  adminFreqTimeId: string;
  execTimeId: string;
  execTime: string;
  execTimeDesc: string;
};

/** 保存给药频次 入参  */
export type SaveAdminFreqReqParams = {
  adminFreqId?: string;
  adminFreqName: string;
  adminFreqDisplayName: string;
  adminFreq2ndName?: string;
  adminFreqExtName?: string;
  adminFreqTypeCode: string;
  adminFreqExecTimes: number;
  adminFreqCycle: number;
  wbNo?: string;
  spellNo?: string;
  enabledFlag: number;
  editableFlag: number;
  sort?: number;
  adminFreqWeekdayList?: {
    adminFreqWeekdayId?: string;
    weekDay: number;
  }[];
  adminFreqOrderTypeList: {
    adminFreqOrderTypeId?: string;
    adminFreqTypeCode: string;
  }[];
  adminFreqEncTypeList: {
    adminFreqEncTypeId?: string;
    encounterTypeCode: string;
  }[];
  adminFreqTimeList?: {
    adminFreqTimeId?: string;
    execTimeId: string;
    execTime?: string;
    execTimeDesc?: string;
  }[];
};

/** 保存给药频次 出参  */
// export type SaveAdminFreqResItem = {};

/** 根据标识删除给药频次 出参  */
export type DeleteAdminFreqReqParams = {
  adminFreqId: string;
};

/** 根据标识删除给药频次 出参  */
// export type DeleteAdminFreqResItem = {};

/** 根据条件查询给药途径 入参  */
export type AdminRouteReqParams = {
  encounterTypeCodes?: string[];
  enabledFlag?: number;
  keyWord?: string;
};

/** 根据条件查询给药途径 出参  */
export type AdminRouteResItem = {
  adminRouteId: string;
  adminRouteName: string;
  adminRouteDisplayName: string;
  adminRoute2ndName: string;
  adminRouteExtName: string;
  adminRouteCategoryCode: string;
  adminRouteCategoryCodeDesc: string;
  wbNo: string;
  spellNo: string;
  enabledFlag: number;
  editableFlag: number;
  sort: number;
  adminRouteEncTypeList: {
    adminRouteEncTypeId: string;
    encounterTypeCode: string;
    encounterTypeCodeDesc: string;
  }[];
  adminRouteBizScenarioList: {
    adminRouteBizScenarioId: string;
    adminRouteBizScenarioCode: string;
    adminRouteBizScenarioCodeDesc: string;
  }[];
};

/** 保存给药途径 入参  */
export type SaveAdminRouteReqParams = {
  adminRouteId?: string;
  adminRouteName: string;
  adminRouteDisplayName: string;
  adminRoute2ndName?: string;
  adminRouteExtName?: string;
  adminRouteCategoryCode: string;
  wbNo?: string;
  spellNo?: string;
  enabledFlag: number;
  editableFlag: number;
  sort?: number;
  adminRouteEncTypeList: {
    adminRouteEncTypeId?: string;
    encounterTypeCode: string;
  }[];
  adminRouteBizScenarioList?: {
    adminRouteBizScenarioId?: string;
    adminRouteBizScenarioCode: string;
  }[];
};

/** 保存给药途径 出参  */
// export type SaveAdminRouteResItem = {};

/** 根据标识删除给药途径 入参  */
export type DeleteAdminRouteReqParams = {
  adminRouteId: string;
};

/** 根据标识删除给药途径 出参  */
// export type DeleteAdminRouteResItem = {};
