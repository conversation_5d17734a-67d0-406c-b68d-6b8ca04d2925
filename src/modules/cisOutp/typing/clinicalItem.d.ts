declare namespace ClinicalItem {
  /**
   * 服务分类数据结构
   */

  /** 服务分类列表 */
  export type CsCategoryListResponse = CsCategoryItem[];

  /**
   * 单个服务分类项
   */
  export interface CsCategoryItem {
    /** 类别标识 */
    csCategoryId?: string;
    /** 类别编码 */
    csCategoryNo?: string;
    /** 类别名称（语言环境） */
    csCategoryNameDisplay?: string;
    /** 类别名称 */
    csCategoryName?: string;
    /** 类别辅助名称 */
    csCategory2ndName?: string;
    /** 类别扩展名称 */
    csCategoryFixName?: string;
    /** 上级类别标识 */
    csPackageoryId?: string;
    /** 临床服务类型代码 */
    csTypeCode?: string;
    /** 临床服务类型描述 */
    csTypeDesc?: string;
    /** 是否叶子节点 */
    childCliServiceCategory?: CsCategoryItem[];
  }

  /**
   * 查询临床项目请求参数
   */
  export interface QueryClinicalItemParams {
    /** 关键字  */
    keyWord?: string;
    /** 所属医院标识 */
    hospitalId?: string;
    /** 临床项目标识集合  */
    ciIds?: string[];
    /** 临床服务类别标识  */
    csCategoryId?: string;
    /** 临床服务类型代码 */
    csTypeCode?: string;
    /** 启用标志  */
    enabledFlag?: number;
    /** 组合标志  */
    groupFlag?: number;
    /** 就诊类型代码集合 */
    encounterTypeCodes?: string[];
    pageNumber: number;
    pageSize: number;
  }

  /**
   * 查询临床项目响应数据结构
   */
  export type QueryClinicalItemResponse = ClinicalProjectItem[];

  /**
   * 单个临床项目项
   */
  export interface ClinicalProjectItem {
    /** 临床项目标识 */
    ciId?: string;
    /** 临床项目编码 */
    ciNo?: string;
    /** 临床项目名称 */
    ciName?: string;
    /** 临床项目名称（语言环境） */
    ciNameDisplay?: string;
    /** 临床项目辅助名称 */
    ci2ndName?: string;
    /** 临床项目扩展名称 */
    ciExtName?: string;
    /** 规格 */
    spec?: string;
    /** 计价单位标识 */
    unitId?: string;
    /** 单位名称 */
    unitName?: string;
    /** 五笔码 */
    wbNo?: string;
    /** 拼音码 */
    spellNo?: string;
    /** 服务类别标识 */
    csCategoryId?: string;
    /** 服务类别名称 */
    csCategoryName?: string;
    /** 临床服务类型代码 */
    csTypeCode?: string;
    /** 临床服务类型代码描述 */
    csTypeCodeDesc?: string;
    /** 项目内涵 */
    conNotation?: string;
    /** 注意事项 */
    notice?: string;
    /** 组合标志 */
    groupFlag?: number;
    /** 启用标志 */
    enabledFlag?: number;
    /** 计价标志 */
    chargeFlag?: number;
    /** 限用医院列表 */
    limitHospitalList?: LimitHospital[];
  }

  /**
   * 限用医院
   */
  export interface LimitHospital {
    /** 临床项目对应医院标识 */
    ciHosipitalId?: string;
    /** 所属医院标识 */
    hospitalId?: string;
    /** 医院名称（语言环境） */
    hospitalName?: string;
    /** 就诊类型列表 */
    encTypeList?: EncTypeItem[];
    /** 执行科室列表 */
    execDeptList?: ExecDeptItem[];
    /** 组合明细列表 */
    subItemList?: SubItem[];
    /** 商品列表 */
    ciCommodityList?: CiCommodityItem[];
    /** 检验项目属性 */
    labItemAttribute?: LabItemAttributeItem;
    /** 检查项目属性 */
    examItemAttribute?: ExamItemAttributeItem;
  }

  /**
   * 就诊类型
   */
  export interface EncTypeItem {
    /** 是否编辑【自定义】 */
    editable: boolean;
    /** 是否添加行【自定义】 */
    isAddRow?: boolean;
    /** 临床项目的就诊类型标识 */
    ciEncTypeId?: string;
    /** 就诊类型代码 */
    encounterTypeCode?: string;
    /** 就诊类型代码描述 */
    encounterTypeCodeDesc?: string;
  }

  /**
   * 执行科室
   */
  export interface ExecDeptItem {
    /** 是否编辑【自定义】 */
    editable: boolean;
    /** 是否添加行【自定义】 */
    isAddRow?: boolean;
    /** 临床项目的执行科室标识 */
    ciExecOrgId?: string;
    /** 执行科室标识 */
    orgId: string;
    /** 执行科室编码 */
    orgNo?: string;
    /** 执行科室名称（语言环境） */
    ciExecOrgName?: string;
    /** 就诊类型代码 */
    encounterTypeCode?: string;
    /** 就诊类型代码描述 */
    encounterTypeCodeDesc?: string;
    /** 默认标志 */
    defaultFlag?: number;
  }

  /**
   * 组合明细
   */
  export interface SubItem {
    /** 是否编辑【自定义】 */
    editable: boolean;
    /** 是否添加行【自定义】 */
    isAddRow?: boolean;
    /** 临床项目组合明细标识 */
    ciSubitemId?: string;
    /** 子项目标识 */
    subCiId?: string;
    /** 子项目名称（语言环境） */
    subCiName?: string;
    /** 子项目编码 */
    subCiNo?: string;
    /** 子项目辅助名称 */
    subCi2ndName?: string;
    /** 子项目扩展名称 */
    subCiExtName?: string;
    /** 数量 */
    num: number;
  }

  /**
   * 商品信息
   */
  export interface CiCommodityItem {
    /** 是否编辑【自定义】 */
    editable: boolean;
    /** 是否添加行【自定义】 */
    isAddRow?: boolean;
    /** 临床项目对应商品标识 */
    ciCommodityId?: string;
    /** 医院的商品标识 */
    hospitalCommodityId?: string;
    /** 商品标识 */
    commodityId?: string;
    /** 商品编码 */
    commodityNo?: string;
    /** 商品类型 */
    commodityTypeCode: string;
    /** 商品名称（语言环境） */
    commodityName?: string;
    /** 规格 */
    commoditySpec?: string;
    /** 数量 */
    num?: number;
    /** 计价单位标识 */
    unitId?: string;
    /** 单位名称 */
    unitName?: string;
  }

  /**
   * 检验项目属性
   */
  export interface LabItemAttributeItem {
    ciLabitemId?: string;
    /** 限用性别代码 */
    limitSexCode?: string;
    /** 限用性别代码描述 */
    limitSexCodeDesc?: string;
    /** 允许加急 */
    allowUrgentFlag?: number;
    /** 允许单选 */
    allowSingleCheckFlag?: number;
    /** 检验项目标本列表 */
    labItemSpecimenList?: LabItemSpecimenItem[];
  }

  /**
   * 检验项目标本
   */
  export interface LabItemSpecimenItem {
    /** 检验项目标本标识 */
    clSpecimenId?: string;
    /** 检验项目标本代码 */
    clSpecimenCode?: string;
    /** 默认标志 */
    defaultFlag?: number;
    /** 检验项目标本代码描述 */
    clSpecimenCodeDesc?: string;
  }

  /**
   * 检查项目属性
   */
  export interface ExamItemAttributeItem {
    ciExamitemId?: string;
    /** 允许加急 */
    allowUrgentFlag?: number;
    /** 限用性别代码 */
    limitSexCode?: string;
    /** 限用性别代码描述 */
    limitSexCodeDesc?: string;
    /** 需要预约 */
    reservationFlag?: number;
  }

  export interface ClinicalItemFormValues {
    /** 临床项目标识 */
    ciId?: string;
    /** 临床项目编码 */
    ciNo: string;
    /** 临床项目名称 */
    ciName: string;
    /** 临床项目辅助名称 */
    ci2ndName?: string;
    /** 临床项目扩展名称 */
    ciExtName?: string;
    /** 规格 */
    spec?: string;
    /** 计价单位标识 */
    unitId: string;
    /** 五笔码 */
    wbNo?: string;
    /** 拼音码 */
    spellNo?: string;
    /** 服务类别标识 */
    csCategoryId: string;
    /** 临床服务类型代码 */
    csTypeCode: string;
    /** 项目内涵 */
    conNotation?: string;
    /** 注意事项 */
    notice?: string;
    /** 组合标志 */
    groupFlag: number;
    /** 启用标志 */
    enabledFlag: number;
    /** 计价标志 */
    chargeFlag: number;
    /** 限用医院列表 */
    limitHospitalList: LimitHospitalFormValue[];
  }

  export interface LimitHospitalFormValue {
    /** 临床项目对应医院标识 */
    ciHosipitalId?: string;
    /** 医院名称: 【自定义，保存需删除】 */
    hospitalName?: string;
    /** 所属医院标识 */
    hospitalId: string;
    /** 就诊类型列表 */
    encTypeList?: EncTypeItemFormValue[];
    /** 执行科室列表 */
    execDeptList?: ExecDeptItemFormValue[];
    /** 组合明细列表 */
    subItemList?: SubItemFormValue[];
    /** 商品列表 */
    ciCommodityList?: CiCommodityItemFormValue[];
    /** 检验项目属性 */
    labItemAttribute?: LabItemAttributeItemFormValue;
    /** 检查项目属性 */
    examItemAttribute?: ExamItemAttributeItemFormValue;
  }

  export interface EncTypeItemFormValue {
    /** 临床项目的就诊类型标识 */
    ciEncTypeId?: string;
    /** 就诊类型代码 */
    encounterTypeCode: string;
  }

  export interface ExecDeptItemFormValue {
    /** 临床项目的执行科室标识 */
    ciExecOrgId?: string;
    /** 执行科室标识 */
    orgId: string;
    /** 就诊类型代码 */
    encounterTypeCode: string;
    /** 默认标志 */
    defaultFlag?: number;
  }

  export interface SubItemFormValue {
    ciSubitemId?: string;
    /** 子项目标识 */
    subCiId: string;
    /** 数量 */
    num: number;
  }

  export interface CiCommodityItemFormValue {
    /** 临床项目对应商品标识 */
    ciCommodityId?: string;
    /** 医院的商品标识 */
    hospitalCommodityId: string;
    /** 商品标识 */
    commodityId: string;
    /** 商品类型 */
    commodityTypeCode: string;
    /** 数量 */
    num: number;
    /** 计价单位标识 */
    unitId: string;
  }

  export interface LabItemAttributeItemFormValue {
    /** 检验项目标识 */
    ciLabitemId?: string;
    /** 限用性别 */
    limitSexCode?: string;
    /** 允许加急 */
    allowUrgentFlag: number;
    /** 允许单选 */
    allowSingleCheckFlag: number;
    /** 检验项目标本列表 */
    labItemSpecimenList?: LabItemSpecimenItemFormValue[];
  }

  export interface LabItemSpecimenItemFormValue {
    /** 检验项目标本标识 */
    clSpecimenId?: string;
    /** 检验项目标本代码 */
    clSpecimenCode: string;
    /** 默认标志 */
    defaultFlag: number;
  }

  export interface ExamItemAttributeItemFormValue {
    /** 检查项目标识 */
    ciExamitemId?: string;
    /** 允许加急 */
    allowUrgentFlag: number;
    /** 限用性别 */
    limitSexCode?: string;
    /** 需要预约 */
    reservationFlag: number;
  }

  export type UnitList = Unit[];
  export interface Unit {
    /** 单位标识 */
    unitId: string;
    /** 单位编码 */
    unitNo: string;
    /** 单位名称 */
    unitName: string;
  }

  export interface OrgAndHospitall {
    /** 组织标识 */
    orgId: string;
    /** 组织名称（语言环境） */
    orgName: string;
    /** 组织类型代码 */
    orgTypeCode: string;
    /** 组织类型描述（语言环境） */
    orgTypeDesc: string;
    /** 角色列表 */
    roleList?: RoleItem[];
    /** 可登录位置列表 */
    orgLocationList: OrgLocationItem[];
  }

  export interface RoleItem {
    /** 角色标识 */
    roleId: string;
    /** 角色名称（语言环境） */
    roleName: string;
  }

  export interface OrgLocationItem {
    /** 组织位置标识 */
    orgLocationId: string;
    /** 组织位置名称（语言环境） */
    orgLocationName: string;
  }

  export interface QueryOrgListByExampleFlatParam {
    // 页码
    pageNumber: number;
    // 页面数量
    pageSize: number;
    /** 关键字 */
    keyWord?: string;
    /** 启用标志 */
    enabledFlag?: number;
    /** 组织类型代码集合 */
    orgTypeCodes?: string[];
    /** 上级组织标识 */
    parentOrgId?: string;
    /** 组织标识 */
    orgId?: string;
    /** 组织编码 */
    orgNo?: string;
  }

  export interface QueryOrgListByExampleFlatResponse {
    /** 组织列表 */
    organizationList?: OrganizationItem[];
  }

  export interface OrganizationItem {
    /** 组织标识 */
    orgId: string;
    /** 组织编码 */
    orgNo: string;
    /** 组织名称 */
    orgName: string;
    /** 组织辅助名称 */
    org2ndName?: string;
    /** 组织扩展名称 */
    orgExtName?: string;
    /** 组织名称（语言环境） */
    orgNameDisplay: string;
    /** 组织类型代码 */
    orgTypeCode: string;
    /** 组织类型描述（语言环境） */
    orgTypeDescDisplay: string;
    /** 组织简介 */
    orgDesc?: string;
    /** 辅助简介 */
    org2ndDesc?: string;
    /** 扩展简介 */
    orgExtDesc?: string;
    /** 组织简介（语言环境） */
    orgDescDisplay?: string;
    /** 拼音码 */
    spellNo?: string;
    /** 五笔码 */
    wbNo?: string;
    /** 上级组织标识 */
    parentOrgId?: string;
    /** 启用标志 */
    enabledFlag: number;
    /** 组织的联系方式列表 */
    orgXContactList?: OrgXContactItem[];
    /** 组织的库房类型列表 */
    orgStorageTypeList?: OrgStorageTypeItem[];
    /** 组织管理的药品类型列表 */
    orgMedicineTypeList?: OrgMedicineTypeItem[];
    /** 医院扩展信息 */
    hospitalExtInfo?: HospitalExtInfo;
    /** 科室扩展信息 */
    deptExtInfo?: DeptExtInfo;
    /** 组织的环境配置 */
    orgEnvSetting?: OrgEnvSetting;
    /** 组织的位置分布列表 */
    orgLocationList?: OrgLocationItemDetail;
    /** 组织负责人列表 */
    orgXDirectorList?: OrgXDirectorItem;
  }

  export interface OrgXDirectorItem {
    /** 组织负责人标识 */
    orgXDirectorId?: string;
    /** 用户标识 */
    userId?: string;
    /** 用户名称 */
    userName?: string;
    /** 组织负责人代码 */
    orgDirectorCode?: string;
    /** 组织负责人代码描述 */
    orgDirectorCodeDesc?: string;
  }

  export interface OrgLocationItemDetail {
    /** 组织位置标识 */
    orgLocationId: string;
    /** 组织位置编码 */
    orgLocationNo: string;
    /** 组织位置名称 */
    orgLocationName: string;
    /** 组织位置辅助名称 */
    orgLocation2ndName?: string;
    /** 组织位置扩展名称 */
    orgLocationExtName?: string;
    /** 组织位置名称（语言环境） */
    orgLocationNameDisplay: string;
    /** 启用标志 */
    enabledFlag: number;
    /** 登录选择标志 */
    loginSelectFlag: number;
    /** 省编码（自治区、直辖市） */
    provinceNo?: string;
    /** 市编码（地区、州） */
    cityNo?: string;
    /** 县编码（区） */
    countyNo?: string;
    /** 地址名称（显示） */
    addrName?: string;
    /** 详细地址（村、路+门牌号等） */
    addrDetail?: string;
  }

  export interface OrgEnvSetting {
    /** LOGO */
    logo?: string;
    /** 背景图片 */
    backgroundPic?: string;
    /** 程序标题 */
    applicationTitle?: string;
    /** 版权描述 */
    copyrightDesc?: string;
  }

  export interface DeptExtInfo {
    /** 科室类型代码 */
    deptTypeCode?: string;
    /** 科室类型描述（语言环境） */
    deptTypeDesc?: string;
  }
  export interface HospitalExtInfo {
    /** 医院等级代码 */
    hospitalLevelCode?: string;
    /** 医院等级描述（语言环境） */
    hospitalLevelDesc?: string;
    /** 租户标识 */
    tenantId?: number;
    /** 租户名称 */
    tenantName?: string;
  }

  /** 组织库房类型 */
  export interface OrgStorageTypeItem {
    /** 组织的库房类型标识 */
    orgStorageTypeId: string;
    /** 库房类型代码 */
    storageTypeCode: string;
    /** 库房类型描述(语言环境) */
    storageTypeDesc: string;
  }

  /** 组织管理的药品类型 */
  export interface OrgMedicineTypeItem {
    /** 组织管理的药品类型标识 */
    orgMedicineTypeId: string;
    /** 药品类型代码 */
    medicineTypeCode: string;
    /** 药品类型描述（语言环境） */
    medicineTypeDesc: string;
  }

  export interface OrgXContactItem {
    /** 组织的联系方式标识 */
    orgXContactId?: string;
    /** 联系方式代码 */
    contactTypeCode?: string;
    /** 联系方式代码描述 */
    contactTypeCodeDesc?: string;
    /** 联系方式号码 */
    contactNo?: string;
  }

  export interface QueryMedicineParam {
    pageNumber: number;
    pageSize: number;
    keyWord?: string;
    hospitalId: string;
    enabledFlag?: number;
    encounterTypeCode?: string;
  }

  export interface QueryChargeItemParam {
    pageNumber: number;
    pageSize: number;
    keyWord?: string;
    hospitalId: string;
    encounterTypeCode?: string;
    priceAt: string;
  }

  export interface MedicineInfo {
    /** 医院商品标识 */
    hospitalCommodityId: string;
    /** 所属医院标识 */
    hospitalId: string;
    /** 所属医院名称（语言环境） */
    hospitalName: string;
    /** 商品费用分类标识 */
    commodityCategoryId: string;
    /** 商品类型【自定义添加】 */
    commodityTypeCode: string;
    /** 费用分类名称（语言环境） */
    commodityCategoryName: string;
    /** 门诊发票分类标识 */
    outCommodityCategoryId?: string;
    /** 门诊发票分类名称（语言环境） */
    outCommodityCategoryName?: string;
    /** 住院发票分类标识 */
    inCommodityCategoryId?: string;
    /** 住院发票分类名称（语言环境） */
    inCommodityCategoryName?: string;
    /** 会计分类标识 */
    accCommodityCategoryId?: string;
    /** 会计分类名称（语言环境） */
    accCommodityCategoryName?: string;
    /** 财务分类标识 */
    fncCommodityCategoryId?: string;
    /** 财务分类名称（语言环境） */
    fncCommodityCategoryName?: string;
    /** 病案分类标识 */
    mrCommodityCategoryId?: string;
    /** 病案分类名称（语言环境） */
    mrCommodityCategoryName?: string;
    /** 启用标志 */
    enabledFlag: number;
    /** 使用范围代码集合 */
    encounterTypeCodes: string[];
    /** 商品进价 */
    commodityPurchasePrice: number;
    /** 销售价格 */
    price: number;
    /** 商品标识 */
    commodityId: string;
    /** 商品编码 */
    commodityNo?: string;
    /** 商品名称 */
    commodityName: string;
    /** 商品辅助名称 */
    commodity2ndName?: string;
    /** 商品扩展名称 */
    commodityExtName?: string;
    /** 商品名称（语言环境） */
    commodityNameDisplay: string;
    /** 商品规格 */
    commoditySpec?: string;
    /** 计价单位标识 */
    unitId: string;
    /** 计价单位名称（语言环境） */
    unitName: string;
    /** 拼音码 */
    spellNo?: string;
    /** 五笔码 */
    wbNo?: string;
    /** 备注 */
    memo?: string;
    /** 生产厂家标识 */
    producedByOrgId?: number;
    /** 生产厂家名称（语言环境） */
    producedByOrgName?: string;
    /** 批准文号 */
    approvalNo?: string;
    /** 有效期（月） */
    validPeriod?: number;
    /** 药品规格标识 */
    medicineSpecId: number;
    /** 基本规格 */
    medicineSpec: string;
    /** 最小单位标识 */
    miniUnitId: number;
    /** 最小单位名称 */
    miniUnitName: string;
    /** 剂量系数 */
    doseFactor: number;
    /** 剂量单位代码 */
    doseUnitCode: string;
    /** 剂量单位描述 */
    doseUnitDesc: string;
    /** 药品通用名标识 */
    cadnId: number;
    /** 药品通用名 */
    cadn: string;
    /** 药品辅助通用名 */
    cadnExt?: string;
    /** 药品扩展通用名 */
    cadn2nd?: string;
    /** 药品通用名（英文） */
    cadnEng?: string;
    /** 药品类型代码 */
    medicineTypeCode: string;
    /** 药品类型描述 */
    medicineTypeDesc: string;
    /** 剂型代码 */
    dosageFormCode?: string;
    /** 剂型描述 */
    dosageFormDesc?: string;
    /** 药理分类代码 */
    pharmacologyClassCode?: string;
    /** 药理分类描述 */
    pharmacologyClassDesc?: string;
    /** 特殊管理药物代码(精麻毒放) */
    specialManageMedicineCode: string;
    /** 特殊管理药物描述 */
    specialManageMedicineDesc: string;
    /** 药品可用包装单位列表 */
    medicinePackUnitList: {
      /** 药品可用包装单位标识 */
      medicinePackUnitId: number;
      /** 包装单位标识 */
      packUnitId: number;
      /** 包装单位名称 */
      packUnitName: string;
      /** 换算系数 */
      convertFactor: number;
    }[];
    /** 应用场景对应的包装单位列表 */
    medicineUseSceneXUnitList: {
      /** 药品应用场景对应单位标识 */
      medicineUseSceneXUnitId: string;
      /** 药品应用场景代码 */
      medicineUseSceneCode: string;
      /** 药品应用场景描述 */
      medicineUseSceneDesc: string;
      /** 包装单位标识 */
      packUnitId: string;
      /** 包装单位名称 */
      packUnitName: string;
    }[];
  }

  export interface QueryDataSetListParam {
    /** 编码体系标识 */
    codeSystemIds?: string[];
    /** 编码体系NO */
    codeSystemNos?: string[];
    /** 关键字 */
    keyWord?: string;
    /** 启用状态 */
    enabledFlag?: number;
    /** 值标识 */
    dataValueIds?: number[];
    /** 值编码 */
    dataValueNos?: string[];
    /** 医院标识 */
    hospitalId: string;
  }

  export interface SelectionOption {
    label: string;
    value: string;
  }
}
