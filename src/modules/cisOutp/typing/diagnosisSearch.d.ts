declare namespace DiagnosisSearch {
  export interface SearchTypeReqParams {
    diagTypeCodes?: string[]; // 诊断类别代码集合
    tagIds?: string[]; // 标签标识集合
    enabledFlag?: number; // 启用标志
    keyWord?: string; // 关键字
  }

  // 诊断标签
  export interface Tag {
    diagTagId: number; // 诊断标签标识
    tagId: string; // 标签标识
    tagName: string; // 标签名称
  }

  // 诊断标签列表
  export type TagList = Tag[];

  // 诊断字典
  export interface Dict {
    diagId: string; // 诊断标识
    diagTypeCode: string; // 诊断类别代码
    diagTypeCodeDesc: string; // 诊断类别代码描述
    diagNo: string; // 诊断编码
    diagName: string; // 诊断名称
    diag2ndName: string; // 诊断辅助名称
    diagExtName: string; // 诊断扩展名称
    diagNameDisplay: string; // 诊断名称（语言环境）
    diagTagList: TagList; // 诊断标签列表
    wbNo: string; // 五笔码
    spellNo: string; // 拼音码
    enabledFlag: number; // 启用标志
  }

  // options选项
  export interface SelectOptions {
    label: string;
    value: string;
  }

  export interface DiagnosisReqParams {
    pageSize: number;
    pageNumber: number;
    diagTypeCodes?: string[]; // 诊断类别代码集合
    tagIds?: number[]; // 标签标识集合
    enabledFlag?: number; // 启用标志
    keyWord?: string; // 关键字
  }

  /**
   * 新增诊断字典请求参数
   */
  export interface DiagnosisiAddItemReqParams {
    diagTypeCode: string; // 诊断类别代码
    diagNo: string; // 诊断编码
    diagName: string; // 诊断名称
    diag2ndName?: string; // 诊断辅助名称
    diagExtName?: string; // 诊断扩展名称
    tagIds?: string[]; // 标签标识集合
    wbNo: string; // 五笔码
    spellNo: string; // 拼音码
    enabledFlag: number; // 启用标志
    diagReportFlag: number; //上报标识
  }

  /**
   * 更新诊断字典请求参数
   */
  export type DiagnosisUpdateItemReqParams = Omit<
    DiagnosisiAddItemReqParams,
    'tagIds'
  > & {
    diagId: string; // 诊断标识
    diagTags: Omit<Tag, 'tagName', 'diagTagId'>[];
  };

  export interface DiagnosisResItem {
    diagId: string; // 诊断标识
    diagTypeCode: string; // 诊断类别代码
    diagTypeCodeDesc: string; // 诊断类别代码
    diagNo: string; // 诊断编码
    diagName: string; // 诊断名称
    diag2ndName: string; // 诊断辅助名称
    diagExtName: string; // 诊断扩展名称
    diagNameDisplay: string; // 诊断名称（语言环境）
    diagTagList: Tag[]; // 诊断标签列表
    wbNo: string; // 五笔码
    spellNo: string; // 拼音码
    enabledFlag: number; // 启用标志
    diagReportFlag: number; // 上报标志
  }
}
