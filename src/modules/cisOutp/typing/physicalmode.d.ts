export interface PhysicalModelByExampleParams {
  keyWord?: string;
  enabledFlag?: number;
  syncFlag?: number;
  pageNumber: number;
  pageSize: number;
  tagId?: string;
  clearTabTypeCodeInit?: string;
  clearTabTypeCodeOnline?: string;
}

export interface PhysicalModelFormReq {
  physicalModelId?: string; // 新增时可不传，编辑时必传
  physicalModelName: string;
  physicalModelDesc: string;
  enabledFlag: number;
  syncFlag: number;
  editable: boolean;
  bizTagId: bizTagIdlist[];
  tagNameDisplay?: string;
  clearTabTypeCodeInit: string;
  clearTabTypeCodeInitDesc?: string;
  clearTabTypeCodeOnline: string;
  clearTabTypeCodeOnlineDesc?: string;
  tagId: string;
}

interface bizTagIdlist {
  tagId: string;
  tagName: string;
}
export interface addPhysicalModelReq {
  physicalModelId: string;
}
export interface queryTagGroupReq {
  bizIdTypeCode: string;
  enabledFlag: number;
  bizId?: string;
}
export interface TagGroupPara {
  tagId: string;
  tagNameDisplay: string;
}
