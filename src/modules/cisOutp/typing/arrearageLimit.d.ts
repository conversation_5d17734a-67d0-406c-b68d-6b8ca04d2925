// 定义通用的表格行类型
export interface BaseTableRow {
  orgName?: string;
  arrearageLimitAmt?: string;
  deptTypeDesc?: string;
  orgNo?: string;
  ciExecOrgName?: string;
  orgDesc?: string;
  editable: boolean;
  editBackup?: BaseTableRow;
  [key: string]: string | number | boolean | undefined | BaseTableRow; // 更精确的类型定义
}
// 定义API参数类型
export interface ApiParams {
  pageNumber: number;
  pageSize: number;
  hospitalId?: string;
  orgTypeCodes?: string[];
  deptTypeCodes?: string[];
  encounterTypeCode?: string;
  parentOrgId?: string;
  orgId?: string;
  enabledFlag: number;
}

export interface ArrearageLimitByExampleReq {
  arrearageLimitList?: arrearageLimitment[];
  arrearageLimitSettingId?: string;
  hospitalId?: string;
  arrearageLimitUseScopeCode?: string;
  useScopeBizId?: string;
  useScopeBizName?: string;
  arrearageLimitAmt?: string;
  disableAt: string;
}

export interface arrearageLimitment {
  arrearageLimitSettingId: string;
  hospitalId: string;
  arrearageLimitUseScopeCode: string;
  useScopeBizId: string;
  useScopeBizName: string;
  arrearageLimitAmt: string;
}

export interface arrearageLimitParams {
  hospitalId: string;
  arrearageLimitUseScopeCode: string;
  useScopeBizIds: string[];
  arrearageLimitAmt?: string;
}

export interface ArrearageLimitByExampleParams {
  hospitalId: string;
  arrearageLimitUseScopeCodes?: string[];
  useScopeBizIds?: string;
}

export interface limittablerow {
  orgName: string;
  arrearageLimitAmt: string;
  deptTypeDesc: string;
  orgNo: string;
  ciExecOrgName: string;
  orgDesc: string;
  editable: boolean;
}
