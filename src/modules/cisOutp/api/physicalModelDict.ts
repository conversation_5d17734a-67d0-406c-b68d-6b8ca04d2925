import { dictRequest } from '@sun-toolkit/request';

import {
  PhysicalModelByExampleParams,
  PhysicalModelFormReq,
  addPhysicalModelReq,
} from '../typing/physicalmode';

/**
 * [1-10520-1]根据条件查询值域列表
 * @param data
 * @returns
 */
export const queryPhysicalModelByExample = (
  params: PhysicalModelByExampleParams,
) => {
  return dictRequest<PhysicalModelFormReq[]>(
    '/PhysicalModel/queryPhysicalModelByExample',
    params,
  );
};

/**
 * [1-10521-1]根据条件新增值域列表
 * @param data
 * @returns
 */
export const addPhysicalModel = (params: PhysicalModelFormReq) => {
  return dictRequest<addPhysicalModelReq>(
    '/PhysicalModel/addPhysicalModel',
    params,
  );
};

/**
 * [1-10522-1]根据条件编辑值域列表
 * @param data
 * @returns
 */
export const editPhysicalModel = (params: PhysicalModelFormReq) => {
  return dictRequest<boolean>('/PhysicalModel/editPhysicalModel', params);
};

/**
 * [1-10536-1]根据条件删除值域列表
 * @param data
 * @returns
 */
export const deletePhysicalModel = (params: addPhysicalModelReq) => {
  return dictRequest<boolean>('/PhysicalModel/deletePhysicalModel', params);
};
