import { dictRequest } from '@sun-toolkit/request';
import { type CodeSystem } from 'sun-biz';

/**
 * [1-10438-1]查询申请单模板
 * @param params
 * @returns
 */
export const queryApplyTemplate = (params: Apply.QueryApplyTemplateParam) => {
  return dictRequest<
    Apply.QueryApplyTemplateResponse,
    Apply.QueryApplyTemplateParam
  >('/CliApplyService/queryApplyTemplate', params);
};

/**
 * [1-10437-1]保存申请单模板
 * @param params
 * @returns
 */
export const saveApplyTemplate = (params: Apply.SaveApplyTemplateParam) => {
  return dictRequest<
    Apply.ApplyTemplateItem['applyTempId'],
    Apply.SaveApplyTemplateParam
  >('/CliApplyService/saveApplyTemplate', params);
};

/**
 * [1-10432-1]根据条件查询申请单模板项目
 * @param params
 * @returns
 */
export const queryCliApplyItemByExample = (
  params: Apply.QueryCliApplyItemByExampleParam,
) => {
  return dictRequest<
    Apply.QueryCliApplyItemByExampleResponse,
    Apply.QueryCliApplyItemByExampleParam
  >('/CliApplyService/queryCliApplyItemByExample', params, {
    cancel: false,
  });
};

/**
 * [1-10431-1]保存申请单模板项目
 * @param params
 * @returns
 */
export const saveCliApplyItem = (params: Apply.SaveCliApplyItemParam) => {
  return dictRequest<null, Apply.SaveCliApplyItemParam>(
    '/CliApplyService/saveCliApplyItem',
    params,
  );
};

/**
 * [1-10171-1]获取当前登录对应的集团及医院列表
 * @param
 * @returns
 */
export const queryOrgAndHospitalList = () => {
  return dictRequest<ClinicalItem.OrgAndHospitall[]>(
    '/organization/queryOrgAndHospitalList',
  );
};

/**
 * [1-10462-1]根据条件查询临床项目
 * @param
 * @returns
 */
export const queryClinicalItem = (
  params: ClinicalItem.QueryClinicalItemParams,
) => {
  return dictRequest<ClinicalItem.QueryClinicalItemResponse>(
    '/ClinicalItem/queryClinicalItem',
    params,
  );
};

/**
 * [1-10006-1]根据条件查询值域列表
 * @param data
 * @returns
 */
export const queryDataSetListByExample = (
  params: Apply.QueryDataSetListParam,
) => {
  return dictRequest<CodeSystem[], Apply.QueryDataSetListParam>(
    '/codeSystem/queryDataSetListByExample',
    params,
  );
};
