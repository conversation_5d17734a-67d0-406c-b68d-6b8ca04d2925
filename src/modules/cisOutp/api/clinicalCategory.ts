import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10413-1]查询服务分类
 * @param params
 * @returns
 */
export const queryCliServiceCategory = () => {
  return dictRequest<ClinicalCategory.IQueryCliServiceCategoryResItem[]>(
    '/CliServiceCategory/queryCliServiceCategory',
    {},
  );
};

/**
 * [1-10411-1]新增服务分类
 * @param params
 * @returns
 */
export const addCliServiceCategory = (
  params: ClinicalCategory.IAddCliServiceCategoryParam,
) => {
  return dictRequest<
    { /** 类别标识 */ csCategoryId: string },
    ClinicalCategory.IAddCliServiceCategoryParam
  >('/CliServiceCategory/addCliServiceCategory', params);
};

/**
 * [1-10412-1]根据标识修改服务分类
 * @param params
 * @returns
 */
export const updateCliServiceCategoryById = (
  params: ClinicalCategory.IUpdateCliServiceCategoryByIdParam,
) => {
  return dictRequest<null, ClinicalCategory.IUpdateCliServiceCategoryByIdParam>(
    '/CliServiceCategory/updateCliServiceCategoryById',
    params,
  );
};

/**
 * [1-10421-1]删除服务分类
 * @param params
 * @returns
 */
export const deleteCliServiceCategory = (params: {
  /** 类别标识 */ csCategoryId: string;
}) => {
  return dictRequest<null, { /** 类别标识 */ csCategoryId: string }>(
    '/CliServiceCategory/deleteCliServiceCategory',
    params,
  );
};
