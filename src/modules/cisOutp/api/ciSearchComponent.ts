import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10429-1]根据条件检索临床项目（业务态）
 * 不分页
 * @param params
 * @returns
 */
export const queryCliItemSearchComponent = (params: {
  enabledFlag?: number;
}) => {
  return dictRequest<
    CiSearchComponent.IQueryCliItemByExampleRespItem[],
    {
      enabledFlag?: number;
    }
  >('/ClinicalItemSearchComponent/queryCliItemSearchComponent', params);
};

/**
 * [1-10428-1] 根据标识更新临床项目检索组件
 * @param params
 * @returns
 */
export const updateCliItemSearchComponentById = (
  params: CiSearchComponent.IUpdateCliItemSearchComponentByIdParam,
) => {
  return dictRequest<
    null,
    CiSearchComponent.IUpdateCliItemSearchComponentByIdParam
  >('/ClinicalItemSearchComponent/updateCliItemSearchComponentById', params);
};

/**
 * [1-10427-1] 新增临床项目检索组件
 * @param params
 * @returns
 */
export const addCliItemSearchComponent = (
  params: CiSearchComponent.IAddCliItemSearchComponentParam,
) => {
  return dictRequest<
    { componentId: string },
    CiSearchComponent.IAddCliItemSearchComponentParam
  >('/ClinicalItemSearchComponent/addCliItemSearchComponent', params);
};

/**
 * [1-10424-1] 根据条件查询临床检索分组设置
 * @param params
 * @returns
 */
export const queryCliItemSearchSettingByExample = (
  params: CiSearchComponent.IQueryCliItemSearchSettingByExampleParam,
) => {
  return dictRequest<
    CiSearchComponent.IQueryCliItemSearchSettingByExampleRespItem[],
    CiSearchComponent.IQueryCliItemSearchSettingByExampleParam
  >('/ClinicalItemSearchComponent/queryCliItemSearchSettingByExample', params);
};

/**
 * [1-10423-1] 根据标识删除临床项目检索分组设置
 * @param params
 * @returns
 */
export const deleteCliItemSearchSettingById = (params: {
  ciSearchGroupId: string;
}) => {
  return dictRequest<
    null,
    {
      ciSearchGroupId: string;
    }
  >('/ClinicalItemSearchComponent/deleteCliItemSearchSettingById', params);
};

/**
 * [1-10422-1] 根据标识更新临床项目检索分组设置
 * @param params
 * @returns
 */
export const updateCliItemSearchSettingById = (
  params: CiSearchComponent.IUpdateCliItemSearchSettingByIdParam,
) => {
  return dictRequest<
    null,
    CiSearchComponent.IUpdateCliItemSearchSettingByIdParam
  >('/ClinicalItemSearchComponent/updateCliItemSearchSettingById', params);
};

/**
 * [1-10382-1] 新增临床项目检索分组设置
 * @param params
 * @returns
 */
export const addCliItemSearchSetting = (
  params: CiSearchComponent.IAddCliItemSearchSettingParam,
) => {
  return dictRequest<
    { ciSearchGroupId: string },
    CiSearchComponent.IAddCliItemSearchSettingParam
  >('/ClinicalItemSearchComponent/addCliItemSearchSetting', params);
};
