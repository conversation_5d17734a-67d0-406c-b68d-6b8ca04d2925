declare namespace EsChargeStandard {
  interface QueryParams {
    hospitalId?: string;
    keyWord?: string;
    esChargeStandardId?: string;
    usingFlag?: number;
  }
  interface UpdateEsChargeStandardEnabledFlag {
    esChargeStandardId?: string;
    enabledFlag?: number;
  }
  interface EsChargeStandardInfo {
    esChargeStandardId?: string;
    esChargeStandardName?: string;
    esChargeStandard2ndName?: string;
    esChargeStandardExtName?: string;
    esChargeStandardNameDisplay?: string;
    regTypeId?: string;
    regTypeName?: string;
    amt?: number;
    enabledFlag?: number;
    createdOrgLocationId?: string;
    createdOrgLocationName?: string;
    createdUserId?: string;
    createdUserName?: string;
    createdAt?: string;
    modifiedOrgLocationId?: string;
    modifiedOrgLocationName?: string;
    modifiedUserId?: string;
    modifiedUserName?: string;
    modifiedAt?: string;
    hospitalChargeItemList?: HospitalChargeItemInfo[] | undefined;
  }
  interface HospitalChargeItemInfo {
    chargeStandardSettingId?: string;
    hospitalCommodityId?: string;
    commodityName?: string;
    unitId?: string;
    unitName?: string;
    price?: number;
    num?: number;
    startAt?: string;
    endAt?: string;
    enabledFlag?: number;
    remindFlag?: number;
  }
}
