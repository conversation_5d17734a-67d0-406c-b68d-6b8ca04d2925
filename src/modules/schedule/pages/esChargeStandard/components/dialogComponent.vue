<script setup lang="ts" name="esChargeStandardDialog">
  import { FLAG } from '@/utils/constant';
  import { cloneDeep, dayjs } from '@sun-toolkit/shared';
  import { useTranslation } from 'i18next-vue';
  import { useEsChargeItemConfig } from '../config/useEsChargeStandardColumnConfig';
  import { useEsChargeStandardConfig } from '../config/useFormConfigData';
  import { type FormInstance, ElMessage } from 'element-sun';
  import { ref, watch, nextTick, computed } from 'vue';
  import {
    useHospitalChargeItemList,
    useRegisterTypeList,
  } from '../hooks/useOptionsData';
  import {
    addEsChargeStandard,
    updateEsChargeStandardById,
  } from '@/modules/schedule/api/esChargeStandard';
  import { Title, ProForm, ProTable, ProDialog, TableRef } from 'sun-biz';

  type formModelType = {
    esChargeStandardName: string | undefined;
    esChargeStandard2ndName: string | undefined;
    esChargeStandardExtName: string | undefined;
    regTypeId: string | undefined;
    enabledFlag: number;
  };

  const emit = defineEmits(['success']);
  const props = defineProps<{
    rowValue: EsChargeStandard.EsChargeStandardInfo;
    dialogTitle: string;
    hospitalId: string;
  }>();

  const { t } = useTranslation();
  // 医院收费项目hooks
  const { esChargeItemList, getHospitalChargeItemList } =
    useHospitalChargeItemList();
  const { registerTypeList, getRegisterTypeList } = useRegisterTypeList();

  // 医院挂号类别hooks

  const dialogRef = ref(); // 弹窗ref
  const formModelRef = ref<{
    ref: FormInstance;
  }>();
  const tableRef = ref<TableRef>();

  const tableData = ref<EsChargeStandard.HospitalChargeItemInfo[]>([]);
  /** 表单数据 */
  const formModel = ref<formModelType>({
    esChargeStandardName: undefined,
    esChargeStandard2ndName: undefined,
    esChargeStandardExtName: undefined,
    regTypeId: undefined,
    enabledFlag: FLAG.YES,
  });

  // 医院id
  const hospitalId = computed(() => {
    return props.hospitalId;
  });

  /** 打开弹窗 */
  const openDialog = async () => {
    nextTick(async () => {
      const rowValue = cloneDeep(props.rowValue);
      dialogRef.value.open();
      formModel.value = {
        esChargeStandardName: rowValue?.esChargeStandardName ?? undefined,
        esChargeStandard2ndName: rowValue?.esChargeStandard2ndName ?? undefined,
        esChargeStandardExtName: rowValue?.esChargeStandardExtName ?? undefined,
        regTypeId: rowValue?.regTypeId ?? undefined,
        enabledFlag: (rowValue?.enabledFlag ?? FLAG.YES) as number,
      };
    });
    await getHospitalChargeItemList({
      hospitalId: hospitalId.value,
    } as ChargeItem.QueryParamsHospitalChargeItem);
    await getRegisterTypeList();
  };

  /**  确定提交 */
  const handleConfirmSubmit = async () => {
    await Promise.all([
      formModelRef.value?.ref.validate(),
      tableRef.value?.formRef.validate(),
    ]);
    if (!(tableData.value && tableData.value?.length > 0)) {
      ElMessage.error(
        t('hospitalChargeItemList.notNull', '收费项目明细不能为空'),
      );
      return [Error('error')];
    }
    const params = {
      esChargeStandardId: props.rowValue?.esChargeStandardId ?? undefined,
      hospitalId: hospitalId.value,
      ...formModel?.value,
      hospitalChargeItemList: tableData.value,
    };
    const flag = Boolean(props.rowValue?.esChargeStandardId);
    return !flag
      ? await addEsChargeStandard(params)
      : await updateEsChargeStandardById(params);
  };

  // form配置
  const { esChargeStandardDialogForm } = useEsChargeStandardConfig(
    getRegisterTypeList,
    registerTypeList,
  );
  // table配置
  const { addItem, tableConfig } = useEsChargeItemConfig({
    tableRef: tableRef,
    data: tableData,
    id: 'chargeStandardSettingId',
    hospitalId: hospitalId,
    esChargeItemList: esChargeItemList,
    getHospitalChargeItemList: getHospitalChargeItemList,
  });

  watch(
    () => props.rowValue,
    (val) => {
      if (val) {
        tableData.value = val?.hospitalChargeItemList ?? [];
      }
    },
  );

  defineExpose({ open: openDialog });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    class="w-4/5"
    :title="props.dialogTitle ?? ''"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :confirm-fn="() => handleConfirmSubmit() as Promise<[never, unknown]>"
    :align-center="true"
    @success="() => emit('success')"
    :before-close="
      (done: () => void) => {
        nextTick(() => {
          done();
        });
      }
    "
  >
    <ProForm
      ref="formModelRef"
      v-model="formModel"
      :data="esChargeStandardDialogForm"
    />
    <div class="mb-2 flex justify-between">
      <Title :title="$t('esChargeStandard.title', '收费项目明细')" />
      <el-button
        type="primary"
        @click="
          () => {
            addItem({
              enabledFlag: FLAG.YES,
              remindFlag: FLAG.YES,
              editable: true,
              startAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              endAt: '2099-12-31 23:59:59',
            });
          }
        "
        >{{ $t('global:add') }}</el-button
      >
    </div>
    <ProTable
      :max-height="300"
      ref="tableRef"
      :editable="true"
      :columns="tableConfig"
      :data="tableData"
    />
  </ProDialog>
</template>
