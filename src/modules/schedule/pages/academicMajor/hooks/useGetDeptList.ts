import { ref } from 'vue';
import { queryDepartmentListByExample } from '@/modules/system/api/org';
import { FLAG } from '@/utils/constant.ts';

export function useGetDeptList() {
  /** 加载状态 */
  const loading = ref(false);
  /** 组织信息 */
  const deptList = ref<Org.Item[]>([]);
  const getDeptList = async (params: Org.queryDepartmentParams) => {
    loading.value = true;
    const defaultParams = {
      enabledFlag: FLAG.YES,
    };
    params = {
      ...defaultParams,
      ...params,
    };
    const [, res] = await queryDepartmentListByExample(params);
    loading.value = false;
    if (res?.success) {
      deptList.value = res.data;
    }
  };
  return {
    getDeptList,
    deptList,
    loading,
  };
}
