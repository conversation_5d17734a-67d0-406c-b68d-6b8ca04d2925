import { generateUUID } from '@sun-toolkit/shared';
/** 数据处理函数 铺平 */
export function flattenData(data: AcademicMajor.EsProviderInfo[]) {
  const result: (AcademicMajor.EsProviderInfo & {
    sort?: number;
    spanColumnIndex?: number;
  })[] = [];

  data.forEach((item, index) => {
    if (item.encounterServiceList && item.encounterServiceList.length > 0) {
      item.encounterServiceList.forEach((mapping, ix) => {
        const newItem = {
          ...item,
          ...mapping,
          key:
            (
              item as {
                key: string;
              }
            ).key ?? generateUUID(),
          cancelToDeleteId: (mapping?.esChargeStandardId ?? '') + ix,
          sort: index + 1,
          spanColumnIndex: ix === 0 ? item.encounterServiceList?.length : 0,
        };
        result.push(newItem);
      });
    } else {
      result.push({
        ...item,
        key:
          (
            item as {
              key: string;
            }
          ).key ?? generateUUID(),
        sort: index + 1,
        spanColumnIndex: 1,
      } as AcademicMajor.EsProviderInfo & {
        sort?: number;
        spanColumnIndex?: number;
      });
    }
  });

  return result;
}

/** 数据处理函数 合并 */
export function spanData(
  data: (AcademicMajor.EsProviderInfo &
    AcademicMajor.EncounterServiceInfo & {
      editable?: boolean;
      key?: string;
    })[],
) {
  const resultMap = new Map();

  data.forEach((item) => {
    const {
      providerId,
      providerName,
      providerNo,
      esProviderTypeCode,
      esChargeStandardId,
      esChargeStandardNameDisplay,
      limitEsSourceFlag,
      autoFreeFlag,
      esProvideTimePeriodList,
      editable,
      key,
      ...rest
    } = item;

    if (!resultMap.has(providerId ?? key)) {
      resultMap.set(providerId ?? key, {
        ...rest,
        providerId,
        providerNo,
        providerName,
        esProviderTypeCode,
        encounterServiceList: [],
      });
    }

    resultMap.get(providerId ?? key).encounterServiceList.push({
      key,
      esChargeStandardId,
      esChargeStandardNameDisplay,
      limitEsSourceFlag,
      autoFreeFlag,
      esProvideTimePeriodList,
      editable,
    });
  });

  return Array.from(resultMap.values());
}
