import { Ref, ComputedRef } from 'vue';
import { FLAG } from '@sun-toolkit/enums';
import { useFormConfig } from 'sun-biz';

export function useAcademicMajorFormConfig(options: {
  deptList: Ref<Org.Item[]>;
  isDisableApt: ComputedRef<boolean>;
  getOrgListData: (keyWord?: string) => Promise<void>;
}) {
  const { deptList, isDisableApt, getOrgListData } = options;

  const academicMajorForm = useFormConfig({
    getData: (t) => {
      return [
        {
          name: 'academicMajorName',
          label: t('academicMajor.detail.name', '专业名称'),
          component: 'input',
          autoConvertSpellNoAndWbNo: true,
          placeholder: t('global:placeholder.input.template', {
            content: t('academicMajor.detail.name', '专业名称'),
          }),
          rules: [
            {
              required: true,
              message: t('global:placeholder.input.template', {
                content: t('academicMajor.detail.name', '专业名称'),
              }),
              trigger: 'change',
            },
          ],
        },
        {
          name: 'academicMajor2ndName',
          label: t('global:secondName'),
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('global:secondName'),
          }),
        },
        {
          name: 'academicMajorExtName',
          label: t('global:thirdName'),
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('global:thirdName'),
          }),
        },
        {
          name: 'deptId',
          label: t('person.belongDeptName', '所属科室'),
          component: 'select',
          placeholder: t('global:placeholder.select.template', {
            name: t('person.belongDeptName', '所属科室'),
          }),
          rules: [
            {
              required: true,
              message: t('global:placeholder.select.template', {
                name: t('person.belongDeptName', '所属科室'),
              }),
              trigger: 'change',
            },
          ],
          extraProps: {
            remote: true,
            disabled: isDisableApt.value,
            filterable: true,
            remoteShowSuffix: true,
            options: deptList.value,
            remoteMethod: getOrgListData,
            props: {
              label: 'orgNameDisplay',
              value: 'orgId',
            },
          },
        },
        {
          name: 'wbNo',
          label: t('global:wbNo'),
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('global:wbNo'),
          }),
        },
        {
          name: 'spellNo',
          label: t('global:spellNo'),
          component: 'input',
          placeholder: t('global:placeholder.input.template', {
            content: t('global:spellNo'),
          }),
        },
        {
          name: 'enabledFlag',
          label: t('global:enabledFlag'),
          component: 'switch',
          extraProps: {
            'inline-prompt': true,
            'active-value': FLAG.YES,
            'inactive-value': FLAG.NO,
            'active-text': t('global:enabled'),
            'inactive-text': t('global:disabled'),
          },
        },
      ];
    },
  });
  return academicMajorForm;
}
