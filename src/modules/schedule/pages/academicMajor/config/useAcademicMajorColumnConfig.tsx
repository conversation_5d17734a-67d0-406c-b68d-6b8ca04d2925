import { Ref, ComputedRef } from 'vue';
import { UserReqItem } from '@/api/types';
import { dayjs, generateUUID } from '@sun-toolkit/shared';
import { useTranslation } from 'i18next-vue';
import { Plus, Minus } from '@element-sun/icons-vue';
import { ES_PROVIDER_TYPE_CODE, FLAG } from '@/utils/constant';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';
// 来源table的tsx
export function useAcademicMajorColumnConfig(options: {
  handleEnableSwitch: (row: AcademicMajor.AcademicMajorInfo) => Promise<void>;
  goDetail: (row: AcademicMajor.AcademicMajorInfo) => Promise<void>;
}) {
  const { handleEnableSwitch, goDetail } = options;

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 100,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t(
          'academicMajor.table.academicMajorNameDisplay',
          '学科专业名称',
        ),
        minWidth: 150,
        prop: 'academicMajorNameDisplay',
      },
      {
        label: t('global:secondName'),
        minWidth: 150,
        prop: 'academicMajor2ndName',
      },
      {
        label: t('global:thirdName'),
        minWidth: 150,
        prop: 'academicMajorExtName',
      },
      {
        label: t('person.belongDeptName', '所属科室'),
        minWidth: 150,
        prop: 'deptName',
      },
      {
        label: t('global:enableStatus'),
        prop: 'enabledFlag',
        minWidth: 150,
        render: (row: AcademicMajor.AcademicMajorInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 150,
        render: (row: AcademicMajor.AcademicMajorInfo) => {
          return (
            <el-button type="primary" link onClick={() => goDetail(row)}>
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });
}

export function useAcademicMajorDetailColumnConfig(options: {
  codeType: string;
  tableRef: Ref<TableRef>;
  tableData: Ref<
    (AcademicMajor.EsProviderInfo & {
      editable: boolean;
    })[]
  >;
  list: ComputedRef<(UserReqItem | Org.Item)[]>;
  esChargeStandardList: Ref<EsChargeStandard.EsChargeStandardInfo[]>;
  changeProviderCode: (
    row: AcademicMajor.EsProviderInfo,
    code?: string,
    val?: string,
  ) => Promise<void>;
  remoteProviderCode: (
    row: AcademicMajor.EsProviderInfo,
    code?: string,
    keyWord?: string,
  ) => Promise<void>;
  changeEsCharge: (
    row: AcademicMajor.EsProviderInfo,
    val?: string,
  ) => Promise<void>;
  getEsChargeStandardListData: (keyWord?: string) => Promise<void>;
  removeItem: (
    row: AcademicMajor.EsProviderInfo & AcademicMajor.EncounterServiceInfo,
    index: number,
    code?: string,
  ) => Promise<void>;
  updateTable: () => Promise<void>;
}) {
  const {
    codeType,
    tableRef,
    tableData,
    list,
    esChargeStandardList,
    changeProviderCode,
    remoteProviderCode,
    changeEsCharge,
    getEsChargeStandardListData,
    removeItem,
    updateTable,
  } = options;

  const { toggleEdit, cancelEdit, addItem, delItem, insertItem } =
    useEditableTable({
      tableRef: tableRef,
      data: tableData,
      id: 'key',
    });

  const { t } = useTranslation();
  // 时间的自定义校验
  const timeValidate = (
    rule: unknown,
    value: unknown | AcademicMajor.EsProvideTimePeriodInfo[],
    callback: (data?: Error | undefined) => void,
  ) => {
    const items = (value ?? []) as AcademicMajor.EsProvideTimePeriodInfo[];

    // 遍历数组进行时间缺失和时间顺序的检查
    for (const item of items) {
      // 检查开始时间和结束时间是否都存在
      if (!(item.startTime && item.endTime)) {
        callback(
          new Error(
            t('global:placeholder.select.template', {
              name: t('timer.tips', '开始时间和结束时间'),
            }),
          ),
        );
        return;
      }

      // 检查开始时间是否大于结束时间
      if (dayjs(item.startTime).isAfter(dayjs(item.endTime))) {
        callback(
          new Error(t('startTime.endTime.error', '开始时间不能大于结束时间')),
        );
        return;
      }
    }

    // 如果所有项都通过校验，则调用 callback 无错误
    callback();
  };

  const timeValid = (row: AcademicMajor.EncounterServiceInfo) => {
    if (row.limitEsSourceFlag === FLAG.YES) {
      return [];
    } else {
      return [
        {
          validator: timeValidate,
          trigger: ['change', 'blur'],
        },
      ];
    }
  };

  // 渲染
  const tableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 100,
        render: (row: AcademicMajor.EsProviderInfo) => <>{row.sort}</>,
      },
      {
        label:
          codeType === ES_PROVIDER_TYPE_CODE.USER
            ? t('academicMajorUser.codeNo', '用户编码')
            : t('academicMajorDept.codeNo', '科室编码'),
        prop: 'providerNo',
        minWidth: 120,
        editable: true,
        required: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name:
                codeType === ES_PROVIDER_TYPE_CODE.USER
                  ? t('academicMajorUser.codeNo', '用户编码')
                  : t('academicMajorDept.codeNo', '科室编码'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: AcademicMajor.EsProviderInfo & {
            editable: boolean;
            tempId: number;
          },
        ) => {
          if (row?.editable && row.tempId) {
            return (
              <div class="w-full">
                <el-select
                  class="mx-auto w-10/12"
                  v-model={row.providerId}
                  onChange={(val: string) =>
                    changeProviderCode(row, codeType, val)
                  }
                  filterable
                  clearable
                  remote={true}
                  remote-method={(keyWord: string) =>
                    remoteProviderCode(row, codeType, keyWord)
                  }
                  placeholder={t('global:placeholder.select.template', {
                    name:
                      codeType === ES_PROVIDER_TYPE_CODE.USER
                        ? t('academicMajorUser.codeNo', '用户编码')
                        : t('academicMajorDept.codeNo', '科室编码'),
                  })}
                >
                  {(
                    (list.value ?? []) as ((UserReqItem | Org.Item) & {
                      label: string;
                      value: string;
                    })[]
                  )
                    ?.filter((im) => {
                      const providerIds = tableData.value
                        .filter(
                          (tableItem) =>
                            tableItem.providerId !== row.providerId,
                        )
                        .map((item) => item.providerId);

                      return !providerIds.includes(im.value);
                    })
                    ?.map((item) => (
                      <el-option
                        key={item.value}
                        label={item.label}
                        value={item.value}
                      />
                    ))}
                </el-select>
              </div>
            );
          } else {
            return <div class={'w-full text-center'}>{row.providerNo}</div>;
          }
        },
      },
      {
        label:
          codeType === ES_PROVIDER_TYPE_CODE.USER
            ? t('academicMajorUser.name', '用户名称')
            : t('academicMajorDept.name', '科室名称'),
        prop: 'providerName',
        minWidth: 120,
        render: (
          row: AcademicMajor.EsProviderInfo & {
            editable: true;
          },
        ) => {
          return <>{row.providerName}</>;
        },
      },
      {
        label: t('academicMajor.detail.esChargeStandard', '收费标准'),
        prop: 'esChargeStandardId',
        minWidth: 150,
        editable: true,
        required: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('academicMajor.detail.esChargeStandard', '收费标准'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (
          row: AcademicMajor.EncounterServiceInfo &
            AcademicMajor.EsProviderInfo & {
              editable: true;
            },
        ) => {
          if (row.editable) {
            return (
              <>
                <el-select
                  v-model={row.esChargeStandardId}
                  onChange={(val: string) => changeEsCharge(row, val)}
                  filterable
                  clearable
                  remote={true}
                  remote-method={getEsChargeStandardListData}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'academicMajor.detail.chargingStandard.',
                      '收费标准',
                    ),
                  })}
                >
                  {(esChargeStandardList.value ?? [])
                    .filter((item) => {
                      // 获取当前行相同providerId的所有行的esChargeStandardId列表
                      const usedStandardIds = tableData.value
                        .filter(
                          (im) =>
                            // 相同providerId但不是当前行
                            im.providerId === row.providerId && im !== row,
                        )
                        .map(
                          (item) =>
                            (
                              item as unknown as AcademicMajor.EncounterServiceInfo
                            ).esChargeStandardId,
                        )
                        .filter(Boolean); // 过滤掉undefined或null值

                      // 返回未被使用的收费标准
                      return !usedStandardIds.includes(item.esChargeStandardId);
                    })
                    .map((item) => (
                      <el-option
                        key={item.esChargeStandardId}
                        label={item.esChargeStandardNameDisplay}
                        value={item.esChargeStandardId}
                      />
                    ))}
                </el-select>
              </>
            );
          } else {
            return <>{row.esChargeStandardNameDisplay}</>;
          }
        },
      },
      {
        label: t('academicMajor.detail.freeFlag', '免费标志'),
        prop: 'autoFreeFlag',
        minWidth: 150,
        editable: true,
        required: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('academicMajor.detail.autoFreeFlag', '免费标志'),
            }),
            trigger: 'change',
          },
        ],
        render: (
          row: AcademicMajor.EncounterServiceInfo & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <>
                <el-select
                  v-model={row.autoFreeFlag}
                  clearable={false}
                  placeholder={t('global:placeholder.select.template', {
                    name: t('academicMajor.detail.autoFreeFlag', '免费标志'),
                  })}
                >
                  <el-option value={FLAG.YES} label={t('global:yes')} />
                  <el-option value={FLAG.NO} label={t('global:no')} />
                </el-select>
              </>
            );
          } else {
            return (
              <>
                {row.autoFreeFlag === FLAG.YES
                  ? t('global:yes')
                  : t('global:no')}
              </>
            );
          }
        },
      },
      {
        label: t('academicMajor.detail.limitEsSourceFlag', '限号标志'),
        prop: 'limitEsSourceFlag',
        minWidth: 150,
        editable: true,
        required: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('academicMajor.detail.limitEsSourceFlag', '限号标志'),
            }),
            trigger: 'change',
          },
        ],
        render: (
          row: AcademicMajor.EncounterServiceInfo & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <>
                <el-select
                  clearable={false}
                  v-model={row.limitEsSourceFlag}
                  onChange={(val: number) => {
                    if (val === FLAG.YES) {
                      row.esProvideTimePeriodList = [
                        {
                          timeList: [],
                          startTime: '',
                          endTime: '',
                        },
                      ];
                    }
                  }}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'academicMajor.detail.limitEsSourceFlag',
                      '限号标志',
                    ),
                  })}
                >
                  <el-option value={FLAG.YES} label={t('global:yes')} />
                  <el-option value={FLAG.NO} label={t('global:no')} />
                </el-select>
              </>
            );
          } else {
            return (
              <>
                {row.limitEsSourceFlag === FLAG.YES
                  ? t('global:yes')
                  : t('global:no')}
              </>
            );
          }
        },
      },
      {
        label: t('academicMajor.detail.timeSetting', '不限号出诊时段限制'),
        prop: 'esProvideTimePeriodList',
        minWidth: 450,
        editable: true,
        required: true,
        rules: (row: AcademicMajor.EncounterServiceInfo) => timeValid(row),
        render: (
          row: AcademicMajor.EncounterServiceInfo & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <div
                class={`flex flex-col gap-y-4 ${row.limitEsSourceFlag === FLAG.YES ? 'w-full' : ''}`}
              >
                {row.limitEsSourceFlag === FLAG.YES ? (
                  <div class={'w-full text-center'}>--</div>
                ) : row?.esProvideTimePeriodList &&
                  row?.esProvideTimePeriodList.length > 0 ? (
                  row?.esProvideTimePeriodList?.map(
                    (
                      item: AcademicMajor.EsProvideTimePeriodInfo,
                      index: number,
                    ) => (
                      <div class={'flex items-center gap-2'}>
                        <el-time-picker
                          v-model={item.timeList}
                          onChange={(val: string) => {
                            item.startTime = val?.[0];
                            item.endTime = val?.[1];
                          }}
                          is-range={true}
                          value-format="HH:mm"
                          format="HH:mm"
                          range-separator="至"
                          start-placeholder="开始时间"
                          end-placeholder="结束时间"
                        />
                        <el-icon
                          onClick={() =>
                            (
                              row.esProvideTimePeriodList as AcademicMajor.EsProvideTimePeriodInfo[]
                            ).push({
                              timeList: [],
                              startTime: '',
                              endTime: '',
                            })
                          }
                        >
                          {<Plus />}
                        </el-icon>
                        {row?.esProvideTimePeriodList.length > 1 && (
                          <el-icon
                            onClick={() =>
                              row.esProvideTimePeriodList.splice(index, 1)
                            }
                          >
                            {<Minus />}
                          </el-icon>
                        )}
                      </div>
                    ),
                  )
                ) : (
                  <el-icon
                    onClick={() =>
                      (row.esProvideTimePeriodList =
                        row.esProvideTimePeriodList || [
                          {
                            timeList: [],
                            startTime: '',
                            endTime: '',
                          },
                        ])
                    }
                  >
                    {<Plus />}
                  </el-icon>
                )}
              </div>
            );
          } else {
            if (
              row.esProvideTimePeriodList &&
              row.esProvideTimePeriodList.length > 0
            ) {
              return (
                <div class="flex flex-wrap gap-2">
                  {// row.limitEsSourceFlag === FLAG.YES ? (
                  //   <div class={'w-full text-center'}>--</div>
                  // ) : (
                  row.esProvideTimePeriodList?.map(
                    (item: AcademicMajor.EsProvideTimePeriodInfo) =>
                      item.startTime || item.endTime ? (
                        <el-tag type="info" size="small">
                          {item.startTime}-{item.endTime}
                        </el-tag>
                      ) : (
                        <div class={'w-full text-center'}>--</div>
                      ),
                  )
                  // )
                  }
                </div>
              );
            } else {
              return <div class={'w-full text-center'}>--</div>;
            }
          }
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 130,
        render: (
          row: AcademicMajor.EsProviderInfo &
            AcademicMajor.EncounterServiceInfo & {
              editable: boolean;
              tempId?: number;
              key: string;
            },
          index: number,
        ) => {
          if (row.editable) {
            return (
              <div class={'flex items-center justify-around'} key="editable">
                <el-button
                  type="danger"
                  link={true}
                  onClick={async () => {
                    await cancelEdit(row, index);
                    await updateTable();
                  }}
                >
                  {t('global:cancel')}
                </el-button>
                <el-button
                  type="primary"
                  link={true}
                  onClick={async () => {
                    await toggleEdit(row);
                    if (!row.editable) {
                      delete row.tempId;
                      await updateTable();
                    }
                  }}
                >
                  {t('global:confirm')}
                </el-button>
              </div>
            );
          } else {
            return (
              <div class={'flex items-center justify-around'}>
                <el-button
                  type="danger"
                  link={true}
                  onClick={async () => {
                    await removeItem(row, index, codeType);
                  }}
                >
                  {t('global:remove')}
                </el-button>
                <el-button
                  type="primary"
                  link={true}
                  onClick={async () => {
                    await insertItem(
                      {
                        editable: true,
                        key: generateUUID(),
                        providerId: row.providerId,
                        providerNo: row.providerNo,
                        providerName: row.providerName,
                        esChargeStandardId: undefined,
                        esProviderTypeCode: row.esProviderTypeCode,
                        esProviderTypeDesc: row.esProviderTypeDesc,
                        autoFreeFlag: FLAG.NO,
                        limitEsSourceFlag: FLAG.YES,
                        sort: row.sort,
                        esProvideTimePeriodList: [
                          {
                            timeList: [],
                            startTime: '',
                            endTime: '',
                          },
                        ],
                      } as AcademicMajor.EsProviderInfo &
                        AcademicMajor.EncounterServiceInfo & {
                          editable: boolean;
                        },
                      index,
                      1,
                    );
                    await updateTable();
                  }}
                >
                  {t('global:append')}
                </el-button>
                <el-button
                  type="primary"
                  link={true}
                  onClick={async () => {
                    await toggleEdit(row);
                    // 初始化timeList
                    if (
                      row.esProvideTimePeriodList &&
                      row.esProvideTimePeriodList.length > 0
                    ) {
                      row.esProvideTimePeriodList.forEach((item) => {
                        if (item.startTime && item.endTime) {
                          item.timeList = [item.startTime, item.endTime];
                        }
                      });
                    } else {
                      row.esProvideTimePeriodList = [
                        {
                          timeList: [],
                          startTime: '',
                          endTime: '',
                        },
                      ];
                    }
                    await updateTable();
                  }}
                >
                  {t('global:edit')}
                </el-button>
              </div>
            );
          }
        },
      },
    ],
  });
  return {
    tableConfig,
    delItem,
    insertItem,
    addItem,
  };
}
