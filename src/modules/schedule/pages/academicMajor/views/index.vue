<script setup lang="ts" name="academicMajor">
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { useTranslation } from 'i18next-vue';
  import { Title, ProForm, ProTable } from 'sun-biz';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { DEPT_TYPE_CODE, ENCOUNTER_TYPE_CODE } from '@/utils/constant.ts';
  import { DEFAULT_PAGE_SIZE, FLAG } from '@sun-toolkit/enums';
  import { useGetDeptList } from '../hooks/useGetDeptList.ts';
  import { useSearchFormConfig } from '../config/useSearchConfigData.ts';
  import { useAcademicMajorData } from '../hooks/useAcademicMajorData.ts';
  import { useAcademicMajorColumnConfig } from '../config/useAcademicMajorColumnConfig';
  import { updateAcademicMajorEnabledFlagById } from '@/modules/schedule/api/academicMajor';

  const router = useRouter();
  const { t } = useTranslation();
  /** 获取科室相关信息 */
  const { getDeptList, deptList } = useGetDeptList();
  // 请求列表方法及一些公共返回
  const { queryAcademicMajorData, academicMajorData, total, loading } =
    useAcademicMajorData();

  type modelValueType = {
    hospitalId: string;
    deptIds?: string;
    keyWord?: string;
  };

  /** 查询model */
  const modelValue = ref<modelValueType>({
    hospitalId: '',
    deptIds: undefined,
    keyWord: undefined,
  });

  /** 分页参数 */
  const pageInfo = ref({
    pageNumber: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    total: 0,
  });
  const formRef = ref();

  /** 查询form切换 */
  const modelChange = async (data: modelValueType, model: string) => {
    modelValue.value = {
      ...modelValue.value,
      ...data,
    };

    await resetPageInfo();
    await fetchData();

    if (model === 'hospitalId') {
      await getDeptList({
        hospitalId: modelValue.value?.hospitalId as string,
        deptTypeCodes: [DEPT_TYPE_CODE.CLINICAL],
        encounterTypeCode: ENCOUNTER_TYPE_CODE.OUTPATIENT,
      });
    }
  };

  /** 获取数据 */
  const fetchData = async () => {
    await queryAcademicMajorData({
      ...modelValue.value,
      deptIds:
        modelValue.value.deptIds && modelValue.value.deptIds.length > 0
          ? [modelValue.value.deptIds]
          : undefined,
      pageNumber: pageInfo.value.pageNumber,
      pageSize: pageInfo.value.pageSize,
    });

    pageInfo.value.total = total.value;
  };

  /** 重置分页数据 */
  const resetPageInfo = async () => {
    pageInfo.value.pageNumber = 1;
    pageInfo.value.total = 0;
  };

  /** 查询科室 */
  const queryDepList = async (keyWord?: string) => {
    await getDeptList({
      hospitalId: modelValue.value?.hospitalId as string,
      deptTypeCodes: [DEPT_TYPE_CODE.CLINICAL],
      encounterTypeCode: ENCOUNTER_TYPE_CODE.OUTPATIENT,
      keyWord: keyWord,
    });
  };

  /** 停启用切换 */
  const handleEnableSwitch = async (row: AcademicMajor.AcademicMajorInfo) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.academicMajorNameDisplay,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        academicMajorId: row.academicMajorId,
        enabledFlag: row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES,
      };

      const [, res] = await updateAcademicMajorEnabledFlagById(params);

      if (res?.success) {
        row.enabledFlag = row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES;

        ElMessage.success(
          t(
            row.enabledFlag === FLAG.YES
              ? 'global:enabled.success'
              : 'global:disabled.success',
          ),
        );
      }
    });
  };

  /** 拖拽排序 */

  /** 跳转详情页 */
  const goDetail = async (row: AcademicMajor.AcademicMajorInfo) => {
    if (row?.academicMajorId) {
      router.push({
        name: 'detail',
        params: {
          id: row.academicMajorId,
        },
        query: {
          hospitalId: modelValue.value.hospitalId,
        },
      });
    } else {
      router.push({
        name: 'detail',
        query: {
          hospitalId: modelValue.value.hospitalId,
        },
      });
    }
  };

  /** 清空和回车进行关键字查询 */
  const handleBlur = async (keyWord?: string) => {
    const keyWordRef = formRef.value?.getItemRef('keyWord');

    modelValue.value = {
      ...modelValue.value,
      keyWord,
    };

    keyWordRef.blur();
    await resetPageInfo();
    fetchData();
  };

  const searchConfig = useSearchFormConfig({
    deptList,
    handleBlur,
    queryDepList,
  });
  const academicMajorColumns = useAcademicMajorColumnConfig({
    handleEnableSwitch,
    goDetail,
  });
</script>
<template>
  <div class="flex h-full flex-col">
    <Title
      :title="$t('academicMajor.manage.title.list', '学科专业列表')"
      class="mb-3"
    />
    <!-- 检索框 -->

    <ProForm
      ref="formRef"
      class="flex flex-wrap"
      layout-mode="inline"
      :data="searchConfig"
      v-model="modelValue"
      @model-change="modelChange"
    >
      <div class="flex-1 text-right">
        <el-button type="primary" @click="fetchData">
          {{ $t('global:query') }}
        </el-button>
        <el-button type="primary" @click="goDetail">
          {{ $t('global:add') }}
        </el-button>
      </div>
    </ProForm>

    <pro-table
      :pagination="true"
      :loading="loading"
      row-key="academicMajorId"
      :data="academicMajorData"
      :columns="academicMajorColumns"
      :page-info="{
        total: pageInfo.total,
        pageNumber: pageInfo.pageNumber,
        pageSize: pageInfo.pageSize,
      }"
      @current-page-change="
        (val: number) => {
          pageInfo.pageNumber = val;
          fetchData();
        }
      "
      @size-page-change="
        (val: number) => {
          pageInfo.pageSize = val;
          fetchData();
        }
      "
    />
  </div>
</template>
