<script setup lang="ts" name="academicMajorDetail">
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { cloneDeep } from '@sun-toolkit/shared';
  import { useRouter, useRoute } from 'vue-router';
  import { Title, ProForm, ProTable } from 'sun-biz';
  import { useGetUserInfo } from '@/hooks/useGetUserList.ts';
  import { flattenData, spanData } from '../hooks/dataSet.ts';
  import { useGetDeptList } from '../hooks/useGetDeptList.ts';
  import { useAcademicMajorFormConfig } from '../config/useFormConfigData';
  import { Ref, watch, ref, computed, onMounted, nextTick } from 'vue';
  import {
    ES_PROVIDER_TYPE_CODE,
    FLAG,
    USER_JOB_CODE,
    DEPT_TYPE_CODE,
    ENCOUNTER_TYPE_CODE,
  } from '@/utils/constant';
  import { useAcademicMajorDetailColumnConfig } from '../config/useAcademicMajorColumnConfig.tsx';
  import { saveAcademicMajor } from '@/modules/schedule/api/academicMajor.ts';
  import {
    useAcademicMajorData,
    useEsChargeStandardData,
  } from '../hooks/useAcademicMajorData.ts';

  type baseInfoType = {
    academicMajorName?: string;
    academicMajor2ndName?: string;
    academicMajorExtName?: string;
    spellNo?: string;
    wbNo?: string;
    deptId?: string;
    enabledFlag: number;
  };

  const router = useRouter();
  const route = useRoute();
  const { t } = useTranslation();
  const { getDeptList, deptList } = useGetDeptList();
  const { userList, getUserList } = useGetUserInfo();
  const { esChargeStandardList, getEsChargeStandardList } =
    useEsChargeStandardData();
  const { queryAcademicMajorData, academicMajorData: academicMajorDataList } =
    useAcademicMajorData();

  const baseFormRef = ref();
  const userTableRef = ref();
  const departmentTableRef = ref();

  /** 初始loading */
  const loading = ref(false);
  /** 提交按钮loading */
  const submitLoading = ref(false);
  /** watch中防止死循环 */
  const isProcessing = ref(false);
  /** 初始数据 */
  const academicMajorData = ref<AcademicMajor.AcademicMajorInfo>();
  /** 基本信息 */
  const baseInfoModel = ref<baseInfoType>({
    academicMajorName: undefined,
    academicMajor2ndName: undefined,
    academicMajorExtName: undefined,
    wbNo: undefined,
    spellNo: undefined,
    deptId: undefined,
    enabledFlag: FLAG.YES,
  });

  const resData = ref<AcademicMajor.EsProviderInfo[]>([]);
  const tableData = ref<AcademicMajor.EsProviderInfo[]>([]);

  /** 就诊人员列表 */
  const academicMajorUserList = ref<AcademicMajor.EsProviderInfo[]>([]);
  /** 就诊科室列表 */
  const academicMajorDeptList = ref<AcademicMajor.EsProviderInfo[]>([]);

  /** 学科专业标识 */
  const academicMajorId = computed(() => route.params?.id);
  /** 医院标识 */
  const hospitalId = computed(() => route.query?.hospitalId);
  /** 是否新增 */
  const isAdd = computed(() => (academicMajorId.value ? false : true));
  /** 是否有数据，禁用科室 */
  const isDisableApt = computed(
    () => !!tableData.value?.length && !!baseInfoModel.value.deptId,
  );

  const orgListData = computed(() => {
    return (
      (deptList.value ?? []).map((item) => ({
        ...item,
        label: item.orgNo + '-' + item.orgNameDisplay,
        value: item.orgId,
      })) ?? []
    );
  });
  const userListData = computed(() => {
    return (
      (userList.value ?? []).map((item) => ({
        ...item,
        label: item.userNo + '-' + item.userNameDisplay,
        value: item.userId,
      })) ?? []
    );
  });

  /** 初始化数据 */
  const initData = async () => {
    loading.value = true;
    if (!isAdd.value) {
      await fetchData();
      /** 获取用户 */
      await getUserListData();
    }

    /** 获取组织科室 */
    await getOrgListData();

    /** 获取就诊服务 */
    await getEsChargeStandardListData();

    academicMajorData.value = (academicMajorDataList.value ?? [])[0];
    const data = cloneDeep(academicMajorData.value);

    /** 赋值基本信息 */
    baseInfoModel.value = {
      academicMajorName: isAdd.value
        ? undefined
        : data?.academicMajorNameDisplay,
      academicMajor2ndName: isAdd.value
        ? undefined
        : data?.academicMajor2ndName,
      academicMajorExtName: isAdd.value
        ? undefined
        : data?.academicMajorExtName,
      deptId: isAdd.value ? undefined : data?.deptId,
      wbNo: isAdd.value ? undefined : data?.wbNo,
      spellNo: isAdd.value ? undefined : data?.spellNo,
      enabledFlag: isAdd.value ? FLAG.YES : (data?.enabledFlag ?? FLAG.YES),
    };

    if (!isAdd.value) {
      resData.value = data?.esProviderList ?? [];

      const arr = flattenData(
        (data?.esProviderList ?? []) as AcademicMajor.EsProviderInfo[],
      );
      tableData.value = arr ?? [];

      await updateTableData();
    }

    loading.value = false;
  };

  /** 更新table的数据 */
  const updateTableData = async () => {
    const data = cloneDeep(tableData.value);
    academicMajorUserList.value =
      (data ?? []).filter(
        (item) => item.esProviderTypeCode === ES_PROVIDER_TYPE_CODE.USER,
      ) ?? [];

    academicMajorDeptList.value =
      (data ?? []).filter(
        (item) => item.esProviderTypeCode === ES_PROVIDER_TYPE_CODE.DEPARTMENT,
      ) ?? [];

    const academicMajorUserData = spanData(
      academicMajorUserList.value as (AcademicMajor.EsProviderInfo &
        AcademicMajor.EncounterServiceInfo & {
          editable?: boolean;
          key?: string;
        })[],
    );

    academicMajorUserList.value = flattenData(academicMajorUserData);

    const academicMajorDeptData = spanData(
      academicMajorDeptList.value as (AcademicMajor.EsProviderInfo &
        AcademicMajor.EncounterServiceInfo & {
          editable?: boolean;
          key?: string;
        })[],
    );
    academicMajorDeptList.value = flattenData(academicMajorDeptData);
  };

  /** 获取数据 */
  const fetchData = async () => {
    await queryAcademicMajorData({
      pageNumber: 1,
      pageSize: 1,
      academicMajorId: academicMajorId.value as string,
      hospitalId: hospitalId.value as string,
    });
  };

  /** 获取组织信息 */
  const getOrgListData = async (keyWord?: string) => {
    await getDeptList({
      keyWord: keyWord,
      hospitalId: hospitalId.value as string,
      deptTypeCodes: [DEPT_TYPE_CODE.CLINICAL],
      encounterTypeCode: ENCOUNTER_TYPE_CODE.OUTPATIENT,
    });
  };

  /** 获取用户信息 */
  const getUserListData = async (keyWord?: string) => {
    await getUserList({
      keyWord: keyWord,
      bizUnitId: baseInfoModel.value.deptId ?? undefined,
      hospitalId: hospitalId.value as string,
      userJobCodes: [USER_JOB_CODE.DOCTOR],
    });
  };

  /** 获取就诊服务信息 */
  const getEsChargeStandardListData = async (keyWord?: string) => {
    await getEsChargeStandardList({
      keyWord: keyWord,
      hospitalId: hospitalId.value as string,
    });
  };

  /** 校验是否选择了所属科室 */
  const validDep = () => {
    if (!baseInfoModel.value.deptId) {
      baseFormRef.value?.ref?.validateField(['deptId']);
      ElMessage.warning(
        t('placeholder.select.deptId', '请先选择学科专业所属科室！'),
      );
      return false;
    }
    return true;
  };

  /** 新增出诊人员 */
  const addUser = async () => {
    userAddItem({
      sort:
        ((academicMajorUserList.value ?? [])[
          (academicMajorUserList.value ?? []).length - 1
        ]?.sort ?? 0) + 1,
      autoFreeFlag: FLAG.NO,
      limitEsSourceFlag: FLAG.YES,
      editable: true,
      tempId: FLAG.YES,
      esProviderTypeCode: ES_PROVIDER_TYPE_CODE.USER,
      esProvideTimePeriodList: [
        {
          timeList: [],
          startTime: '',
          endTime: '',
        },
      ],
    } as AcademicMajor.EsProviderInfo & {
      editable: boolean;
    });

    await updateTable();

    nextTick(() => {
      const row = userTableRef.value?.proTableRef?.$el?.querySelector(
        `.el-table__body-wrapper tbody tr:nth-child(${academicMajorUserList.value.length - 1})`,
      );
      row?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });
  };

  /** 新增出诊科室 */
  const addDept = async () => {
    await deptAddItem({
      sort:
        ((academicMajorDeptList.value ?? [])[
          (academicMajorDeptList.value ?? []).length - 1
        ]?.sort ?? 0) + 1,
      autoFreeFlag: FLAG.NO,
      limitEsSourceFlag: FLAG.YES,
      editable: true,
      tempId: FLAG.YES,
      esProviderTypeCode: ES_PROVIDER_TYPE_CODE.DEPARTMENT,
      esProvideTimePeriodList: [
        {
          timeList: [],
          startTime: '',
          endTime: '',
        },
      ],
    } as AcademicMajor.EsProviderInfo & {
      editable: boolean;
    });

    await updateTable();

    nextTick(() => {
      const row = departmentTableRef.value?.proTableRef?.$el?.querySelector(
        `.el-table__body-wrapper tbody tr:nth-child(${academicMajorDeptList.value.length - 1})`,
      );
      row?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });
  };

  /** 用户、科室编码变化 */
  const changeProviderCode = async (
    row: AcademicMajor.EsProviderInfo,
    code?: string,
    val?: string,
  ) => {
    if (code === ES_PROVIDER_TYPE_CODE.USER) {
      const obj = userList.value.find((item) => item.userId === val);
      row.providerName = obj?.userNameDisplay;
      row.providerNo = obj?.userNo;
    } else {
      const obj = deptList.value.find((item) => item.orgId === val);
      row.providerName = obj?.orgNameDisplay;
      row.providerNo = obj?.orgNo;
    }
  };

  /** 用户、科室编码检索 */
  const remoteProviderCode = async (
    row: AcademicMajor.EsProviderInfo,
    code?: string,
    keyWord?: string,
  ) => {
    userList.value = [];
    deptList.value = [];
    if (!validDep()) return;
    if (code === ES_PROVIDER_TYPE_CODE.USER) {
      await getUserListData(keyWord);
    } else {
      /** 暂时写死和所属科室保持一致 */
      await getOrgListData(keyWord);

      deptList.value = baseInfoModel.value?.deptId
        ? deptList.value?.filter(
            (item) => item.orgId === baseInfoModel.value.deptId,
          ) || []
        : [];
    }
  };

  /** 就诊服务收费标准变化 */
  const changeEsCharge = async (
    row: AcademicMajor.EsProviderInfo,
    val?: string,
  ) => {
    const obj = esChargeStandardList.value?.find(
      (item) => item.esChargeStandardId === val,
    );
    (
      row as AcademicMajor.EsProviderInfo & {
        esChargeStandardNameDisplay?: string;
      }
    ).esChargeStandardNameDisplay = obj?.esChargeStandardNameDisplay;
  };

  /** 合并方法 */
  const spanMethod = (data: {
    row: AcademicMajor.EsProviderInfo &
      AcademicMajor.EncounterServiceInfo & { spanColumnIndex: number };
    column: unknown;
    rowIndex: number;
    columnIndex: number;
  }) => {
    const { row, columnIndex } = data;

    if (columnIndex < 3) {
      if (row.spanColumnIndex > 0) {
        if ((row as unknown as { tempId: number }).tempId) {
          if (columnIndex === 1) {
            return { rowspan: 1, colspan: 2 };
          }
          if (columnIndex === 2) {
            return { rowspan: 1, colspan: 0 };
          }
        }
        return {
          rowspan: row.spanColumnIndex,
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 1,
        };
      }
    }

    return {
      rowspan: 1,
      colspan: 1,
    };
  };

  /** 移除方法 */
  const removeItem = async (
    row: AcademicMajor.EsProviderInfo & AcademicMajor.EncounterServiceInfo,
    index: number,
    code?: string,
  ) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action: t('global:remove'),
        name: row.providerName + '-' + row.esChargeStandardNameDisplay,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      if (code === ES_PROVIDER_TYPE_CODE.USER) {
        userDelItem(index);
      } else {
        deptDelItem(index);
      }
      updateTable();
    });
  };

  /** 手动更新tableData */
  const updateTable = async () => {
    tableData.value = JSON.parse(
      JSON.stringify([
        ...academicMajorUserList.value,
        ...academicMajorDeptList.value,
      ]),
    );
  };

  /** 保存校验 */
  const validSave = async () => {
    const userObj = (tableData.value ?? []).find(
      (item) =>
        (
          item as {
            editable: boolean;
          }
        ).editable && item.esProviderTypeCode === ES_PROVIDER_TYPE_CODE.USER,
    );
    const deptObj = (tableData.value ?? []).find(
      (item) =>
        (
          item as {
            editable: boolean;
          }
        ).editable &&
        item.esProviderTypeCode === ES_PROVIDER_TYPE_CODE.DEPARTMENT,
    );

    if (userObj) {
      ElMessage.warning(
        t('user.academicMajor.tip', '就诊人员中存在编辑中的数据，请先保存！'),
      );
      return false;
    }

    if (deptObj) {
      ElMessage.warning(
        t('dept.academicMajor.tip', '就诊科室中存在编辑中的数据，请先保存！'),
      );
      return false;
    }
    return true;
  };

  /** 保存 */
  const handleSubmit = async () => {
    try {
      let arr = [
        baseFormRef.value?.ref?.validate(),
        userTableRef.value?.formRef?.validate(),
        departmentTableRef.value?.formRef?.validate(),
      ];

      await Promise.all(arr);
      const valid = await validSave();
      if (!valid) return;

      const result = spanData(
        tableData.value as (AcademicMajor.EsProviderInfo &
          AcademicMajor.EncounterServiceInfo & {
            editable?: boolean;
            key?: string;
          })[],
      );

      submitLoading.value = true;

      const params = {
        academicMajorId: isAdd.value
          ? undefined
          : academicMajorData.value?.academicMajorId,
        academicMajorName: baseInfoModel.value.academicMajorName,
        academicMajor2ndName: baseInfoModel.value.academicMajor2ndName,
        academicMajorExtName: baseInfoModel.value.academicMajorExtName,
        enabledFlag: baseInfoModel.value.enabledFlag,
        deptId: baseInfoModel.value.deptId,
        spellNo: baseInfoModel.value.spellNo,
        wbNo: baseInfoModel.value.wbNo,
        esProviderList: (result ?? []).map((item, index) => ({
          esProviderTypeCode: item.esProviderTypeCode,
          providerId: item.providerId,
          sort: index + 1,
          encounterServiceList: (item.encounterServiceList ?? [])?.map(
            (im: AcademicMajor.EncounterServiceInfo) => ({
              esChargeStandardId: im.esChargeStandardId,
              limitEsSourceFlag: im.limitEsSourceFlag,
              autoFreeFlag: im.autoFreeFlag,
              esProvideTimePeriodList: (im.esProvideTimePeriodList ?? [])
                ?.filter((time) => time?.startTime || time?.endTime)
                ?.map((time) => ({
                  esProvideTimePeriodId: time?.esProvideTimePeriodId,
                  startTime: time?.startTime,
                  endTime: time?.endTime,
                })),
            }),
          ),
        })),
      };

      const [, res] = await saveAcademicMajor(params);

      if (res?.success) {
        ElMessage.success(
          isAdd.value ? t('global:add.success') : t('global:modify.success'),
        );
        router.push('/');
      }
    } catch (error) {
      console.log(error, 'error');
    } finally {
      submitLoading.value = false;
    }
  };

  /** 取消 */
  const handleCancel = async () => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要{{action}}{{name}} 吗？', {
        action: t('cancel.exit', '取消'),
        name: isAdd.value
          ? `${t('global:add')} “${t('academicMajor', '学科专业')}”`
          : `${t('global:modify')} “${academicMajorData.value?.academicMajorName ?? t('academicMajor', '学科专业')}”`,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      router.push('/');
    });
  };

  // 基本信息form配置
  const academicMajorForm = useAcademicMajorFormConfig({
    deptList,
    isDisableApt,
    getOrgListData,
  });
  /** 用户 */
  const {
    tableConfig: userTableConfig,
    addItem: userAddItem,
    delItem: userDelItem,
  } = useAcademicMajorDetailColumnConfig({
    codeType: ES_PROVIDER_TYPE_CODE.USER,
    tableRef: userTableRef,
    tableData: academicMajorUserList as Ref<
      (AcademicMajor.EsProviderInfo & {
        editable: boolean;
      })[]
    >,
    list: userListData,
    esChargeStandardList,
    changeProviderCode,
    remoteProviderCode,
    changeEsCharge,
    getEsChargeStandardListData,
    removeItem,
    updateTable,
  });
  /** 科室 */
  const {
    tableConfig: deptTableConfig,
    addItem: deptAddItem,
    delItem: deptDelItem,
  } = useAcademicMajorDetailColumnConfig({
    codeType: ES_PROVIDER_TYPE_CODE.DEPARTMENT,
    tableRef: departmentTableRef,
    tableData: academicMajorDeptList as Ref<
      (AcademicMajor.EsProviderInfo & {
        editable: boolean;
      })[]
    >,
    list: orgListData,
    esChargeStandardList,
    changeProviderCode,
    remoteProviderCode,
    changeEsCharge,
    getEsChargeStandardListData,
    removeItem,
    updateTable,
  });

  watch(
    () => tableData.value,
    async (newValue) => {
      // 防止循环更新
      if (isProcessing.value) return;

      try {
        isProcessing.value = true;
        // 先更新原始数据结构
        const newResData = spanData(
          newValue as unknown as (AcademicMajor.EsProviderInfo &
            AcademicMajor.EncounterServiceInfo & {
              editable?: boolean;
              key?: string;
            })[],
        );

        // 检查数据是否真的发生变化
        if (JSON.stringify(newResData) !== JSON.stringify(resData.value)) {
          resData.value = newResData;

          // 重新计算展平后的数据
          tableData.value = flattenData(resData.value);

          await updateTableData();
        }
      } finally {
        isProcessing.value = false;
      }
    },
    {
      deep: true,
    },
  );

  onMounted(() => {
    initData();
  });
</script>
<template>
  <div class="flex h-full flex-col" v-loading="loading">
    <el-page-header @back="handleCancel">
      <template #content>
        <span class="text-base">
          {{
            isAdd
              ? $t('academicMajor.add', '新增学科专业')
              : `${$t('global:edit')}-${academicMajorData?.academicMajorName ?? ''}`
          }}
        </span>
      </template>
    </el-page-header>
    <div class="flex h-full flex-1 flex-col overflow-y-hidden pb-2">
      <Title :title="$t('academicMajor.baseInfo', '基本信息')" class="my-3" />
      <ProForm
        ref="baseFormRef"
        :column="4"
        v-model="baseInfoModel"
        :data="academicMajorForm"
      />
      <Title :title="$t('academicMajor.user', '出诊人员')" class="mb-3">
        <el-button
          type="primary"
          @click="addUser"
          :disabled="!baseInfoModel.deptId"
        >
          {{ $t('global:add') }}
        </el-button>
      </Title>
      <!--  -->
      <ProTable
        ref="userTableRef"
        :editable="true"
        :columns="userTableConfig"
        :data="academicMajorUserList"
        row-key="key"
        :span-method="spanMethod"
      />
      <Title :title="$t('academicMajor.department', '出诊科室')" class="my-3">
        <el-button
          type="primary"
          @click="addDept"
          :disabled="!baseInfoModel.deptId"
        >
          {{ $t('global:add') }}
        </el-button>
      </Title>
      <ProTable
        ref="departmentTableRef"
        :editable="true"
        :columns="deptTableConfig"
        :data="academicMajorDeptList"
        row-key="key"
        :span-method="spanMethod"
      />
    </div>
    <div class="text-right">
      <el-button @click="handleCancel">{{ $t('global:cancel') }}</el-button>
      <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
        {{ $t('global:save') }}
      </el-button>
    </div>
  </div>
</template>
