<script setup lang="ts" name="nursingGuide">
  import { onMounted, ref, onUnmounted } from 'vue';
  import {
    ATTACH_SCOPE_CODE,
    ATTACH_CATEGORY_CODE,
  } from './untils/healtnEducation.ts';
  import { FLAG } from '@sun-toolkit/enums';
  import { queryAttachByExample } from '@/api/common';
  import { AttachItem } from '@/api/types';
  import NursingGuideItem from './components/NursingGuideItem.vue';
  import TopGuide from '@/modules/schedule/pages/healthEducation/components/topGuide.vue';
  const loading = ref(false);
  const attachList = ref<AttachItem[]>([]);
  const currentItem = ref();
  const isFullScreen = ref(false); //是否全屏
  const initData = async () => {
    await fetchData();
  };

  const fetchData = async () => {
    loading.value = true;
    const [, res] = await queryAttachByExample({
      pageNumber: 1,
      pageSize: 1000,
      deleteFlag: FLAG.NO,
      attachScopeCode: ATTACH_SCOPE_CODE.Health_Education,
    });
    loading.value = false;
    if (res?.success) {
      attachList.value = res?.data ?? [];
      currentItem.value = (attachList.value ?? [])[0];
    }
  };

  const changeCurrentItem = async (val: AttachItem) => {
    currentItem.value = val;
  };
  // 全屏方法
  const changeScreen = () => {
    const root = document.querySelector('.p-box'); // 全屏整个页面主容器
    if (!isFullScreen.value) {
      if (root?.requestFullscreen) {
        root.requestFullscreen();
      } else if (
        (
          root as HTMLElement & {
            webkitRequestFullscreen?: () => Promise<void>;
          }
        )?.webkitRequestFullscreen
      ) {
        (
          root as HTMLElement & { webkitRequestFullscreen: () => Promise<void> }
        ).webkitRequestFullscreen();
      } else if (
        (root as HTMLElement & { msRequestFullscreen?: () => Promise<void> })
          ?.msRequestFullscreen
      ) {
        (
          root as HTMLElement & { msRequestFullscreen: () => Promise<void> }
        ).msRequestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (
        (document as Document & { webkitExitFullscreen?: () => Promise<void> })
          ?.webkitExitFullscreen
      ) {
        (
          document as Document & { webkitExitFullscreen: () => Promise<void> }
        ).webkitExitFullscreen();
      } else if (
        (document as Document & { msExitFullscreen?: () => Promise<void> })
          ?.msExitFullscreen
      ) {
        (
          document as Document & { msExitFullscreen: () => Promise<void> }
        ).msExitFullscreen();
      }
    }
  };
  // 监听全屏状态变化
  const handleFullscreenChange = () => {
    isFullScreen.value = !!(
      document.fullscreenElement ||
      ('webkitFullscreenElement' in document &&
        document.webkitFullscreenElement) ||
      ('msFullscreenElement' in document && document.msFullscreenElement)
    );
  };

  onMounted(() => {
    initData();
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('msfullscreenchange', handleFullscreenChange);
  });
  // 在组件卸载时移除监听
  onUnmounted(() => {
    document.removeEventListener('fullscreenchange', handleFullscreenChange);
    document.removeEventListener(
      'webkitfullscreenchange',
      handleFullscreenChange,
    );
    document.removeEventListener('msfullscreenchange', handleFullscreenChange);
  });
</script>

<template>
  <div class="flex size-full flex-col bg-blue-100 p-[5px]" ref="container">
    <div class="p-box flex size-full flex-col gap-2" v-loading="loading">
      <!--      头部-->
      <el-card body-class="p-[10px]">
        <top-guide @change-screen="changeScreen"></top-guide>
      </el-card>
      <!--      中间-->
      <el-card
        class="flex size-full flex-col"
        body-class="p-0 size-full flex flex-col"
        style="border: 0; border-radius: 4px 4px 0 0"
      >
        <div class="flex-1 overflow-hidden">
          <el-row class="size-full" :gutter="0" style="height: 100%; margin: 0">
            <el-col
              :span="4"
              style="padding-left: 0"
              class="flex size-full flex-col overflow-hidden"
            >
              <div class="size-full overflow-auto border-r">
                <el-scrollbar class="p-[8px]" style="height: calc(100% - 40px)">
                  <div class="flex flex-col gap-[10px] overflow-auto">
                    <NursingGuideItem
                      @change-current-item="changeCurrentItem"
                      :current-item="currentItem"
                      v-for="(item, index) in attachList"
                      :key="item?.attachId + '_' + index"
                      :data="item"
                    />
                  </div>
                </el-scrollbar>
              </div>
            </el-col>
            <el-col
              :span="20"
              style="height: 100%; padding-right: 0"
              class="flex size-full flex-col overflow-hidden"
            >
              <div class="flex size-full flex-col overflow-hidden">
                <div class="flex size-full flex-col overflow-hidden rounded-lg">
                  <div class="flex size-full flex-col overflow-auto">
                    <div
                      class="flex size-full flex-col overflow-auto rounded-md"
                    >
                      <div
                        class="size-full"
                        v-if="
                          currentItem?.attachCategoryCode ===
                          ATTACH_CATEGORY_CODE.VIDEO
                        "
                      >
                        <video
                          controls
                          class="size-full rounded-md object-cover"
                          :src="currentItem?.attachPosition"
                        ></video>
                      </div>
                      <!-- 图片显示区域 -->
                      <div
                        class="size-full"
                        v-else-if="
                          currentItem?.attachCategoryCode ===
                          ATTACH_CATEGORY_CODE.IMAGE
                        "
                      >
                        <img
                          :src="currentItem?.attachPosition"
                          class="size-full rounded-md object-contain"
                          alt="教育内容图片"
                        />
                      </div>
                      <!-- PDF显示区域 -->
                      <div
                        class="size-full"
                        v-else-if="currentItem?.attachCategoryCode === 'pdf'"
                      >
                        <iframe
                          :src="currentItem?.attachPosition"
                          class="h-full w-full rounded-md"
                          frameborder="0"
                        ></iframe>
                      </div>
                      <!-- 默认显示区域 -->
                      <div
                        v-else
                        class="flex size-full items-center justify-center rounded bg-gray-100"
                      >
                        <span class="text-gray-500">暂无可预览内容</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
  </div>
</template>
