<template>
  <div
    @click="
      () => {
        isFullScreen = !isFullScreen;
        emits('changeScreen', isFullScreen);
      }
    "
    class="fullscreen-btn cursor-pointer"
  >
    <el-icon v-if="isFullScreen === false"><FullScreen /></el-icon>
    <el-icon v-else><CopyDocument /></el-icon>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, onUnmounted, ref } from 'vue';
  const isFullScreen = ref(false);

  // 监听全屏状态变化
  const handleFullscreenChange = () => {
    isFullScreen.value = !!(
      document.fullscreenElement ||
      ('webkitFullscreenElement' in document &&
        document.webkitFullscreenElement) ||
      ('msFullscreenElement' in document && document.msFullscreenElement)
    );
  };

  onMounted(() => {
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('msfullscreenchange', handleFullscreenChange);
  });
  // 在组件卸载时移除监听
  onUnmounted(() => {
    document.removeEventListener('fullscreenchange', handleFullscreenChange);
    document.removeEventListener(
      'webkitfullscreenchange',
      handleFullscreenChange,
    );
    document.removeEventListener('msfullscreenchange', handleFullscreenChange);
  });

  const emits = defineEmits(['changeScreen']);
</script>

<style scoped>
  .fullscreen-btn {
    text-align: right;
  }
</style>
