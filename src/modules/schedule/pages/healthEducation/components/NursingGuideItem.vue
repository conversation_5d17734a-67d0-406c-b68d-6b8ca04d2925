<script setup lang="ts" name="NursingGuideItem">
  import { computed } from 'vue';
  import { AttachItem } from '@/api/types';

  const emits = defineEmits(['changeCurrentItem']);

  const { data, currentItem } = defineProps<{
    currentItem?: AttachItem;
    data?: AttachItem;
  }>();

  const isSelect = computed(() => {
    return currentItem?.attachId === data?.attachId;
  });
</script>
<template>
  <div>
    <el-card
      class="border-none"
      :body-class="
        [
          isSelect ? 'bg-[#2468DA] border-[#2468DA]' : '',
          'cursor-pointer border rounded-lg',
        ].join(' ')
      "
      @click="emits('changeCurrentItem', data)"
    >
      <div
        :class="[
          isSelect ? 'bg-[#2468DA] text-white' : '',
          'flex flex-col gap-1 rounded-lg px-2 py-[28px]',
        ]"
      >
        <div class="truncate text-lg font-semibold">
          {{ data?.attachName ?? '--' }}
        </div>
      </div>
    </el-card>
  </div>
</template>
<style scoped>
  :deep(.el-card__body) {
    @apply p-0;
  }
</style>
