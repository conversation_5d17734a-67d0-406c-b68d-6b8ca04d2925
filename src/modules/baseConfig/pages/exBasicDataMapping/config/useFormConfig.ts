import { Ref } from 'vue';
import { SelectOptions } from '@/typings/common';
import { useFormConfig } from 'sun-biz';
import { FLAG_STR } from '@/utils/constant';

export function useHisDataSearchFormConfig(
  exBasicDataDictSelections: Ref<SelectOptions[]>,
  hisBasicDataTypeSelections: Ref<SelectOptions[]>,
  queryHisDataAndMappingList: (
    data?: Partial<ExBasicDataMapping.HisDataAndMappingQueryParams>,
  ) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t('exBasicDataMapping.search.basicDataDictId', '目录'),
        name: 'basicDataDictId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('exBasicDataMapping.search.basicDataDictId', '目录'),
        }),
        triggerModelChange: true,
        extraProps: {
          options: exBasicDataDictSelections.value,
          className: 'w-60',
          clearable: false,
        },
      },
      {
        label: t('exBasicDataMapping.search.hisBasicDataTypeCode', '分类'),
        name: 'hisBasicDataTypeCode',
        component: 'tree-select',
        placeholder: t('global:placeholder.select.template', {
          name: t('exBasicDataMapping.search.hisBasicDataTypeCode', '分类'),
        }),
        triggerModelChange: true,
        extraProps: {
          className: 'w-60',
          clearable: false,
          filterable: true,
          data: hisBasicDataTypeSelections.value,
          'check-strictly': true,
          'render-after-expand': false,
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-40',
        },
      },
      {
        label: t('exBasicDataMapping.search.mappingStatusCode', '对照'),
        name: 'mappingStatusCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('exBasicDataMapping.search.mappingStatusCode', '对照'),
        }),
        triggerModelChange: true,
        extraProps: {
          options: [
            {
              label: t('global:all'),
              value: FLAG_STR.ALL,
            },
            {
              label: t('global:yes'),
              value: FLAG_STR.YES,
            },
            {
              label: t('global:no'),
              value: FLAG_STR.NO,
            },
          ],
          clearable: false,
          className: 'w-40',
        },
      },
      {
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-60',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryHisDataAndMappingList({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryHisDataAndMappingList({
              keyWord: '',
            });
          },
        },
      },
    ],
  });
  return data;
}

export function useMappingSearchFormConfig(
  exBasicDtaDictTypeSelections: Ref<SelectOptions[]>,
  queryExBasicDtaList: (
    data?: Partial<ExBasicDataDict.ExBasicDtaQueryParams>,
  ) => void,
) {
  const data = useFormConfig({
    getData: (t) => [
      {
        label: t(
          'exBasicDataMapping.mappingSearch.basicDataDictTypeId',
          '目录分类',
        ),
        name: 'basicDataDictTypeId',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t(
            'exBasicDataMapping.mappingSearch.basicDataDictTypeId',
            '目录分类',
          ),
        }),
        triggerModelChange: true,
        extraProps: {
          clearable: false,
          options: exBasicDtaDictTypeSelections.value,
          className: 'w-40',
        },
      },
      {
        name: 'keyword',
        component: 'input',
        placeholder: t('global:placeholder.keyword'),
        className: 'w-60',
        extraProps: {
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              queryExBasicDtaList({
                keyword: (e.target as HTMLInputElement).value,
              });
            }
          },
          onClear: () => {
            queryExBasicDtaList({
              keyword: '',
            });
          },
        },
      },
    ],
  });
  return data;
}
