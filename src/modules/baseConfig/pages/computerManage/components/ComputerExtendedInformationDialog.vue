<template>
  <ProDialog
    :title="$t('Computer Extension Properties', '计算机扩展属性')"
    ref="dialogRef"
  >
    <FormDesignRender
      ref="formRenderRef"
      form-design-id="1705110641947557888"
      :data="formData"
      :include-footer="false"
      v-loading="loading"
    />

    <template #footer>
      <el-button @click="dialogRef.close()">{{ t('global:cancel') }}</el-button>
      <el-button type="primary" @click="handleSave">{{
        t('global:save')
      }}</el-button>
    </template>
  </ProDialog>
</template>
<script lang="ts" setup>
  import { ProDialog } from 'sun-biz';
  import { ref, defineAsyncComponent } from 'vue';
  import {
    queryComputerExtInfoByExample,
    saveComputerExtInfo,
  } from '@/modules/baseConfig/api/computerManage.ts';
  import { useTranslation } from 'i18next-vue';
  import { ElMessage } from 'element-sun';
  const { t } = useTranslation();
  const formData = ref<Record<string, unknown>>({});
  const dialogRef = ref();
  const loading = ref(false);
  const formRenderRef = ref();
  const props = defineProps<{
    rowValue: ComputerManage.addComputerParams;
  }>();
  const FormDesignRender = defineAsyncComponent(
    () => import('basic_provider/FormDesignRender'),
  );
  const openDialog = async () => {
    dialogRef.value.open(); // 立即打开弹窗
    formData.value = {}; // 清空表单，避免上次数据残留
    loading.value = true;
    setTimeout(async () => {
      const [, res] = await queryComputerExtInfoByExample({
        computerId: props.rowValue.computerId || '',
      });
      if (res?.success) {
        if (res?.success && Array.isArray(res.data)) {
          formData.value = Object.fromEntries(
            res.data.map((item) => [
              item.computerExtAttributeCode,
              item.computerExtAttributeValue,
            ]),
          );
        }
      }
    });
    loading.value = false;
  };

  const handleSave = async () => {
    if (!formRenderRef.value) return;
    const isok = await formRenderRef.value.submit();
    if (isok) {
      const data = formRenderRef.value?.getCurrentValue();
      const params = buildParams(data);
      const [, res] = await saveComputerExtInfo(params);
      if (res?.success) {
        ElMessage.success('操作成功');
        dialogRef.value.close();
      }
    }
  };
  function buildParams(formData: Record<string, string>) {
    // 1. 取 computerId
    const computerId = props.rowValue.computerId; // 如果不是这个字段，请替换

    // 2. 组装扩展属性列表
    const computerExtInfoList = Object.entries(formData)
      .filter(
        ([key, value]) =>
          key !== 'admitDeptId' && // 排除 computerId 字段
          key !== 'null' && // 排除无效 key
          value !== null &&
          value !== undefined &&
          value !== '', // 排除无效值
      )
      .map(([key, value]) => ({
        computerExtAttributeCode: key,
        computerExtAttributeValue: value,
      }));

    return {
      computerId,
      computerExtInfoList,
    };
  }

  defineExpose({ open: openDialog });
</script>
