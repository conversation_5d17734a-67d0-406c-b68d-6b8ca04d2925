import { Ref } from 'vue';
import { ENABLED_FLAG } from '@/utils/constant';
import { SelectOptions } from '@/typings/common';
import {
  useColumnConfig,
  convertToWbNo,
  convertToSpellNo,
  useEditableTable,
  TableRef,
} from 'sun-biz';
import { BASIC_DATA_SOURCE_CODE, DATA_DOWNLOAD_STATUS_CODE } from '../constant';

export function useExBasicDtaDictTypeTableConfig(
  handleExBasicDtaDictTypeDownloadOrStop: (
    data: ExBasicDataDict.ExBasicDtaDictTypeInfo & { downloading: boolean },
  ) => void,
  handleView: (data: ExBasicDataDict.ExBasicDtaDictTypeInfo) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t(
          'exBasicDataDict.exBasicDtaDictTypeTable.basicDataDictTypeId',
          '目录分类标识',
        ),
        prop: 'basicDataDictTypeId',
        minWidth: 150,
      },
      {
        label: t(
          'exBasicDataDict.exBasicDtaDictTypeTable.basicDataDictTypeNo',
          '目录分类编码',
        ),
        prop: 'basicDataDictTypeNo',
        minWidth: 140,
      },
      {
        label: t(
          'exBasicDataDict.exBasicDtaDictTypeTable.basicDataDictTypeName',
          '目录分类名称',
        ),
        prop: 'basicDataDictTypeName',
        minWidth: 150,
      },
      {
        label: t(
          'exBasicDataDict.exBasicDtaDictTypeTable.basicDataSourceDesc',
          '数据来源',
        ),
        prop: 'basicDataSourceDesc',
        minWidth: 130,
      },
      {
        label: t(
          'exBasicDataDict.exBasicDtaDictTypeTable.dataDownloadStatusDesc',
          '下载状态',
        ),
        prop: 'dataDownloadStatusDesc',
        minWidth: 120,
      },
      {
        label: t(
          'exBasicDataDict.exBasicDtaDictTypeTable.dataDownloadStatusMemo',
          '下载备注',
        ),
        prop: 'dataDownloadStatusMemo',
        minWidth: 130,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: ExBasicDataDict.ExBasicDtaDictTypeInfo) => {
          return (
            <div>
              {row.enabledFlag === ENABLED_FLAG.YES
                ? t('global:enabled')
                : t('global:disabled')}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 120,
        render: (
          row: ExBasicDataDict.ExBasicDtaDictTypeInfo & {
            downloading: boolean;
          },
        ) => {
          return (
            <div class={'flex justify-around'}>
              <el-button
                type="primary"
                link={true}
                onClick={() => handleView(row)}
              >
                {t('global:view')}
              </el-button>
              {row.basicDataSourceCode === BASIC_DATA_SOURCE_CODE.DOWNLOAD && (
                <el-button
                  type={
                    row.dataDownloadStatusCode ===
                    DATA_DOWNLOAD_STATUS_CODE.DOWNLOADING
                      ? 'danger'
                      : 'primary'
                  }
                  link={true}
                  loading={!!row.downloading}
                  disabled={!!row.downloading}
                  onClick={() => handleExBasicDtaDictTypeDownloadOrStop?.(row)}
                >
                  {row.dataDownloadStatusCode ===
                  DATA_DOWNLOAD_STATUS_CODE.DOWNLOADING
                    ? t('exBasicDataDict.exBasicDtaDictTypeTable.stop', '终止')
                    : t(
                        'exBasicDataDict.exBasicDtaDictTypeTable.download',
                        '下载',
                      )}
                </el-button>
              )}
            </div>
          );
        },
      },
    ],
  });
}

type ExBasicDtaDictTypeEditTableItem =
  ExBasicDataDict.ExBasicDtaDictTypeInfo & {
    editable: boolean;
  };

export function useExBasicDtaDictTypeEditTableConfig(
  tableRef: Ref<TableRef>,
  tableData: Ref<ExBasicDtaDictTypeEditTableItem[]>,
  disabled: Ref<boolean>,
  basicDataSourceCodeList: Ref<SelectOptions[]>,
  hisBasicDataTypeCodeList: Ref<SelectOptions[]>,
  dataDownloadStatusCodeList: Ref<SelectOptions[]>,
) {
  const { toggleEdit, cancelEdit, addItem, delItem } = useEditableTable({
    tableRef,
    data: tableData,
    id: 'basicDataDictTypeId',
  });

  const onItemDeleteClick = (index: number) => {
    delItem(index);
  };

  const onItemConfirmClick = (data: ExBasicDtaDictTypeEditTableItem) => {
    toggleEdit(data);
  };

  const onItemEditClick = (data: ExBasicDtaDictTypeEditTableItem) => {
    toggleEdit(data);
  };

  const exBasicDtaDictTypeTableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: ExBasicDtaDictTypeEditTableItem, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t(
          'exBasicDataDict.exBasicDtaDictTypeTable.basicDataDictTypeNo',
          '目录分类编码',
        ),
        prop: 'basicDataDictTypeNo',
        minWidth: 120,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'exBasicDataDict.exBasicDtaDictTypeTable.basicDataDictTypeNo',
                '目录分类编码',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaDictTypeEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.basicDataDictTypeNo}
                  maxLength={20}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDtaDictTypeTable.basicDataDictTypeNo',
                      '目录分类编码',
                    ),
                  })}
                />
              ) : (
                <>{row.basicDataDictTypeNo}</>
              )}
            </div>
          );
        },
      },
      {
        label: t(
          'exBasicDataDict.exBasicDtaDictTypeTable.basicDataDictTypeName',
          '目录分类名称',
        ),
        prop: 'basicDataDictTypeName',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'exBasicDataDict.exBasicDtaDictTypeTable.basicDataDictTypeName',
                '目录分类名称',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaDictTypeEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.basicDataDictTypeName}
                  maxLength={256}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDtaDictTypeTable.basicDataDictTypeName',
                      '目录分类名称',
                    ),
                  })}
                />
              ) : (
                <>{row.basicDataDictTypeName}</>
              )}
            </div>
          );
        },
      },
      {
        label: t(
          'exBasicDataDict.exBasicDtaDictTypeTable.basicDataSourceCode',
          '数据来源',
        ),
        prop: 'basicDataSourceCode',
        minWidth: 120,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'exBasicDataDict.exBasicDtaDictTypeTable.basicDataSourceCode',
                '数据来源',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaDictTypeEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.basicDataSourceCode}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'exBasicDataDict.exBasicDtaDictTypeTable.basicDataSourceCode',
                      '数据来源',
                    ),
                  })}
                  onChange={(val: string) => {
                    const item = basicDataSourceCodeList.value.find(
                      (codeItem) => codeItem.value === val,
                    );
                    row.basicDataSourceDesc = item?.label as string;
                  }}
                >
                  {basicDataSourceCodeList.value?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.basicDataSourceDesc}</>
              )}
            </div>
          );
        },
      },
      {
        label: t(
          'exBasicDataDict.exBasicDtaDictTypeTable.mappingFlag',
          '需要对照',
        ),
        prop: 'mappingFlag',
        minWidth: 120,
        editable: true,
        render: (row: ExBasicDtaDictTypeEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-checkbox
                  class="w-full justify-center"
                  true-value={ENABLED_FLAG.YES}
                  false-value={ENABLED_FLAG.NO}
                  v-model={row.mappingFlag}
                  onChange={(val: boolean) => {
                    if (!val) {
                      row.hisBasicDataTypeCode = '';
                      row.hisBasicDataTypeDesc = '';
                    }
                  }}
                  size="large"
                />
              ) : (
                <>
                  {row.mappingFlag === ENABLED_FLAG.YES
                    ? t('global:yes')
                    : t('global:no')}
                </>
              )}
            </div>
          );
        },
      },
      {
        label: t(
          'exBasicDataDict.exBasicDtaDictTypeTable.hisBasicDataTypeCode',
          '对应HIS分类',
        ),
        prop: 'hisBasicDataTypeCode',
        minWidth: 120,
        editable: true,
        rules: (row: ExBasicDtaDictTypeEditTableItem) => [
          {
            required: row.mappingFlag === ENABLED_FLAG.YES,
            message: t('global:placeholder.select.template', {
              name: t(
                'exBasicDataDict.exBasicDtaDictTypeTable.hisBasicDataTypeCode',
                '对应HIS分类',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaDictTypeEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.hisBasicDataTypeCode}
                  disabled={row.mappingFlag !== ENABLED_FLAG.YES}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'exBasicDataDict.exBasicDtaDictTypeTable.hisBasicDataTypeCode',
                      '对应HIS分类',
                    ),
                  })}
                  onChange={(val: string) => {
                    const item = hisBasicDataTypeCodeList.value.find(
                      (codeItem) => codeItem.value === val,
                    );
                    row.hisBasicDataTypeDesc = item?.label as string;
                  }}
                >
                  {hisBasicDataTypeCodeList.value?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.hisBasicDataTypeDesc}</>
              )}
            </div>
          );
        },
      },
      {
        label: t(
          'exBasicDataDict.exBasicDtaDictTypeTable.dataDownloadStatusCode',
          '下载状态',
        ),
        prop: 'dataDownloadStatusCode',
        minWidth: 120,
        editable: true,
        render: (row: ExBasicDtaDictTypeEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.dataDownloadStatusCode}
                  disabled={
                    row.basicDataSourceCode !== BASIC_DATA_SOURCE_CODE.DOWNLOAD
                  }
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'exBasicDataDict.exBasicDtaDictTypeTable.dataDownloadStatusCode',
                      '下载状态',
                    ),
                  })}
                  onChange={(val: string) => {
                    const item = dataDownloadStatusCodeList.value.find(
                      (codeItem) => codeItem.value === val,
                    );
                    row.dataDownloadStatusDesc = item?.label as string;
                  }}
                >
                  {dataDownloadStatusCodeList.value?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.dataDownloadStatusDesc}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('global:enabledFlag'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaDictTypeEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-switch
                  v-model={row.enabledFlag}
                  inline-prompt
                  active-value={ENABLED_FLAG.YES}
                  inactive-value={ENABLED_FLAG.NO}
                  active-text={t('global:enabled')}
                  inactive-text={t('global:disabled')}
                />
              ) : (
                <div>
                  {row.enabledFlag === ENABLED_FLAG.YES
                    ? t('global:enabled')
                    : t('global:disabled')}
                </div>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 100,
        render: (row: ExBasicDtaDictTypeEditTableItem, $index: number) => {
          return row.editable ? (
            <div class={'flex justify-around'} key="editable">
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => onItemConfirmClick(row)}
              >
                {t('global:confirm')}
              </el-button>
            </div>
          ) : (
            <div class={'flex justify-around'}>
              <el-button
                type="danger"
                link={true}
                disabled={!!row.basicDataDictTypeId || disabled.value}
                onClick={() => onItemDeleteClick($index)}
              >
                {t('global:remove')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                disabled={disabled.value}
                onClick={() => onItemEditClick(row)}
              >
                {t('global:edit')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return {
    exBasicDtaDictTypeTableConfig,
    addItem,
  };
}

type ExBasicDtaEditTableItem = ExBasicDataDict.ExBasicDtaInfo & {
  editable: boolean;
};

export function useExBasicGenericDtaTableConfig(
  tableRef: Ref<TableRef>,
  tableData: Ref<ExBasicDtaEditTableItem[]>,
  basicDataSourceCodeList: Ref<SelectOptions[]>,
) {
  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef,
    data: tableData,
    id: 'basicDataId',
  });

  const onItemConfirmClick = (data: ExBasicDtaEditTableItem) => {
    toggleEdit(data);
  };

  const onItemEditClick = (data: ExBasicDtaEditTableItem) => {
    toggleEdit(data);
  };

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: ExBasicDtaEditTableItem, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t(
          'exBasicDataDict.exBasicDta.table.basicDataNo',
          '基础数据编码',
        ),
        prop: 'basicDataNo',
        minWidth: 120,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'exBasicDataDict.exBasicDta.table.basicDataNo',
                '基础数据编码',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.basicDataNo}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDta.table.basicDataNo',
                      '基础数据编码',
                    ),
                  })}
                />
              ) : (
                <>{row.basicDataNo}</>
              )}
            </div>
          );
        },
      },
      {
        label: t(
          'exBasicDataDict.exBasicDta.table.basicDataName',
          '基础数据名称',
        ),
        prop: 'basicDataName',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'exBasicDataDict.exBasicDta.table.basicDataName',
                '基础数据名称',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.basicDataName}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDta.table.basicDataName',
                      '基础数据名称',
                    ),
                  })}
                  onChange={(val: string) => {
                    row.spellNo = convertToSpellNo(val);
                    row.wbNo = convertToWbNo(val);
                  }}
                />
              ) : (
                <>{row.basicDataName}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:spellNo'),
        prop: 'spellNo',
        editable: false,
        minWidth: 120,
      },
      {
        label: t('global:wbNo'),
        prop: 'wbNo',
        editable: false,
        minWidth: 120,
      },
      {
        label: t(
          'exBasicDataDict.exBasicDta.table.basicDataSourceDesc',
          '数据来源',
        ),
        prop: 'basicDataSourceDesc',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'exBasicDataDict.exBasicDta.table.basicDataSourceDesc',
                '数据来源',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.basicDataSourceCode}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'exBasicDataDict.exBasicDta.table.basicDataSourceDesc',
                      '数据来源',
                    ),
                  })}
                  onChange={(val: string) => {
                    const item = basicDataSourceCodeList.value.find(
                      (codeItem) => codeItem.value === val,
                    );
                    row.basicDataSourceDesc = item?.label as string;
                  }}
                >
                  {basicDataSourceCodeList.value?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.basicDataSourceDesc}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('global:enabledFlag'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-switch
                  v-model={row.enabledFlag}
                  inline-prompt
                  active-value={ENABLED_FLAG.YES}
                  inactive-value={ENABLED_FLAG.NO}
                  active-text={t('global:enabled')}
                  inactive-text={t('global:disabled')}
                />
              ) : (
                <div>
                  {row.enabledFlag === ENABLED_FLAG.YES
                    ? t('global:enabled')
                    : t('global:disabled')}
                </div>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 100,
        render: (row: ExBasicDtaEditTableItem, $index: number) => {
          return row.editable ? (
            <div class={'flex justify-around'} key="editable">
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => onItemConfirmClick(row)}
              >
                {t('global:confirm')}
              </el-button>
            </div>
          ) : (
            <el-button
              type="primary"
              link={true}
              disabled={true}
              onClick={() => onItemEditClick(row)}
            >
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });
}

export function useExBasicProjectDtaTableConfig(
  tableRef: Ref<TableRef>,
  tableData: Ref<ExBasicDtaEditTableItem[]>,
  basicDataSourceCodeList: Ref<SelectOptions[]>,
) {
  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef,
    data: tableData,
    id: 'basicDataId',
  });

  const onItemConfirmClick = (data: ExBasicDtaEditTableItem) => {
    toggleEdit(data);
  };

  const onItemEditClick = (data: ExBasicDtaEditTableItem) => {
    toggleEdit(data);
  };

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: ExBasicDtaEditTableItem, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t(
          'exBasicDataDict.exBasicDta.table.basicDataNo',
          '基础数据编码',
        ),
        prop: 'basicDataNo',
        minWidth: 120,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'exBasicDataDict.exBasicDta.table.basicDataNo',
                '基础数据编码',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.basicDataNo}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDta.table.basicDataNo',
                      '基础数据编码',
                    ),
                  })}
                />
              ) : (
                <>{row.basicDataNo}</>
              )}
            </div>
          );
        },
      },
      {
        label: t(
          'exBasicDataDict.exBasicDta.table.basicDataName',
          '基础数据名称',
        ),
        prop: 'basicDataName',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'exBasicDataDict.exBasicDta.table.basicDataName',
                '基础数据名称',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.basicDataName}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDta.table.basicDataName',
                      '基础数据名称',
                    ),
                  })}
                  onChange={(val: string) => {
                    row.spellNo = convertToSpellNo(val);
                    row.wbNo = convertToWbNo(val);
                  }}
                />
              ) : (
                <>{row.basicDataName}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('exBasicDataDict.exBasicDta.table.billingUnit', '计价单位'),
        prop: 'billingUnit',
        minWidth: 100,
        editable: true,
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.billingUnit}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDta.table.billingUnit',
                      '计价单位',
                    ),
                  })}
                />
              ) : (
                <>{row.billingUnit}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('exBasicDataDict.exBasicDta.table.description', '项目说明'),
        prop: 'description',
        minWidth: 130,
        editable: true,
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.description}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDta.table.description',
                      '项目说明',
                    ),
                  })}
                />
              ) : (
                <>{row.description}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('exBasicDataDict.exBasicDta.table.connotation', '项目内涵'),
        prop: 'connotation',
        minWidth: 140,
        editable: true,
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.connotation}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDta.table.connotation',
                      '项目内涵',
                    ),
                  })}
                />
              ) : (
                <>{row.connotation}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('exBasicDataDict.exBasicDta.table.exceptContent', '除外内容'),
        prop: 'exceptContent',
        minWidth: 150,
        editable: true,
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.exceptContent}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDta.table.exceptContent',
                      '除外内容',
                    ),
                  })}
                />
              ) : (
                <>{row.exceptContent}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:spellNo'),
        prop: 'spellNo',
        editable: false,
        minWidth: 120,
      },
      {
        label: t('global:wbNo'),
        prop: 'wbNo',
        editable: false,
        minWidth: 120,
      },
      {
        label: t(
          'exBasicDataDict.exBasicDta.table.basicDataSourceDesc',
          '数据来源',
        ),
        prop: 'basicDataSourceDesc',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'exBasicDataDict.exBasicDta.table.basicDataSourceDesc',
                '数据来源',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.basicDataSourceCode}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'exBasicDataDict.exBasicDta.table.basicDataSourceDesc',
                      '数据来源',
                    ),
                  })}
                  onChange={(val: string) => {
                    const item = basicDataSourceCodeList.value.find(
                      (codeItem) => codeItem.value === val,
                    );
                    row.basicDataSourceDesc = item?.label as string;
                  }}
                >
                  {basicDataSourceCodeList.value?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.basicDataSourceDesc}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('global:enabledFlag'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-switch
                  v-model={row.enabledFlag}
                  inline-prompt
                  active-value={ENABLED_FLAG.YES}
                  inactive-value={ENABLED_FLAG.NO}
                  active-text={t('global:enabled')}
                  inactive-text={t('global:disabled')}
                />
              ) : (
                <div>
                  {row.enabledFlag === ENABLED_FLAG.YES
                    ? t('global:enabled')
                    : t('global:disabled')}
                </div>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 100,
        render: (row: ExBasicDtaEditTableItem, $index: number) => {
          return row.editable ? (
            <div class={'flex justify-around'} key="editable">
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => onItemConfirmClick(row)}
              >
                {t('global:confirm')}
              </el-button>
            </div>
          ) : (
            <el-button
              type="primary"
              link={true}
              disabled={true}
              onClick={() => onItemEditClick(row)}
            >
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });
}

export function useExBasicDrugDtaTableConfig(
  tableRef: Ref<TableRef>,
  tableData: Ref<ExBasicDtaEditTableItem[]>,
  basicDataSourceCodeList: Ref<SelectOptions[]>,
) {
  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef,
    data: tableData,
    id: 'basicDataId',
  });

  const onItemConfirmClick = (data: ExBasicDtaEditTableItem) => {
    toggleEdit(data);
  };

  const onItemEditClick = (data: ExBasicDtaEditTableItem) => {
    toggleEdit(data);
  };

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: ExBasicDtaEditTableItem, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t(
          'exBasicDataDict.exBasicDta.table.basicDataNo',
          '基础数据编码',
        ),
        prop: 'basicDataNo',
        minWidth: 120,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'exBasicDataDict.exBasicDta.table.basicDataNo',
                '基础数据编码',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.basicDataNo}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDta.table.basicDataNo',
                      '基础数据编码',
                    ),
                  })}
                />
              ) : (
                <>{row.basicDataNo}</>
              )}
            </div>
          );
        },
      },
      {
        label: t(
          'exBasicDataDict.exBasicDta.table.basicDataName',
          '基础数据名称',
        ),
        prop: 'basicDataName',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'exBasicDataDict.exBasicDta.table.basicDataName',
                '基础数据名称',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.basicDataName}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDta.table.basicDataName',
                      '基础数据名称',
                    ),
                  })}
                  onChange={(val: string) => {
                    row.spellNo = convertToSpellNo(val);
                    row.wbNo = convertToWbNo(val);
                  }}
                />
              ) : (
                <>{row.basicDataName}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('exBasicDataDict.exBasicDta.table.spec', '规格'),
        prop: 'spec',
        minWidth: 100,
        editable: true,
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.spec}
                  placeholder={t('global:placeholder.input.template', {
                    content: t('exBasicDataDict.exBasicDta.table.spec', '规格'),
                  })}
                />
              ) : (
                <>{row.spec}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('exBasicDataDict.exBasicDta.table.dedicineDosageForm', '剂型'),
        prop: 'dedicineDosageForm',
        minWidth: 100,
        editable: true,
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.dedicineDosageForm}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDta.table.dedicineDosageForm',
                      '剂型',
                    ),
                  })}
                />
              ) : (
                <>{row.dedicineDosageForm}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('exBasicDataDict.exBasicDta.table.unitPrice', '单价'),
        prop: 'unitPrice',
        minWidth: 100,
        editable: true,
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.unitPrice}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDta.table.unitPrice',
                      '单价',
                    ),
                  })}
                />
              ) : (
                <>{row.unitPrice}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('exBasicDataDict.exBasicDta.table.approvalNo', '批准文号'),
        prop: 'approvalNo',
        minWidth: 130,
        editable: true,
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.approvalNo}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDta.table.approvalNo',
                      '批准文号',
                    ),
                  })}
                />
              ) : (
                <>{row.approvalNo}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('exBasicDataDict.exBasicDta.table.provider', '生产厂家'),
        prop: 'provider',
        minWidth: 140,
        editable: true,
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.provider}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'exBasicDataDict.exBasicDta.table.provider',
                      '生产厂家',
                    ),
                  })}
                />
              ) : (
                <>{row.provider}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:spellNo'),
        prop: 'spellNo',
        editable: false,
        minWidth: 120,
      },
      {
        label: t('global:wbNo'),
        prop: 'wbNo',
        editable: false,
        minWidth: 120,
      },
      {
        label: t(
          'exBasicDataDict.exBasicDta.table.basicDataSourceDesc',
          '数据来源',
        ),
        prop: 'basicDataSourceDesc',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'exBasicDataDict.exBasicDta.table.basicDataSourceDesc',
                '数据来源',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.basicDataSourceCode}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'exBasicDataDict.exBasicDta.table.basicDataSourceDesc',
                      '数据来源',
                    ),
                  })}
                  onChange={(val: string) => {
                    const item = basicDataSourceCodeList.value.find(
                      (codeItem) => codeItem.value === val,
                    );
                    row.basicDataSourceDesc = item?.label as string;
                  }}
                >
                  {basicDataSourceCodeList.value?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.basicDataSourceDesc}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('global:enabledFlag'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: ExBasicDtaEditTableItem) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-switch
                  v-model={row.enabledFlag}
                  inline-prompt
                  active-value={ENABLED_FLAG.YES}
                  inactive-value={ENABLED_FLAG.NO}
                  active-text={t('global:enabled')}
                  inactive-text={t('global:disabled')}
                />
              ) : (
                <div>
                  {row.enabledFlag === ENABLED_FLAG.YES
                    ? t('global:enabled')
                    : t('global:disabled')}
                </div>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 100,
        render: (row: ExBasicDtaEditTableItem, $index: number) => {
          return row.editable ? (
            <div class={'flex justify-around'} key="editable">
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => onItemConfirmClick(row)}
              >
                {t('global:confirm')}
              </el-button>
            </div>
          ) : (
            <el-button
              type="primary"
              link={true}
              disabled={true}
              onClick={() => onItemEditClick(row)}
            >
              {t('global:edit')}
            </el-button>
          );
        },
      },
    ],
  });
}
