import { useFormConfig } from 'sun-biz';
import { CodeSystemType } from '@/typings/codeManage';

export function useProductDefineFormConfig() {
  const data = useFormConfig({
    dataSetCodes: [CodeSystemType.CODE_REPOSITORY_TYPE_CODE],
    getData: (t, data) => [
      {
        label: '关键字',
        name: 'keyWord',
        component: 'input',
        placeholder: t('useProductDefineFormConfig.keyword', '请输入关键字'),
        className: 'w-60',
        triggerModelChange: true,
        extraProps: {
          suffixIcon: 'Search',
        },
      },
      {
        name: 'codeRepositoryTypeCode',
        label: t(
          'useProductDefineFormConfig.codeRepositoryTypeCode',
          '仓库类型',
        ),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t(
            'useProductDefineFormConfig.codeRepositoryTypeCode',
            '仓库类型',
          ),
        }),
        triggerModelChange: true,
        extraProps: {
          filterable: true,
          options: data?.value
            ? data.value[CodeSystemType.CODE_REPOSITORY_TYPE_CODE].filter(
                (item) => item.dataValueNo === '1' || item.dataValueNo === '2',
              )
            : [],
        },
      },
    ],
  });
  return data;
}
