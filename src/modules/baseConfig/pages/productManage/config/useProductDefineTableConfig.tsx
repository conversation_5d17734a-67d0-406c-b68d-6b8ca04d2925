import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import { Ref } from 'vue';
import { CodeSystemType } from '@/typings/codeManage';
import { SelectOptions } from '@/typings/common.ts';

// 扩展基础类型以匹配useEditableTable需要的类型
interface EditableRepository
  extends ProductManage.UpsertOrgXProdCodeRepository {
  editable: boolean;
}

// 定义扩展的SelectOptions类型
interface CodeRepositoryOption extends SelectOptions {
  codeRepositoryId: string;
  codeRepositoryName: string;
}

export function useProductDefineTableConfig(options: {
  id: string;
  data: Ref<EditableRepository[]>;
  tableRef: Ref<TableRef>;
  codeRepositoryUse: Ref<CodeRepositoryOption[]>;
  onCodeRepositoryChange?: (
    val: string,
    row: ProductManage.UpsertOrgXProdCodeRepository,
  ) => void;
  handleSave: (
    row: ProductManage.UpsertOrgXProdCodeRepository,
  ) => Promise<void>;
  deleteItem: (
    row: ProductManage.UpsertOrgXProdCodeRepository,
  ) => Promise<void>;
  handleEdit: (row: ProductManage.UpsertOrgXProdCodeRepository) => void;
  cancelEditRow: (
    row: ProductManage.UpsertOrgXProdCodeRepository,
    $index: number,
  ) => void;
  isCloudEnv: boolean | undefined;
}) {
  const {
    id,
    data,
    tableRef,
    codeRepositoryUse,
    onCodeRepositoryChange,
    handleSave,
    deleteItem,
    cancelEditRow,
    isCloudEnv,
  } = options;

  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef,
    data,
    id,
  });

  const tableColumns = useColumnConfig({
    dataSetCodes: [CodeSystemType.CODE_REPOSITORY_TYPE_CODE],
    getData: (t) => [
      {
        prop: 'selection',
        editable: true,
        type: 'selection',
      },
      {
        label: t(
          'hospitalList.hospitalListTable.codeRepositoryId',
          '代码仓库名称',
        ),
        prop: 'codeRepositoryId',
        minWidth: 150,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'hospitalList.hospitalListTable.codeRepositoryId',
                '代码仓库名称',
              ),
            }),
            trigger: 'change',
          },
        ],
        render: (row: EditableRepository) => {
          if (row.editable) {
            return (
              <el-select
                v-model={row.codeRepositoryId}
                filterable={true}
                clearable={false} // 防止清空
                onChange={(val: string) => {
                  if (onCodeRepositoryChange && val) {
                    onCodeRepositoryChange(val, row);
                  }
                }}
                placeholder={t('global:placeholder.select.template', {
                  name: t(
                    'hospitalList.hospitalListTable.codeRepositoryId',
                    '代码仓库名称',
                  ),
                })}
              >
                {codeRepositoryUse.value?.map((item) => (
                  <el-option
                    key={item.codeRepositoryId}
                    label={item.codeRepositoryName}
                    value={item.codeRepositoryId}
                  />
                ))}
              </el-select>
            );
          } else {
            return <>{row.codeRepositoryName}</>;
          }
        },
      },
      {
        label: t(
          'hospitalList.hospitalListTable.codeRepositoryDesc',
          '代码仓库描述',
        ),
        prop: 'codeRepositoryDesc',
        minWidth: 150,
        editable: false,
      },
      {
        label: t(
          'hospitalList.hospitalListTable.codeRepositoryTypeCodeDesc',
          '代码仓库类型',
        ),
        prop: 'codeRepositoryTypeCodeDesc',
        minWidth: 150,
        editable: false,
      },

      {
        label: t('global:operation', '操作'),
        prop: 'operation',
        fixed: 'right',
        width: 120,
        render: (row: EditableRepository, $index: number) => {
          return (
            <div key={row.codeRepositoryId}>
              {row.editable ? (
                <div class={'flex justify-center'} key="editable">
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => cancelEditRow(row, $index)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => handleSave(row)}
                  >
                    {t('global:save')}
                  </el-button>
                </div>
              ) : (
                <div class={'flex justify-center'}>
                  {/*<el-button*/}
                  {/*  type="primary"*/}
                  {/*  link*/}
                  {/*  disabled={!isCloudEnv}*/}
                  {/*  onClick={() => handleEdit(row)}*/}
                  {/*>*/}
                  {/*  {t('global:edit')}*/}
                  {/*</el-button>*/}
                  <el-button
                    type="danger"
                    link
                    disabled={!isCloudEnv}
                    onClick={() => deleteItem(row)}
                  >
                    {t('global:delete')}
                  </el-button>
                </div>
              )}
            </div>
          );
        },
      },
    ],
  });
  return { tableColumns, toggleEdit, cancelEdit };
}
