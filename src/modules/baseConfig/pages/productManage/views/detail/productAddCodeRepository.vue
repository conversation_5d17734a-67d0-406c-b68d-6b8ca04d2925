<script lang="ts" name="hospitalList" setup>
  import { onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useTranslation } from 'i18next-vue';
  import { ProForm, ProTable } from 'sun-biz';
  import { SelectOptions } from '@/typings/common.ts';
  import { addOrgXProdCodeRepository } from '@/modules/baseConfig/api/productManage.ts';
  import {
    queryCodeRepositoryByExample,
    querySystemListByExample,
  } from '@/modules/system/api/menu';
  import { useProductAddCodeRepositoryTableConfig } from '../../config/useProductAddCodeRepositoryTableConfig.tsx';
  import { useProductAddCodeRepositoryFormConfig } from '../../config/useProductAddCodeRepositoryFromConfig.tsx';

  const route = useRoute();
  const router = useRouter();

  // 扩展查询参数类型，添加可能的过滤字段
  interface ExtendedQueryParams extends ProductManage.QueryProductManageParams {
    codeRepositoryTypeCode?: string;
    systemId: string;
  }

  const searchParams = ref<ExtendedQueryParams>({
    keyWord: '',
    codeRepositoryTypeCode: '',
    systemId: '',
  });

  // 扩展产品信息类型，确保包含所需属性
  interface ProductInfo extends ProductManage.UpsertOrgXProdCodeRepository {
    productId?: string;
    productName?: string;
    orgId?: string;
    orgName?: string;
  }

  const info = ref<ProductInfo>({});

  const productAddCodeRepositoryRef =
    ref<ProductManage.UpsertOrgXProdCodeRepository>(); // 使用any类型避免类型检查错误

  const selections = ref<ProductManage.UpsertOrgXProdCodeRepository[]>([]);
  const codeRepositorySource = ref<
    ProductManage.UpsertOrgXProdCodeRepository[]
  >([]);
  const codeRepositoryUse = ref<ProductManage.UpsertOrgXProdCodeRepository[]>(
    [],
  );

  const { t } = useTranslation();

  const loading = ref(false);

  const queryCodeRepositoryList = async () => {
    const [, res] = await queryCodeRepositoryByExample({
      keyWord: '',
      enabledFlag: 1,
    });
    if (res?.success) {
      codeRepositorySource.value = res.data || [];
      codeRepositoryUse.value = codeRepositorySource.value;
    }
  };

  const systemList = ref<Menu.SystemInfo[]>([]);
  const systemSelections = ref<SelectOptions[]>([]);
  const querySystemList = async () => {
    const [, res] = await querySystemListByExample({});
    if (res?.success) {
      if (res.data?.length > 0) {
        systemList.value = res.data || [];
        systemSelections.value = res.data
          .sort((a, b) => {
            return Number(a?.sort) - Number(b?.sort);
          })
          .map((item) => ({
            value: item.sysId,
            label: item.sysName,
          }));
      } else {
        systemSelections.value = [];
      }
    }
  };

  const saveProductDefine = async () => {
    const ids = selections.value.map((item) => item.codeRepositoryId);
    const params = {
      orgXProdId: info.value.orgXProdId,
      codeRepositoryIds: ids,
    };
    const [, res] = await addOrgXProdCodeRepository(params);

    if (res?.success) {
      go2ProductDefine();
    }
  };
  // 选中行设置
  const selectionChange = (
    val: ProductManage.UpsertOrgXProdCodeRepository[],
  ) => {
    selections.value = val;
  };

  // 表格配置数据
  const { tableColumns } = useProductAddCodeRepositoryTableConfig();

  const go2ProductList = () => {
    router.push({
      name: 'productManage',
    });
  };
  const go2HospitalList = () => {
    router.push({
      name: 'hospitalList',
      params: {
        productId: info.value.productId,
      },
    });
  };
  const go2ProductDefine = () => {
    router.push({
      name: 'productDefine',
      params: {
        productId: info.value.productId,
        orgId: info.value.orgId,
      },
      query: {
        productName: info.value.productName,
        orgName: info.value.orgName,
        orgXProdId: info.value.orgXProdId,
      },
    });
  };
  const filterData = (data?: ExtendedQueryParams) => {
    // 重置为原始数据
    codeRepositoryUse.value = codeRepositorySource.value;

    // 如果没有筛选条件，直接返回所有数据
    if (
      !data ||
      (!data.keyWord && !data.codeRepositoryTypeCode && !data.systemId)
    ) {
      return;
    }

    // 构建筛选条件
    const conditions = {
      keyword: data.keyWord?.toLowerCase(),
      typeCode: data.codeRepositoryTypeCode,
      systemId: data.systemId,
    };

    // 应用筛选
    codeRepositoryUse.value = codeRepositoryUse.value.filter((item) => {
      // 关键字匹配
      if (conditions.keyword) {
        const name = item.codeRepositoryName?.toLowerCase() || '';
        const desc = item.codeRepositoryDesc?.toLowerCase() || '';
        if (
          !name.includes(conditions.keyword) &&
          !desc.includes(conditions.keyword)
        ) {
          return false;
        }
      }

      // 类型匹配
      if (
        conditions.typeCode &&
        item.codeRepositoryTypeCode !== conditions.typeCode
      ) {
        return false;
      }

      // 系统匹配
      if (conditions.systemId) {
        const system = systemList.value.find(
          (sys) => sys.sysId === conditions.systemId,
        );
        const hasCodeRepository = system?.codeRepositoryList?.some(
          (repo) => repo.codeRepositoryId === item.codeRepositoryId,
        );
        if (!hasCodeRepository) {
          return false;
        }
      }

      return true;
    });
  };
  const searchConfig = useProductAddCodeRepositoryFormConfig(systemSelections);

  onMounted(() => {
    // 解析路由参数
    const { productId, orgId } = route.params;
    const { productName, orgName, orgXProdId } = route.query;

    if (productId && orgId) {
      info.value = {
        productId,
        orgId,
        orgXProdId,
        productName: typeof productName === 'string' ? productName : '',
        orgName: typeof orgName === 'string' ? orgName : '',
      } as ProductInfo;
      console.log('解析产品信息:', info.value);
    }
    queryCodeRepositoryList();
    querySystemList();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div class="el-form-item text-base">
      <el-button link @click="go2ProductList">产品列表</el-button>
      /
      <el-button link @click="go2HospitalList">医院列表</el-button>
      /
      <el-button link type="primary" @click="go2ProductDefine"
        >代码仓库列表
      </el-button>
    </div>
    <div class="mt-3 flex justify-start">
      <div class="el-form-item">
        <ProForm
          ref="proForm"
          v-model="searchParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="filterData"
        />
      </div>
      <div class="flex justify-between">
        <el-button
          :disabled="selections.length === 0"
          type="primary"
          @click="saveProductDefine"
        >
          {{ t('hospitalList.searchForm.save', '保存') }}
        </el-button>

        <el-button plain type="primary" @click="go2ProductDefine">
          {{ t('global:cancel', '取消') }}
        </el-button>
      </div>
    </div>
    <ProTable
      ref="productAddCodeRepositoryRef"
      :columns="tableColumns"
      :data="codeRepositoryUse"
      :loading="loading"
      row-key="codeRepositoryId"
      @selection-change="selectionChange"
    />
  </div>
</template>
