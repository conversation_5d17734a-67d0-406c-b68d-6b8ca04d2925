<script lang="ts" name="hospitalList" setup>
  import { computed, onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useTranslation } from 'i18next-vue';
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    useAppConfigData,
  } from 'sun-biz';
  import {
    addOrgXProdCodeRepository,
    deleteOrgXProdCodeRepository,
    queryCodeRepositoryByExampleV1,
  } from '@/modules/baseConfig/api/productManage.ts';
  import { queryCodeRepositoryByExample } from '@/modules/system/api/menu';
  import { useProductDefineTableConfig } from '../../config/useProductDefineTableConfig.tsx';
  import { useProductDefineFormConfig } from '../../config/useProductDefineFormConfig.tsx';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant.ts';

  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);

  const route = useRoute();
  const router = useRouter();

  // 扩展查询参数类型，添加可能的过滤字段
  interface ExtendedQueryParams extends ProductManage.QueryProductManageParams {
    codeRepositoryTypeCode?: string;
    systemId: string;
  }

  const searchParams = ref<ExtendedQueryParams>({
    keyWord: '',
    codeRepositoryTypeCode: '',
    systemId: '',
  });

  // 扩展产品信息类型，确保包含所需属性
  interface ProductInfo extends ProductManage.UpsertOrgXProdCodeRepository {
    productId?: string;
    productName?: string;
    orgId?: string;
    orgName?: string;
  }

  // 扩展代码仓库类型，确保包含editable属性
  type EditableCodeRepository = ProductManage.UpsertOrgXProdCodeRepository & {
    editable: boolean;
  };

  const info = ref<ProductInfo>({});
  const isAdd = ref(false);

  const productDefineRef = ref<EditableCodeRepository>(); // 使用any类型避免类型检查错误
  const codeRepositoryListSource = ref<EditableCodeRepository[]>([]);
  const codeRepositoryList = ref<EditableCodeRepository[]>([]);
  const selections = ref<ProductManage.UpsertOrgXProdCodeRepository[]>([]);
  const codeRepositorySource = ref<EditableCodeRepository[]>([]);
  const codeRepositoryUse = ref<EditableCodeRepository[]>([]);

  const { t } = useTranslation();

  const loading = ref(false);

  /** 查询产品仓库 */
  async function queryCodeRepositoryDataList() {
    if (!info.value.productId || !info.value.orgId) return;

    const params = {
      productId: info.value.productId,
      orgId: info.value.orgId,
    };
    const [, res] = await queryCodeRepositoryByExampleV1(params);
    if (res?.success) {
      // 确保每个项目都有editable属性
      const data = (res.data || []).map((item) => ({
        ...item,
        editable: false,
      }));
      codeRepositoryListSource.value = data;
      codeRepositoryList.value = data;
    }
  }

  const queryCodeRepositoryList = async () => {
    const [, res] = await queryCodeRepositoryByExample({
      keyWord: '',
      enabledFlag: 1, // 修改为数字类型
    });
    if (res?.success) {
      codeRepositorySource.value = (res.data || []).map((item) => ({
        label: `${item?.codeRepositoryDesc || ''}（${item?.codeRepositoryName || ''}）`,
        value: item?.codeRepositoryId || '',
        codeRepositoryId: item?.codeRepositoryId || '',
        codeRepositoryTypeCode: item?.codeRepositoryTypeCode || '',
        codeRepositoryTypeCodeDesc: item?.codeRepositoryTypeCodeDesc || '',
        codeRepositoryDesc: item?.codeRepositoryDesc || '',
        codeRepositoryName: item?.codeRepositoryName || '',
        port: item?.port,
        appPath: item?.appPath,
        backupPath: item?.backupPath,
      }));
      codeRepositoryUse.value = codeRepositorySource.value;
    }
  };

  const canUpsertTableRow = () => {
    const isEditing = codeRepositoryList.value.some((item) => !!item.editable);
    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    } else {
      return true;
    }
  };

  const addNewProductDefine = () => {
    if (!canUpsertTableRow()) return;
    router.push({
      name: 'productAddCodeRepository',
      params: {
        productId: info.value.productId,
        orgId: info.value.orgId,
      },
      query: {
        productName: info.value.productName,
        orgName: info.value.orgName,
        orgXProdId: info.value.orgXProdId,
      },
    });
  };
  const saveProductDefine = async (row) => {
    const ids = codeRepositoryList.value.map((item) => item.codeRepositoryId);
    const params = {
      orgXProdId: info.value.orgXProdId,
      codeRepositoryIds: ids,
    };
    const [, res] = await addOrgXProdCodeRepository(params);

    if (res?.success) {
      row.editable = false;
      isAdd.value = false;
      queryCodeRepositoryDataList();
      queryCodeRepositoryList();
    } else {
      row.editable = true;
    }
  };

  const onCodeRepositoryChange = (
    codeRepositoryId: string,
    row: ProductManage.UpsertOrgXProdCodeRepository,
  ) => {
    if (codeRepositoryId) {
      const tmp = codeRepositoryUse.value.find(
        (item) => item.codeRepositoryId === codeRepositoryId,
      );
      if (tmp) {
        row.codeRepositoryName = tmp.codeRepositoryName || '';
        row.codeRepositoryDesc = tmp.codeRepositoryDesc || '';
        row.codeRepositoryTypeCodeDesc = tmp.codeRepositoryTypeCodeDesc || '';
      }
    } else {
      row.codeRepositoryName = '';
      row.codeRepositoryDesc = '';
      row.codeRepositoryTypeCodeDesc = '';
    }
  };
  const handleSave = async (
    row: ProductManage.UpsertOrgXProdCodeRepository,
  ) => {
    saveProductDefine(row);
  };
  // 删除
  const deleteItem = async (
    row: ProductManage.UpsertOrgXProdCodeRepository,
  ) => {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} "{{name}}" 吗？', {
        action: t('global:delete'),
        name: row.codeRepositoryName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        const [, res] = await deleteOrgXProdCodeRepository({
          ...row,
        });
        if (res?.success) {
          productDefineRef?.value.proTableRef.clearSelection();
          selections.value = [];
          await queryCodeRepositoryDataList();
        }
      })
      .catch(() => {});
  };

  // 编辑
  const handleEdit = async (
    row: ProductManage.UpsertOrgXProdCodeRepository,
  ) => {
    if (!canUpsertTableRow()) return;
    // 确保row有editable属性
    isAdd.value = true;
    toggleEdit(row as EditableCodeRepository);
  };
  // 取消
  const cancelEditRow = async (
    row: ProductManage.UpsertOrgXProdCodeRepository,
    index: number,
  ) => {
    // 确保row有editable属性
    isAdd.value = false;
    cancelEdit(row as EditableCodeRepository, index);
  };

  const bizData = computed(() => {
    const list = selections.value.map((item) => {
      return item.orgXProdDefId || '';
    });
    return list;
  });

  // 选中行设置
  const selectionChange = (
    val: ProductManage.UpsertOrgXProdCodeRepository[],
  ) => {
    selections.value = val;
  };

  // 表格配置数据
  const { tableColumns, toggleEdit, cancelEdit } = useProductDefineTableConfig({
    id: 'codeRepositoryId',
    data: codeRepositoryList as EditableCodeRepository,
    tableRef: productDefineRef,
    codeRepositoryUse: codeRepositoryUse as EditableCodeRepository,
    handleSave,
    onCodeRepositoryChange,
    deleteItem,
    handleEdit,
    cancelEditRow,
    isCloudEnv,
  });

  const filterData = (data?: ExtendedQueryParams) => {
    // 重置为原始数据
    codeRepositoryList.value = codeRepositoryListSource.value;

    // 如果没有筛选条件，直接返回所有数据
    if (!data) {
      return;
    }

    // 应用筛选
    codeRepositoryList.value = codeRepositoryList.value.filter((item) => {
      // 关键字匹配
      if (data.keyWord) {
        const name = item.codeRepositoryName || '';
        const desc = item.codeRepositoryDesc || '';
        if (!name.includes(data.keyWord) && !desc.includes(data.keyWord)) {
          return false;
        }
      }

      // 类型匹配
      if (data.codeRepositoryTypeCode) {
        if (item.codeRepositoryTypeCode !== data.codeRepositoryTypeCode) {
          return false;
        }
      }

      return true;
    });
  };

  const searchConfig = useProductDefineFormConfig();

  onMounted(() => {
    // 解析路由参数
    const { productId, orgId } = route.params;
    const { productName, orgName, orgXProdId } = route.query;

    if (productId && orgId) {
      info.value = {
        productId,
        orgId,
        orgXProdId,
        productName: typeof productName === 'string' ? productName : '',
        orgName: typeof orgName === 'string' ? orgName : '',
      } as ProductInfo;
      console.log('解析产品信息:', info.value);
      queryCodeRepositoryDataList();
    }
    queryCodeRepositoryList();
  });
  const go2ProductList = () => {
    router.push({
      name: 'productManage',
    });
  };
  const go2hospitalList = () => {
    router.push({
      name: 'hospitalList',
      params: {
        productId: info.value.productId,
      },
    });
  };
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <div class="el-form-item text-base">
      <el-button link @click="go2hospitalList"
        >{{ info.productName || '' }}
      </el-button>
      /
      <el-button link>{{ info.orgName }}</el-button>
    </div>
    <div class="mt-3 flex justify-start">
      <div class="el-form-item">
        <ProForm
          ref="proForm"
          v-model="searchParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="filterData"
        />
      </div>
      <div class="flex justify-between">
        <el-button
          :disabled="!isCloudEnv"
          class="mr-4"
          type="primary"
          @click="addNewProductDefine"
        >
          {{ t('hospitalList.searchForm.add', '添加') }}
        </el-button>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_ORG_X_PROD_DEF"
          class="mr-4"
          @success="
            () => {
              productDefineRef?.proTableRef.clearSelection();
            }
          "
        />
        <el-button plain type="primary" @click="go2ProductList">
          {{ t('global:cancel', '取消') }}
        </el-button>
      </div>
    </div>
    <ProTable
      ref="productDefineRef"
      :columns="tableColumns"
      :data="codeRepositoryList"
      :loading="loading"
      row-key="codeRepositoryId"
      @selection-change="selectionChange"
    />
  </div>
</template>
