<script setup lang="ts">
  import { ref } from 'vue';
  import { Title, ProForm, ProTable } from 'sun-biz';
  import { useBusinessRecyclingFormConfig } from './config/useRecyclingRecordsFormConfig.ts';
  import { useRecyclingRecordsTable } from './config/useRecyclingRecordsTableConfig.tsx';
  import { queryBizNoRecycleRecordByExample } from '@modules/baseConfig/api/bizNoGenerateRule';
  import { queryDataSetByCodeSystemCodes2 } from '@/api/common.ts';
  import { FLAG, BIZ_NO_OBJECT_CODE_NAME } from '@/utils/constant.ts';
  import { DEFAULT_PAGE_SIZE } from '@sun-toolkit/enums';
  const recyclingList = ref([]); //回收列表
  const loading = ref(true);
  //分页配置
  const pageInfo = ref({
    pageNumber: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    total: 0,
  });
  const searchParams = ref<BizNoGenerateRule.RecyclingParameters>({
    bizNoObjectCodes: [''],
    usedFlag: FLAG.NO,
  });
  //查询
  const queryComputerIndexList = async () => {
    loading.value = true;
    if (!searchParams.value.bizNoObjectCodes.length) {
      loading.value = false;
      return;
    }
    const [, res] = await queryBizNoRecycleRecordByExample({
      ...searchParams.value,
      pageNumber: pageInfo.value.pageNumber,
      pageSize: pageInfo.value.pageSize,
    });
    if (res?.success) {
      recyclingList.value = res?.data || [];
      if (res?.total) {
        pageInfo.value.total = res?.total;
      }
    }
    loading.value = false;
  };
  const searchConfig = useBusinessRecyclingFormConfig();

  //获取值域列表
  const valueRangeList = async () => {
    const [, res] = await queryDataSetByCodeSystemCodes2({
      codeSystemCodes: [BIZ_NO_OBJECT_CODE_NAME],
      enabledFlag: FLAG.YES,
    });
    if (res?.success) {
      searchParams.value.bizNoObjectCodes = (
        res.data ? res.data[BIZ_NO_OBJECT_CODE_NAME] : []
      ).map((item) => {
        return item.dataValueNo;
      });
    }
  };
  //初始化数据
  const initFun = async () => {
    await valueRangeList();
    await queryComputerIndexList();
  };
  //获取分页的方法
  const currentPageChange = (pageNumber: number) => {
    pageInfo.value.pageNumber = pageNumber;
    queryComputerIndexList();
  };
  //当前多少条数据发生改变
  const sizePageChange = (num: number) => {
    pageInfo.value.pageSize = num;
    queryComputerIndexList();
  };
  const modelChange = (data: BizNoGenerateRule.RecyclingParameters) => {
    searchParams.value = {
      ...searchParams.value,
      ...(data ?? {}),
    };
    queryComputerIndexList();
  };
  initFun();
  const recyclingRecords = useRecyclingRecordsTable();
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('bizNoGenerateRule.list.title', '业务编码回收记录')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchParams"
          :data="searchConfig"
          layout-mode="inline"
          :show-search-button="true"
          @model-change="modelChange"
        />
      </div>
    </div>
    <div class="mt-3 flex h-full flex-1 flex-col overflow-hidden">
      <ProTable
        :columns="recyclingRecords"
        :data="recyclingList"
        :page-info="pageInfo"
        :pagination="true"
        :loading="loading"
        @current-page-change="currentPageChange"
        @size-page-change="sizePageChange"
      ></ProTable>
    </div>
  </div>
</template>
