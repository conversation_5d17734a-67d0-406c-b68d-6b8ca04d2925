<script setup lang="ts">
  import {
    ElRadioGroup,
    ElRadio,
    ElPopover,
    ElButton,
    ElInput,
    ElTag,
  } from 'element-sun';
  import { Chrome } from '@ckpack/vue-color';
  import { ref, watch, computed, Ref, onMounted } from 'vue';
  import {
    TAG_CONFIG_COLORS,
    BACKGROUND_COLOR,
    FONT_COLOR,
  } from '@/utils/constant';
  import { ArrowDown } from '@element-sun/icons-vue';
  const props = defineProps<{
    modelValue?: {
      tagCssId: string;
      cssTypeCode: string;
      cssTypeDesc: string;
      cssValue: string;
    }[];
  }>();
  const emit = defineEmits(['update:modelValue']);

  // 实际生效的颜色
  const bgColor: Ref<string> = ref(TAG_CONFIG_COLORS[0].bg);
  const fontColor: Ref<string> = ref(TAG_CONFIG_COLORS[0].font);
  // 弹窗内临时变量
  const tempBgColor: Ref<string> = ref(bgColor.value);
  const tempFontColor: Ref<string> = ref(fontColor.value);
  // 预览用变量
  const previewBgColor: Ref<string> = ref(bgColor.value);
  const previewFontColor: Ref<string> = ref(fontColor.value);
  const colorType = ref<'bg' | 'font'>('bg');
  const popoverVisible = ref(false);

  const currentColor = computed({
    get() {
      return colorType.value === 'bg' ? tempBgColor.value : tempFontColor.value;
    },
    set(val) {
      let colorStr: string;
      if (hasHex(val)) {
        colorStr = val.hex;
      } else {
        colorStr = String(val);
      }
      if (colorType.value === 'bg') {
        tempBgColor.value = colorStr;
        previewBgColor.value = colorStr;
      } else {
        tempFontColor.value = colorStr;
        previewFontColor.value = colorStr;
      }
    },
  });

  function hasHex(val: unknown): val is { hex: string } {
    return (
      typeof val === 'object' &&
      val !== null &&
      'hex' in val &&
      typeof (val as { hex: unknown }).hex === 'string'
    );
  }

  // 工具函数：同步input和预览色
  function syncPreview(type: 'bg' | 'font', val: string) {
    if (type === 'bg') previewBgColor.value = val;
    else previewFontColor.value = val;
  }

  // 弹窗打开时，临时变量赋值
  function handlePopoverShow() {
    tempBgColor.value = bgColor.value;
    tempFontColor.value = fontColor.value;
    previewBgColor.value = bgColor.value;
    previewFontColor.value = fontColor.value;
    colorType.value = 'bg';
  }

  function handleConfirm() {
    bgColor.value = previewBgColor.value;
    fontColor.value = previewFontColor.value;
    tempBgColor.value = previewBgColor.value;
    tempFontColor.value = previewFontColor.value;
    emit('update:modelValue', [
      {
        cssTypeCode: BACKGROUND_COLOR,
        cssValue: bgColor.value,
      },
      {
        cssTypeCode: FONT_COLOR,
        cssValue: fontColor.value,
      },
    ]);
    popoverVisible.value = false;
  }
  function handleCancel() {
    previewBgColor.value = bgColor.value;
    previewFontColor.value = fontColor.value;
    tempBgColor.value = bgColor.value;
    tempFontColor.value = bgColor.value;
    popoverVisible.value = false;
  }
  function setPresetColor(color: { bg: string; font: string }) {
    tempBgColor.value = color.bg;
    tempFontColor.value = color.font;
    previewBgColor.value = color.bg;
    previewFontColor.value = color.font;
  }

  watch(
    () => props.modelValue,
    (val) => {
      if (val) {
        (props.modelValue || []).map((item) => {
          if (item.cssTypeCode === BACKGROUND_COLOR) {
            bgColor.value = item.cssValue;
            previewBgColor.value = item.cssValue;
          }
          if (item.cssTypeCode === FONT_COLOR) {
            fontColor.value = item.cssValue;
            previewFontColor.value = item.cssValue;
          }
        });
      }
    },
    { immediate: true },
  );

  onMounted(() => {
    if (!props.modelValue) {
      emit('update:modelValue', [
        {
          cssTypeCode: BACKGROUND_COLOR,
          cssValue: bgColor.value,
        },
        {
          cssTypeCode: FONT_COLOR,
          cssValue: fontColor.value,
        },
      ]);
    }
  });
</script>

<template>
  <el-popover
    placement="right"
    trigger="click"
    width="300"
    v-model:visible="popoverVisible"
    @show="handlePopoverShow"
    @hide="handleCancel"
  >
    <template #reference>
      <div class="flex cursor-pointer items-center">
        <el-tag
          class="mr-2"
          :icon="ArrowDown"
          :style="{ background: previewBgColor, color: previewFontColor }"
          disable-transitions
        >
          标签示例
        </el-tag>
        <el-button size="small" :icon="ArrowDown"></el-button>
      </div>
    </template>
    <div>
      <el-radio-group v-model="colorType" size="small">
        <div class="mb-2 flex w-full items-center">
          <el-radio label="bg">
            <span
              :class="[
                'color-radio-label',
                colorType === 'bg'
                  ? 'active font-bold text-pink-500'
                  : 'text-gray-600',
              ]"
              class="mr-1 text-base font-medium"
            >
              背景色
            </span>
          </el-radio>
          <el-input
            v-model="tempBgColor"
            size="small"
            class="ml-2 flex-1"
            @input="syncPreview('bg', $event)"
          />
        </div>
        <div class="mb-2 flex w-full items-center">
          <el-radio label="font">
            <span
              :class="[
                'color-radio-label',
                colorType === 'font'
                  ? 'active font-bold text-pink-500'
                  : 'text-gray-600',
              ]"
              class="mr-1 text-base font-medium"
            >
              字体色
            </span>
          </el-radio>
          <el-input
            v-model="tempFontColor"
            size="small"
            class="ml-2 flex-1"
            @input="syncPreview('font', $event)"
          />
        </div>
      </el-radio-group>
      <div class="flex">
        <Chrome
          v-model="currentColor"
          :disable-alpha="false"
          :disable-fields="true"
          class="my-2 w-full"
        />
        <div class="mb-2 mt-2 flex flex-col items-center gap-1.5 pl-2">
          <span class="text-gray-500">推荐</span>
          <el-tag
            v-for="(preset, idx) in TAG_CONFIG_COLORS"
            :key="idx"
            class="mb-2 cursor-pointer"
            :style="{ background: preset.bg, color: preset.font }"
            @click="setPresetColor(preset)"
          >
            标签
          </el-tag>
        </div>
      </div>
      <div class="mt-1 flex justify-end gap-1.5">
        <el-button size="small" @click="handleCancel"> 取消 </el-button>
        <el-button size="small" type="primary" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </div>
  </el-popover>
</template>
