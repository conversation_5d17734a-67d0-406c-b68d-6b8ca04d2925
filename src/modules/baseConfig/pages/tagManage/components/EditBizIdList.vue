<script lang="ts" setup>
  import { nextTick, ref } from 'vue';
  import type { SelectInstance } from 'element-sun';
  import { queryBizDataByExample } from '@/modules/baseConfig/api/tagManage';

  // Types
  interface Option {
    value: string;
    label: string;
    disabled?: boolean;
  }

  interface Props {
    bizIds: TagManage.BizIdInfo[];
    bizIdTypeCode?: string | undefined;
    disabled: boolean;
  }

  // Props & Emits
  const props = defineProps<Props>();
  const emit = defineEmits<{
    (e: 'add-biz-id', bizId: string, bizName: string): void;
    (e: 'delete-biz-id', bizId: string): void;
  }>();

  // State
  const selectValue = ref('');
  const selectVisible = ref(false);
  const SelectRef = ref<SelectInstance>();
  const options = ref<Option[]>([]);

  // Methods
  const handleClose = (bizId: string) => {
    emit('delete-biz-id', bizId);
  };

  const showSelect = () => {
    selectVisible.value = true;
    nextTick(() => {
      SelectRef.value?.focus();
    });
  };

  const handleSelectConfirm = () => {
    if (!selectValue.value) {
      selectVisible.value = false;
      return;
    }

    const selectedOption = options.value.find(
      (opt) => opt.value === selectValue.value,
    );

    if (selectedOption) {
      emit('add-biz-id', selectedOption.value, selectedOption.label);
    }

    selectValue.value = '';
    selectVisible.value = false;
  };

  const fetchBizDataByExample = async (keyWord = '') => {
    const [, result] = await queryBizDataByExample({
      bizIdTypeCode: props.bizIdTypeCode as string,
      keyWord,
    });

    if (result?.success) {
      options.value = result.data.map((item) => ({
        value: item.bizId,
        label: item.bizName,
        disabled: props.bizIds.some((z) => z.bizId === item.bizId),
      }));
    }
  };
</script>

<template>
  <div
    :class="[
      'flex-1 rounded border border-gray-200 p-4 shadow-sm',
      {
        'cursor-not-allowed opacity-70': props.disabled,
      },
    ]"
  >
    <el-tag
      v-for="item in props.bizIds.filter((item) => item.bizId)"
      :key="item.bizId"
      closable
      :class="[
        'mb-4 mr-4',
        {
          'pointer-events-none': props.disabled,
        },
      ]"
      size="large"
      :disable-transitions="true"
      @close="handleClose(item.bizId)"
    >
      {{ item.bizName }}
    </el-tag>

    <el-select
      v-if="selectVisible"
      ref="SelectRef"
      v-model="selectValue"
      class="mb-4 w-60"
      :remote="true"
      filterable
      :remote-method="fetchBizDataByExample"
      :placeholder="$t('tagManage.range.select.bizId', '请选择')"
      @change="handleSelectConfirm"
      @blur="handleSelectConfirm"
    >
      <el-option
        v-for="item in options"
        :key="item.value"
        :disabled="item.disabled"
        :label="item.label"
        :value="item.value"
      />
    </el-select>

    <el-button
      v-else
      class="mb-4"
      :class="{ 'pointer-events-none': props.disabled }"
      @click="showSelect"
    >
      +
    </el-button>
  </div>
</template>
