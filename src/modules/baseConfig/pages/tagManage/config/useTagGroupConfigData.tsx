import { useColumnConfig, useFormConfig } from 'sun-biz';
import { FLAG } from '@/utils/constant';
import { useRouter } from 'vue-router';
export function useTagGroupFormConfig() {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'tagGroupName',
        label: t('addTagManage.tagGroupName', '标签分组名称'),
        autoConvertSpellNoAndWbNo: true,
        component: 'input',
        placeholder: t('addTagManage.tagGroupName.placeholder', '标签分组名称'),
        rules: [
          {
            required: true,
            message: t('addTagManage.tagGroupName.placeholder', '标签分组名称'),
            trigger: 'change',
          },
        ],
      },
      {
        name: 'tagGroup2ndName',
        label: t('global:secondName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:secondName'),
        }),
      },
      {
        name: 'tagGroupExtName',
        label: t('global:thirdName'),
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:thirdName'),
        }),
      },
      {
        name: 'spellNo',
        component: 'input',
        label: t('global:spellNo'),
        placeholder: t('global:placeholder.input.template', {
          content: t('global:spellNo'),
        }),
      },
      {
        name: 'wbNo',
        component: 'input',
        label: t('global:wbNo'),
        placeholder: t('global:placeholder.input.template', {
          content: t('global:wbNo'),
        }),
      },
      {
        name: 'enabledFlag',
        component: 'switch',
        label: t('addTagManage.enabledFlag', '启用状态'),
        defaultValue: FLAG.YES,
        extraProps: {
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'inline-prompt': true,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
    ],
  });
  return data;
}

export function useTagGroupColumnConfig(
  handleEnableSwitch: (row: TagManage.TagGroup) => void,
  openDialog: (data: { title: string; row: TagManage.TagGroup }) => void,
) {
  const router = useRouter();
  return useColumnConfig({
    getData: (t) => {
      return [
        {
          prop: 'indexNo',
          editable: false,
          type: 'selection',
        },
        {
          label: t('global:sequenceNumber'),
          prop: 'codeSystemNo',
          render: (row: object, index: number) => <>{index + 1}</>,
          minWidth: 70,
        },
        {
          label: t('tagManage.tagGroupId', '标签分组标识'),
          prop: 'tagGroupId',
          supportCopyAndTips: true,
          minWidth: 220,
        },
        {
          label: t('global:name'),
          prop: 'tagGroupNameDisplay',
          supportCopyAndTips: true,
          supportTextCopy: false,
          minWidth: 100,
        },
        {
          label: t('global:secondName'),
          prop: 'tagGroup2ndName',
          supportCopyAndTips: true,
          supportTextCopy: false,
          minWidth: 100,
        },
        {
          label: t('global:thirdName', '扩展名称'),
          prop: 'tagGroupExtName',
          supportCopyAndTips: true,
          supportTextCopy: false,
          minWidth: 100,
        },
        {
          label: t('global:enableStatus'),
          prop: 'enableStatus',
          render: (row: TagManage.TagGroup) => {
            return (
              <el-switch
                modelValue={row.enabledFlag}
                inline-prompt
                active-value={FLAG.YES}
                inactive-value={FLAG.NO}
                before-change={() => handleEnableSwitch(row)}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
              />
            );
          },
          minWidth: 90,
        },
        {
          label: t('global:operation'),
          prop: 'operation',
          minWidth: 140,
          fixed: 'right',
          render: (row: TagManage.TagGroup) => {
            return (
              <>
                <el-button
                  link
                  type="primary"
                  onClick={() => {
                    openDialog({
                      row: { ...row },
                      title: t('useTagGroupConfigData', '编辑分组 “{{name}}”', {
                        name: row.tagGroupNameDisplay,
                      }),
                    });
                  }}
                >
                  {t('global:edit')}
                </el-button>
                <el-button
                  link
                  type="primary"
                  onClick={() => {
                    router.push(
                      `/range/${row.tagGroupId}?name=${row.tagGroupNameDisplay}`,
                    );
                  }}
                >
                  {t('tagGroup.configData.table.range.title', '范围')}
                </el-button>
              </>
            );
          },
        },
      ];
    },
  });
}
