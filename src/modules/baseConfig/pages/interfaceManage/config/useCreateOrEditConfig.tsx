import { Ref } from 'vue';
import {
  useColumnConfig,
  useFormConfig,
  useEditableTable,
  TableRef,
} from 'sun-biz';
import {
  INVOKE_TYPE_CODE_NAME,
  INVOKE_TYPE_CODE,
  FLAG,
  INTERFACE_TYPE_CODE_NAME,
} from '@/utils/constant';
import { dayjs } from '@sun-toolkit/shared';

export function useTableColumnConfig(
  tableRef: Ref<TableRef>,
  data: Ref<(Interface.HospitalInfo & { editable: boolean })[]>,
  hospitalList: Ref<InterfaceManage.OrganizationInfo[]>,
) {
  const { toggleEdit, cancelEdit, addItem } = useEditableTable({
    tableRef,
    data,
    id: 'interfaceXHospitalId',
  });

  const tableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:hospital', '医院'),
        prop: 'hospitalId',
        required: true,
        editable: true,
        minWidth: 150,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('global:hospital', '医院'),
            }),
            trigger: 'change',
          },
        ],
        render: (
          row: InterfaceManage.HospitalInfo & { editable?: boolean },
        ) => {
          const findObj = (hospitalList.value || []).find(
            (item) => item.orgId === row.hospitalId,
          );
          return row.editable && !row.interfaceXHospitalId ? (
            <el-select
              v-model={row.hospitalId}
              filterable={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('global:hospital', '医院'),
              })}
            >
              {(hospitalList.value || [])?.map((item) => {
                const findObj = data.value.find(
                  (cur) => item.orgId === cur?.hospitalId,
                );
                return (
                  <el-option
                    key={item.orgId}
                    disabled={findObj}
                    label={item.orgName}
                    value={item.orgId}
                  />
                );
              })}
            </el-select>
          ) : (
            <div class="w-full text-center">{findObj?.orgName}</div>
          );
        },
      },
      {
        label: t('createOrEditConfig.table.beginDate', '开始日期'),
        prop: 'beginDate',
        minWidth: 150,
        required: true,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('createOrEditConfig.table.beginDate', '开始日期'),
            }),
            trigger: 'change',
          },
        ],
        render: (
          row: Interface.HospitalInfo & {
            editable: boolean;
          },
        ) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-date-picker
                  v-model={row.beginDate}
                  type="datetime"
                  placeholder={t('global:placeholder.select.template', {
                    name: t('createOrEditConfig.table.beginDate', '开始日期'),
                  })}
                  default-value={new Date()}
                  clearable={false}
                  format={'YYYY-MM-DD HH:mm:ss'}
                  value-format={'YYYY-MM-DD HH:mm:ss'}
                  disabled-date={(time: Date) => {
                    return (
                      row.endDate &&
                      dayjs(time).isAfter(dayjs(row.endDate), 'second')
                    );
                  }}
                />
              ) : (
                <>{row.beginDate}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('createOrEditConfig.table.endDate', '结束日期'),
        required: true,
        editable: true,
        prop: 'endDate',
        minWidth: 150,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('createOrEditConfig.table.endDate', '结束日期'),
            }),
            trigger: 'change',
          },
        ],
        render: (
          row: Interface.HospitalInfo & {
            editable: boolean;
          },
        ) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-date-picker
                  v-model={row.endDate}
                  type="datetime"
                  placeholder={t('global:placeholder.select.template', {
                    name: t('createOrEditConfig.table.endDate', '结束日期'),
                  })}
                  default-value={new Date()}
                  clearable={false}
                  format={'YYYY-MM-DD HH:mm:ss'}
                  value-format={'YYYY-MM-DD HH:mm:ss'}
                  disabled-date={(time: Date) => {
                    return (
                      row.beginDate &&
                      dayjs(time).isBefore(dayjs(row.beginDate), 'second')
                    );
                  }}
                />
              ) : (
                <>{row.endDate}</>
              )}
            </div>
          );
        },
      },

      {
        label: t('global:operation'),
        prop: 'interfaceId',
        width: 130,
        render: (
          row: InterfaceManage.HospitalInfo & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return (
            <>
              {row.editable ? (
                <div class={'flex justify-around'} key="editable">
                  <el-button
                    type="danger"
                    link={true}
                    onClick={() => cancelEdit(row, $index, false)}
                  >
                    {t('global:cancel')}
                  </el-button>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => toggleEdit(row)}
                  >
                    {t('global:confirm')}
                  </el-button>
                </div>
              ) : (
                <div class={'flex justify-around'}>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => toggleEdit(row)}
                  >
                    {t('global:edit')}
                  </el-button>
                  {/* <el-button
                    type="danger"
                    link={true}
                    onClick={() => delItem($index)}
                  >
                    {t('global:delete')}
                  </el-button> */}
                </div>
              )}
            </>
          );
        },
      },
    ],
  });
  return {
    tableConfig,
    addItem,
  };
}

export function useFromConfigData(
  formModel: Ref<
    Partial<InterfaceManage.AddInterface> | undefined,
    Partial<InterfaceManage.AddInterface> | undefined
  >,
) {
  const data = useFormConfig({
    dataSetCodes: [INVOKE_TYPE_CODE_NAME, INTERFACE_TYPE_CODE_NAME],
    getData: (t, dataSet) => [
      {
        label: t('createOrEditConfig.form.interfaceName', '接口名称'),
        name: 'interfaceName',
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('createOrEditConfig.interfaceName', '接口名称'),
            }),
            trigger: 'change',
          },
        ],
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('createOrEditConfig.interfaceName', '接口名称'),
        }),
        extraProps: {
          maxlength: 64,
        },
      },
      {
        label: '接口类型',
        name: 'interfaceTypeCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('createOrEditConfig.interfaceTypeCode', '接口类型'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('createOrEditConfig.interfaceTypeCode', '接口类型'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          options: dataSet?.value
            ? dataSet.value?.[INTERFACE_TYPE_CODE_NAME]
            : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
      },
      {
        label: t('createOrEditConfig.invokeTypeCode', '交互方式'),
        name: 'invokeTypeCode',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('createOrEditConfig.invokeTypeCode', '交互方式'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('createOrEditConfig.invokeTypeCode', '交互方式'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          options: dataSet?.value ? dataSet.value?.[INVOKE_TYPE_CODE_NAME] : [],
          props: {
            valueKey: 'dataValueNo',
            label: 'dataValueNameDisplay',
          },
        },
        triggerModelChange: true,
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': FLAG.YES,
          'inactive-value': FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
      {
        label: t('createOrEditConfig.url', 'URL地址'),
        name: 'url',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('createOrEditConfig.url', 'URL地址'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('createOrEditConfig.url', 'URL地址'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          maxlength: 256,
        },
        isHidden: formModel.value?.['invokeTypeCode'] !== INVOKE_TYPE_CODE.HTTP,
      },
      {
        label: t('createOrEditConfig.builtInFlag', '内置接口'),
        name: 'builtInFlag',
        component: 'checkbox',
        extraProps: {
          trueLabel: 1,
          falseLabel: 0,
        },
      },
      {
        label: t('createOrEditConfig.dllName', '程序集'),
        name: 'dllName',
        component: 'input',
        isHidden: formModel.value?.['invokeTypeCode'] === INVOKE_TYPE_CODE.HTTP,
        placeholder: t('global:placeholder.input.template', {
          content: t('createOrEditConfig.dllName', '程序集'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('createOrEditConfig.dllName', '程序集'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          maxlength: 64,
          suffixIcon: <>.dll</>,
        },
      },
      {
        label: t('createOrEditConfig.namespace', '命名空间'),
        name: 'namespace',
        component: 'input',
        isHidden: formModel.value?.['invokeTypeCode'] === INVOKE_TYPE_CODE.HTTP,
        placeholder: t('global:placeholder.input.template', {
          content: t('createOrEditConfig.namespace', '命名空间'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('createOrEditConfig.namespace', '命名空间'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          maxlength: 64,
        },
      },
      {
        label: t('createOrEditConfig.className', '类名'),
        name: 'className',
        component: 'input',
        isHidden: formModel.value?.['invokeTypeCode'] === INVOKE_TYPE_CODE.HTTP,
        placeholder: t('global:placeholder.input.template', {
          content: t('createOrEditConfig.className', '类名'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('createOrEditConfig.className', '类名'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          maxlength: 64,
        },
      },
      {
        label: t('createOrEditConfig.methodName', '方法名'),
        name: 'methodName',
        component: 'input',
        isHidden: formModel.value?.['invokeTypeCode'] === INVOKE_TYPE_CODE.HTTP,
        placeholder: t('global:placeholder.input.template', {
          content: t('createOrEditConfig.methodName', '方法名'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('createOrEditConfig.methodName', '方法名'),
            }),
            trigger: 'change',
          },
        ],
        extraProps: {
          maxlength: 64,
        },
      },
    ],
  });
  return data;
}
