import { useColumnConfig } from 'sun-biz';
import { ENABLED_FLAG } from '@sun-toolkit/enums';

export function useTableColumnConfig(
  isCloudEnv: boolean | undefined,
  openCreateDialog: (data: {
    title: string;
    row: Interface.InterfaceInfo;
  }) => void,
  openViewDialog: (data: {
    title: string;
    row: Interface.InterfaceInfo;
  }) => void,
  go2interfaceConfig: (row: Interface.InterfaceInfo) => void,
  openTransactionDialog: (data: {
    title: string;
    row: Interface.InterfaceInfo;
  }) => void,
  handleEnableSwitch: (row: InterfaceManage.InterfaceInfo) => Promise<void>,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo1',
        type: 'selection',
      },
      {
        label: t('global:sequence'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: ReasonManage.ReasonInfo, $index: number) => (
          <>{$index + 1}</>
        ),
      },
      {
        label: t('InterfaceManage.table.interfaceId', '接口标识'),
        prop: 'interfaceId',
        supportCopyAndTips: true,
        minWidth: 170,
      },
      {
        label: t('InterfaceManage.table.interfaceName', '接口名称'),
        prop: 'interfaceName',
        minWidth: 170,
      },
      {
        label: t('InterfaceManage.table.interfaceTypeDesc', '接口类型'),
        prop: 'interfaceTypeDesc',
        minWidth: 170,
      },
      {
        label: t('InterfaceManage.table.invokeTypeDesc', '交互方式'),
        prop: 'invokeTypeDesc',
        minWidth: 170,
      },
      {
        label: t('reasonManage.table.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: InterfaceManage.InterfaceInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              disabled={!isCloudEnv}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 280,
        fixed: 'right',
        render: (row: InterfaceManage.InterfaceInfo) => {
          return (
            <div class="flex justify-around" key="editable">
              <el-button
                onClick={() => {
                  openViewDialog({
                    title: t(
                      'InterfaceManage.view.interfaceDetail',
                      '查看 {{name}}',
                      {
                        name: row.interfaceName,
                      },
                    ),
                    row: { ...row },
                  });
                }}
                link={true}
                type="primary"
              >
                {t('global:detail', '详情')}
              </el-button>
              <el-button
                onClick={() => {
                  openCreateDialog({
                    title: t(
                      'InterfaceManage.editDialog.title',
                      '编辑 {{name}}',
                      {
                        name: row.interfaceName,
                      },
                    ),
                    row: { ...row },
                  });
                }}
                disabled={!isCloudEnv}
                link={true}
                type="primary"
              >
                {t('global:edit')}
              </el-button>
              <el-button
                onClick={() => {
                  go2interfaceConfig({ ...row, title: row.interfaceName });
                }}
                link={true}
                type="primary"
              >
                {t('InterfaceManage.table.interfaceConfig', '接口配置')}
              </el-button>
              <el-button
                onClick={() => {
                  openTransactionDialog({
                    title: t(
                      'InterfaceManage.edit.interfaceTransaction',
                      '编辑 “{{name}}” 的接口交易',
                      {
                        name: row.interfaceName,
                      },
                    ),
                    row: { ...row },
                  });
                }}
                link={true}
                type="primary"
              >
                {t('InterfaceManage.table.interfaceTransaction', '接口交易')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
}
