import { Ref, ComputedRef } from 'vue';
import { COMMUNICATE_TYPE_CODE_NAME } from '@/utils/constant';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';

export function useBizEventConfig(options: {
  tableRef: Ref<TableRef>;
  bizEventList: Ref<BizEvent.BizEventInfo[]>;
  queryDictBizEventList: (params: BizEvent.QueryParams) => Promise<void>;
  data: Ref<InterfaceManage.TranXBizEventReqItem[]>;
  hospitalId: ComputedRef<string | undefined>;
}) {
  const { tableRef, data, bizEventList, queryDictBizEventList, hospitalId } =
    options;
  const { toggleEdit, cancelEdit, addItem, delItem } = useEditableTable({
    tableRef,
    data: data as Ref<
      (InterfaceManage.TranXBizEventReqItem & { editable: boolean })[]
    >,
    id: 'tranXBizEventId',
  });

  const tableConfig = useColumnConfig({
    dataSetCodes: [COMMUNICATE_TYPE_CODE_NAME],
    getData: (t, dataCodes) => [
      {
        label: t('bizEvent.bizEventId', '业务事件'),
        prop: 'bizEventId',
        required: true,
        editable: true,
        minWidth: 150,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('bizEvent.bizEventId', '业务事件'),
            }),
            trigger: 'change',
          },
        ],
        render: (
          row: InterfaceManage.TranXBizEventReqItem & {
            editable?: boolean;
          },
        ) => {
          return row.editable && !row.tranXBizEventId ? (
            <el-select
              v-model={row.bizEventId}
              remote={true}
              filterable={true}
              remote-show-suffix={true}
              remote-method={async (keyWord: string) => {
                await queryDictBizEventList({
                  keyWord: keyWord,
                  hospitalId: hospitalId.value as string,
                });
              }}
              onChange={(val: string) => {
                const findObj = bizEventList.value.find(
                  (item) => item.bizEventId === val,
                );
                row.bizEventName = findObj?.bizEventName || '';
              }}
              placeholder={t('global:placeholder.select.template', {
                name: t('bizEvent.bizEventId', '业务事件'),
              })}
            >
              {((bizEventList.value ?? []).length > 0
                ? bizEventList.value
                : [
                    {
                      bizEventId: row.bizEventId,
                      bizEventName: row.bizEventName,
                    },
                  ]
              ).map((item) => {
                const findObj = (data.value ?? [])?.find(
                  (im) => im.bizEventId === item.bizEventId,
                );
                return (
                  <el-option
                    disabled={!!findObj?.bizEventId}
                    key={item.bizEventId}
                    label={item.bizEventName}
                    value={item.bizEventId}
                  ></el-option>
                );
              })}
            </el-select>
          ) : (
            <>{row.bizEventName}</>
          );
        },
      },
      {
        label: t('bizEvent.communicateTypeCode', '通讯模式'),
        prop: 'communicateTypeCode',
        required: true,
        editable: true,
        minWidth: 150,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('bizEvent.communicateTypeCode', '通讯模式'),
            }),
            trigger: 'change',
          },
        ],
        render: (
          row: InterfaceManage.TranXBizEventReqItem & {
            editable?: boolean;
          },
        ) => {
          return row.editable ? (
            <el-select
              v-model={row.communicateTypeCode}
              filterable={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('bizEvent.communicateTypeCode', '通讯模式'),
              })}
              onChange={(val: string) => {
                const findObj = (
                  dataCodes?.value
                    ? dataCodes.value[COMMUNICATE_TYPE_CODE_NAME]
                    : []
                )?.find((item) => item.dataValueNo === val);
                row.communicateTypeDesc = findObj?.dataValueNameDisplay || '';
              }}
            >
              {(dataCodes?.value
                ? dataCodes.value[COMMUNICATE_TYPE_CODE_NAME]
                : []
              ).map((item) => {
                return (
                  <el-option
                    key={item.dataValueNo}
                    label={item.dataValueNameDisplay}
                    value={item.dataValueNo}
                  ></el-option>
                );
              })}
            </el-select>
          ) : (
            <>{row.communicateTypeDesc}</>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'interfaceId',
        minWidth: 150,
        render: (
          row: InterfaceManage.TranXBizEventReqItem & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return (
            <>
              <div class="flex justify-around">
                {row.editable ? (
                  <>
                    <el-button
                      key="cancel"
                      type="danger"
                      link={true}
                      onClick={() => cancelEdit(row, $index, false)}
                    >
                      {t('global:cancel')}
                    </el-button>
                    <el-button
                      key="confirm"
                      type="primary"
                      link={true}
                      onClick={() => toggleEdit(row)}
                    >
                      {t('global:confirm')}
                    </el-button>
                  </>
                ) : (
                  <>
                    <el-button
                      key="edit"
                      type="primary"
                      link={true}
                      onClick={() => toggleEdit(row)}
                    >
                      {t('global:edit')}
                    </el-button>
                    <el-button
                      key="remove"
                      type={'danger'}
                      link={true}
                      onClick={() => delItem($index)}
                    >
                      {t('global:remove')}
                    </el-button>
                  </>
                )}
              </div>
            </>
          );
        },
      },
    ],
  });
  return {
    tableConfig,
    addItem,
  };
}
