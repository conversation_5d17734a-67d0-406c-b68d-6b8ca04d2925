import { ComputedRef, ref, Ref } from 'vue';
import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import { ENABLED_FLAG } from '@sun-toolkit/enums';
import { CodeSystem } from '@/typings/codeManage';
import BizEvent from '../components/bizEvent.vue';

export function useTableColumnConfig(
  tableRef: Ref<TableRef>,
  data: Ref<
    (InterfaceManage.InterfaceTransactionInfo & { editable: boolean })[]
  >,
  transactionCodes: ComputedRef<CodeSystem[]>,
  queryFormList: (keyWord: string) => Promise<void>,
  formList: Ref<FormDesign.FormInfo[]>,
  handleSwitch: (
    row: InterfaceManage.InterfaceTransactionInfo,
  ) => Promise<void>,
) {
  const { toggleEdit, cancelEdit, addItem, delItem } = useEditableTable({
    tableRef,
    data,
    id: 'transactionId',
  });
  const queryValue = ref('');

  const tableConfig = useColumnConfig({
    getData: (t) => [
      {
        type: 'expand',
        render: (row: InterfaceManage.InterfaceTransactionInfo) => {
          return (
            <div class={'px-7 py-5'}>
              <BizEvent rowValue={row} />
            </div>
          );
        },
      },
      {
        label: t('global:sequenceNumber', '序号'),
        prop: 'indexNo',
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('InterfaceTransactionInfo.transactionCode', '交易代码'),
        prop: 'transactionCode',
        required: true,
        editable: true,
        minWidth: 150,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('InterfaceTransactionInfo.transactionCode', '交易代码'),
            }),
            trigger: 'change',
          },
        ],
        render: (
          row: InterfaceManage.InterfaceTransactionInfo & {
            editable?: boolean;
          },
        ) => {
          return row.editable && !row.transactionId ? (
            <el-select
              v-model={row.transactionCode}
              onChange={(val: string) => {
                const findObj = transactionCodes.value.find(
                  (item) => item.dataValueNo === val,
                );
                row.transactionDesc = findObj?.dataValueNameDisplay || '';
              }}
              placeholder={t('global:placeholder.select.template', {
                name: t('InterfaceTransactionInfo.transactionCode', '交易代码'),
              })}
              filterable={true}
              filter-method={(value: string) => {
                queryValue.value = value;
              }}
            >
              {transactionCodes.value
                .filter((cur) => {
                  if (row.editable) {
                    if (!queryValue.value) return true; // 如果没有输入查询条件，则显示所有选项
                    const valueMatch = cur.dataValueNo
                      .toString()
                      .toLowerCase()
                      .includes(queryValue.value.toLowerCase());
                    const labelMatch = (cur.dataValueNameDisplay || '')
                      .toString()
                      .toLowerCase()
                      .includes(queryValue.value.toLowerCase());
                    return valueMatch || labelMatch;
                  }
                  return true;
                })
                .map((item) => {
                  const findObj = data.value.find(
                    (cur) => cur.transactionCode === item.dataValueNo,
                  );
                  return (
                    <el-option
                      key={item.dataValueNo}
                      disabled={findObj !== undefined}
                      data-item={item}
                      label={`${item.dataValueNameDisplay}`}
                      value={item.dataValueNo}
                    ></el-option>
                  );
                })}
            </el-select>
          ) : (
            <>{row.transactionCode}</>
          );
        },
      },
      {
        label: t('InterfaceTransactionInfo.transactionDesc', '交易描述'),
        prop: 'transactionDesc',
        editable: true,
        minWidth: 150,
        render: (
          row: InterfaceManage.InterfaceTransactionInfo & {
            editable?: boolean;
          },
        ) => {
          return row.editable && !row.transactionId ? (
            ''
          ) : (
            <>{row.transactionDesc}</>
          );
        },
      },
      {
        label: t('InterfaceTransactionInfo.formId', '选择项表单'),
        prop: 'formId',
        editable: true,
        minWidth: 150,
        render: (
          row: InterfaceManage.InterfaceTransactionInfo & {
            editable?: boolean;
          },
        ) => {
          return row.editable ? (
            <el-select
              v-model={row.formId}
              remote={true}
              clearable={true}
              filterable={true}
              remote-show-suffix={true}
              remote-method={async (keyWord: string) => {
                await queryFormList(keyWord);
              }}
              onChange={(val: string) => {
                const formObj = formList.value?.find(
                  (item) => item.formId === val,
                );
                row.formName = formObj?.formName ?? undefined;
              }}
              placeholder={t('global:placeholder.select.template', {
                name: t('InterfaceTransactionInfo.formId', '选择项表单'),
              })}
            >
              {(formList.value.length > 0
                ? formList.value
                : [
                    {
                      formId: row.formId,
                      formName: row.formName,
                    },
                  ]
              )?.map((item) => {
                return (
                  <el-option
                    value={item.formId}
                    label={item.formName}
                    key={item.formId}
                  ></el-option>
                );
              })}
            </el-select>
          ) : (
            <>{row.formName}</>
          );
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        render: (
          row: InterfaceManage.InterfaceTransactionInfo & {
            editable?: boolean;
          },
        ) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              disabled={!row.editable}
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              onChange={() => handleSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        minWidth: 200,
        render: (
          row: InterfaceManage.InterfaceTransactionInfo & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return (
            <>
              <div class="flex justify-around">
                {row.editable ? (
                  <>
                    <el-button
                      key="cancel"
                      type="danger"
                      link={true}
                      onClick={() => cancelEdit(row, $index, false)}
                    >
                      {t('global:cancel')}
                    </el-button>
                    <el-button
                      key="confirm"
                      type="primary"
                      link={true}
                      onClick={() => toggleEdit(row)}
                    >
                      {t('global:confirm')}
                    </el-button>
                  </>
                ) : (
                  <div key={row.transactionId}>
                    <el-button
                      key="edit"
                      type="primary"
                      link={true}
                      onClick={() => toggleEdit(row)}
                    >
                      {t('global:edit')}
                    </el-button>
                    <el-button
                      key="remove"
                      type={row.transactionId ? '' : 'danger'}
                      link={true}
                      disabled={!!row.transactionId}
                      onClick={() => delItem($index)}
                    >
                      {t('global:remove')}
                    </el-button>
                  </div>
                )}
                <el-button
                  key="eventListening"
                  type="primary"
                  link={true}
                  onClick={() => {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    (tableRef.value as any)?.proTableRef.toggleRowExpansion(
                      row,
                    );
                  }}
                >
                  {t('eventListening', '事件监听')}
                </el-button>
              </div>
            </>
          );
        },
      },
    ],
  });
  return {
    tableConfig,
    addItem,
  };
}
