<script setup lang="ts">
  import { ref, computed, useAttrs } from 'vue';
  import { ProTable, ProDialog } from 'sun-biz';
  import { useTranslation } from 'i18next-vue';

  const { t } = useTranslation();
  type Props = {
    transactions?: InterfaceManage.InterfaceTransactionInfo[];
  };
  const props = defineProps<Props>();
  const emits = defineEmits(['success']);
  const dialogRef = ref();
  const tableRef = ref();
  const attrs = useAttrs();
  const selections = ref<InterfaceManage.InterfaceTransactionInfo[]>([]);

  /**
   * 点击确认后
   */
  function submit() {
    return new Promise<[never, unknown]>((resolve) => {
      emits('success', selections.value);
      resolve([null, selections.value] as unknown as [never, unknown]);
    });
  }

  function getTableColumns() {
    return [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t('InterfaceTransactionInfo.transactionCode', '交易代码'),
        prop: 'transactionCode',
        minWidth: 150,
      },
      {
        label: t('InterfaceTransactionInfo.transactionDesc', '交易描述'),
        prop: 'transactionDesc',
        minWidth: 150,
      },
    ];
  }

  const handleSelectionChange = (value: BizEvent.BizEventInfo[]) => {
    selections.value = value;
  };

  const tableConfig = computed(() => getTableColumns());

  defineExpose({
    dialogRef,
    tableRef,
  });
</script>

<template>
  <ProDialog
    :confirm-fn="submit"
    :width="800"
    ref="dialogRef"
    :title="attrs.title"
    :link="attrs.link"
    :button-text="attrs['button-text']"
    destroy-on-close
  >
    <ProTable
      :max-height="242"
      ref="tableRef"
      :default-selected="props.transactions"
      @selection-change="handleSelectionChange"
      row-key="interfaceTypeCodeTranId"
      :columns="tableConfig"
      :data="props.transactions"
    />
  </ProDialog>
</template>
