<script lang="ts" setup>
  import { computed, nextTick, onMounted, ref } from 'vue';
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProTable,
    Title,
    useAppConfigData,
  } from 'sun-biz';
  import { Search } from '@element-sun/icons-vue';
  import { ElMessage } from 'element-sun';
  import {
    queryInterfaceSettingListByKeys,
    saveInterfaceSetting,
  } from '../../../api/interfaceManage';
  import { debounce } from '@sun-toolkit/shared';
  import { useTableColumnConfig } from '../config/useInterfaceTableConfig';
  import { useTranslation } from 'i18next-vue';
  import { useRoute, useRouter } from 'vue-router';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant.ts';

  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const { t } = useTranslation();

  const route = useRoute();
  const router = useRouter();
  const interfaceId = ref('');
  const interfaceName = ref('');
  const tableRef = ref();
  const keyWord = ref<string>('');
  const filterValue = ref<string>('');
  const loading = ref<boolean>(false);
  const tableData = ref<
    (InterfaceManage.InterfaceConfigInfo & { editable: boolean })[]
  >([]);
  const handleEdit = (row: InterfaceManage.InterfaceConfigInfo) => {
    if (!canUpsertTableRow()) return;
    toggleEdit(row);
  };
  const selectTableData = ref<InterfaceManage.InterfaceConfigInfo[]>([]);

  let inputChange = debounce((value) => {
    filterValue.value = value;
  }, 500);
  const { tableConfig, addItem, toggleEdit } = useTableColumnConfig(
    tableRef,
    tableData,
    isCloudEnv,
    handleEdit,
  );

  const fetchDataList = async (interfaceId: string | undefined) => {
    if (!interfaceId) return;
    loading.value = true;
    let [, result] = await queryInterfaceSettingListByKeys({ interfaceId });
    loading.value = false;
    if (result?.success) {
      tableData.value = result.data;
    }
  };
  // 选中行设置
  const selectionChange = (val: InterfaceManage.InterfaceConfigInfo[]) => {
    selectTableData.value = val;
  };
  const bizData = computed(() => {
    const list = selectTableData.value.map((item) => {
      return item.interfaceSettingId;
    });
    return list ?? [];
  });
  /**
   * 点击确认后
   */
  const submit = () => {
    if (showData.value.length === 0) {
      ElMessage({
        type: 'error',
        message: t('interfaceConfig.config.error.empty', '请先添加配置'),
      });
      return false;
    }
    // eslint-disable-next-line no-async-promise-executor
    return new Promise<[never, unknown]>(async (resolve, reject) => {
      try {
        await tableRef?.value?.formRef?.validate();
        let [, result] = await saveInterfaceSetting({
          interfaceId: interfaceId.value || '',
          interfaceSettingList: tableData.value.map(
            ({
              interfaceSettingId,
              configKey,
              configDesc,
              configDefaultValue,
              configValue,
            }) => ({
              interfaceSettingId,
              configKey,
              configDesc,
              configDefaultValue,
              configValue,
            }),
          ),
        });
        if (result?.success) {
          fetchDataList(interfaceId.value);

          resolve([] as unknown as [never, unknown]);
        } else {
          reject(['', new Error('接口错误')]);
        }
      } catch (error) {
        console.log(error);
        reject(['', new Error('参数错误')]);
      }
    });
  };

  const addClick = () => {
    if (!canUpsertTableRow()) return;
    keyWord.value = '';
    filterValue.value = '';
    addItem({
      editable: true,
    });
    nextTick(() => {
      const row = tableRef.value?.proTableRef?.$el?.querySelector(
        `.el-table__body-wrapper tbody tr:nth-child(${tableData.value.length - 1})`,
      );
      row?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });
  };
  const canUpsertTableRow = () => {
    const isEditing = showData.value.some((item) => !!item.editable);
    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    } else {
      return true;
    }
  };

  const showData = computed(() => {
    return tableData.value.filter((item) => {
      return (
        (item.configValue || '').includes(filterValue.value) ||
        (item.configDesc || '').includes(filterValue.value) ||
        (item.configKey || '').includes(filterValue.value)
      );
    });
  });
  onMounted(() => {
    interfaceId.value = route.params.interfaceId;
    interfaceName.value = route.query.interfaceName;
    console.log(interfaceId.value);
    fetchDataList(interfaceId.value);
  });
  const goBack = () => {
    router.push({ name: 'interfaceManage' });
  };
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title
      :title="$t('interfaceConfig.config.list', `${interfaceName}配置列表`)"
      class="mb-4"
    />
    <div class="mt-3 flex justify-between">
      <div class="mb-4 flex justify-start">
        <el-input
          v-model="keyWord"
          :placeholder="t('global:placeholder.keyword')"
          :suffix-icon="Search"
          class="mr-5 w-72"
          clearable
          @input="inputChange"
        />
        <el-button type="primary" @click="fetchDataList(interfaceId)"
          >{{ $t('global:search') }}
        </el-button>
        <el-button
          :disabled="!isCloudEnv"
          class="mr-3"
          type="primary"
          @click="addClick"
          >{{ $t('global:add') }}
        </el-button>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_INTERFACE_SETTING"
          @success="
            () => {
              tableRef?.proTableRef.clearSelection();
              selectTableData = [];
            }
          "
        />
      </div>
      <div class="mb-4 flex justify-end">
        <el-button :disabled="!isCloudEnv" type="primary" @click="submit"
          >{{ $t('global:save') }}
        </el-button>
        <el-button plain type="primary" @click="goBack"
          >{{ $t('global:cancel') }}
        </el-button>
      </div>
    </div>
    <ProTable
      ref="tableRef"
      :columns="tableConfig"
      :data="showData"
      :editable="true"
      :loading="loading"
      row-key="interfaceSettingId"
      @selection-change="selectionChange"
    />
  </div>
</template>
