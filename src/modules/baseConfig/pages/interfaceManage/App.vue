<script lang="ts" name="reasonManage" setup>
  import { computed, nextTick, onMounted, reactive, ref } from 'vue';
  import {
    queryFlatOrgList,
    queryInterfaceListByExample,
    updateInterfaceEnabledFlagById,
  } from '@modules/baseConfig/api/interfaceManage';
  import { BIZ_ID_TYPE_CODE, FLAG, ORG_TYPE_CODE } from '@/utils/constant';
  import { useTableColumnConfig } from './config/useTableColumnConfig.tsx';
  import { useSearchBarConfig } from './config/useSearchBarConfig.tsx';
  import AddOrEditInterface from './components/AddOrEditInterface.vue';
  import ViewInterfaceDetail from './components/ViewInterfaceDetail.vue';
  import InterfaceTransaction from './components/InterfaceTransaction.vue';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { ENABLED_FLAG } from '@sun-toolkit/enums';
  import { useTranslation } from 'i18next-vue';
  import { useRouter } from 'vue-router';

  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    Title,
    useAppConfigData,
  } from 'sun-biz';

  const { t } = useTranslation();
  const router = useRouter();

  //isCloudEnv，true指云端，false其他是用户端（暂定）
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);
  const searchModel = ref<InterfaceManage.ReqQueryInterfaceListByExample>({
    keyWord: '',
    enabledFlag: FLAG.ALL,
  });
  const tableRef = ref();

  const detailInfo = reactive<{
    title?: string;
    row?: InterfaceManage.InterfaceInfo;
  }>({});
  const editInfo = reactive<{
    title?: string;
    row?: InterfaceManage.InterfaceInfo;
  }>({});
  const transactionInfo = reactive<{
    title?: string;
    row?: InterfaceManage.InterfaceInfo;
  }>({});
  const addOrEditInterfaceRef = ref();
  const viewInterfaceDetailRef = ref();
  const interfaceTransactionRef = ref();
  // const interfaceConfigRef = ref();
  const interfaceList = ref<Interface.InterfaceInfo[]>([]);
  const loading = ref(false);
  const hospitalList = ref<InterfaceManage.OrganizationInfo[]>([]);
  const checkedKeys = ref<Interface.InterfaceInfo[]>([]);

  async function queryInterfaceList(params = { pageNumber: 1, pageSize: 10 }) {
    const [, result] = await queryInterfaceListByExample({
      pageNumber: params.pageNumber,
      pageSize: params.pageSize,
      ...searchModel.value,
      enabledFlag:
        searchModel.value?.enabledFlag === FLAG.ALL
          ? undefined
          : searchModel.value.enabledFlag,
    });
    if (result?.success) {
      return {
        data: result.data,
        total: result.total,
      };
    }
  }

  function handleSelectChange(value: Interface.InterfaceInfo[]) {
    checkedKeys.value = value;
  }

  onMounted(() => {
    fetchOrgList();
  });

  async function fetchOrgList() {
    const [, result] = await queryFlatOrgList({
      enabledFlag: FLAG.YES,
      orgTypeCodes: [ORG_TYPE_CODE.HOSPITAL],
    });
    if (result.success) {
      hospitalList.value = result.data;
    }
  }

  /**
   * 打开配置新页面
   */
  const go2interfaceConfig = (row?: Partial<Interface.InterfaceInfo>) => {
    router.push({
      name: 'interfaceConfigDetail',
      params: {
        interfaceId: row?.interfaceId,
      },
      query: {
        interfaceName: row?.interfaceName,
      },
    });
  };

  const searchConfig = useSearchBarConfig(hospitalList, fetchTableList);
  const tableColumns = useTableColumnConfig(
    isCloudEnv,
    openCreateDialog,
    openViewDialog,
    go2interfaceConfig,
    openTransactionDialog,
    handleEnableSwitch,
  );

  /** 启用状态切换 */
  async function handleEnableSwitch(row: InterfaceManage.InterfaceInfo) {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.interfaceName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        ...row,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      const [, res] = await updateInterfaceEnabledFlagById(params);
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        fetchTableList();
      }
    });
  }

  /**
   * 查看接口详情
   * @param row
   */
  function openViewDialog(data: {
    title: string;
    row: Partial<Interface.InterfaceInfo>;
  }) {
    Object.assign(detailInfo, data);
    viewInterfaceDetailRef.value.openDialog();
  }

  /**
   * 打开新增接口弹窗
   * @param row
   */
  function openCreateDialog(data: {
    title?: string;
    row?: Partial<Interface.InterfaceInfo>;
  }) {
    Object.assign(editInfo, data);
    addOrEditInterfaceRef.value.dialogRef.open();
  }

  /**
   * 打开接口交易弹窗
   * @param row
   */
  function openTransactionDialog(data: {
    title?: string;
    row?: Partial<Interface.InterfaceInfo>;
  }) {
    Object.assign(transactionInfo, data);
    interfaceTransactionRef.value.dialogRef.open();
  }

  const bizData = computed(() => {
    return checkedKeys.value.map((item) => {
      return item.interfaceId || '';
    });
  });

  function fetchTableList(init = false) {
    nextTick(() => {
      //兼容proform model-change 修改后modelValue值不是最新的
      tableRef.value.fetchList(init);
    });
  }
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('interfaceManage.list.title', '接口列表')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item mr-5">
        <ProForm
          v-model="searchModel"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="
            () => {
              fetchTableList(true);
            }
          "
        />
      </div>
      <div>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_INTERFACE"
          class="mr-4"
          @success="
            () => {
              tableRef?.proTableRef.clearSelection();
              checkedKeys = [];
            }
          "
        />
        <el-button
          :disabled="!isCloudEnv"
          type="primary"
          @click="
            () => {
              openCreateDialog({
                row: {},
                title: $t('interfaceManage.addTitle', '新增接口'),
              });
            }
          "
        >
          {{ $t('global:add') }}
        </el-button>
      </div>
    </div>
    <pro-table
      ref="tableRef"
      :columns="tableColumns"
      :data="interfaceList"
      :fetch-data="queryInterfaceList"
      :loading="loading"
      :pagination="true"
      row-key="interfaceId"
      @selection-change="handleSelectChange"
    />
  </div>
  <ViewInterfaceDetail ref="viewInterfaceDetailRef" v-bind="detailInfo" />
  <AddOrEditInterface
    ref="addOrEditInterfaceRef"
    :hospitals="hospitalList"
    v-bind="editInfo"
    @success="
      () => {
        fetchTableList();
      }
    "
  />
  <!--  <InterfaceConfig ref="interfaceConfigRef" v-bind="configInfo" />-->
  <InterfaceTransaction
    ref="interfaceTransactionRef"
    v-bind="transactionInfo"
  />
</template>
