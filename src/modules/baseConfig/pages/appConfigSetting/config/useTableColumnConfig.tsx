import { useColumnConfig } from 'sun-biz';
import NodeTable from '@/modules/baseConfig/pages/appConfigSetting/components/nodeTable.vue';

export function useAppConfigSettingTableColumnConfig(options: {
  openAddNodeDialog: (
    mode: string,
    data?: AppConfigSetting.AppConfigList,
  ) => void;
  deleteRow: (row: AppConfigSetting.AppConfigList, index: number) => void;
  openAddNodeListDialog: (
    row: AppConfigSetting.AppConfigList,
    index: number,
  ) => void;
  queryAppConfigByExampleList: () => void;
  isCloudEnv: boolean | undefined;
}) {
  const {
    openAddNodeDialog,
    deleteRow,
    openAddNodeListDialog,
    queryAppConfigByExampleList,
    isCloudEnv,
  } = options;
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        type: 'expand',
        prop: 'appConfigKeyList',
        render: (row: AppConfigSetting.AppConfigList) => {
          return row?.appConfigItemId ? (
            <div class={'px-7 py-5'}>
              <NodeTable
                queryAppConfigByExampleList={queryAppConfigByExampleList}
                rowValue={row}
              />
            </div>
          ) : (
            <span></span>
          );
        },
      },
      {
        label: t('appConfigSetting.settingTable.sort', '序号'),
        prop: 'indexNo',
        minWidth: 80,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('appConfigSetting.settingTable.appConfigItemId', '配置项标识'),
        prop: 'appConfigItemId',
        minWidth: 150,
      },
      {
        label: t(
          'appConfigSetting.settingTable.appConfigItemName',
          '配置项名称',
        ),
        prop: 'appConfigItemName',
        minWidth: 180,
      },
      {
        label: t('appConfigSetting.settingTable.createdUserName', '创建人'),
        prop: 'createdUserName',
        minWidth: 120,
      },
      {
        label: t('appConfigSetting.settingTable.createdAt', '创建时间'),
        prop: 'createdAt',
        minWidth: 170,
      },
      {
        label: t(
          'appConfigSetting.settingTable.modifiedUserName',
          '最后修改人',
        ),
        prop: 'modifiedUserName',
        minWidth: 130,
      },
      {
        label: t('appConfigSetting.settingTable.modifiedAt', '最后修改时间'),
        prop: 'modifiedAt',
        minWidth: 120,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        width: 150,
        render: (row: AppConfigSetting.AppConfigList, $index: number) => {
          return (
            <>
              <div class={'flex justify-around'}>
                <el-button
                  type={!isCloudEnv ? '' : 'primary'}
                  link={true}
                  disabled={!isCloudEnv}
                  onClick={() => openAddNodeDialog('edit', row)}
                >
                  {t('global:edit')}
                </el-button>
                <el-button
                  link={true}
                  type={!isCloudEnv ? '' : 'danger'}
                  disabled={!isCloudEnv}
                  onClick={() => deleteRow(row, $index)}
                >
                  {t('global:delete')}
                </el-button>
                <el-button
                  type="primary"
                  link={true}
                  onClick={() => openAddNodeListDialog(row, $index)}
                >
                  {t('appConfigSetting.settingTable.add', '添加')}
                </el-button>
              </div>
            </>
          );
        },
      },
    ],
  });
}

export function useAppConfigNodeTableColumnConfig(options: {
  openAddNodeDialog: (
    mode: string,
    data?: AppConfigSetting.AppConfigKeyItem,
  ) => void;
  deleteRow: (row: AppConfigSetting.AppConfigKeyItem, index: number) => void;
  isCloudEnv: boolean | undefined;
}) {
  const { openAddNodeDialog, deleteRow, isCloudEnv } = options;
  // const { toggleEdit, cancelEdit, addItem, delItem } = useEditableTable({
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t('appConfigSetting.nodeTable.appConfigKeyId', '配置节点标识'),
        prop: 'appConfigKeyId',
        minWidth: 150,
      },
      {
        label: t('appConfigSetting.nodeTable.appConfigKeyName', '配置节点名称'),
        prop: 'appConfigKeyName',
        minWidth: 150,
      },
      {
        label: t('appConfigSetting.nodeTable.appConfigKeyDesc', '配置节点描述'),
        prop: 'appConfigKeyDesc',
        minWidth: 150,
      },
      {
        label: t('appConfigSetting.nodeTable.dataTypeName', '数据类型'),
        prop: 'dataTypeName',
        minWidth: 120,
      },
      {
        label: t('appConfigSetting.nodeTable.codeSystemName', '编码体系'),
        prop: 'codeSystemName',
        minWidth: 120,
      },
      {
        label: t('appConfigSetting.nodeTable.createdUserName', '创建人'),
        prop: 'createdUserName',
        minWidth: 130,
      },
      {
        label: t('appConfigSetting.nodeTable.createdAt', '创建时间'),
        prop: 'createdAt',
        minWidth: 150,
      },
      {
        label: t('appConfigSetting.nodeTable.modifiedUserName', '最后修改人'),
        prop: 'modifiedUserName',
        minWidth: 170,
      },
      {
        label: t('appConfigSetting.nodeTable.modifiedAt', '最后修改时间'),
        prop: 'modifiedAt',
        minWidth: 150,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        width: 120,
        render: (row: AppConfigSetting.AppConfigKeyItem, $index: number) => {
          return (
            <>
              <div class={'flex justify-around'}>
                <el-button
                  type={!isCloudEnv ? '' : 'primary'}
                  link={true}
                  disabled={!isCloudEnv}
                  onClick={() => openAddNodeDialog('edit', row)}
                >
                  {t('global:edit')}
                </el-button>
                <el-button
                  type={!isCloudEnv ? '' : 'danger'}
                  link={true}
                  disabled={!isCloudEnv}
                  onClick={() => deleteRow(row, $index)}
                >
                  {t('global:delete')}
                </el-button>
              </div>
            </>
          );
        },
      },
    ],
  });
}
