import { Ref } from 'vue';
import {
  COMPONENT_CODE,
  MAIN_APP_CONFIG,
  PatientAccess,
  PatientAccessResponse,
  useAppConfigData,
  useFormConfig,
} from 'sun-biz';
import { SelectOptions } from '@/typings/common.ts';

export function useBizLockFormConfig(
  operatorList: Ref<SelectOptions[]>,
  rangList: Ref<SelectOptions[]>,
  isHidden: Ref<boolean>,
  fetchOperatorList: (params: BizLock.QueryUserListParams) => Promise<void>,
  handlePatientChange: (data: PatientAccessResponse['patientInfo']) => void,
) {
  const { menuId } = useAppConfigData([MAIN_APP_CONFIG.MENU_ID]);
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'bizLockSceneCode',
        label: t('bizLock.bizLockSceneCode', '业务场景'),
        component: 'select',
        placeholder: t('bizLock.bizLockSceneCodeTips', '请选择业务锁场景'),
        triggerModelChange: true,
        extraProps: {
          options: rangList.value,
          className: 'w-80',
        },
      },
      {
        label: t('bizLock.regId', '患者'),
        name: 'regId',
        isHidden: !isHidden.value,
        formItemProps: {
          style: 'margin-right: 30px;',
        },
        render: () => {
          return (
            <>
              <div style=" width: 320px" class="mr-2">
                <PatientAccess
                  showName={true}
                  menuId={menuId}
                  onClear={handlePatientChange}
                  code={COMPONENT_CODE.PATIENT_INFO}
                  style="width:200px"
                  onChange={handlePatientChange}
                />
              </div>
            </>
          );
        },
      },
      {
        name: 'lockUserId',
        label: t('bizLock.operator', '锁定操作员'),
        component: 'select',
        placeholder: t('bizLock.operatorTips', '请选择锁定操作员'),
        triggerModelChange: true,
        extraProps: {
          options: operatorList.value,
          className: 'w-80',
          filterable: true,
          remote: true,
          onVisibleChange: () => {
            fetchOperatorList({
              type: 'load',
              enabledFlag: 1,
            } as BizLock.QueryUserListParams);
          },
          remoteMethod: (keyWord: string) => {
            fetchOperatorList({
              type: 'search',
              keyWord: keyWord,
            } as BizLock.QueryUserListParams);
          },
        },
      },
      {
        label: t('bizLock.lockTime', '锁定时间'),
        name: 'lockDate',
        component: 'datePicker',
        triggerModelChange: true,
        extraProps: {
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          dateFormat: 'YYYY/MM/DD',
          timeFormat: 'HH:mm:ss',
          defaultTime: [
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 2, 1, 23, 59, 59),
          ],
          clearable: false,
          rangeSeparator: t('bizLock.rangeSeparator', '至'),
          startPlaceholder: t('bizLock.startPlaceholder', '开始日期'),
          endPlaceholder: t('bizLock.endPlaceholder', '结束日期'),
        },
        rules: [
          {
            required: false,
            trigger: ['change', 'blur'],
          },
        ],
      },
    ],
  });
  return data;
}
