import { Upload } from '@element-sun/icons-vue';
import { PrinterResItem } from '@/api/types';
import { useTranslation } from 'i18next-vue';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';
import { Ref, ComputedRef } from 'vue';
// import { filterSelectData } from '@sun-toolkit/shared';
import {
  FLAG,
  RECEIPT_PRINT_CATEG_CODE_NAME,
  DATA_SOURCE_TYPE_CODE,
} from '@/utils/constant';

export function useTableColumnsConfig(options: {
  operationFn: (row: Receipt.ReceiptReqItem) => Promise<void>;
  operationDesignFn: (row: Receipt.ReceiptReqItem) => Promise<void>;
}) {
  const { operationFn, operationDesignFn } = options;
  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        prop: 'selection',
        editable: false,
        type: 'selection',
      },
      {
        label: t('global:sequence'),
        minWidth: 100,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('receipt.receiptId', '单据标识'),
        prop: 'receiptId',
        minWidth: 150,
      },
      {
        label: t('receipt.receiptName', '单据名称'),
        prop: 'receiptName',
        minWidth: 150,
      },
      {
        label: t('receipt.receipt2ndName', '单据辅助名称'),
        prop: 'receipt2ndName',
        minWidth: 150,
      },
      {
        label: t('receipt.receiptExtName', '单据扩展名称'),
        prop: 'receiptExtName',
        minWidth: 150,
      },
      {
        label: t('receipt.receiptSplitTypeDesc', '分单方式'),
        prop: 'receiptSplitTypeDesc',
        minWidth: 150,
      },
      {
        label: t('receipt.receiptPrintCategDesc', '打印类别'),
        prop: 'receiptPrintCategDesc',
        minWidth: 150,
      },
      {
        label: t('receipt.redPrintCategDesc', '红票打印类别'),
        prop: 'redPrintCategDesc',
        minWidth: 150,
      },
      {
        label: t('receipt.receiptTemplateRuleDesc', '单据模板规则'),
        prop: 'receiptTemplateRuleDesc',
        minWidth: 150,
      },
      {
        label: t('receipt.receiptTemplateName', '模板名称'),
        prop: 'receiptTemplateName',
        minWidth: 150,
        render: (row: Receipt.ReceiptReqItem) => {
          const receiptTemplateNameList =
            row.chooseReceiptTemplateRuleList?.map(
              (item) => item.receiptTemplateName,
            );
          return <>{receiptTemplateNameList}</>;
        },
      },
      {
        label: t('receipt.dataSourceTypeDesc', '数据源类型'),
        prop: 'dataSourceTypeDesc',
        minWidth: 150,
      },
      {
        label: t('receipt.dataSource.spName', '数据源脚本'),
        prop: 'spName',
        minWidth: 150,
        render: (row: Receipt.ReceiptReqItem) => {
          return (
            <div>
              {row?.dataSourceTypeCode === DATA_SOURCE_TYPE_CODE.STORAGE
                ? row.spName
                : row?.dataSourceTypeCode === DATA_SOURCE_TYPE_CODE.JSON
                  ? row.designJson
                  : '--'}
            </div>
          );
        },
      },
      {
        label: t('receipt.printQty', '打印份数'),
        prop: 'printQty',
        minWidth: 150,
      },
      {
        label: t('receipt.invoiceInterfaceName', '票据接口'),
        prop: 'invoiceInterfaceName',
        minWidth: 150,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 150,
        fixed: 'right',
        render: (row: Receipt.ReceiptReqItem) => {
          return (
            <div class="flex items-center justify-around">
              <el-button
                type="primary"
                link={true}
                onClick={() => operationFn(row)}
              >
                {t('global:edit')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => operationDesignFn(row)}
              >
                {t('design', '设计')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return { tableColumns };
}

export function useTemplateRuleTableConfig(options: {
  id: string;
  tableRef: Ref<TableRef>;
  radioSelect: Ref<string | undefined>;
  data: Ref<Receipt.ChooseReceiptTemplateRuleItem[]>;
  selectFile: (row: Receipt.ChooseReceiptTemplateRuleItem) => Promise<void>;
  printerList: Ref<PrinterResItem[]>;
  isLocalPrint: boolean;
  reportList: Ref<Report.ReportTempReqItem[]>;
}) {
  const { t } = useTranslation();
  const {
    data,
    tableRef,
    id,
    radioSelect,
    selectFile,
    printerList,
    isLocalPrint,
    reportList,
  } = options;
  const { delItem, addItem } = useEditableTable({
    tableRef,
    data: data as unknown as Ref<
      (Receipt.ChooseReceiptTemplateRuleItem & { editable: boolean })[]
    >,
    id,
  });

  const receiptTemplateRule = (
    rule: unknown,
    value: unknown,
    callback: (data?: Error | undefined) => void,
  ) => {
    if (!value) {
      callback(
        new Error(
          t('global:placeholder.select.template', {
            name: t('receiptTemplateName', '模板名称'),
          }),
        ),
      );
    }
    const arr = data.value.filter((item) => item.receiptTemplateName === value);
    if (arr.length > 1) {
      callback(new Error(t('receiptTemplateName.exist', '模板已存在')));
    }
    callback();
  };

  // 在组件外部创建一个 Map 存储引用
  const selectRefs = new Map();
  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        label: t('receipt.receiptTemplateRuleId', '模板标识'),
        prop: 'receiptTemplateRuleId',
        minWidth: 150,
      },
      {
        label: t('receipt.receiptTemplateName', '模板名称'),
        prop: 'receiptTemplateName',
        minWidth: 150,
        editable: true,
        rules: [
          {
            validator: receiptTemplateRule,
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: Receipt.ChooseReceiptTemplateRuleItem & {
            editable: boolean;
            _selectRef: HTMLDivElement;
            rowKey: string;
          },
        ) => {
          return !isLocalPrint ? (
            <el-select
              clearable
              v-model={row.receiptTemplateName}
              filterable={true}
              ref={(el: HTMLDivElement) => {
                if (el) {
                  // 使用某个唯一标识作为键
                  selectRefs.set(row.receiptTemplateRuleId || row.rowKey, el);
                }
              }}
              onBlur={() => {
                // 从 Map 中获取引用
                const selectRef = selectRefs.get(
                  row.receiptTemplateRuleId || row.rowKey,
                );
                if (selectRef) {
                  const inputEl = selectRef.$el.querySelector('input');
                  if (inputEl && inputEl.value) {
                    // 检查是否以 .frx 结尾，如果没有则添加
                    const value = inputEl.value.trim();
                    row.receiptTemplateName = value.endsWith('.frx')
                      ? value
                      : `${value}.frx`;
                  }
                }
              }}
              // allow-create
              placeholder={t('global:placeholder.select.template', {
                name: t('receipt.receiptTemplateName', '模板名称'),
              })}
              onChange={(val: string) => {
                row.receiptTemplateName = val;
              }}
            >
              {reportList.value?.map((item) => (
                <el-option
                  value={item.fileName}
                  key={item.fileName}
                  label={item.fileName}
                ></el-option>
              ))}
            </el-select>
          ) : (
            <el-input
              v-model={row.receiptTemplateName}
              placeholder={t('global:placeholder.select.template', {
                name: t('receipt.receiptTemplateName', '模板名称'),
              })}
              onBlur={() => {
                if (row.receiptTemplateName) {
                  row.receiptTemplateName = row.receiptTemplateName.endsWith(
                    '.frx',
                  )
                    ? row.receiptTemplateName
                    : `${row.receiptTemplateName}.frx`;
                }
              }}
            >
              {{
                append: () => {
                  return [
                    <el-button icon={Upload} onClick={() => selectFile(row)} />,
                  ];
                },
              }}
            </el-input>
          );
        },
      },
      {
        label: t('receipt.printer', '打印机'),
        prop: 'printerName',
        minWidth: 150,
        editable: true,
        isHidden: !isLocalPrint,
        render: (
          row: Receipt.ChooseReceiptTemplateRuleItem & {
            editable: boolean;
            printerName: string;
          },
        ) => {
          return (
            <el-select
              v-model={row.printerName}
              filterable={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('receipt.printer', '打印机'),
              })}
              onChange={(val: string) => {
                row.printerName = val;
              }}
            >
              {printerList.value.map((item) => (
                <el-option
                  value={item.printerName}
                  key={item.printerName}
                  label={item.printerName}
                ></el-option>
              ))}
            </el-select>
          );
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 150,
        render: (
          row: Receipt.ChooseReceiptTemplateRuleItem & {
            editable: boolean;
          },
        ) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              before-change={async () => {
                if (row.enabledFlag === FLAG.YES) {
                  row.enabledFlag = FLAG.NO;
                } else {
                  row.enabledFlag = FLAG.YES;
                }
              }}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('receipt.defaultFlag', '默认标志'),
        prop: 'defaultFlag',
        minWidth: 150,
        render: (
          row: Receipt.ChooseReceiptTemplateRuleItem & {
            editable: boolean;
            rowKey: string;
          },
        ) => {
          return (
            <el-radio
              v-model={(radioSelect as Ref<string>).value}
              value={row.receiptTemplateRuleId ?? row.rowKey}
              onChange={() => {
                radioSelect.value = row.receiptTemplateRuleId ?? row.rowKey;
              }}
            ></el-radio>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 120,
        fixed: 'right',
        render: (
          row: Receipt.ChooseReceiptTemplateRuleItem & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return (
            <el-button
              type={row.receiptTemplateRuleId ? 'default' : 'danger'}
              link={true}
              disabled={Boolean(row.receiptTemplateRuleId)}
              onClick={() => delItem($index)}
            >
              {t('global:remove')}
            </el-button>
          );
        },
      },
    ],
  });
  return { tableColumns, addItem };
}

export function useMenuXReceiptTableConfig(options: {
  tableRef: Ref<TableRef>;
  data: Ref<Receipt.MenuXReceiptItem[]>;
  id: string;
  menuFilterData: ComputedRef<Menu.MenuInfo[]>;
  getMenuList: (params: {
    sysId?: string;
    enabledFlag?: 0 | 1;
    userId?: string;
  }) => Promise<void>;
}) {
  const { data, tableRef, id, menuFilterData } = options;
  const { delItem, addItem } = useEditableTable({
    tableRef,
    data: data as unknown as Ref<
      (Receipt.MenuXReceiptItem & { editable: boolean })[]
    >,
    id,
  });
  const tableColumns = useColumnConfig({
    dataSetCodes: [RECEIPT_PRINT_CATEG_CODE_NAME],
    getData: (t, dataCodes) => [
      {
        label: t('global:menu'),
        prop: 'menuId',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('global:menu'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: Receipt.MenuXReceiptItem & {
            editable: boolean;
          },
        ) => {
          return (
            <el-select
              v-model={row.menuId}
              filterable={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('global:menu'),
              })}
              onChange={(val: string) => {
                const obj = menuFilterData.value.find(
                  (item) => item.menuId === val,
                );
                row.menuName = obj?.menuNameDisplay as string;
              }}
            >
              {
                // filterSelectData(
                //   menuFilterData.value ?? [],
                //   data.value.filter((item) => item.sysXMenuId !== row.sysXMenuId),
                //   'sysXMenuId',
                // )

                (
                  (menuFilterData.value as (Menu.MenuInfo & {
                    menuGroupName: string;
                  })[]) ?? []
                ).map(
                  (
                    item: Menu.MenuInfo & {
                      menuGroupName: string;
                    },
                  ) => (
                    <el-option
                      value={item.menuId}
                      key={item.menuId}
                      label={item.menuGroupName}
                    ></el-option>
                  ),
                )
              }
            </el-select>
          );
        },
      },
      {
        label: t('receipt.receiptPrintCategCode', '单据打印类别'),
        prop: 'receiptPrintCategCode',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('receipt.receiptPrintCategCode', '单据打印类别'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: Receipt.MenuXReceiptItem & {
            editable: boolean;
          },
        ) => {
          return (
            <el-select
              v-model={row.receiptPrintCategCode}
              filterable={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('receipt.receiptPrintCategCode', '单据打印类别'),
              })}
              onChange={(val: string) => {
                row.receiptPrintCategCode = val;
                const obj = dataCodes?.value
                  ? dataCodes.value[RECEIPT_PRINT_CATEG_CODE_NAME].find(
                      (item) => item.dataValueNo === val,
                    )
                  : undefined;
                row.receiptPrintCategDesc = obj?.dataValueNameDisplay as string;
              }}
            >
              {(dataCodes?.value
                ? dataCodes.value[RECEIPT_PRINT_CATEG_CODE_NAME]
                : []
              )
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                ?.map((item: any) => (
                  <el-option
                    value={item.dataValueNo}
                    key={item.dataValueNo}
                    label={item.dataValueNameDisplay}
                  ></el-option>
                ))}
            </el-select>
          );
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 150,
        render: (
          row: Receipt.MenuXReceiptItem & {
            editable: boolean;
          },
        ) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              before-change={() => {
                if (row.enabledFlag === FLAG.YES) {
                  row.enabledFlag = FLAG.NO;
                } else {
                  row.enabledFlag = FLAG.YES;
                }
              }}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 120,
        fixed: 'right',
        render: (
          row: Receipt.MenuXReceiptItem & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return (
            <el-button
              type={row.menuXReceiptId ? 'default' : 'danger'}
              link={true}
              disabled={Boolean(row.menuXReceiptId)}
              onClick={() => delItem($index)}
            >
              {t('global:remove')}
            </el-button>
          );
        },
      },
    ],
  });
  return { tableColumns, addItem };
}

export function useMenuXReceiptTableConfigNotOperation() {
  const tableColumns = useColumnConfig({
    dataSetCodes: [RECEIPT_PRINT_CATEG_CODE_NAME],
    getData: (t, dataCodes) => [
      {
        label: t('receipt.receiptName', '单据名称'),
        prop: 'receiptNameDisplay',
        minWidth: 150,
      },
      {
        label: t('receipt.receiptPrintCategCode', '单据打印类别'),
        prop: 'receiptPrintCategCode',
        minWidth: 180,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('receipt.receiptPrintCategCode', '单据打印类别'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: Receipt.MenuXReceiptItem & {
            editable: boolean;
          },
        ) => {
          return (
            <el-select
              v-model={row.receiptPrintCategCode}
              filterable={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('receipt.receiptPrintCategCode', '单据打印类别'),
              })}
              onChange={(val: string) => {
                row.receiptPrintCategCode = val;
                const obj = dataCodes?.value
                  ? dataCodes.value[RECEIPT_PRINT_CATEG_CODE_NAME].find(
                      (item) => item.dataValueNo === val,
                    )
                  : undefined;
                row.receiptPrintCategDesc = obj?.dataValueNameDisplay as string;
              }}
            >
              {(dataCodes?.value
                ? dataCodes.value[RECEIPT_PRINT_CATEG_CODE_NAME]
                : []
              )
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                ?.map((item: any) => (
                  <el-option
                    value={item.dataValueNo}
                    key={item.dataValueNo}
                    label={item.dataValueNameDisplay}
                  ></el-option>
                ))}
            </el-select>
          );
        },
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (
          row: Receipt.MenuXReceiptItem & {
            editable: boolean;
          },
        ) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={FLAG.YES}
              inactive-value={FLAG.NO}
              before-change={() => {
                if (row.enabledFlag === FLAG.YES) {
                  row.enabledFlag = FLAG.NO;
                } else {
                  row.enabledFlag = FLAG.YES;
                }
              }}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
    ],
  });
  return { tableColumns };
}
