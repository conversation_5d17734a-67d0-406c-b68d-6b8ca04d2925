<script setup lang="ts">
  import { MAIN_APP_CONFIG, ProDialog, useAppConfigData } from 'sun-biz';
  import { ref } from 'vue';
  import {
    queryWindowDisRuleListById,
    saveWindowDisRule,
  } from '@/modules/baseConfig/api/windowManage.ts';
  import {
    BIZ_SCENE_CODE,
    FLAG,
    PARAMSETTING_DEFAULT_TYPE,
    WINDOW_SERVICE_TYPE_CODE,
  } from '@/utils/constant.ts';
  import { queryBizSceneXLimitConditionByExample } from '@/modules/system/api/bizScene.ts';
  import { queryDictDataListByExample } from '@/modules/baseConfig/api/code.ts';
  interface Props {
    windowId: string;
  }

  const { currentOrg, menuId } = useAppConfigData([
    MAIN_APP_CONFIG.CURRENT_ORG,
    MAIN_APP_CONFIG.MENU_ID,
  ]);
  const { windowId = '' } = defineProps<Props>();
  const contains = 'Contains';
  const dialogRef = ref();
  const activeName = ref(0); //窗口选择
  const popUpValueRangeList = ref([
    {
      dataValueNameDisplay: '配药',
      dataValueNo: WINDOW_SERVICE_TYPE_CODE.DISPENSING,
    },
    {
      dataValueNameDisplay: '发药',
      dataValueNo: WINDOW_SERVICE_TYPE_CODE.DISPENSING_OUT,
    },
  ]);
  const listOfLabels = ref<
    (WindowManage.WindowDisRule & {
      componentRenderConditions?: (DizLimitCondition.RestrictiveConditionsList &
        WindowManage.AdditionalParameters)[];
    })[]
  >([]);
  //查询业务场景可用限定条件
  const limitationConditions = async (
    row: WindowManage.WindowDisRule & {
      componentRenderConditions?: (DizLimitCondition.RestrictiveConditionsList &
        WindowManage.AdditionalParameters)[];
    },
    bizSceneCodes: string[],
  ) => {
    if (row?.componentRenderConditions && row?.componentRenderConditions.length)
      return;
    const [, res] = await queryBizSceneXLimitConditionByExample({
      bizSceneCodes,
    });

    if (res?.success) {
      row.componentRenderConditions = (
        res.data[0]?.bizSceneXConditionList || []
      ).filter(
        (
          item: DizLimitCondition.RestrictiveConditionsList &
            WindowManage.AdditionalParameters,
        ) => {
          const exist = item?.conditionSymbolList?.some(
            (item) => (item.symbolTypeCode = '"Contains"'),
          );
          return (
            item.valueTypeCode === PARAMSETTING_DEFAULT_TYPE.CODESYSTEM && exist
          );
        },
      );

      (row.componentRenderConditions || []).forEach((item) => {
        row.windowDisRuleDtList?.forEach((parameters) => {
          if (
            parameters.bizSceneLimitConditionId ===
            item.bizSceneLimitConditionId
          ) {
            retrieval(
              item,
              parameters.dataSearchBizIdTypeCode || '',
              parameters.codeSystemNo,
              undefined,
            ).then(() => {
              item.selectedList =
                (parameters?.windowDisRuleDtValueList || []).map(
                  (i: { [key: string]: string }) => {
                    let data: { [key: string]: string } = {};
                    if (item.propsType?.value) {
                      data[item.propsType?.value] = i?.value;
                      data.windowDisRuleDtValueId = i.windowDisRuleDtValueId;
                    }
                    return data;
                  },
                ) || [];
            });
          }
        });
      });
    }
  };
  //根据条件查询窗口分配规则
  const windowAssignmentRules = async () => {
    const [, res] = await queryWindowDisRuleListById({
      windowIds: [windowId],
    });
    if (res?.success) {
      listOfLabels.value = res.data;
    }
  };
  //字典数据检索
  const retrieval = async (
    row: DizLimitCondition.RestrictiveConditionsList &
      WindowManage.AdditionalParameters,
    dataSearchBizIdTypeCode: string,
    codeSystemNo?: string,
    keyWord?: string,
  ) => {
    const [, res] = await queryDictDataListByExample({
      keyWord,
      dataSearchBizIdTypeCode,
      codeSystemNo,
      hospitalId: currentOrg?.orgId || '',
      menuId: menuId || '',
    });
    if (res?.success) {
      row.propsType = {
        value: res.data.primaryKey,
        label: res.data.displayKey,
      };
      row.multiCheckBoxOptions = res.data.data.data as { value: string }[];
    }
  };
  //当弹窗显示
  const openThePopUpWindow = async () => {
    activeName.value = 0;
    await windowAssignmentRules();
    if (listOfLabels.value.length > 0) {
      listOfLabels.value.forEach((item, index) => {
        limitationConditions(listOfLabels.value[index], [
          listOfLabels.value[index].windowServiceTypeCode ===
          WINDOW_SERVICE_TYPE_CODE.DISPENSING
            ? BIZ_SCENE_CODE.CHEMIST
            : BIZ_SCENE_CODE.DISPENSING_MEDICINES,
        ]);
      });
    }
  };
  //当前选中的值
  const tabClick = (value: { index: number }) => {
    if (listOfLabels.value.length > 0) {
      //获取选中的业务条件编码
      limitationConditions(listOfLabels.value[value.index], [
        listOfLabels.value[value.index].windowServiceTypeCode ===
        WINDOW_SERVICE_TYPE_CODE.DISPENSING
          ? BIZ_SCENE_CODE.CHEMIST
          : BIZ_SCENE_CODE.DISPENSING_MEDICINES,
      ]);
    }
  };
  //选中标签
  const command = (val: {
    dataValueNo: string;
    dataValueNameDisplay: string;
  }) => {
    listOfLabels.value.push({
      windowServiceTypeDesc: val.dataValueNameDisplay,
      windowServiceTypeCode: val.dataValueNo,
      enabledFlag: FLAG.YES,
      componentRenderConditions: [],
      windowDisRuleDtList: [],
    });
    activeName.value = listOfLabels.value.length - 1;
    limitationConditions(listOfLabels.value[listOfLabels.value.length - 1], [
      val.dataValueNo === WINDOW_SERVICE_TYPE_CODE.DISPENSING
        ? BIZ_SCENE_CODE.CHEMIST
        : BIZ_SCENE_CODE.DISPENSING_MEDICINES,
    ]);
    // activeName.value = val.dataValueNo;
  };
  //点击保存了
  const onConfirm = async () => {
    const [, res] = await saveWindowDisRule({
      windowId: windowId,
      windowDisRuleList: listOfLabels.value.map((item) => {
        const windowDisRuleDtList = item.componentRenderConditions
          ?.filter((filterItems) => filterItems?.selectedList)
          .map((items) => {
            return {
              windowDisRuleDtId: items?.windowDisRuleDtId as string,
              bizSceneLimitConditionId: items.bizSceneLimitConditionId,
              symbolTypeCode: contains,
              windowDisRuleDtValueList: items.selectedList?.map((selected) => {
                return {
                  value:
                    items.propsType?.value && selected[items.propsType?.value],
                  windowDisRuleDtValueId: selected?.windowDisRuleDtValueId,
                };
              }),
            };
          });
        return {
          windowDisRuleId: item.windowDisRuleId,
          windowServiceTypeCode: item.windowServiceTypeCode,
          enabledFlag: item.enabledFlag,
          windowDisRuleDtList: windowDisRuleDtList?.filter((item) => {
            return (
              item.windowDisRuleDtValueList &&
              item.windowDisRuleDtValueList.length > 0
            );
          }),
        };
      }),
      // windowServiceTypeCode:
    });
    if (res?.success) {
      dialogRef.value.close();
    }
  };
  //退出
  const closePopUpWindow = () => {
    listOfLabels.value = [];
  };
  //点击移除标签
  const tabRemove = (index: number) => {
    listOfLabels.value.splice(index, 1);
  };
  defineExpose({
    dialogRef,
  });
</script>

<template>
  <ProDialog
    ref="dialogRef"
    :title="$t('windowManage:log.title', '窗口分配规则')"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-confirm-button="true"
    :confirm-fn="onConfirm"
    :confirm-btn-text="$t('global:save')"
    class="h-[60vh]"
    @open="openThePopUpWindow"
    @close="closePopUpWindow"
  >
    <div class="flex h-[calc(60vh-160px)] flex-1 flex-col overflow-y-auto">
      <el-tabs
        v-model="activeName"
        @tab-click="tabClick"
        editable
        @tab-remove="tabRemove"
      >
        <el-tab-pane
          v-for="(item, index) in listOfLabels"
          :key="item.windowDisRuleId"
          :name="index"
        >
          <template #label>
            {{ item.windowServiceTypeDesc
            }}<el-switch
              class="ml-1"
              v-model="item.enabledFlag"
              :active-value="FLAG.YES"
              :inactive-value="FLAG.NO"
              active-color="#13ce66"
              inactive-color="#ff4949"
            >
            </el-switch>
          </template>
          <div class="flex flex-wrap">
            <div
              class="flex w-1/3 p-2"
              v-for="options in item.componentRenderConditions"
              :key="options.bizSceneXConditionId"
            >
              <el-row class="w-full items-center">
                <el-col :span="8"
                  ><div class="w-full pr-3 text-right">
                    {{ options.bizSceneLimitConditionDesc }}
                  </div></el-col
                >
                <el-col :span="16">
                  <el-select
                    v-model="options.selectedList"
                    :placeholder="
                      $t('global:placeholder.select.template', {
                        name: $t('windowManage.conditions', '条件'),
                      })
                    "
                    multiple
                    collapse-tags
                    :value-key="options.propsType?.value"
                    filterable
                    remote
                    :remote-method="
                      (val: string) => {
                        if (!options.dataSearchBizIdTypeCode) return;
                        retrieval(
                          options,
                          options.dataSearchBizIdTypeCode || '',
                          options.codeSystemNo,
                          val,
                        );
                      }
                    "
                  >
                    <el-option
                      v-for="items in options.multiCheckBoxOptions"
                      :key="items.value"
                      :label="
                        options.propsType?.label &&
                        items[options.propsType.label]
                      "
                      :value="items"
                    >
                    </el-option> </el-select
                ></el-col>
              </el-row>
            </div>
          </div>
        </el-tab-pane>
        <template #add-icon>
          <!--        <el-icon> <Plus /> </el-icon>-->
          <el-dropdown @command="command" trigger="click">
            <el-icon> <Plus /> </el-icon>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="item in popUpValueRangeList"
                  :key="item.dataValueNo"
                  :command="item"
                  >{{ item.dataValueNameDisplay }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-tabs>
    </div>
  </ProDialog>
</template>
