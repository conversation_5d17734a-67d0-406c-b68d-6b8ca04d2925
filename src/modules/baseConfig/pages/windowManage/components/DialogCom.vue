<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { ElMessage } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { ENABLED_FLAG } from '@/utils/constant';
  import { useComputerHooks } from '../hooks/useComputerHooks';
  import { useDialogFormConfig } from '../config/useFormConfig';
  import { addAndUpdateWindow } from '@/modules/baseConfig/api/windowManage';
  import { ProForm, ProDialog, convertToWbNo, convertToSpellNo } from 'sun-biz';
  import { useGetDepartment } from '@/modules/system/components/employeeDetail/hooks/useGetDepartment.ts';
  const { t } = useTranslation();
  const props = defineProps<{
    mode: string;
    hospitalId: string;
  }>();
  const dialogType = computed(() => {
    return props.mode === 'add' ? true : false;
  });
  const dialogForm = ref<WindowManage.DialogForm>({
    wbNo: '',
    spellNo: '',
    windowNo: '',
    machineIds: [],
    windowName: '',
    windowServiceTypeCodes: [],
    hospitalId: props.hospitalId,
    enabledFlag: ENABLED_FLAG.YES,
    orgIds: [],
  });
  const formRef = ref();
  const dialogRef = ref();
  const handleConfirm = () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      formRef?.value?.ref.validate(async (valid: boolean) => {
        if (valid) {
          const params = {
            ...dialogForm.value,
            ...formRef?.value?.model,
            hospitalId: props.hospitalId,
          };
          const [, res] = await addAndUpdateWindow(params, dialogType.value);
          if (res?.success) {
            ElMessage.success(
              t(
                props.mode === 'edit'
                  ? 'global:edit.success'
                  : 'global:add.success',
              ),
            );
            resolve([] as unknown as [never, unknown]);
          } else {
            reject();
          }
        } else {
          reject(['', new Error('参数错误')]);
        }
      });
    });
  };
  const handleClose = () => {
    dialogForm.value = {
      wbNo: '',
      spellNo: '',
      windowNo: '',
      machineIds: [],
      windowName: '',
      windowServiceTypeCodes: [],
      hospitalId: props.hospitalId,
      enabledFlag: ENABLED_FLAG.YES,
      orgIds: [],
    };
  };
  const setFormData = (value: string) => {
    dialogForm.value = {
      ...dialogForm.value,
      spellNo: convertToSpellNo(value),
      wbNo: convertToWbNo(value),
    };
  };
  const { list, getList } = useComputerHooks();
  const { departmentList, getDepartmentList } = useGetDepartment();
  const handleOpen = (data: WindowManage.DialogForm, type: string) => {
    if (type === 'edit') {
      getList({ hospitalId: props.hospitalId, keyWord: '' });
      getDepartmentList({
        hospitalId: props.hospitalId,
        keyWord: '',
      });
    }
    dialogForm.value = {
      ...data,
    };
  };

  const formConfig = useDialogFormConfig(
    dialogType,
    list,
    getList,
    departmentList,
    getDepartmentList,
    computed(() => props.hospitalId),
    setFormData,
  );
  const emits = defineEmits(['success']);
  defineExpose({
    dialogRef,
    handleOpen,
  });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    destroy-on-close
    :width="900"
    :title="`${$t(`global:${props.mode}`)}${$t('windowManage.dialogTitle', '窗口')}`"
    :align-center="true"
    :confirm-fn="handleConfirm"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @success="emits('success')"
    @close="handleClose"
  >
    <ProForm
      ref="formRef"
      v-model="dialogForm"
      :column="2"
      :data="formConfig"
    />
  </ProDialog>
</template>
