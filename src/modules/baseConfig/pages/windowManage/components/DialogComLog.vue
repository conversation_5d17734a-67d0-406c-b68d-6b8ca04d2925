<script setup lang="ts">
  import { ref } from 'vue';
  import { ProDialog, ProTable } from 'sun-biz';
  import { dayjs } from '@sun-toolkit/shared';
  import { DEFAULT_PAGE_SIZE } from '@sun-toolkit/enums';
  import { useTableLogConfig } from '../config/useTableConfig';
  import { queryWindowSwitchList } from '@/modules/baseConfig/api/windowManage';
  const props = defineProps<{
    data: WindowManage.TableItem;
  }>();
  const dialogRef = ref();
  const dates = ref([
    dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
    dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss '),
  ]);
  const tableData = ref();
  const loading = ref(false);
  const pageInfo = ref({
    pageNumber: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    total: 0,
  });
  const hanldeChange = () => {
    handleSearch();
  };
  const handleSearch = async (windowId?: string) => {
    loading.value = true;
    const [startDate, endDate] = dates.value;
    const params = {
      windowId: windowId || props.data.windowId,
      modifiedBeginAt: dayjs(startDate)
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss'),
      modifiedEndAt: dayjs(endDate).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
      ...pageInfo.value,
    };
    const [, res] = await queryWindowSwitchList(params);
    if (res?.success) {
      tableData.value = res.data;
      pageInfo.value.total = res?.total || 0;
    }
    loading.value = false;
  };
  const handleCancel = () => {
    dates.value = [
      dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss '),
    ];
    tableData.value = [];
  };
  const handleOpen = (windowId: string) => {
    handleSearch(windowId);
    dialogRef.value?.open();
  };
  const tableConfig = useTableLogConfig();
  defineExpose({
    dialogRef,
    handleOpen,
  });
</script>
<template>
  <ProDialog
    ref="dialogRef"
    :title="
      $t('windowManage:log.title', `“${props.data.windowName}” 开关窗日志`)
    "
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @cancel="handleCancel"
    :show-confirm-button="false"
  >
    <el-date-picker
      v-model="dates"
      type="daterange"
      range-separator="至"
      :start-placeholder="$t('global:startDate')"
      :end-placeholder="$t('global:endTime')"
      @change="hanldeChange"
      class="w-70"
      value-format="YYYY-MM-DD"
      format="YYYY-MM-DD"
      :clearable="false"
    />
    <ProTable
      :columns="tableConfig"
      :data="tableData"
      :loading="loading"
      :pagination="true"
      class="mt-2"
      :page-info="{
        total: pageInfo.total,
        pageNumber: pageInfo.pageNumber,
        pageSize: pageInfo.pageSize,
      }"
      @current-page-change="
        (val: number) => {
          pageInfo.pageNumber = val;
          handleSearch();
        }
      "
      @size-page-change="
        (val: number) => {
          pageInfo.pageSize = val;
          pageInfo.pageNumber = 1;
          handleSearch();
        }
      "
    />
  </ProDialog>
</template>
