import { ref } from 'vue';
import { ENABLED_FLAG } from '@/utils/constant';
import { queryComputerManageListByExample } from '@/modules/baseConfig/api/computerManage';
export function useComputerHooks() {
  const list = ref();
  const getList = async (data: { hospitalId: string; keyWord: string }) => {
    const [, res] = await queryComputerManageListByExample({
      ...data,
      enabledFlag: ENABLED_FLAG.YES,
    });
    if (res?.data) {
      list.value = res.data;
    }
  };
  return {
    list,
    getList,
  };
}
