import { Ref, ComputedRef } from 'vue';
import { useFormConfig, useAppConfigData, MAIN_APP_CONFIG } from 'sun-biz';
import { WINDOW_SERVICE_TYPE_CODE_NAME, ENABLED_FLAG } from '@/utils/constant';
import { useGetDepartment } from '@/modules/system/components/employeeDetail/hooks/useGetDepartment.ts';
export function useSearchFormConfig(
  handleQuery: (params?: WindowManage.SearchForm) => void,
) {
  const { departmentList, getDepartmentList } = useGetDepartment();
  const currentOrg = useAppConfigData(MAIN_APP_CONFIG.CURRENT_ORG);
  return useFormConfig({
    dataSetCodes: [WINDOW_SERVICE_TYPE_CODE_NAME],
    getData: (t, dataSet) => [
      {
        label: t('person.belongHospital', '所属医院'),
        name: 'hospitalId',
        component: 'hospitalSelect',
        triggerModelChange: true,
        extraProps: {
          clearable: false,
          className: 'w-40',
        },
      },
      {
        label: t('global:enabledFlag'),
        name: 'enabledFlag',
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('global:enabledFlag'),
        }),
        extraProps: {
          clearable: false,
          className: 'w-40',
        },
      },
      {
        label: t('windowManage: windowServiceTypeCodes', '服务类型'),
        name: 'windowServiceTypeCodes',
        component: 'select',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('windowManage.windowServiceTypeCodes', '服务类型'),
        }),
        extraProps: {
          options: dataSet?.value
            ? dataSet.value[WINDOW_SERVICE_TYPE_CODE_NAME]
            : [],
          className: 'w-60',
          multiple: true,
          collapseTags: true,
          collapseTagsTooltip: true,
          maxCollapseTags: 2,
        },
      },

      {
        label: t('windowManage: serviceDept', '服务科室'),
        name: 'orgIds',
        triggerModelChange: true,
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('windowManage.serviceDept', '服务科室'),
        }),
        extraProps: {
          filterable: true,
          remote: true,
          remoteShowSuffix: true,
          options: departmentList.value,
          remoteMethod: (keyWord: string) => {
            getDepartmentList({
              hospitalId: currentOrg?.orgId,
              keyWord: keyWord,
            });
          },
          props: {
            value: 'orgId',
            label: 'orgName',
          },
          className: 'w-60',
          multiple: true,
          collapseTags: true,
          collapseTagsTooltip: true,
        },
      },

      {
        label: t('global:keyword', '关键字'),
        name: 'keyWord',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('global:keyword', '关键字'),
        }),
        triggerModelChange: true,
        extraProps: {
          className: 'w-40',
          filterable: true,
          prefixIcon: 'Search',
          onkeydown: (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              handleQuery({
                keyWord: (e.target as HTMLInputElement).value,
              });
            }
          },
        },
      },
    ],
  });
}
export function useDialogFormConfig(
  type: ComputedRef<boolean>,
  computerList: Ref<
    {
      computerId: string;
      computerName: string;
    }[]
  >,
  getComputerList: (data: {
    hospitalId: string;
    keyWord: string;
  }) => Promise<void>,

  departmentList: Ref<
    {
      orgId?: string;
      orgName?: string;
    }[]
  >,
  getDepartmentList: (data: {
    hospitalId: string;
    keyWord: string;
  }) => Promise<void>,

  hospitalId: ComputedRef<string>,
  callback: (value: string) => void,
) {
  return useFormConfig({
    dataSetCodes: [WINDOW_SERVICE_TYPE_CODE_NAME],
    getData: (t, dataSet) => [
      {
        label: t('windowManage.windowNo', '窗口编码'),
        name: 'windowNo',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('windowManage.windowNo', '窗口编码'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('windowManage.windowNo', '窗口编码'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          disabled: !type.value,
        },
      },
      {
        label: t('windowManage.windowName', '窗口名称'),
        name: 'windowName',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('windowManage.windowName', '窗口名称'),
        }),
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('windowManage.windowName', '窗口名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        extraProps: {
          onblur: (e: Event) => {
            callback((e.target as HTMLInputElement).value);
          },
        },
      },
      {
        label: t('academicMajorCategory.spellNo', '拼音码'),
        name: 'spellNo',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('academicMajorCategory.spellNo', '拼音码'),
        }),
      },
      {
        label: t('academicMajorCategory.wbNo', '五笔码'),
        name: 'wbNo',
        component: 'input',
        placeholder: t('global:placeholder.input.template', {
          content: t('academicMajorCategory.wbNo', '五笔码'),
        }),
      },
      {
        name: 'enabledFlag',
        label: t('global:enabledFlag'),
        component: 'switch',
        extraProps: {
          'inline-prompt': true,
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
          'active-text': t('global:enabled'),
          'inactive-text': t('global:disabled'),
        },
      },
      {
        label: t('windowManage: windowServiceTypeCodes', '服务类型'),
        name: 'windowServiceTypeCodes',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('windowManage.windowServiceTypeCodes', '服务类型'),
        }),
        extraProps: {
          options: dataSet?.value
            ? dataSet.value[WINDOW_SERVICE_TYPE_CODE_NAME]
            : [],
          multiple: true,
          collapseTags: true,
          collapseTagsTooltip: true,
          maxCollapseTags: 2,
        },
      },
      {
        label: t('windowManage: machineIds', '对应计算机'),
        name: 'machineIds',
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('windowManage.machineIds', '对应计算机'),
        }),
        isFullWidth: true,
        extraProps: {
          options: computerList.value,
          props: {
            label: 'computerDesc',
            value: 'computerId',
          },
          className: 'w-full',
          multiple: true,
          collapseTags: true,
          collapseTagsTooltip: true,
          maxCollapseTags: 2,
          remote: true,
          filterable: true,
          remoteShowSuffix: true,
          remoteMethod: async (query: string) => {
            await getComputerList({
              hospitalId: hospitalId.value,
              keyWord: query,
            });
          },
        },
      },

      {
        label: t('windowManage: serviceDept', '服务科室'),
        name: 'orgIds',
        component: 'select',
        triggerModelChange: true,
        required: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('windowManage.orgIds', '服务科室'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        placeholder: t('global:placeholder.select.template', {
          name: t('windowManage.serviceDept', '服务科室'),
        }),
        extraProps: {
          filterable: true,
          remote: true,
          remoteShowSuffix: true,
          options: departmentList.value,
          remoteMethod: async (keyWord: string) => {
            await getDepartmentList({
              hospitalId: hospitalId.value,
              keyWord: keyWord,
            });
          },
          className: 'w-60',
          multiple: true,
          collapseTags: true,
          collapseTagsTooltip: true,
          maxCollapseTags: 2,
          props: {
            label: 'orgName',
            value: 'orgId',
          },
        },
      },
    ],
  });
}
