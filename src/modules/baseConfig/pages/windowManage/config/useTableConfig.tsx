import { useColumnConfig } from 'sun-biz';
import { ENABLED_FLAG } from '@sun-toolkit/enums';
export function useTableConfig(
  handleEnableSwitch: (data: WindowManage.TableItem) => void,
  handleEdit: (mode: string, data: WindowManage.TableItem) => void,
  handleWindowLog: (data: WindowManage.TableItem) => void,
  windowAssignmentRules: (data: WindowManage.TableItem) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global.sequence', '顺序'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('windowManage.list.windowNo', '编码'),
        prop: 'windowNo',
        minWidth: 150,
      },
      {
        label: t('windowManage.list.windowName', '窗口名称'),
        prop: 'windowName',
        minWidth: 150,
      },
      {
        label: t('windowManage.list.spellNo', '拼音码'),
        prop: 'spellNo',
        minWidth: 150,
      },
      {
        label: t('windowManage.list.wbNo', '五笔码'),
        prop: 'wbNo',
        minWidth: 150,
      },
      {
        label: t('global.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: WindowManage.TableItem) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },

      {
        label: t('windowManage.list.serviceDept', '服务科室'),
        prop: 'orgName',
        minWidth: 150,
        render: (row: WindowManage.TableItem) => {
          return (
            <div class="w-full text-wrap">
              {row.windowServiceBizUnitList &&
              row.windowServiceBizUnitList?.length > 0
                ? row.windowServiceBizUnitList
                    ?.map((item) => item?.orgName)
                    .join('、')
                : '--'}
            </div>
          );
        },
      },

      {
        label: t('windowManage.list.type', '服务类型'),
        prop: 'type',
        minWidth: 150,
        render: (row: WindowManage.TableItem) => (
          <>
            {row.windowServiceTypeList?.map(
              (item: WindowManage.WindowServiceTypeItem) => (
                <el-tag type="primary" effect="plain">
                  {item.windowServiceTypeDesc}
                </el-tag>
              ),
            )}
          </>
        ),
      },
      {
        label: t('global:operation', '操作'),
        prop: 'operation',
        minWidth: 220,
        render: (row: WindowManage.TableItem) => (
          <>
            <el-button type="text" onClick={() => handleEdit('edit', row)}>
              {t('global:edit')}
            </el-button>
            <el-button type="text" onClick={() => windowAssignmentRules(row)}>
              {t('windowManage.list.log', '窗口分配规则')}
            </el-button>
            <el-button type="text" onClick={() => handleWindowLog(row)}>
              {t('windowManage.list.log', '开关窗日志')}
            </el-button>
          </>
        ),
      },
    ],
  });
}
export function useTableLogConfig() {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global.sequence', '顺序'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('windowManage.log.list.openWindowFlag', '操作类型'),
        prop: 'openWindowFlag',
        width: 150,
        render: (row: WindowManage.DialogLogTableItem) => (
          <el-tag type={row.openWindowFlag === 1 ? 'primary' : 'info'}>
            {row.openWindowFlag === 1
              ? t('windowManage.log.list.open', '开窗')
              : t('windowManage.log.list.close', '关窗')}
          </el-tag>
        ),
      },
      {
        label: t('windowManage.log.list.ipAddress', 'IP地址'),
        prop: 'ipAddress',
        minWidth: 150,
      },
      {
        label: t('windowManage.log.list.macAddress', 'MAC地址'),
        prop: 'macAddress',
      },
      {
        label: t('windowManage.log.list.modifiedUserName', '操作用户'),
        prop: 'modifiedUserName',
      },
      {
        label: t('windowManage.log.list.modifiedAt', '操作时间'),
        prop: 'modifiedAt',
      },
    ],
  });
}
