<script setup lang="tsx">
  import { onMounted, ref } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { ENABLED_FLAG } from '@/utils/constant.ts';
  import { ProForm, ProTable, Title } from 'sun-biz';
  import DialogCom from './components/DialogCom.vue';
  import { DEFAULT_PAGE_SIZE } from '@sun-toolkit/enums';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import DialogComLog from './components/DialogComLog.vue';
  import DialogWind from './components/DialogWind.vue';
  import { useTableConfig } from './config/useTableConfig.tsx';
  import { useSearchFormConfig } from './config/useFormConfig.tsx';
  import {
    queryWindowList,
    updateEnabledFlagById,
  } from '@/modules/baseConfig/api/windowManage';
  const { t } = useTranslation();
  const searchForm = ref<WindowManage.SearchForm>({
    keyWord: '',
    hospitalId: '',
    windowServiceTypeCodes: [],
    pageNumber: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    enabledFlag: ENABLED_FLAG.ALL,
    orgIds: [],
  });
  const total = ref(0);
  const tableData = ref<WindowManage.TableItem[]>([]);
  const loading = ref(false);
  const mode = ref();
  const dialogRef = ref();
  const dialogRefLogDetail = ref();
  const dialogWindRef = ref();
  const windowId = ref<string>([]); //窗口标识集合
  const logDetailActiveData = ref<WindowManage.TableItem>(
    {} as WindowManage.TableItem,
  );
  const handleQuery = async (data?: WindowManage.SearchForm) => {
    loading.value = true;
    if (data) {
      searchForm.value = {
        ...searchForm.value,
        ...data,
      };
    }
    const params = {
      ...searchForm.value,
      enabledFlag:
        searchForm.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchForm.value.enabledFlag,
    };
    const [, res] = await queryWindowList(params);
    if (res?.success) {
      tableData.value = res.data;
      total.value = res?.total || 0;
    }
    loading.value = false;
  };
  const handleOpenDialog = (type: string, data?: WindowManage.TableItem) => {
    mode.value = type;
    if (type === 'edit') {
      const temp = {
        ...data,
        windowServiceTypeCodes: data?.windowServiceTypeList.map(
          (el) => el.windowServiceTypeCode,
        ),
        machineIds: data?.windowComputerList.map((el) => el.machineId),
        orgIds: data?.windowServiceBizUnitList?.map((el) => el.orgId),
      };
      dialogRef.value.handleOpen(temp, type);
    }
    dialogRef?.value.dialogRef.open();
  };
  const handleEnableSwitch = (row: WindowManage.TableItem) => {
    return new Promise<void>((resolve) => {
      ElMessageBox.confirm(
        t('switch.ask.title', '您确定要{{action}} “{{name}}” 吗？', {
          action:
            row.enabledFlag === ENABLED_FLAG.YES
              ? t('global:disabled')
              : t('global:enabled'),
          name: row.windowName,
        }),
        t('global:tip'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      ).then(async () => {
        const params = {
          ...row,
          enabledFlag:
            row.enabledFlag === ENABLED_FLAG.YES
              ? ENABLED_FLAG.NO
              : ENABLED_FLAG.YES,
        };
        const [, res] = await updateEnabledFlagById(params);
        if (res?.success) {
          ElMessage({
            type: 'success',
            message: t(
              row.enabledFlag === ENABLED_FLAG.YES
                ? 'global:disabled.success'
                : 'global:enabled.success',
            ),
          });
          resolve();
          handleQuery();
        }
      });
    });
  };
  const handleWindowLog = (data: WindowManage.TableItem) => {
    logDetailActiveData.value = data;
    dialogRefLogDetail?.value.handleOpen(data.windowId);
  };
  //新增窗口分配规则
  const windowAssignmentRules = (data: WindowManage.TableItem) => {
    windowId.value = data.windowId;
    dialogWindRef.value.dialogRef.open();
  };
  const searchConfig = useSearchFormConfig(handleQuery);
  const tableConfig = useTableConfig(
    handleEnableSwitch,
    handleOpenDialog,
    handleWindowLog,
    windowAssignmentRules,
  );
  onMounted(() => {
    handleQuery();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('windowManage.list.title', '窗口列表')" />
    <div class="mb-4">
      <ProForm
        v-model="searchForm"
        layout-mode="inline"
        :data="searchConfig"
        @model-change="handleQuery"
      >
        <div class="flex flex-1 justify-end">
          <el-button type="primary" @click="handleOpenDialog('add')">{{
            $t('windowManage:add', '新增')
          }}</el-button>
        </div>
      </ProForm>
    </div>
    <ProTable
      ref="tableRef"
      :columns="tableConfig"
      :data="tableData"
      :loading="loading"
      :page-info="{
        pageNumber: searchForm.pageNumber,
        pageSize: searchForm.pageSize,
        total: total,
      }"
      :pagination="true"
      @current-page-change="(val: number)=>{
          searchForm.pageNumber = val;
          handleQuery({ pageNumber: val });
        }"
      @size-page-change="(val: number)=>{
          searchForm.pageSize = val;
          searchForm.pageNumber = 1;
          handleQuery({ pageSize: val, pageNumber: 1});
        }"
    />
    <DialogCom
      ref="dialogRef"
      :mode="mode"
      :hospital-id="searchForm.hospitalId || ''"
      @success="handleQuery"
    />
    <DialogComLog ref="dialogRefLogDetail" :data="logDetailActiveData" />
    <DialogWind ref="dialogWindRef" :window-id="windowId"></DialogWind>
  </div>
</template>
