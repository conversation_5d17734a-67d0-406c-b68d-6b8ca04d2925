import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import { Ref } from 'vue';
import { ENABLED_FLAG } from '@sun-toolkit/enums';
import {
  AppEnvDefineAppEnvList,
  AppEnvDefineUpsertAppEnv,
} from '@/modules/baseConfig/typings/appEnvDefine.ts';

export function useAppEnvTableConfig(options: {
  id: string;
  tableRef: Ref<TableRef>;
  data: Ref<AppEnvDefineAppEnvList[]>;
  saveRow: (row: AppEnvDefineUpsertAppEnv, index: number) => Promise<void>;
  handleEnableSwitch: (
    row: AppEnvDefineUpsertAppEnv,
    action: string,
  ) => Promise<void>;
  deleteAppEnvByID: (row: AppEnvDefineUpsertAppEnv) => Promise<void>;
  isCloudEnv: boolean | undefined;
  handleEdit: (row: AppEnvDefineUpsertAppEnv) => void;
}) {
  const {
    id,
    tableRef,
    data,
    saveRow,
    handleEnableSwitch,
    deleteAppEnvByID,
    isCloudEnv,
    handleEdit,
  } = options;
  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef,
    data: data as unknown as Ref<
      (AppEnvDefineAppEnvList & { editable: boolean })[]
    >,
    id,
  });
  const tableColumns = useColumnConfig({
    getData: (t) => [
      {
        prop: 'selection',
        editable: false,
        type: 'selection',
      },
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('appEnvDefine.appEnvDefineTable.appEnvId', '环境标识'),
        prop: 'appEnvId',
        minWidth: 150,
      },
      {
        label: t('appEnvDefine.appEnvDefineTable.appEnvName', '环境名称'),
        prop: 'appEnvName',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('appEnvDefine.appEnvName', '环境名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: AppEnvDefineAppEnvList & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.appEnvName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('appEnvDefine.appEnvName', '环境名称'),
                })}
              />
            );
          } else {
            return <>{row.appEnvName}</>;
          }
        },
      },
      {
        label: t('appEnvDefine.appEnvDefineTable.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: AppEnvDefineAppEnvList) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() =>
                handleEnableSwitch(row as AppEnvDefineUpsertAppEnv, 'enable')
              }
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('appEnvDefine.appEnvDefineTable.editableFlag', '允许编辑'),
        prop: 'editableFlag',
        minWidth: 150,
        render: (row: AppEnvDefineAppEnvList) => {
          return (
            <el-switch
              modelValue={row.editableFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() =>
                handleEnableSwitch(row as AppEnvDefineUpsertAppEnv, 'edit')
              }
              active-text={t(
                'appEnvDefine.appEnvDefineTable.enabledEdit',
                '是',
              )}
              inactive-text={t(
                'appEnvDefine.appEnvDefineTable.disabledEdit',
                '否',
              )}
            />
          );
        },
      },
      {
        label: t('appEnvDefine.appEnvDefineTable.createdUserName', '创建人'),
        prop: 'createdUserName',
        minWidth: 150,
      },
      {
        label: t('appEnvDefine.appEnvDefineTable.createdAt', '创建时间'),
        prop: 'createdAt',
        minWidth: 150,
      },
      {
        label: t(
          'appEnvDefine.appEnvDefineTable.modifiedUserName',
          '最后修改人',
        ),
        prop: 'modifiedUserName',
        minWidth: 150,
      },
      {
        label: t('appEnvDefine.appEnvDefineTable.modifiedAt', '最后修改时间'),
        prop: 'modifiedAt',
        minWidth: 150,
      },
      {
        label: t('global:operation'),
        fixed: 'right',
        prop: 'operation',
        width: 160,
        render: (
          row: AppEnvDefineUpsertAppEnv & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return row.editable ? (
            <div class="flex items-center justify-around">
              <el-button
                type="primary"
                link={true}
                onClick={() => saveRow(row, $index)}
              >
                {t('global:save')}
              </el-button>
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel')}
              </el-button>
            </div>
          ) : (
            <div class="flex items-center justify-around">
              <el-button
                link={true}
                disabled={!isCloudEnv && row.editableFlag === ENABLED_FLAG.NO}
                type={
                  !isCloudEnv && row.editableFlag === ENABLED_FLAG.NO
                    ? ''
                    : 'primary'
                }
                onClick={() => handleEdit(row)}
              >
                {t('global:edit')}
              </el-button>
              <el-button
                link={true}
                disabled={!isCloudEnv && row.editableFlag === ENABLED_FLAG.NO}
                type={
                  !isCloudEnv && row.editableFlag === ENABLED_FLAG.NO
                    ? ''
                    : 'danger'
                }
                onClick={() => deleteAppEnvByID(row)}
              >
                {t('global:delete')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return { tableColumns, toggleEdit };
}
