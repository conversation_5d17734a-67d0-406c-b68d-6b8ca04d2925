import { useFormConfig } from 'sun-biz';
import { ENABLED_FLAG, MR_TYPE_CODE_NAME } from '@/utils/constant.ts';
import { Ref } from 'vue';

export function useAddNewMedicalFormConfig(
  encounterTypeList: Ref<Selection[]> | undefined,
  bizUnitList: MedicalTypeDict.OrganizationList[] | undefined,
  mrTempList: Ref<ManageTemplate.MrTempItem[]> | undefined,
) {
  const data = useFormConfig({
    dataSetCodes: [MR_TYPE_CODE_NAME],
    getData: (t, dataSet) => [
      {
        name: 'medicalRecordTypeName',
        label: t('addNewMedical.form.medicalRecordTypeName', '病历类型名称'),
        component: 'input',
        placeholder: t(
          'addNewMedical.form.medicalRecordTypeNameTips',
          '请输入病历类型名称',
        ),
        rules: [
          {
            required: true,
            message: t(
              'addNewMedical.form.medicalRecordTypeNameTips',
              '请输入病历类型名称',
            ),
            trigger: ['blur', 'change'],
          },
        ],
        extraProps: {
          class: 'w-80',
          clearable: true,
        },
      },
      {
        name: 'encounterTypeCode',
        label: t('addNewMedical.form.encounterTypeCode', '就诊类型范围'),
        component: 'select',
        placeholder: t(
          'addNewMedical.form.encounterTypeCode',
          '请选择就诊类型范围',
        ),
        rules: [
          {
            required: true,
            message: t(
              'addNewMedical.form.encounterTypeCode',
              '请选择就诊类型范围',
            ),
            trigger: ['blur', 'change'],
          },
        ],
        extraProps: {
          class: 'w-64',
          'collapse-tags': true,
          'collapse-tags-tooltip': true,
          'max-collapse-tags': 2,
          options: encounterTypeList?.value || [],
          filterable: true,
        },
      },
      {
        name: 'enabledFlag',
        component: 'switch',
        placeholder: t(
          'addNewMedical.enabledFlag.placeholder',
          '请输入启用状态',
        ),
        label: t('addOrEditMenu.enabledFlag', '启用状态'),
        defaultValue: ENABLED_FLAG.YES,
        extraProps: {
          'active-value': ENABLED_FLAG.YES,
          'inactive-value': ENABLED_FLAG.NO,
        },
      },
      {
        label: t('addNewMedical.form.maxMrFileCount', '最大病历文件数'),
        name: 'maxMrFileCount',
        component: 'input-number',
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'addNewMedical.form..maxMrFileCount',
                '最大病历文件数',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        extraProps: {
          class: 'w-80',
          'controls-position': 'right',
          min: 1,
          max: 999999999,
          step: 1,
          precision: 0,
        },
      },
      {
        name: 'mrTempIdDefault',
        label: t('addNewMedical.form.mrTempIdDefault', '默认病历模板'),
        component: 'select',
        placeholder: t(
          'addNewMedical.form.mrTempIdDefault',
          '请选择默认病历模板',
        ),
        rules: [
          {
            required: true,
            message: t(
              'addNewMedical.form.mrTempIdDefault',
              '请选择默认病历模板',
            ),
            trigger: ['blur', 'change'],
          },
        ],
        extraProps: {
          class: 'w-64',
          options: mrTempList?.value || [],
          filterable: true,
          props: {
            label: 'mrTempName',
            value: 'mrTempId',
          },
        },
      },
      {
        name: 'mrTypeCode',
        label: t('addNewMedical.form.mrTypeCode', '病历类型代码'),
        component: 'select',
        placeholder: t('global:placeholder.select.template', {
          name: t('addNewMedical.form.mrTypeCode', '病历类型代码'),
        }),
        extraProps: {
          class: 'w-64',
          options: dataSet?.value ? dataSet.value[MR_TYPE_CODE_NAME] : [],
          filterable: true,
          props: {
            label: 'dataValueNameDisplay',
            value: 'dataValueNo',
          },
        },
      },
      {
        name: 'mrTypeDisplayName',
        label: t('addNewMedical.form.mrTypeDisplayName', '病历类型显示名称'),
        component: 'input',
        isFullWidth: true,
        placeholder: t(
          'addNewMedical.form.mrTypeDisplayName',
          '请输入病历类型名称',
        ),
        rules: [
          {
            required: true,
            message: t(
              'addNewMedical.form.mrTypeDisplayName',
              '请输入病历类型名称',
            ),
            trigger: ['blur', 'change'],
          },
        ],
        extraProps: {
          class: 'w-64',
          clearable: true,
        },
      },
      {
        name: 'departmentIds',
        label: t('addNewMedical.form.departmentIds', '使用科室'),
        component: 'select',
        isFullWidth: true,
        placeholder: t('addNewMedical.form.departmentIds', '请选择使用科室'),
        extraProps: {
          className: 'w-full',
          'collapse-tags': true,
          'collapse-tags-tooltip': true,
          'max-collapse-tags': 5,
          options: bizUnitList?.value || [],
          filterable: true,
          multiple: true,
          props: {
            label: 'orgNameDisplay',
            value: 'orgId',
          },
        },
      },
    ],
  });
  return data;
}
