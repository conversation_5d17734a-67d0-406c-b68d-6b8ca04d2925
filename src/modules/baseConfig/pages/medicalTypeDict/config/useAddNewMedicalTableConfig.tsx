import { Ref } from 'vue';
import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';

export function useAddNewMedicalTableConfig(
  tableRef: Ref<TableRef>,
  data: Ref<MedicalTypeDict.MrTypeScopeList[]>,
  saveRow: (row: MedicalTypeDict.MrTypeScopeList) => Promise<void>,
  editItem: (row: MedicalTypeDict.MrTypeScopeList) => Promise<void>,
  deleteItem: (row: MedicalTypeDict.MrTypeScopeList) => Promise<void>,
  cancelItem: (
    row: MedicalTypeDict.MrTypeScopeList,
    index: number,
  ) => Promise<void>,
) {
  const { toggleEdit, addItem, cancelEdit } = useEditableTable({
    id: 'key',
    tableRef,
    data: data as unknown as Ref<MedicalTypeDict.MedicalRecordTypeList[]>,
  });
  const columnConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('addNewMedical.table.encounterTypeCode', '就诊类型'),
        prop: 'encounterTypeCodeDesc',
        minWidth: 150,
      },
      {
        label: t('addNewMedical.table.maxMrFileCount', '最大病历文件数'),
        prop: 'maxMrFileCount',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'addNewMedical.table..maxMrFileCount',
                '最大病历文件数',
              ),
            }),
            trigger: 'blur',
          },
        ],
        render: (row: MedicalTypeDict.MedicalRecordTypeList) => {
          return row.editable ? (
            <el-input-number
              v-model={row.maxMrFileCount}
              min={1}
              class={'w-80'}
              max={999999999}
              step={1}
              precision={0}
              placeholder={t('global:placeholder.input.template', {
                content: t(
                  'addNewMedical.table..maxMrFileCount',
                  '最大病历文件数',
                ),
              })}
              controls-position="right"
            />
          ) : (
            <span>{row.maxMrFileCount}</span>
          );
        },
      },
      {
        label: t('global:operation', '操作'),
        prop: 'operation',
        fixed: 'right',
        width: 120,
        // renderHeader: () => {
        //   return (
        //     <span class={'flex items-center justify-center'}>
        //       {t('addNewMedical.table.operation', '操作')}
        //       <el-icon
        //         class={'ml-2 cursor-pointer'}
        //         color="#409eff"
        //         onClick={() => {
        //           insertRow();
        //         }}
        //       >
        //         <Plus />
        //       </el-icon>
        //     </span>
        //   );
        // },
        render: (
          row: MedicalTypeDict.MedicalRecordTypeList,
          $index: number,
        ) => {
          return row.editable ? (
            <>
              <el-button
                type="danger"
                link
                onClick={() => cancelItem(row, $index)}
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                type="primary"
                link
                onClick={() => saveRow(row, $index)}
              >
                {t('global:confirm')}
              </el-button>
            </>
          ) : (
            <div>
              <el-button type="primary" link onClick={() => editItem(row)}>
                {t('global:edit')}
              </el-button>
              <el-button type="danger" link onClick={() => deleteItem(row)}>
                {t('global:delete')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return {
    columnConfig,
    toggleEdit,
    addItem,
    cancelEdit,
  };
}
