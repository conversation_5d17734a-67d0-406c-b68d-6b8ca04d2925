import { Ref } from 'vue';
import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import { FLAG } from '../../../../../utils/constant.ts';

export function useMedicalRecordTypeConfig(
  id: string,
  tableRef: Ref<TableRef>,
  tableData: Ref<MedicalTypeDict.UpdateMedicalRecordType[]>,
  handleSave: (row: MedicalTypeDict.UpdateMedicalRecordType) => void,
  handleEdit: (row: MedicalTypeDict.UpdateMedicalRecordType) => void,
  handleEnableSwitch: (
    row: MedicalTypeDict.UpdateMedicalRecordType,
    prop: keyof MedicalTypeDict.UpdateMedicalRecordType,
  ) => void,
  deleteItem: (
    row: MedicalTypeDict.UpdateMedicalRecordType,
    index: number,
  ) => void,
  isCloudEnv: boolean | undefined,
) {
  const { toggleEdit, cancelEdit, addItem } = useEditableTable({
    id,
    tableRef,
    data: tableData as unknown as Ref<
      { [key: string]: unknown; editable: boolean }[]
    >,
  });
  const data = useColumnConfig({
    getData: (t) => [
      {
        prop: 'selection',
        type: 'selection',
      },
      {
        label: t('medicalRecordType.table.medicalRecordTypeId', '病历类型标识'),
        prop: 'medicalRecordTypeId',
        minWidth: 120,
      },
      {
        label: t(
          'medicalRecordType.table.medicalRecordTypeName',
          '病历类型名称',
        ),
        prop: 'medicalRecordTypeName',
        minWidth: 120,
      },
      {
        label: t(
          'medicalRecordType.table.mrTypeDisplayName',
          '病历类型显示名称',
        ),
        prop: 'mrTypeDisplayName',
        minWidth: 140,
      },
      {
        label: t('medicalRecordType.table.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        width: 100,
        editable: true,
        render: (row: MedicalTypeDict.UpdateMedicalRecordType) => {
          return (
            <div class="flex w-full items-center justify-center">
              <el-switch
                modelValue={row.enabledFlag}
                inline-prompt
                active-value={FLAG.YES}
                inactive-value={FLAG.NO}
                before-change={() => handleEnableSwitch(row, 'enabledFlag')}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
              />
            </div>
          );
        },
      },
      {
        label: t('medicalRecordType.table.encounterTypeCodeDesc', '就诊类型'),
        prop: 'encounterTypeCodeDesc',
        minWidth: 50,
      },
      {
        label: t('medicalRecordType.table.mrTypeCode', '病历类型代码'),
        prop: 'mrTypeCodeDesc',
        minWidth: 100,
      },
      {
        label: t('medicalRecordType.table.maxMrFileCount', '最大病历文件数'),
        prop: 'maxMrFileCount',
        minWidth: 70,
      },
      {
        label: t('medicalRecordType.table.mrTempNameDefault', '默认病历模板'),
        prop: 'mrTempNameDefault',
        minWidth: 120,
      },

      {
        label: t('medicalRecordType.table.departmentList', '科室范围'),
        prop: 'departmentList',
        minWidth: 120,
        render: (row: MedicalTypeDict.UpdateMedicalRecordType) => {
          return (
            <div class={'flex flex-wrap items-center gap-2'}>
              {row.departmentList && row.departmentList.length
                ? row.departmentList.map((dept) =>
                    dept.departmentName ? (
                      <el-tag type="primary" key={dept.mrTypeUseScopeId}>
                        {dept.departmentName}
                      </el-tag>
                    ) : (
                      '--'
                    ),
                  )
                : '--'}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 120,
        fixed: 'right',
        render: (
          row: MedicalTypeDict.UpdateMedicalRecordType,
          $index: number,
        ) => {
          if (row.editable) {
            return (
              <div class={'flex justify-center'} key="editable">
                <el-button
                  type="danger"
                  link={true}
                  onClick={() => cancelEdit(row, $index)}
                >
                  {t('global:cancel')}
                </el-button>
                <el-button
                  type="primary"
                  link={true}
                  onClick={() => handleSave(row)}
                >
                  {t('global:save')}
                </el-button>
              </div>
            );
          } else {
            return (
              <>
                <div class={'flex justify-center'}>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => handleEdit(row)}
                  >
                    {t('global:edit')}
                  </el-button>
                  <el-button
                    disabled={!isCloudEnv}
                    type="danger"
                    link={true}
                    onClick={() => deleteItem(row, $index)}
                  >
                    {t('global:delete')}
                  </el-button>
                </div>
              </>
            );
          }
        },
      },
    ],
  });
  return { data, toggleEdit, addItem };
}
