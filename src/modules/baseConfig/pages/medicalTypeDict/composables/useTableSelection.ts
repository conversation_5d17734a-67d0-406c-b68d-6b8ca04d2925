import { ref, computed } from 'vue';

export function useTableSelection() {
  const selections = ref<MedicalTypeDict.MedicalRecordTypeList[]>([]);

  const bizData = computed(() => {
    return selections.value.map((item) => {
      return item.medicalRecordTypeId || '';
    });
  });

  const selectionChange = (value: MedicalTypeDict.MedicalRecordTypeList[]) => {
    selections.value = value;
  };

  return {
    selections,
    bizData,
    selectionChange,
  };
}
