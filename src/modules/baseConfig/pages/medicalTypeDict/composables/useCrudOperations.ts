import { ref, Ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-sun';
import { useTranslation } from 'i18next-vue';
import { ENABLED_FLAG } from '@sun-toolkit/enums';
import { FLAG } from '../../../../../utils/constant';
import {
  addMedicalRecordType,
  deleteMedicalRecordType,
  saveMedicalRecordType,
} from '../../../api/medicalTypeDict';

export function useCrudOperations(
  medicalRecordTypeTableRef: Ref,
  medicalRecordTypeDialogRef: Ref,
  medicalRecordTypeList: Ref<MedicalTypeDict.MedicalRecordTypeList[]>,
  queryMedicalRecordType: () => Promise<void>,
) {
  const { t } = useTranslation();
  const loading = ref(false);

  const canUpsertTableRow = () => {
    const isEditing = medicalRecordTypeList.value.some(
      (item: MedicalTypeDict.MedicalRecordTypeList) => !!item.editable,
    );
    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    }
    return true;
  };

  const openAddNewMedicalDialog = () => {
    if (!canUpsertTableRow()) return;
    medicalRecordTypeDialogRef.value.open();
  };

  const handleSave = async (row: MedicalTypeDict.UpdateMedicalRecordType) => {
    medicalRecordTypeTableRef.value?.formRef.validate(
      async (tableValid: boolean) => {
        if (tableValid) {
          let result;
          if (row.medicalRecordTypeId) {
            const [, res] = await saveMedicalRecordType(row);
            result = res;
          } else {
            const [, res] = await addMedicalRecordType(row);
            result = res;
          }
          if (result?.success) {
            // toggleEdit(row); // This will be passed from parent
            queryMedicalRecordType();
          }
        }
      },
    );
  };

  const handleEdit = (row?: MedicalTypeDict.UpdateMedicalRecordType) => {
    if (!row) return;
    if (!canUpsertTableRow()) return;
    medicalRecordTypeDialogRef.value.open('edit', {
      ...row,
      editable: true,
    });
  };

  const handleEnableSwitch = async (
    row: MedicalTypeDict.UpdateMedicalRecordType,
  ) => {
    if (!canUpsertTableRow()) return;
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} "{{name}}" 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.medicalRecordTypeName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        ...row,
        enabledFlag: row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES,
      };
      loading.value = true;
      const [, res] = await saveMedicalRecordType(params);
      loading.value = false;
      if (res?.success) {
        row.enabledFlag = row.enabledFlag === FLAG.YES ? FLAG.NO : FLAG.YES;
        ElMessage.success(
          t(
            row.enabledFlag === FLAG.YES
              ? 'global:enabled.success'
              : 'global:disabled.success',
          ),
        );
      }
    });
  };

  const deleteItem = async (row: MedicalTypeDict.UpdateMedicalRecordType) => {
    if (!canUpsertTableRow()) return;
    ElMessageBox.confirm(
      t('delete.ask.title', '您确定要删除 "{{name}}" 吗？', {
        name: row.medicalRecordTypeName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      loading.value = true;
      const [, res] = await deleteMedicalRecordType({
        medicalRecordTypeId: row.medicalRecordTypeId,
      });
      loading.value = false;
      if (res?.success) {
        queryMedicalRecordType();
      }
    });
  };

  return {
    loading,
    openAddNewMedicalDialog,
    handleSave,
    handleEdit,
    handleEnableSwitch,
    deleteItem,
    canUpsertTableRow,
  };
}
