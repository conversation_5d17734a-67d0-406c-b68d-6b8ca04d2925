import { ENABLED_FLAG } from '@/utils/constant';
import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import { Ref } from 'vue';
import { SelectOptions } from '@/typings/common.ts';

export function useBizEventTableConfig(
  isCloudEnv: boolean | undefined,
  handleEnableSwitch: (data: BizEvent.BizEventInfo) => void,
  onOpenBizEventDialog: (mode: string, data: BizEvent.BizEventInfo) => void,
  onOpenMessageDialog: (data: BizEvent.BizEventInfo) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t('global.sequence', '顺序'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('bizEvent.bizEventTable.bizEventName', '业务事件名称'),
        prop: 'bizEventName',
        minWidth: 150,
      },
      {
        label: t('bizEvent.bizEventTable.bizEventDesc', '业务事件描述'),
        prop: 'bizEventDesc',
        minWidth: 180,
      },
      {
        label: t('global:spellNo'),
        prop: 'spellNo',
        minWidth: 150,
      },
      {
        label: t('global:wbNo'),
        prop: 'wbNo',
        minWidth: 150,
      },
      {
        label: t('global:secondName', '辅助名称'),
        prop: 'bizEvent2ndName',
        minWidth: 150,
      },
      {
        label: t('global:thirdName', '扩展名称'),
        prop: 'bizEventExtName',
        minWidth: 150,
      },
      {
        label: t('global:createTime'),
        prop: 'createdAt',
        minWidth: 170,
      },
      {
        label: t('global:lastModifiedTime'),
        prop: 'modifiedAt',
        minWidth: 170,
      },
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: BizEvent.BizEventInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 140,
        fixed: 'right',
        render: (row: BizEvent.BizEventInfo) => {
          return (
            <div class={'flex justify-around'}>
              <el-button
                type="primary"
                link={true}
                disabled={!isCloudEnv}
                onClick={() => onOpenBizEventDialog('edit', row)}
              >
                {t('global:edit')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => onOpenBizEventDialog('view', row)}
              >
                {t('global:view')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => onOpenMessageDialog(row)}
              >
                {t('bizEvent.bizEventTable.messageBtn', '消息')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
}

interface SelectContact {
  msgSendWayId: string;
  value: string;
  label: string;
}

export function useBizEventMessageTableConfig(options: {
  id: string;
  tableRef: Ref<TableRef>;
  data: Ref<(BizEvent.EventMsgSettingList & { editable: boolean })[]>;
  deleteRow: (row: BizEvent.EventMsgSettingList, index: number) => void;
  saveNewRow: (row: BizEvent.EventMsgSettingList) => void;
  msgSendWayList: Ref<SelectOptions[]>;
  contactInfoList: Ref<SelectContact[]>;
}) {
  const {
    tableRef,
    data,
    deleteRow,
    saveNewRow,
    msgSendWayList,
    contactInfoList,
  } = options;
  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef,
    data,
    id: 'eventMsgSettingId',
  });
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('bizEvent.bizEventTable.msgSendWayName', '发送渠道'),
        prop: 'msgSendWayName',
        minWidth: 150,
        editable: true,
        required: true,
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('appManage.appManageTable.contactNo', '发送渠道'),
        }),
        extraProps: {
          filterable: true,
          options: [],
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('appManage.appManageTable.contactNo', '发送渠道'),
            }),
            trigger: 'change',
          },
        ],
        render: (row: BizEvent.EventMsgSettingList) => {
          return row.editable ? (
            <el-select
              v-model={row.msgSendWayId}
              filterable
              placeholder={t('global:placeholder.select.template', {
                name: t('appManage.appManageTable.contactNo', '发送渠道'),
              })}
              onChange={(val: string) => {
                // 切换的时候清空已选择的号码
                row.contactNo = '';
                row.contactInfoId = '';
                row.msgSendWayName =
                  msgSendWayList?.value?.find((item) => item?.value === val)
                    ?.label || '';
              }}
            >
              {msgSendWayList?.value?.map((item) => (
                <el-option
                  key={item.value}
                  label={item.label}
                  value={item.value}
                />
              ))}
            </el-select>
          ) : (
            <>{row.msgSendWayName}</>
          );
        },
      },
      {
        label: t('bizEvent.bizEventTable.startTime', '开始时间'),
        prop: 'startTime',
        minWidth: 150,
        editable: true,
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('appManage.appManageTable.startTime', '开始时间'),
        }),
        required: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('appManage.appManageTable.startTime', '开始时间'),
            }),
            trigger: 'change',
          },
        ],
        render: (row: BizEvent.EventMsgSettingList) => {
          return row.editable ? (
            <el-time-picker
              v-model={row.startTime}
              format={'HH:mm'}
              value-format={'HH:mm'}
              type="datetime"
              placeholder={t('global:placeholder.select.template', {
                name: t('appManage.appManageTable.startTime', '开始时间'),
              })}
            />
          ) : (
            <>{row.startTime}</>
          );
        },
      },
      {
        label: t('bizEvent.bizEventTable.endTime', '结束时间'),
        prop: 'endTime',
        minWidth: 150,
        editable: true,
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('appManage.appManageTable.endTime', '结束时间'),
        }),
        required: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('appManage.appManageTable.endTime', '结束时间'),
            }),
            trigger: 'change',
          },
        ],
        render: (row: BizEvent.EventMsgSettingList) => {
          return row.editable ? (
            <el-time-picker
              v-model={row.endTime}
              format={'HH:mm'}
              value-format={'HH:mm'}
              type="datetime"
              placeholder={t('global:placeholder.select.template', {
                name: t('appManage.appManageTable.endTime', '结束时间'),
              })}
            />
          ) : (
            <>{row.endTime}</>
          );
        },
      },
      {
        label: t('bizEvent.bizEventTable.contactNo', '联系方式号码'),
        prop: 'contactNo',
        minWidth: 180,
        editable: true,
        required: true,
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('appManage.appManageTable.contactNo', '联系方式号码'),
        }),
        extraProps: {
          filterable: true,
          options: [],
        },
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('appManage.appManageTable.contactNo', '联系方式号码'),
            }),
            trigger: 'change',
          },
        ],
        render: (row: BizEvent.EventMsgSettingList) => {
          return row.editable ? (
            <el-select
              v-model={row.contactInfoId}
              filterable
              disabled={!row.msgSendWayId}
              placeholder={t('global:placeholder.select.template', {
                name: t('appManage.appManageTable.contactNo', '联系方式号码'),
              })}
              onChange={(val: string) => {
                row.contactNo =
                  contactInfoList?.value?.find((item) => item?.value === val)
                    ?.label || '';
              }}
            >
              {contactInfoList?.value
                ?.filter((item) => item.msgSendWayId === row.msgSendWayId)
                .map((item) => (
                  <el-option
                    key={item.value}
                    label={item.label}
                    value={item.value}
                  />
                ))}
            </el-select>
          ) : (
            <>{row.contactNo}</>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        fixed: 'right',
        width: 120,
        render: (
          row: BizEvent.EventMsgSettingList & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return row.editable ? (
            <div key="editable">
              <el-button
                onClick={() => cancelEdit(row, $index)}
                type="danger"
                link
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                onClick={(e: { preventDefault: () => void }) => {
                  e.preventDefault();
                  saveNewRow(row);
                }}
                type="primary"
                link
              >
                {t('global:save')}
              </el-button>
            </div>
          ) : (
            <div>
              <el-button
                onClick={(e: { preventDefault: () => void }) => {
                  e.preventDefault();
                  toggleEdit(row);
                }}
                type="primary"
                link
              >
                {t('global:edit')}
              </el-button>
              <el-button
                onClick={() => deleteRow(row, $index)}
                type="danger"
                link
              >
                {t('global:delete')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
}
