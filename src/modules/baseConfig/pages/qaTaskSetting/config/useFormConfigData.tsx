import { useFormConfig } from 'sun-biz';

export function useMrqaTaskSearchFormConfig() {
  const data = useFormConfig({
    getData: (t) => [
      {
        name: 'enabledFlag',
        label: t('priceFactorManage.form.groupFlag', '启用标志'),
        component: 'flagSelect',
        triggerModelChange: true,
        placeholder: t('global:placeholder.select.template', {
          name: t('priceFactorManage.form.enabledFlag', '启用标志'),
        }),
        extraProps: {
          className: 'w-64',
        },
      },
    ],
  });

  return data;
}
