import { Ref } from 'vue';
import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import {
  FLAG,
  MR_STATUS_CODE,
  QA_TASK_CODE,
} from '../../../../../utils/constant.ts';

interface SelectionModel {
  dataValueNo?: string;
  dataValueNameDisplay?: string;
}

interface MrqaTaskSettingItem {
  mrqaTaskSettingId: string;
  codeSystemId: string;
  dataValueId: string;
  dataValueNo: string;
  dataValueCnName: string;
}

export function useMrqaTaskConfig(
  id: string,
  tableRef: Ref<TableRef>,
  tableData: Ref<MrqaTask.UpsertMrqaTask[]>,
  handleSave: (
    row: MrqaTask.UpsertMrqaTask,
    mrStatusOptions?: SelectionModel[],
  ) => void,
  handleEdit: (row: MrqaTask.UpsertMrqaTask) => void,
  handleEnableSwitch: (row: MrqaTask.UpsertMrqaTask) => void,
  handleNeedModifySwitch: (row: MrqaTask.UpsertMrqaTask) => void,
  handleNeedApproveSwitch: (row: MrqaTask.UpsertMrqaTask) => void,
) {
  const { toggleEdit, cancelEdit, addItem } = useEditableTable({
    id,
    tableRef,
    data: tableData as unknown as Ref<
      { [key: string]: unknown; editable: boolean }[]
    >,
  });
  const data = useColumnConfig({
    dataSetCodes: [QA_TASK_CODE, MR_STATUS_CODE],

    getData: (t, dataCodes) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t('mrqaTask.table.mrqaTaskId', '质控任务标识'),
        prop: 'mrqaTaskId',
        minWidth: 120,
      },
      {
        label: t('mrqaTask.table.qaTaskCode', '质控任务'),
        prop: 'qaTaskCode',
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t('mrqaTask.table.qaTaskCode', '质控任务'),
            }),
          },
        ],
        minWidth: 150,
        render: (row: MrqaTask.UpsertMrqaTask) => {
          return row.editable ? (
            <el-select
              v-model={row.qaTaskCode}
              filterable={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('mrqaTask.table.qaTaskCode', '质控任务'),
              })}
              onChange={(val: string) => {
                row.qaTaskCode = val;
                const obj = dataCodes?.value
                  ? dataCodes.value[QA_TASK_CODE].find(
                      (item) => item.dataValueNo === val,
                    )
                  : undefined;
                row.qaTaskCodeDesc = obj?.dataValueNameDisplay as string;
              }}
            >
              {(dataCodes?.value ? dataCodes.value[QA_TASK_CODE] : [])?.map(
                (item: SelectionModel) => (
                  <el-option
                    value={item.dataValueNo}
                    key={item.dataValueNo}
                    label={item.dataValueNameDisplay}
                  ></el-option>
                ),
              )}
            </el-select>
          ) : (
            <>{row.qaTaskCodeDesc || '--'}</>
          );
        },
      },
      {
        label: t('mrqaTask.table.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        width: 150,
        editable: true,
        render: (row: MrqaTask.UpsertMrqaTask) => {
          return (
            <div class="flex w-full items-center justify-center">
              <el-switch
                modelValue={row.enabledFlag}
                inline-prompt
                active-value={FLAG.YES}
                inactive-value={FLAG.NO}
                before-change={() => handleEnableSwitch(row)}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
              />
            </div>
          );
        },
      },
      {
        label: t('mrqaTask.table.needModifyFlag', '需要整改标志'),
        prop: 'needModifyFlag',
        width: 150,
        editable: true,
        render: (row: MrqaTask.UpsertMrqaTask) => {
          return (
            <div class="flex w-full items-center justify-center">
              <el-switch
                modelValue={row.needModifyFlag}
                inline-prompt
                active-value={FLAG.YES}
                inactive-value={FLAG.NO}
                before-change={() => handleNeedModifySwitch(row)}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
              />
            </div>
          );
        },
      },
      {
        label: t('mrqaTask.table.needApproveFlag', '需要审核标志'),
        prop: 'needApproveFlag',
        width: 150,
        editable: true,
        render: (row: MrqaTask.UpsertMrqaTask) => {
          return (
            <div class="flex w-full items-center justify-center">
              <el-switch
                modelValue={row.needApproveFlag}
                inline-prompt
                active-value={FLAG.YES}
                inactive-value={FLAG.NO}
                before-change={() => handleNeedApproveSwitch(row)}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
              />
            </div>
          );
        },
      },
      {
        label: t('mrqaTask.table.mrStatusCodes', '病历状态'),
        prop: 'mrStatusCodes',
        editable: true,
        minWidth: 150,
        render: (row: MrqaTask.UpsertMrqaTask) => {
          const rowWithSettingList = row as MrqaTask.UpsertMrqaTask & {
            mrqaTaskSettingList?: MrqaTaskSettingItem[];
          };
          return row.editable ? (
            <el-select
              v-model={row.mrStatusCodes}
              filterable={true}
              multiple={true}
              placeholder={t('global:placeholder.select.template', {
                name: t('mrqaTask.table.mrStatusCodes', '病历状态'),
              })}
              onChange={(val: string[]) => {
                row.mrStatusCodes = val;
              }}
            >
              {(dataCodes?.value ? dataCodes.value[MR_STATUS_CODE] : [])?.map(
                (item: SelectionModel) => (
                  <el-option
                    value={item.dataValueNo}
                    key={item.dataValueNo}
                    label={item.dataValueNameDisplay}
                  ></el-option>
                ),
              )}
            </el-select>
          ) : (
            <>
              {rowWithSettingList.mrqaTaskSettingList &&
              rowWithSettingList.mrqaTaskSettingList.length > 0
                ? rowWithSettingList.mrqaTaskSettingList
                    .map((item: MrqaTaskSettingItem) => item.dataValueCnName)
                    .join('、')
                : row.mrStatusSettingList && row.mrStatusSettingList.length > 0
                  ? row.mrStatusSettingList.join('、')
                  : '--'}
            </>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 120,
        fixed: 'right',
        render: (row: MrqaTask.UpsertMrqaTask, $index: number) => {
          if (row.editable) {
            return (
              <div class={'flex justify-center'} key="editable">
                <el-button
                  type="danger"
                  link={true}
                  onClick={() => cancelEdit(row, $index)}
                >
                  {t('global:cancel')}
                </el-button>
                <el-button
                  type="primary"
                  link={true}
                  onClick={() => handleSave(row)}
                >
                  {t('global:save')}
                </el-button>
              </div>
            );
          } else {
            return (
              <>
                <div class={'flex justify-center'}>
                  <el-button
                    type="primary"
                    link={true}
                    onClick={() => handleEdit(row)}
                  >
                    {t('global:edit')}
                  </el-button>
                </div>
              </>
            );
          }
        },
      },
    ],
  });

  return { data, toggleEdit, addItem };
}
