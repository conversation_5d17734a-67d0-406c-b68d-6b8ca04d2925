<script lang="ts" setup>
  import { computed, onMounted, ref, Ref } from 'vue';
  import {
    DmlButton,
    MAIN_APP_CONFIG,
    ProForm,
    ProTable,
    Title,
    useAppConfigData,
    useFetchDataset,
  } from 'sun-biz';

  import { useTranslation } from 'i18next-vue';
  import { useMrqaTaskSearchFormConfig } from './config/useFormConfigData.tsx';
  import { useMrqaTaskConfig } from './config/useTableColumnConfig.tsx';
  import { ENABLED_FLAG } from '@sun-toolkit/enums';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import {
    addMrqaTask,
    editMrqaTask,
    queryMrqaTaskByExample,
  } from '../../api/qaTaskSetting.ts';
  import { BIZ_ID_TYPE_CODE, MR_STATUS_CODE } from '@/utils/constant.ts';

  interface MrqaTaskSettingItem {
    mrqaTaskSettingId?: string;
    codeSystemId?: string;
    dataValueId?: string;
    dataValueNo?: string;
    dataValueCnName?: string;
  }

  interface MrStatusSettingList {
    mrqaTaskSettingId?: string;
    codeSystemId?: string;
    dataValueId?: string;
    dataValueNo?: string;
    dataValueCnName?: string;
  }

  const { t } = useTranslation();
  const { isCloudEnv } = useAppConfigData([MAIN_APP_CONFIG.IS_CLOUD_ENV]);

  const dataSetList = useFetchDataset([MR_STATUS_CODE]);
  const selections = ref<MrqaTask.MrqaTaskList[]>([]);

  const bizData = computed(() => {
    const list = selections.value.map((item) => {
      return item.mrqaTaskId;
    });
    return list ?? [];
  });

  const mrqaTaskTableRef = ref();
  const loading = ref(false);

  const searchParams = ref<MrqaTask.QueryMrqaTaskByExample>({
    enabledFlag: ENABLED_FLAG.ALL,
  });
  const mrqaTaskList = ref<MrqaTask.MrqaTaskList[]>([]);
  // 搜索条件改变
  const modelChangeSearch = async (data?: MrqaTask.QueryMrqaTaskByExample) => {
    searchParams.value = {
      ...searchParams.value,
      ...data,
    };
    await queryMrqaTaskList();
  };

  const insertRow = () => {
    if (!canUpsertTableRow()) return;
    const newRow: MrqaTask.UpsertMrqaTask = {
      qaTaskCode: '',
      qaTaskCodeDesc: '',
      enabledFlag: ENABLED_FLAG.YES,
      needModifyFlag: ENABLED_FLAG.YES,
      needApproveFlag: ENABLED_FLAG.YES,
      mrStatusCodes: [],
      mrStatusSettingList: [],
      editable: true,
    };
    addItem(newRow);
  };
  const handleSave = async (row: MrqaTask.UpsertMrqaTask) => {
    let mrStatusOptions: MrStatusSettingList[] = [];
    if (dataSetList.value && dataSetList.value[MR_STATUS_CODE]?.length > 0) {
      mrStatusOptions = dataSetList.value[MR_STATUS_CODE].map(
        (item: MrStatusSettingList) => ({
          dataValueNo: item.dataValueNo || '',
          dataValueCnName: item.dataValueCnName || '',
          dataValueId: item.dataValueId || '',
          codeSystemId: item.codeSystemId || '',
        }),
      );
    }
    let submitRow = row;
    if (row.mrqaTaskId && row.mrStatusCodes && mrStatusOptions) {
      // 取原有的 mrqaTaskSettingList
      const oldList: MrqaTaskSettingItem[] =
        (
          row as MrqaTask.UpsertMrqaTask & {
            mrqaTaskSettingList?: MrqaTaskSettingItem[];
          }
        ).mrqaTaskSettingList || [];
      // 组装 mrStatusSettingList
      const mrStatusSettingList: MrStatusSettingList[] = row.mrStatusCodes.map(
        (code: string) => {
          const foundOption = mrStatusOptions.find(
            (item) => item.dataValueNo === code,
          );
          const old = oldList.find(
            (item: MrqaTaskSettingItem) => item.dataValueNo === code,
          );
          return {
            mrqaTaskSettingId: old ? old.mrqaTaskSettingId : undefined,
            codeSystemId: foundOption?.codeSystemId || '',
            dataValueId: foundOption?.dataValueId || '',
            dataValueNo: foundOption?.dataValueNo || '',
            dataValueCnName: foundOption?.dataValueCnName || '',
          } as MrStatusSettingList;
        },
      );
      submitRow = { ...row, mrStatusSettingList };
    }
    mrqaTaskTableRef.value?.formRef.validate(async (tableValid: boolean) => {
      if (tableValid) {
        let result;
        if (submitRow.mrqaTaskId) {
          const [, res] = await editMrqaTask(submitRow);
          result = res;
        } else {
          const [, res] = await addMrqaTask(submitRow);
          result = res;
        }
        if (result?.success) {
          toggleEdit(row);
          queryMrqaTaskList();
        }
      }
    });
  };

  const handleEdit = (row?: MrqaTask.UpsertMrqaTask) => {
    if (!row) return;
    if (!canUpsertTableRow()) return;
    // 兼容后端返回的 mrqaTaskSettingList
    const anyRow = row as MrqaTask.UpsertMrqaTask & {
      mrqaTaskSettingList?: MrqaTaskSettingItem[];
    };
    if (anyRow.mrqaTaskSettingList) {
      row.mrStatusCodes = anyRow.mrqaTaskSettingList.map(
        (item: MrqaTaskSettingItem) => item.dataValueNo,
      );
    } else if (row.mrStatusSettingList) {
      row.mrStatusCodes = row.mrStatusSettingList;
    }
    toggleEdit(row);
  };

  // 停启用切换
  const handleEnableSwitch = async (row: MrqaTask.UpsertMrqaTask) => {
    if (!canUpsertTableRow()) return;
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} "{{name}}" 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.qaTaskCodeDesc,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        ...row,
        enabledFlag:
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      loading.value = true;
      const [, res] = await editMrqaTask(params);
      loading.value = false;
      if (res?.success) {
        row.enabledFlag =
          row.enabledFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES;
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:enabled.success'
              : 'global:disabled.success',
          ),
        );
      }
    });
  };

  const handleNeedModifySwitch = async (row: MrqaTask.UpsertMrqaTask) => {
    if (!canUpsertTableRow()) return;
    ElMessageBox.confirm(
      t(
        'switch.ask.title',
        '您确定要 {{action}} "{{name}}" 的需要整改标志吗？',
        {
          action:
            row.needModifyFlag === ENABLED_FLAG.YES
              ? t('global:disabled')
              : t('global:enabled'),
          name: row.qaTaskCodeDesc,
        },
      ),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        ...row,
        needModifyFlag:
          row.needModifyFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      loading.value = true;
      const [, res] = await editMrqaTask(params);
      loading.value = false;
      if (res?.success) {
        row.needModifyFlag =
          row.needModifyFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES;
        ElMessage.success(
          t(
            row.needModifyFlag === ENABLED_FLAG.YES
              ? 'global:enabled.success'
              : 'global:disabled.success',
          ),
        );
      }
    });
  };
  const handleNeedApproveSwitch = async (row: MrqaTask.UpsertMrqaTask) => {
    if (!canUpsertTableRow()) return;
    ElMessageBox.confirm(
      t(
        'switch.ask.title',
        '您确定要 {{action}} "{{name}}" 的需要审核标志吗？',
        {
          action:
            row.needApproveFlag === ENABLED_FLAG.YES
              ? t('global:disabled')
              : t('global:enabled'),
          name: row.qaTaskCodeDesc,
        },
      ),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        ...row,
        needApproveFlag:
          row.needApproveFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES,
      };
      loading.value = true;
      const [, res] = await editMrqaTask(params);
      loading.value = false;
      if (res?.success) {
        row.needApproveFlag =
          row.needApproveFlag === ENABLED_FLAG.YES
            ? ENABLED_FLAG.NO
            : ENABLED_FLAG.YES;
        ElMessage.success(
          t(
            row.needApproveFlag === ENABLED_FLAG.YES
              ? 'global:enabled.success'
              : 'global:disabled.success',
          ),
        );
      }
    });
  };
  const handleSelectChange = (value: AppConfigSetting.AppConfigList[]) => {
    selections.value = value;
  };
  const searchConfig = useMrqaTaskSearchFormConfig();

  const {
    data: mrqaTaskColumns,
    toggleEdit,
    addItem,
  } = useMrqaTaskConfig(
    'mrqaTaskId',
    mrqaTaskTableRef,
    mrqaTaskList as Ref<MrqaTask.UpsertMrqaTask[]>,
    handleSave,
    handleEdit,
    handleEnableSwitch,
    handleNeedModifySwitch,
    handleNeedApproveSwitch,
  );
  const queryMrqaTaskList = async (data?: MrqaTask.QueryMrqaTaskByExample) => {
    loading.value = true;
    searchParams.value = {
      ...searchParams.value,
      ...data,
    };
    const [, res] = await queryMrqaTaskByExample({
      ...searchParams.value,
      enabledFlag:
        searchParams.value.enabledFlag === ENABLED_FLAG.ALL
          ? undefined
          : searchParams.value.enabledFlag,
    });
    loading.value = false;
    if (res?.success) {
      mrqaTaskList.value = (res.data as MrqaTask.UpsertMrqaTask[]) || [];
    }
  };
  const canUpsertTableRow = () => {
    const isEditing = mrqaTaskList.value.some(
      (item: MrqaTask.MrqaTaskList) => !!item.editable,
    );
    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    } else {
      return true;
    }
  };
  onMounted(() => {
    queryMrqaTaskList();
  });
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="t('mrqaTask.list.title', '质控任务')" />
    <div class="mt-3 flex justify-between">
      <div class="el-form-item">
        <ProForm
          ref="proForm"
          v-model="searchParams"
          :data="searchConfig"
          layout-mode="inline"
          @model-change="modelChangeSearch"
        />
        <el-button class="mr-2" plain type="primary" @click="queryMrqaTaskList">
          {{ t('mrqaTask.searchForm.search', '查询') }}
        </el-button>
        <el-button
          :disabled="!isCloudEnv"
          class="mr-2"
          type="primary"
          @click="insertRow"
        >
          {{ t('global:add') }}
        </el-button>
      </div>
      <div class="flex justify-between">
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_MRQA_TASK"
          class="mx-3"
          @success="
            () => {
              mrqaTaskTableRef?.proTableRef.clearSelection();
              selections = [];
            }
          "
        />
      </div>
    </div>
    <ProTable
      ref="mrqaTaskTableRef"
      :columns="mrqaTaskColumns"
      :data="mrqaTaskList"
      :editable="true"
      :loading="loading"
      row-key="mrqaTaskId"
      @selection-change="handleSelectChange"
    />
  </div>
</template>
