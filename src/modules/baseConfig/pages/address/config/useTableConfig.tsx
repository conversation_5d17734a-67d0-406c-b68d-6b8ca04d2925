import { ENABLED_FLAG } from '@/utils/constant';
import { useColumnConfig } from 'sun-biz';
import SecondaryAddressTable from '../components/SecondaryAddressTable.vue';

export function useAddressTableConfig(
  handleEnableSwitch: (data: Address.AddressInfo) => void,
  handleNativeFlagChange: (data: Address.AddressInfo) => void,
) {
  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 120,
        render: (row: Address.AddressInfo) => (
          <>
            {Number(row.addressLevelCode) > 1
              ? Number(row.sort) + 1
              : Number(row.sort)}
          </>
        ),
        fixed: 'left',
      },
      {
        label: t('address.addressTable.addressLevelDesc', '地址级别'),
        prop: 'addressLevelDesc',
        minWidth: 150,
      },
      {
        label: t('address.addressTable.addressNo', '地址编码'),
        prop: 'addressNo',
        width: 120,
      },
      {
        label: t('address.addressTable.addressName', '地址名称'),
        prop: 'addressName',
        minWidth: 180,
      },
      {
        label: t('global:secondName', '辅助名称'),
        prop: 'address2ndName',
        minWidth: 150,
      },
      {
        label: t('global:thirdName', '扩展名称'),
        prop: 'addressExtName',
        minWidth: 150,
      },
      {
        label: t('address.addressTable.postalNo', '邮政编码'),
        prop: 'postalNo',
        minWidth: 150,
      },
      // 是否开启
      {
        label: t('global:enabledFlag'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: Address.AddressInfo) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() => handleEnableSwitch(row)}
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t('address.addressTable.nativeFlag', '籍贯标识'),
        prop: 'nativeFlag',
        minWidth: 100,
        render: (row: Address.AddressInfo) => {
          return (
            <el-checkbox
              v-show={
                Number(row.addressLevelCode) === 2 ||
                Number(row.addressLevelCode) === 3
              }
              true-label={1}
              false-label={0}
              v-model={row.nativeFlag}
              onChange={() => handleNativeFlagChange(row)}
            />
          );
        },
      },
    ],
  });
}

export function useSecondaryAddressTableConfig(
  queryAddressData: () => void,
  handleEnableSwitch: (data: Address.AddressInfo) => void,
  handleNativeFlagChange: (data: Address.AddressInfo) => void,
) {
  return useColumnConfig({
    getData: (t) => {
      return [
        {
          type: 'expand',
          props: 'addressId',
          render: (row: Address.AddressInfo) => {
            return row.addressId ? (
              <div class={'px-2 py-1'}>
                <SecondaryAddressTable
                  rowValue={row}
                  queryAddressData={queryAddressData}
                  handleEnableSwitch={handleEnableSwitch}
                />
              </div>
            ) : (
              <span></span>
            );
          },
        },
        {
          label: t('global.sequence', '序号'),
          prop: 'sort',
          minWidth: 100,
          render: (row: object, $index: number) => <span>{$index + 1}</span>,
        },
        {
          label: t('address.addressTable.addressLevelDesc', '地址级别'),
          prop: 'addressLevelDesc',
          minWidth: 150,
        },
        {
          label: t('address.addressTable.addressNo', '地址编码'),
          prop: 'addressNo',
          minWidth: 180,
        },
        {
          label: t('address.addressTable.addressName', '地址名称'),
          prop: 'addressName',
          minWidth: 180,
        },
        {
          label: t('global:secondName', '辅助名称'),
          prop: 'address2ndName',
          minWidth: 100,
        },
        {
          label: t('global:thirdName', '扩展名称'),
          prop: 'addressExtName',
          minWidth: 100,
        },
        {
          label: t('address.addressTable.postalNo', '邮政编码'),
          prop: 'postalNo',
          minWidth: 150,
        },
        // 是否开启
        {
          label: t('global:enabledFlag'),
          prop: 'enabledFlag',
          minWidth: 100,
          render: (row: Address.AddressInfo) => {
            return (
              <el-switch
                modelValue={row.enabledFlag}
                inline-prompt
                active-value={ENABLED_FLAG.YES}
                inactive-value={ENABLED_FLAG.NO}
                before-change={() => handleEnableSwitch(row)}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
              />
            );
          },
        },
        // 籍贯标识
        {
          label: t('address.addressTable.nativeFlag', '籍贯标识'),
          prop: 'nativeFlag',
          minWidth: 100,
          render: (row: Address.AddressInfo) => {
            return (
              <el-checkbox
                v-show={
                  Number(row.addressLevelCode) === 2 ||
                  Number(row.addressLevelCode) === 3
                }
                true-label={1}
                false-label={0}
                v-model={row.nativeFlag}
                onChange={() => handleNativeFlagChange(row)}
              />
            );
          },
        },
      ];
    },
  });
}

export function useSecondaryAddressTableConfigNoExpand(
  handleEnableSwitch: (data: Address.AddressInfo) => void,
) {
  return useColumnConfig({
    getData: (t) => {
      return [
        {
          label: t('global.sequence', '序号'),
          prop: 'sort',
          minWidth: 100,
          render: (row: object, $index: number) => <>{$index + 1}</>,
        },
        {
          label: t('address.addressTable.addressLevelDesc', '地址级别'),
          prop: 'addressLevelDesc',
          minWidth: 150,
        },
        {
          label: t('address.addressTable.addressNo', '地址编码'),
          prop: 'addressNo',
          minWidth: 180,
        },
        {
          label: t('address.addressTable.addressName', '地址名称'),
          prop: 'addressName',
          minWidth: 180,
        },
        {
          label: t('global:secondName', '辅助名称'),
          prop: 'address2ndName',
          minWidth: 100,
        },
        {
          label: t('global:thirdName', '扩展名称'),
          prop: 'addressExtName',
          minWidth: 100,
        },
        {
          label: t('address.addressTable.postalNo', '邮政编码'),
          prop: 'postalNo',
          minWidth: 150,
        },
        // 是否开启
        {
          label: t('global:enabledFlag'),
          prop: 'enabledFlag',
          minWidth: 100,
          render: (row: Address.AddressInfo) => {
            return (
              <el-switch
                modelValue={row.enabledFlag}
                inline-prompt
                active-value={ENABLED_FLAG.YES}
                inactive-value={ENABLED_FLAG.NO}
                before-change={() => handleEnableSwitch(row)}
                active-text={t('global:enabled')}
                inactive-text={t('global:disabled')}
              />
            );
          },
        },
      ];
    },
  });
}
