<script lang="ts" name="address" setup>
  import { onMounted, ref } from 'vue';
  import { useTranslation } from 'i18next-vue';
  import { ProTable, Title } from 'sun-biz';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { ENABLED_FLAG } from '@/utils/constant.ts';
  import { useAddressTableConfig } from './config/useTableConfig.tsx';

  import {
    queryAddressListByExample,
    updateAddressById,
    updateAddressEnabledFlagById,
    updateAddressSortByIds,
  } from '@modules/baseConfig/api/address';

  const expandedRowKeys = ref<string[]>([]); // 展开行
  let draggableFlag = ref(true); //是否拖拽

  const addressList = ref<Address.AddressInfo[]>([]);
  const { t } = useTranslation();
  const searchParams = ref<Address.QueryParams>({
    keyWord: '',
    addressLevelCode: '1',
    addressId: '',
  });
  const loading = ref(false);
  const checkedKeys = ref<string[]>([]);
  const addressTableRef = ref();

  /** 查询地址列表 */
  async function queryAddressData(data?: Address.QueryParams) {
    loading.value = true;
    if (data?.keyWord) {
      searchParams.value = {
        ...searchParams.value,
        keyWord: data.keyWord,
        addressId: '',
        addressLevelCode: '',
      };
    } else {
      searchParams.value = {
        keyWord: '',
        addressLevelCode: '1',
        addressId: '',
      };
    }
    const params = {
      ...searchParams.value,
    };
    const [, res] = await queryAddressListByExample(params);
    loading.value = false;
    if (res?.success) {
      res.data.sort(
        (a: Address.AddressInfo, b: Address.AddressInfo) =>
          Number(a.sort) - Number(b.sort),
      );
      addressList.value = res.data.map((item: Address.AddressInfo) => ({
        ...item,
        hasChildren: Number(item.addressLevelCode) < 4, // 4层以下才有子节点
        parentAddressId: 0,
      }));
    }
  }

  /** 启用状态切换 */
  async function handleEnableSwitch(row: Address.AddressInfo) {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} "{{name}}" 吗？', {
        action:
          row.enabledFlag === ENABLED_FLAG.YES
            ? t('global:disabled')
            : t('global:enabled'),
        name: row.addressName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        addressId: row.addressId,
        enabledFlag: (row.enabledFlag === ENABLED_FLAG.YES
          ? ENABLED_FLAG.NO
          : ENABLED_FLAG.YES) as number,
      };
      const [, res] = await updateAddressEnabledFlagById(params);
      if (res?.success) {
        ElMessage.success(
          t(
            row.enabledFlag === ENABLED_FLAG.YES
              ? 'global:disabled.success'
              : 'global:enabled.success',
          ),
        );
        queryAddressData();
      }
    });
  }

  const loadChildren = async (
    row: Address.AddressInfo,
    treeNode: unknown,
    resolve: (children: Address.AddressInfo[]) => void,
  ) => {
    // 只查当前节点的下一级
    const params = {
      addressId: row.addressId,
      addressLevelCode: String(Number(row.addressLevelCode) + 1),
    };
    const [, res] = await queryAddressListByExample(params);
    if (res?.success) {
      const children = res.data.map((item: Address.AddressInfo) => ({
        ...item,
        hasChildren: Number(item.addressLevelCode) < 4, // 4层以下才有子节点
        parentAddressId: row.addressId,
      }));
      row.subAddressList = children;
      // setChildrenByRowKey(
      //   row.addressId,
      //   children,
      //   addressList.value,
      //   'subAddressList',
      // );
      resolve(children);
    } else {
      resolve([]);
    }
  };

  const handleExpandChange = async (
    _row: Address.AddressInfo,
    expanded?: boolean,
  ) => {
    if (expanded) {
      expandedRowKeys.value.push(_row.addressId);
    } else {
      _row.subAddressList = [];
      const index = expandedRowKeys.value.indexOf(_row.addressId);
      if (index > -1) {
        expandedRowKeys.value.splice(index, 1);
      }
    }

    let findObj = expandedRowKeys.value.find((item) => _row.addressId === item);
    console.log('handleExpand', _row, findObj);
    const params = {
      addressId: _row.addressId,
      addressLevelCode: String(Number(_row.addressLevelCode) + 1),
    };
    const [, res] = await queryAddressListByExample(params);

    if (res?.success) {
      const children = res.data.map((item: Address.AddressInfo) => ({
        ...item,
        hasChildren: Number(item.addressLevelCode) < 4, // 4层以下才有子节点
        parentAddressId: _row.addressId,
      }));

      setChildrenByRowKey(
        _row.addressId,
        children,
        addressList.value,
        'subAddressList',
      );
    }
  };

  /** 籍贯标识切换 */
  async function handleNativeFlagChange(row: Address.AddressInfo) {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要为 "{{name}}" {{action}}籍贯标识吗？', {
        action:
          row.nativeFlag === 1
            ? t('address.nativeFlagTrue', '设置')
            : t('address.nativeFlagFalse', '移除'),
        name: row.addressName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      const params = {
        ...row,
        nativeFlag: row.nativeFlag === 1 ? ENABLED_FLAG.YES : ENABLED_FLAG.NO,
      };

      const [, res] = await updateAddressById(params);
      if (res?.success) {
        row.nativeFlag = params.nativeFlag;
      } else {
        row.nativeFlag =
          row.nativeFlag === 1 ? ENABLED_FLAG.NO : ENABLED_FLAG.YES;
      }
    });
  }

  /** 拖拽排序 */
  const handleSortEnd = async (list: Address.AddressInfo[]) => {
    console.log('handleSortEnd', list);
    const arr: Address.UpdateSortParams[] = [];
    for (const item of list) {
      const index = list.indexOf(item);
      arr.push({
        addressId: item.addressId,
        sort: index + 1,
      });
    }
    await updateAddressSortByIds({ addressSortList: arr });

    await queryAddressData();
  };

  onMounted(() => {
    queryAddressData();
  });

  const tableColumnsConfig = useAddressTableConfig(
    handleEnableSwitch,
    handleNativeFlagChange,
  );

  // 工具函数：递归根据 rowKey 找到 addressList.value 里的真实对象并赋值 children。
  // 用法：setChildrenByRowKey(rowKey, children, addressList.value, 'addressId', 'subAddressList')
  function setChildrenByRowKey(
    rowKeyValue: string,
    children: Address.AddressInfo[],
    data: Address.AddressInfo[],
    childrenKey = 'subAddressList',
  ) {
    for (let i = 0; i < data.length; i++) {
      if (data[i]['addressId'] === rowKeyValue) {
        data[i][childrenKey] = children;
        return true;
      }
      if (Array.isArray(data[i][childrenKey])) {
        if (
          setChildrenByRowKey(
            rowKeyValue,
            children,
            data[i][childrenKey],
            childrenKey,
          )
        ) {
          return true;
        }
      }
    }
    return false;
  }
</script>
<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('address.list.title', '地址列表')" class="mb-2" />
    <div class="mb-4 mr-2.5 flex w-80">
      <el-input
        v-model="searchParams.keyWord"
        :placeholder="$t('global:placeholder.keyword', '请输入关键字查询')"
        class="mr-2"
        clearable
        prefix-icon="Search"
        @clear="
          () => {
            searchParams.keyWord = '';
            queryAddressData();
          }
        "
        @keyup.enter="() => queryAddressData({ keyWord: searchParams.keyWord })"
      ></el-input>
      <el-button
        v-if="!checkedKeys?.length"
        class="mr-2"
        icon="Search"
        type="primary"
        @click="
          () => {
            queryAddressData({ keyWord: searchParams.keyWord });
          }
        "
      >
        {{ $t('global:search', '查询') }}
      </el-button>
    </div>
    <ProTable
      ref="addressTableRef"
      :columns="tableColumnsConfig"
      :data="addressList"
      :default-expand-all="false"
      :draggable="draggableFlag"
      :expand-row-keys="expandedRowKeys"
      :highlight-current-row="true"
      :lazy="true"
      :load="loadChildren"
      :loading="loading"
      :tree-props="{ children: 'subAddressList', hasChildren: 'hasChildren' }"
      row-key="addressId"
      @expand-change="handleExpandChange"
      @drag-end="handleSortEnd"
    />
  </div>
</template>
