<script lang="ts" name="SecondaryAddressTable" setup>
  import { ref, watch } from 'vue';
  import { ProTable } from 'sun-biz';
  import {
    queryAddressListByExample,
    updateAddressById,
    updateAddressSortByIds,
  } from '@modules/baseConfig/api/address';
  import {
    useSecondaryAddressTableConfig,
    useSecondaryAddressTableConfigNoExpand,
  } from '../config/useTableConfig.tsx';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { ENABLED_FLAG } from '@/utils/constant.ts';
  import { useTranslation } from 'i18next-vue';

  const { t } = useTranslation();

  const loading = ref(false);
  const addressChildList = ref<Address.AddressInfo[]>([]);

  /** 元素ref */
  const secondaryAddressTableRef = ref();
  const addressLevelCode = ref<number>(0);
  const { rowValue, queryAddressData, handleEnableSwitch } = defineProps<{
    rowValue: Address.AddressInfo;
    queryAddressData: (row?: Address.AddressInfo) => void;
    handleEnableSwitch: (row: Address.AddressInfo) => void;
  }>();

  watch(
    () => rowValue,
    (newVal) => {
      if (newVal) {
        addressLevelCode.value = Number(newVal?.addressLevelCode);
        handleExpandRow(newVal);
      }
    },
    { immediate: true },
  );

  async function handleExpandRow(row: Address.AddressInfo) {
    loading.value = true;
    const params = {
      addressLevelCode: Number(row.addressLevelCode) + 1,
      addressId: row.addressId,
    };
    const [, res] = await queryAddressListByExample(params);
    loading.value = false;
    if (res?.success) {
      if (res.data.length > 0) {
        res.data.sort(
          (a: Address.AddressInfo, b: Address.AddressInfo) =>
            Number(a.sort) - Number(b.sort),
        );
      }
      addressChildList.value = res.data || [];
    }
  }

  const tableConfig =
    addressLevelCode.value === 1 || addressLevelCode.value === 2
      ? useSecondaryAddressTableConfig(
          queryAddressData,
          handleEnableSwitch,
          handleNativeFlagChange,
        )
      : useSecondaryAddressTableConfigNoExpand(handleEnableSwitch);

  /** 拖拽排序 */
  const handleSortEnd = async (list: Address.AddressInfo[]) => {
    const arr: Address.UpdateSortParams[] = [];
    for (const item of list) {
      const index = list.indexOf(item);
      arr.push({
        addressId: item.addressId,
        sort: index + 1,
      });
    }
    await updateAddressSortByIds({ addressSortList: arr });
    await queryAddressData();
    await handleExpandRow(rowValue);
  };

  /** 籍贯标识切换 */
  async function handleNativeFlagChange(row: Address.AddressInfo) {
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要为 “{{name}}” {{action}}籍贯标识吗？', {
        action:
          row.nativeFlag === 1
            ? t('address.nativeFlagTrue', '设置')
            : t('address.nativeFlagFalse', '移除'),
        name: row.addressName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        const params = {
          ...row,
          nativeFlag: row.nativeFlag === 1 ? ENABLED_FLAG.YES : ENABLED_FLAG.NO,
        };

        const [, res] = await updateAddressById(params);
        if (res?.success) {
          row.nativeFlag = params.nativeFlag;
          ElMessage.success(t('address.execute.success', '执行成功'));
        } else {
          row.nativeFlag =
            row.nativeFlag === 1 ? ENABLED_FLAG.NO : ENABLED_FLAG.YES;
        }
      })
      .catch(() => {
        row.nativeFlag =
          row.nativeFlag === 1 ? ENABLED_FLAG.NO : ENABLED_FLAG.YES;
      });
  }
</script>

<template>
  <ProTable
    ref="secondaryAddressTableRef"
    :columns="tableConfig"
    :data="addressChildList"
    :draggable="true"
    :is-show-drag-tips="false"
    drag-tips-class-name="drag-tips-class"
    row-key="addressId"
    @drag-end="handleSortEnd"
  />
</template>
<style lang="scss" scoped>
  .drag-tips-class {
    display: none !important;
  }
</style>
