<script lang="ts" name="addAttachDialog" setup>
  import { ref, watch } from 'vue';
  import { ProDialog, ProForm, ProTable } from 'sun-biz';
  import { ElMessage, ElMessageBox, FormInstance } from 'element-sun';
  import { useTranslation } from 'i18next-vue';
  import { useAddAttachDialogFormConfig } from '../config/useFormConfig.tsx';
  import { useFileUploadTableConfig } from '../config/useTableConfig.tsx';
  import AttachmentUpload from './AttachmentUpload/index.vue';
  import { Upload } from '@element-sun/icons-vue';
  import {
    addAttachManage,
    deleteFile,
  } from '@/modules/baseConfig/api/attachManage.ts';

  const { t } = useTranslation();
  const attachList = ref<AttachManage.AttachList[]>([]);
  const formRef = ref<{
    ref: FormInstance;
  }>();
  const dialogRef = ref();
  const attachFileUploadTableRef = ref();
  watch(
    () => attachList.value,
    (val) => {
      if (val?.length) {
        console.log(val, 'val');
        attachFileUploadTableRef.value?.setData(val);
      }
    },
  );

  const dialogForm = ref<AttachManage.AttachList>();
  const emits = defineEmits<{ success: [] }>();
  const onConfirm = async () => {
    return new Promise<[never, unknown]>((resolve, reject) => {
      Promise.all([
        formRef?.value?.ref.validateField(),
        attachFileUploadTableRef?.value?.formRef?.validateField(),
      ])
        .then(async () => {
          if (!attachList.value.every((item) => item.editable === false)) {
            ElMessage.warning(
              t(
                'attachManage.attachFileUploadTable.uploadFileTips',
                '请先完成当前表格的编辑',
              ),
            );

            reject(['', new Error('请先完成当前表格的编辑')]);
            return;
          }
          const params = {
            ...formRef?.value?.model,
            attachList: attachList.value,
          };
          const [, res] = await addAttachManage(params);
          if (res?.success) {
            emits('success');
            dialogRef.value.close();
            resolve([] as unknown as [never, unknown]);
          } else {
            reject(['', new Error('接口错误')]);
          }
        })
        .catch((error) => {
          console.log(error);
          reject(['', new Error('参数错误')]);
        });
    });
  };
  const initTable = () => {
    attachList.value = [];
  };
  const handelEditAttach = (row?: AttachManage.AttachList) => {
    if (!canUpsertTableRow()) return;
    toggleEdit(row);
  };
  const deleteAttachFile = (row?: AttachManage.AttachList, index: number) => {
    if (!canUpsertTableRow()) return;
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} “{{name}}” 吗？', {
        action: t('global:delete'),
        name: row.attachName,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    ).then(async () => {
      console.log(row, index, 'row');
      const [, res] = await deleteFile({
        filePosition: row.attachPosition,
        tips: true,
      });
      if (res?.success) {
        attachList.value.splice(index, 1);
      }
    });
  };
  const formConfig = useAddAttachDialogFormConfig();
  const { toggleEdit, tableColumns } = useFileUploadTableConfig({
    id: 'fileName',
    tableRef: attachFileUploadTableRef,
    data: attachList,
    deleteAttachFile,
    handelEditAttach,
  });
  const canUpsertTableRow = () => {
    const isEditing = attachList.value.some((item) => !!item.editable);
    if (isEditing) {
      ElMessage.warning(
        t(
          'storageRack.errorTip.toggleIsEditingWarning',
          '存在编辑状态中的信息，请先保存！',
        ),
      );
      return false;
    } else {
      return true;
    }
  };
  const handleClose = () => {
    console.log(attachList.value, 'attachList.value');
    if (attachList.value.length === 0) {
      dialogRef.value.close();
    } else {
      ElMessageBox.confirm(
        t(
          'switch.ask.title',
          '您确定要{{action}}吗？未保存的数据将全部清空！',
          {
            action: t('global:close'),
          },
        ),
        t('global:tip'),
        {
          confirmButtonText: t('global:confirm'),
          cancelButtonText: t('global:cancel'),
          type: 'warning',
        },
      ).then(async () => {
        attachList.value.forEach(async (row, index) => {
          const [, res] = await deleteFile({
            filePosition: row.attachPosition,
            tips: false,
          });
          if (res?.success) {
            attachList.value.splice(index, 1);
          }
        });
        dialogRef.value.close();
      });
    }
  };
  defineExpose({ dialogRef });
</script>

<template>
  <ProDialog
    ref="dialogRef"
    :align-center="true"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :confirm-fn="onConfirm"
    :include-footer="false"
    :title="`${$t('appConfigManage.AppConfigEnv', '新增附件')}`"
    :width="1000"
    destroy-on-close
    @open="initTable"
    @success="emits('success')"
  >
    <div class="mt-3 flex justify-start">
      <div class="el-form-item">
        <ProForm
          ref="formRef"
          v-model="dialogForm"
          :column="1"
          :data="formConfig"
        />
      </div>
      <AttachmentUpload
        v-model="attachList"
        :upload-btn-icon="Upload"
        class="ml-2 mr-2"
      />
      <el-button type="primary" @click="onConfirm"
        >{{ $t('global:save') }}
      </el-button>
      <el-button plain ype="primary" @click="handleClose"
        >{{ $t('global:close') }}
      </el-button>
    </div>
    <ProTable
      ref="attachFileUploadTableRef"
      :columns="tableColumns"
      :data="attachList"
      :editable="true"
    />
  </ProDialog>
</template>
