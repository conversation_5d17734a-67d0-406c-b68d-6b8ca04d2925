import { Ref } from 'vue';
import { dayjs } from '@sun-toolkit/shared';
import { useRouter } from 'vue-router';
import { useTranslation } from 'i18next-vue';
import { VALUE_TYPE_CODE, FULL_TYPE_CODE } from '../constant';
import { FLAG } from '@/utils/constant';
import { SelectOptions } from '@/typings/common';
import { useColumnConfig, useEditableTable, TableRef } from 'sun-biz';

export function useBizNoGenerateRuleTableConfig(
  handleEnableSwitch: (row: BizNoGenerateRule.BizNoGenerateRuleInfo) => void,
) {
  const router = useRouter();

  return useColumnConfig({
    getData: (t) => [
      {
        label: t('global:select'),
        prop: 'indexNo',
        type: 'selection',
      },
      {
        label: t('bizNoGenerateRule.table.bizNoObjectDesc', '业务编码对象'),
        prop: 'bizNoObjectDesc',
        minWidth: 120,
      },
      {
        label: t('bizNoGenerateRule.table.ruleName', '规则名称'),
        prop: 'ruleName',
        minWidth: 120,
      },
      {
        label: t('bizNoGenerateRule.table.enabledFlag', '启用状态'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: BizNoGenerateRule.BizNoGenerateRuleInfo) => (
          <el-switch
            modelValue={row.enabledFlag}
            inline-prompt
            active-value={FLAG.YES}
            inactive-value={FLAG.NO}
            active-text={t('global:enabled')}
            inactive-text={t('global:disabled')}
            before-change={() => handleEnableSwitch(row)}
          />
        ),
      },
      {
        label: t('bizNoGenerateRule.table.maxCharSize', '最大长度'),
        prop: 'maxCharSize',
        minWidth: 100,
      },
      {
        label: t('bizNoGenerateRule.table.modifiedUserName', '更新人'),
        prop: 'modifiedUserName',
        minWidth: 120,
      },
      {
        label: t('bizNoGenerateRule.table.modifiedAt', '更新时间'),
        prop: 'modifiedAt',
        minWidth: 150,
      },
      {
        label: t('bizNoGenerateRule.table.ruleDesc', '规则描述'),
        prop: 'ruleDesc',
        minWidth: 230,
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 130,
        render: (row: BizNoGenerateRule.BizNoGenerateRuleInfo) => {
          return (
            <>
              <el-button
                type="primary"
                link={true}
                onClick={() => {
                  router.push({
                    name: 'bizNoGenerateRuleDetail',
                    query: {
                      bizNoObjectCode: row.bizNoObjectCode,
                    },
                  });
                }}
              >
                {t('global:edit')}
              </el-button>
            </>
          );
        },
      },
    ],
  });
}

type BizNoFragmentTableRow = BizNoGenerateRule.BizNoFragment & {
  editable: boolean;
};

export function useBizNoFragmentTableConfig(
  tableRef: Ref<TableRef>,
  data: Ref<BizNoGenerateRule.BizNoFragment[]>,
  valueTypeCodeList: Ref<SelectOptions[]>,
  fullTypeCodeList: Ref<SelectOptions[]>,
  resetTypeCodeList: Ref<SelectOptions[]>,
  handleResetBizNoFragmentListSort: () => void,
) {
  const { t } = useTranslation();

  const { toggleEdit, cancelEdit, addItem, delItem } = useEditableTable({
    tableRef,
    data: data as unknown as Ref<
      (BizNoGenerateRule.BizNoFragment & { editable: boolean })[]
    >,
    id: 'bizNoFragmentId',
  });

  const onItemDelete = (index: number) => {
    delItem(index);
    handleResetBizNoFragmentListSort();
  };

  const onItemConfirm = (data: BizNoFragmentTableRow) => {
    toggleEdit(data);
  };

  const incStepValueRules = (row: BizNoFragmentTableRow) => [
    {
      validator: (
        rule: unknown,
        value: number,
        callback: (error?: Error | undefined) => void,
      ) => {
        if (row.valueTypeCode === VALUE_TYPE_CODE.NUMERIC_TYPE && !value) {
          return callback(
            new Error(
              t('global:placeholder.input.template', {
                content: t(
                  'bizNoGenerateRule.bizNoFragment.incStepValue',
                  '步长',
                ),
              }),
            ),
          );
        } else if (value && Number(value) > 9) {
          return callback(
            new Error(
              t(
                'bizNoGenerateRule.errorTip.incStepValueCannotGreaterThanNine',
                '步长值不能大于9',
              ),
            ),
          );
        }
        callback();
      },
      trigger: ['blur', 'change'],
    },
  ];

  const bizNoFragmentTableConfig = useColumnConfig({
    getData: (t) => [
      {
        label: t('global:sort'),
        minWidth: 60,
        prop: 'indexNo',
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t('bizNoGenerateRule.bizNoFragment.valueTypeCode', '数据类型'),
        prop: 'valueTypeCode',
        minWidth: 120,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.select.template', {
              name: t(
                'bizNoGenerateRule.bizNoFragment.valueTypeCode',
                '数据类型',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: BizNoFragmentTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.valueTypeCode}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'bizNoGenerateRule.bizNoFragment.valueTypeCode',
                      '数据类型',
                    ),
                  })}
                  onChange={(val: string) => {
                    const item = valueTypeCodeList.value.find(
                      (codeItem) => codeItem.value === val,
                    );
                    row.valueTypeCodeDesc = item?.label as string;
                    if (val === VALUE_TYPE_CODE.DATE_TYPE) {
                      row.currentValue = dayjs().format('YYYYMMDD');
                    } else if (val === VALUE_TYPE_CODE.TIME_TYPE) {
                      row.currentValue = dayjs().format('HHmmss');
                    } else if (val === VALUE_TYPE_CODE.DATETIME_TYPE) {
                      row.currentValue = dayjs().format('YYYYMMDDHHmmss');
                    }
                  }}
                >
                  {valueTypeCodeList.value?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.valueTypeCodeDesc}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('bizNoGenerateRule.bizNoFragment.currentValue', '当前值'),
        prop: 'currentValue',
        minWidth: 140,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t(
                'bizNoGenerateRule.bizNoFragment.currentValue',
                '当前值',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: BizNoFragmentTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.currentValue}
                  type={
                    row.valueTypeCode === VALUE_TYPE_CODE.NUMERIC_TYPE
                      ? 'number'
                      : 'text'
                  }
                  disabled={[
                    VALUE_TYPE_CODE.DATE_TYPE,
                    VALUE_TYPE_CODE.TIME_TYPE,
                    VALUE_TYPE_CODE.DATETIME_TYPE,
                  ].includes(row.valueTypeCode)}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'bizNoGenerateRule.bizNoFragment.currentValue',
                      '当前值',
                    ),
                  })}
                />
              ) : (
                <>{row.currentValue}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('bizNoGenerateRule.bizNoFragment.startValue', '初始值'),
        prop: 'startValue',
        minWidth: 140,
        editable: true,
        rules: (row: BizNoGenerateRule.BizNoFragment) => [
          {
            required: row.valueTypeCode === VALUE_TYPE_CODE.NUMERIC_TYPE,
            message: t('global:placeholder.input.template', {
              content: t(
                'bizNoGenerateRule.bizNoFragment.startValue',
                '初始值',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: BizNoFragmentTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.startValue}
                  type={
                    row.valueTypeCode === VALUE_TYPE_CODE.NUMERIC_TYPE
                      ? 'number'
                      : 'text'
                  }
                  disabled={row.valueTypeCode !== VALUE_TYPE_CODE.NUMERIC_TYPE}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'bizNoGenerateRule.bizNoFragment.startValue',
                      '初始值',
                    ),
                  })}
                />
              ) : (
                <>{row.startValue}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('bizNoGenerateRule.bizNoFragment.maxValue', '最大值'),
        prop: 'maxValue',
        minWidth: 140,
        editable: true,
        rules: (row: BizNoGenerateRule.BizNoFragment) => [
          {
            required: row.valueTypeCode === VALUE_TYPE_CODE.NUMERIC_TYPE,
            message: t('global:placeholder.input.template', {
              content: t('bizNoGenerateRule.bizNoFragment.maxValue', '最大值'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: BizNoFragmentTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.maxValue}
                  type={
                    row.valueTypeCode === VALUE_TYPE_CODE.NUMERIC_TYPE
                      ? 'number'
                      : 'text'
                  }
                  disabled={row.valueTypeCode !== VALUE_TYPE_CODE.NUMERIC_TYPE}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'bizNoGenerateRule.bizNoFragment.maxValue',
                      '最大值',
                    ),
                  })}
                />
              ) : (
                <>{row.maxValue}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('bizNoGenerateRule.bizNoFragment.incStepValue', '步长'),
        prop: 'incStepValue',
        minWidth: 120,
        editable: true,
        rules: incStepValueRules,
        render: (row: BizNoFragmentTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input-number
                  v-model={row.incStepValue}
                  disabled={row.valueTypeCode !== VALUE_TYPE_CODE.NUMERIC_TYPE}
                  min={0}
                  controls-position={'right'}
                  style={{ width: '130px' }}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'bizNoGenerateRule.bizNoFragment.incStepValue',
                      '步长',
                    ),
                  })}
                />
              ) : (
                <>{row.incStepValue}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('bizNoGenerateRule.bizNoFragment.fullTypeCode', '补位方式'),
        prop: 'fullTypeCode',
        minWidth: 120,
        editable: true,
        rules: (row: BizNoGenerateRule.BizNoFragment) => [
          {
            required: row.valueTypeCode === VALUE_TYPE_CODE.NUMERIC_TYPE,
            message: t('global:placeholder.select.template', {
              name: t(
                'bizNoGenerateRule.bizNoFragment.fullTypeCode',
                '补位方式',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: BizNoFragmentTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.fullTypeCode}
                  disabled={row.valueTypeCode !== VALUE_TYPE_CODE.NUMERIC_TYPE}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'bizNoGenerateRule.bizNoFragment.fullTypeCode',
                      '补位方式',
                    ),
                  })}
                  onChange={(val: string) => {
                    const item = fullTypeCodeList.value.find(
                      (codeItem) => codeItem.value === val,
                    );
                    row.fullTypeCodeDesc = item?.label as string;
                  }}
                >
                  {fullTypeCodeList.value?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.fullTypeCodeDesc}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('bizNoGenerateRule.bizNoFragment.fullChar', '补位符'),
        prop: 'fullChar',
        minWidth: 100,
        editable: true,
        rules: (row: BizNoGenerateRule.BizNoFragment) => [
          {
            required:
              row.valueTypeCode === VALUE_TYPE_CODE.NUMERIC_TYPE &&
              [
                FULL_TYPE_CODE.LEFT_PADDING,
                FULL_TYPE_CODE.RIGHT_PADDING,
              ].includes(row.fullTypeCode),
            message: t('global:placeholder.input.template', {
              content: t('bizNoGenerateRule.bizNoFragment.fullChar', '补位符'),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: BizNoFragmentTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-input
                  v-model={row.fullChar}
                  disabled={row.valueTypeCode !== VALUE_TYPE_CODE.NUMERIC_TYPE}
                  maxlength={1}
                  placeholder={t('global:placeholder.input.template', {
                    content: t(
                      'bizNoGenerateRule.bizNoFragment.fullChar',
                      '补位符',
                    ),
                  })}
                />
              ) : (
                <>{row.fullChar}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('bizNoGenerateRule.bizNoFragment.resetTypeCode', '重置方式'),
        prop: 'resetTypeCode',
        minWidth: 100,
        editable: true,
        rules: (row: BizNoGenerateRule.BizNoFragment) => [
          {
            required: row.valueTypeCode === VALUE_TYPE_CODE.NUMERIC_TYPE,
            message: t('global:placeholder.select.template', {
              name: t(
                'bizNoGenerateRule.bizNoFragment.resetTypeCode',
                '重置方式',
              ),
            }),
            trigger: ['blur', 'change'],
          },
        ],
        render: (row: BizNoFragmentTableRow) => {
          return (
            <div class={'w-full'}>
              {row.editable ? (
                <el-select
                  v-model={row.resetTypeCode}
                  disabled={row.valueTypeCode !== VALUE_TYPE_CODE.NUMERIC_TYPE}
                  placeholder={t('global:placeholder.select.template', {
                    name: t(
                      'bizNoGenerateRule.bizNoFragment.resetTypeCode',
                      '重置方式',
                    ),
                  })}
                  onChange={(val: string) => {
                    const item = resetTypeCodeList.value.find(
                      (codeItem) => codeItem.value === val,
                    );
                    row.resetTypeCodeDesc = item?.label as string;
                  }}
                >
                  {resetTypeCodeList.value?.map((item) => (
                    <el-option
                      key={item.value}
                      label={item.label}
                      value={item.value}
                    />
                  ))}
                </el-select>
              ) : (
                <>{row.resetTypeCodeDesc}</>
              )}
            </div>
          );
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        minWidth: 100,
        render: (row: BizNoFragmentTableRow, $index: number) => {
          return row.editable ? (
            <div class={'flex justify-around'} key="editable">
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index, false)}
              >
                {t('global:cancel')}
              </el-button>
              <el-button
                type="primary"
                link={true}
                onClick={() => onItemConfirm(row)}
              >
                {t('global:confirm')}
              </el-button>
            </div>
          ) : (
            <div class={'flex justify-around'}>
              <el-button
                type="primary"
                link={true}
                onClick={() => toggleEdit(row)}
              >
                {t('global:edit')}
              </el-button>
              <el-button
                type="danger"
                link={true}
                onClick={() => onItemDelete($index)}
              >
                {t('global:delete')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return {
    bizNoFragmentTableConfig,
    addItem,
    delItem,
  };
}
