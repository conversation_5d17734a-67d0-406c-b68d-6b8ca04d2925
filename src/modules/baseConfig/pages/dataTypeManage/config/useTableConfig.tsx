import { TableRef, useColumnConfig, useEditableTable } from 'sun-biz';
import { Ref, ref } from 'vue';
import { ENABLED_FLAG } from '@sun-toolkit/enums';
import { DT_USAGE_SCOPE_CODE } from '@/utils/constant.ts';

export function useDataTypeManageTableConfig(options: {
  id: string;
  tableRef: Ref<TableRef>;
  data: Ref<DataTypeManage.DataTypeManageList[]>;
  saveRow: (
    row: DataTypeManage.UpsertDataTypeManageParams,
    index: number,
  ) => Promise<void>;
  handleEnableSwitch: (
    row: DataTypeManage.UpsertDataTypeManageParams,
  ) => Promise<void>;
  deleteDataTypeManage: (
    row: DataTypeManage.UpsertDataTypeManageParams,
  ) => Promise<void>;
  isCloudEnv: boolean | undefined;
  handleEdit: (row: DataTypeManage.UpsertDataTypeManageParams) => void;
  mode: string | undefined;
}) {
  const {
    id,
    tableRef,
    data,
    saveRow,
    handleEnableSwitch,
    deleteDataTypeManage,
    isCloudEnv,
    handleEdit,
    mode,
  } = options;
  const { toggleEdit, cancelEdit } = useEditableTable({
    tableRef,
    data: data as unknown as Ref<
      (DataTypeManage.DataTypeManageList & { editable: boolean })[]
    >,
    id,
  });
  const tableColumns = useColumnConfig({
    dataSetCodes: [DT_USAGE_SCOPE_CODE],
    getData: (t, dataSet) => [
      {
        prop: 'selection',
        editable: false,
        type: 'selection',
      },
      {
        label: t('global:sequenceNumber'),
        prop: 'indexNo',
        minWidth: 60,
        render: (row: object, $index: number) => <>{$index + 1}</>,
      },
      {
        label: t(
          'dataTypeManage.dataTypeManageTable.dataTypeName',
          '数据类型名称',
        ),
        prop: 'dataTypeName',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('dataTypeManage.dataTypeName', '数据类型名称'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: DataTypeManage.DataTypeManageList & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.dataTypeName}
                placeholder={t('global:placeholder.input.template', {
                  content: t('dataTypeManage.dataTypeName', '数据类型名称'),
                })}
              />
            );
          } else {
            return <>{row.dataTypeName}</>;
          }
        },
      },
      {
        label: t(
          'dataTypeManage.dataTypeManageTable.dataTypeDesc',
          '数据类型描述',
        ),
        prop: 'dataTypeDesc',
        minWidth: 150,
        editable: true,
        rules: [
          {
            required: true,
            message: t('global:placeholder.input.template', {
              content: t('dataTypeManage.dataTypeDesc', '数据类型描述'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: DataTypeManage.DataTypeManageList & {
            editable: boolean;
          },
        ) => {
          if (row.editable) {
            return (
              <el-input
                v-model={row.dataTypeDesc}
                placeholder={t('global:placeholder.input.template', {
                  content: t('dataTypeManage.dataTypeDesc', '数据类型描述'),
                })}
              />
            );
          } else {
            return <>{row.dataTypeDesc}</>;
          }
        },
      },
      {
        label: t('dataTypeManage.dataTypeManageTable.enabledFlag', '启用标志'),
        prop: 'enabledFlag',
        minWidth: 100,
        render: (row: DataTypeManage.DataTypeManageList) => {
          return (
            <el-switch
              modelValue={row.enabledFlag}
              inline-prompt
              active-value={ENABLED_FLAG.YES}
              inactive-value={ENABLED_FLAG.NO}
              before-change={() =>
                handleEnableSwitch(
                  row as DataTypeManage.UpsertDataTypeManageParams,
                )
              }
              active-text={t('global:enabled')}
              inactive-text={t('global:disabled')}
            />
          );
        },
      },
      {
        label: t(
          'dataTypeManage.dataTypeManageTable.dtUsageScopeCodes',
          '应用范围',
        ),
        prop: 'dtUsageScopeCodes',
        minWidth: 150,
        editable: true,
        isHidden: mode.value === 'edit',
        rules: [
          {
            required: mode.value === 'add',
            message: t('global:placeholder.select.template', {
              name: t('dataTypeManage.dtUsageScopeCodes', '应用范围'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (row: DataTypeManage.DataTypeManageList) => {
          const dataSetList = dataSet?.value
            ? dataSet.value[DT_USAGE_SCOPE_CODE].map((item) => ({
                dataValueNo: item.dataValueNo,
                dataValueNameDisplay: item.dataValueNameDisplay,
                dtUsageScopeId: '',
              }))
            : [];
          if (row.editable) {
            return (
              <el-select
                v-model={row.dtUsageScopeCodes}
                clearable
                multiple
                filterable
                placeholder={t('global:placeholder.select.template', {
                  name: t(
                    'dataTypeManage.dataTypeManageTable.dtUsageScopeCodes',
                    '应用范围',
                  ),
                })}
              >
                {dataSetList.map((item) => (
                  <el-option
                    key={item.dataValueNo}
                    label={item.dataValueNameDisplay}
                    value={item.dataValueNo}
                  />
                ))}
              </el-select>
            );
          } else {
            return (
              <>
                {row.dtUsageScopeCodeList?.map(
                  (item) =>
                    item.dtUsageScopeCodeDesc && (
                      <div key={item.dtUsageScopeCode} class="mr-1">
                        {item.dtUsageScopeCodeDesc || '-'}
                      </div>
                    ),
                )}
              </>
            );
          }
        },
      },
      {
        label: t(
          'dataTypeManage.dataTypeManageTable.dtUsageScopeCodeList',
          '应用范围',
        ),
        prop: 'dtUsageScopeCodeList',
        minWidth: 150,
        editable: true,
        isHidden: mode.value === 'add',
        rules: [
          {
            required: mode.value === 'edit',
            message: t('global:placeholder.select.template', {
              name: t('dataTypeManage.dtUsageScopeCodeList', '应用范围'),
            }),
            trigger: ['change', 'blur'],
          },
        ],
        render: (
          row: DataTypeManage.DataTypeManageList & {
            editable: boolean;
          },
          $index: number,
        ) => {
          const dataSetList = dataSet?.value
            ? dataSet.value[DT_USAGE_SCOPE_CODE].map((item) => ({
                dataValueNo: item.dataValueNo,
                dataValueNameDisplay: item.dataValueNameDisplay,
                dtUsageScopeId: '',
              }))
            : [];
          if (row.editable) {
            const currentRow = options.data.value[$index];
            const selectedValues = ref(
              currentRow.dtUsageScopeCodeList?.map(
                (item) => item.dtUsageScopeCode || '',
              ) || [],
            );

            return (
              <el-select
                v-model={selectedValues.value}
                clearable
                multiple
                filterable
                onChange={(val: string[]) => {
                  const targetRow = options.data.value[$index];

                  if (!targetRow.dataTypeId) {
                    targetRow.dtUsageScopeCodeList = dataSetList
                      .filter((obj) => val.includes(obj.dataValueNo))
                      .map((item) => ({
                        dtUsageScopeCode: item.dataValueNo,
                        dtUsageScopeId: item.dtUsageScopeId,
                        dtUsageScopeCodeDesc: item.dataValueNameDisplay,
                      })) as DataTypeManage.DtUsageScopeCodeList[];
                  } else {
                    targetRow.dtUsageScopeCodeList = dataSetList
                      .filter((obj) => val.includes(obj.dataValueNo))
                      .map((item) => ({
                        dtUsageScopeCode: item.dataValueNo,
                        dtUsageScopeId:
                          targetRow.dtUsageScopeCodeList?.find(
                            (codeObj) =>
                              codeObj.dtUsageScopeCode === item.dataValueNo,
                          )?.dtUsageScopeId || undefined,
                        dtUsageScopeCodeDesc: item.dataValueNameDisplay,
                      }));
                  }
                }}
                placeholder={t('global:placeholder.select.template', {
                  name: t(
                    'dataTypeManage.dataTypeManageTable.dtUsageScopeCodeList',
                    '应用范围',
                  ),
                })}
              >
                {dataSetList.map((item) => (
                  <el-option
                    key={item.dataValueNo}
                    label={item.dataValueNameDisplay}
                    value={item.dataValueNo}
                  />
                ))}
              </el-select>
            );
          } else {
            return (
              <>
                {row.dtUsageScopeCodeList?.map(
                  (item) =>
                    item.dtUsageScopeCodeDesc && (
                      <div key={item.dtUsageScopeCode} class="mr-1">
                        {item.dtUsageScopeCodeDesc || '-'}
                      </div>
                    ),
                )}
              </>
            );
          }
        },
      },
      {
        label: t('global:operation'),
        prop: 'operation',
        width: 160,
        render: (
          row: DataTypeManage.UpsertDataTypeManageParams & {
            editable: boolean;
          },
          $index: number,
        ) => {
          return row.editable ? (
            <div class="flex items-center justify-around">
              <el-button
                type="primary"
                link={true}
                onClick={() => saveRow(row, $index)}
              >
                {t('global:save')}
              </el-button>
              <el-button
                type="danger"
                link={true}
                onClick={() => cancelEdit(row, $index)}
              >
                {t('global:cancel')}
              </el-button>
            </div>
          ) : (
            <div class="flex items-center justify-around">
              <el-button
                link={true}
                type={!isCloudEnv ? '' : 'primary'}
                disabled={!isCloudEnv}
                onClick={() => handleEdit(row)}
              >
                {t('global:edit')}
              </el-button>
              <el-button
                link={true}
                type={!isCloudEnv ? '' : 'danger'}
                disabled={!isCloudEnv}
                onClick={() => deleteDataTypeManage(row)}
              >
                {t('global:delete')}
              </el-button>
            </div>
          );
        },
      },
    ],
  });
  return { tableColumns, toggleEdit };
}
