<script lang="ts" name="holidaySetting" setup>
  import {
    AnyObject,
    DmlButton,
    ProForm,
    Title,
    useFetchDataset,
  } from 'sun-biz';
  import { computed, nextTick, onMounted, ref, watch } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-sun';
  import { dayjs } from '@sun-toolkit/shared';
  import { useTranslation } from 'i18next-vue';
  import xiu from './assets/xiu.png';
  import ban from './assets/ban.png';
  import {
    addHoliday,
    deleteHoliday,
    editHoliday,
    queryHolidayByExample,
  } from '@/modules/baseConfig/api/holidaySetting.ts';
  import { CodeSystemType } from '@/typings/codeManage.ts';
  import { useHolidaySettingFormConfig } from './config/useFormConfig.tsx';
  import { BIZ_ID_TYPE_CODE } from '@/utils/constant.ts';

  interface HolidaySettingInfoWithList extends HolidaySetting.HolidayInfo {
    holidaySettingList: HolidaySettingListWithId[];
  }

  interface HolidaySettingListWithId extends HolidaySetting.HolidaySettingList {
    holidayId: string;
  }

  interface HolidayEvent {
    holidayId?: string;
    holidaySettingId?: string;
    title: string;
    start: string;
    end?: string;
    allDay: boolean;
    holidayCode?: string;
    holidayTypeCode?: string;
    highlight?: {
      color: string;
      fillMode: string;
    };
    dot?: {
      key?: string | number;
      color?: string;
      class?: string | unknown[];
      style?: Record<string, unknown>;
    };
    bar?: {
      color: string;
    };
    textColor?: string;
    customData?: {
      holidayType: string;
    };
  }

  interface PageAddress {
    day?: number;
    week?: number;
    month: number;
    year: number;
  }

  interface CalendarAttribute {
    key?: string;
    highlight?: boolean | object;
    dates:
      | Date
      | Date[]
      | { start: Date | string; end: Date | string }
      | string;
    dot?:
      | {
          key?: string | number;
          color?: string;
          class?: string | unknown[];
          style?: Record<string, unknown>;
        }
      | boolean;
    popover?: { label: string; visibility?: string; customData?: unknown };
    bar?: { color: string } | boolean;
    content?: string;
    contentStyle?: {
      color?: string;
      background?: string;
      [key: string]: unknown;
    };

    [key: string]: unknown;
  }

  const { t } = useTranslation();
  const holidayFormRef = ref();
  const dataSetList = useFetchDataset([
    CodeSystemType.HOLIDAY_CODE,
    CodeSystemType.HOLIDAY_TYPE_CODE,
  ]);

  // 节假日类型颜色配置
  const holidayTypeColors = {
    '1': {
      highlight: {
        color: 'red', // 红色
        fillMode: 'light',
      },
      dot: {
        color: 'red',
        style: {
          width: '15px',
          height: '15px',
          border: 'none',
          position: 'absolute',
          top: '-7px',
          right: '-7px',
          backgroundColor: '#fff',
          backgroundSize: '15px 15px',
          backgroundImage: `url(${xiu})`,
        },
      },
    },
    '2': {
      highlight: {
        color: 'gray', // 灰色
        fillMode: 'light',
      },
      dot: {
        color: 'gray',
        style: {
          width: '15px',
          height: '15px',
          border: 'none',
          position: 'absolute',
          top: '-7px',
          right: '-7px',
          backgroundColor: '#fff',
          backgroundSize: '15px 15px',
          backgroundImage: `url(${ban})`,
        },
      },
    },
    default: {
      highlight: {
        color: 'gray', // 灰色
        fillMode: 'light',
      },
      dot: {
        color: 'gray',
        style: {
          width: '15px',
          height: '15px',
          border: 'none',
          position: 'absolute',
          top: '-7px',
          right: '-7px',
          backgroundColor: '#fff',
          backgroundSize: '15px 15px',
          backgroundImage: `url(${ban})`,
        },
      },
    },
  };

  let searchParams = ref<HolidaySetting.QueryHolidayParams>({
    holidayYear: dayjs().year(),
    deleteFlag: 0, // 只查询未删除的节假日
  }); // 查询参数
  let events = ref<HolidayEvent[]>([]); // 日历事件数据
  const loading = ref(false);

  const bizData = computed(() => {
    return events.value.map((item) => {
      return item.holidayId || '';
    });
  });
  // 日历配置
  const calendarConfig = ref({
    isExpanded: true,
    firstDayOfWeek: 0,
    rows: 3,
    columns: 4,
    showWeekNumbers: true,
    showDayPopover: true,
    navVisibility: true,
    masks: {
      weekdays: 'W',
      title: 'MMMM', // 只显示月份
    },
    titlePosition: 'top', // 标题位置
    popover: {
      visibility: 'hover-focus',
      isInteractive: true,
      placement: 'bottom',
    },
  });

  const calendar = ref();
  const showCalendar = ref(true);
  // 选中的日期
  const selectedDate = ref('');
  const currentYear = ref(dayjs().year()); // 当前年份

  const attributesConfig = ref<CalendarAttribute[]>([]);

  const pageAddress = ref<PageAddress>({
    year: searchParams.value.holidayYear || dayjs().year(),
    month: 1, // 从1月开始
  });

  // 弹窗相关
  const dialogVisible = ref(false);
  const selectedDay = ref('');
  const isEditMode = ref(false);
  const holidayForm = ref({
    holidayId: '',
    holidaySettingId: '',
    holidayCode: '',
    holidayTypeCode: '',
    dayDate: '',
    holidayYear: '',
    holidayMonth: '',
  });

  // 更新日历属性，包括事件标记
  const updateCalendarAttributes = () => {
    attributesConfig.value = [
      // 添加默认的 popover 属性，使每个日期都可以显示 popover
      {
        key: 'default-popover',
        dates: {
          start: dayjs(currentYear.value + '-01-01').format('YYYY-MM-DD'),
          end: dayjs(currentYear.value + '-12-31').format('YYYY-MM-DD'),
        },
        popover: {
          label: '',
          visibility: 'click',
          customData: null,
        },
      },
      // 添加节假日事件标记
      ...events.value.map((event, index) => ({
        key: `event-${index}-${event.holidayId || ''}`,
        dates: event.start,
        highlight: event.highlight,
        dot: {
          ...event.dot,
        },
        popover: {
          label: event.title,
          visibility: 'hover-focus',
          isInteractive: true,
          customData: event,
        },
      })),
    ];
  };

  // 查询联系方式列表
  const queryHolidaySettingListData = async (
    params?: HolidaySetting.QueryHolidayParams,
  ) => {
    loading.value = true;
    searchParams.value = {
      ...searchParams.value,
      ...params,
    };
    let [, res] = await queryHolidayByExample({
      ...searchParams.value,
    });
    loading.value = false;
    if (res?.success) {
      events.value = [];
      (res.data as HolidaySettingInfoWithList[]).forEach(
        (item: HolidaySettingInfoWithList) => {
          if (
            item.holidayYear &&
            item.holidayMonth &&
            item.holidaySettingList
          ) {
            item.holidaySettingList.forEach(
              (setting: HolidaySettingListWithId) => {
                if (setting.dayDate) {
                  const eventDate = dayjs()
                    .year(Number(item.holidayYear))
                    .month(Number(item.holidayMonth) - 1)
                    .date(Number(setting.dayDate));

                  const colors =
                    holidayTypeColors[
                      setting.holidayTypeCode as keyof typeof holidayTypeColors
                    ] || holidayTypeColors.default;

                  const event: HolidayEvent = {
                    holidayId: item.holidayId,
                    holidaySettingId: setting.holidaySettingId,
                    holidayCode: item.holidayCode,
                    holidayTypeCode: setting.holidayTypeCode,
                    title: `${item.holidayCodeDesc || ''}(${setting.holidayTypeCodeDesc})`,
                    start: eventDate.format('YYYY-MM-DD'),
                    allDay: true,
                    ...colors,
                    customData: {
                      holidayType: setting.holidayTypeCode || '',
                    },
                  };
                  events.value.push(event);
                }
              },
            );
          }
        },
      );
      // 更新日历属性
      updateCalendarAttributes();
    }
  };

  // 处理节假日事件点击
  // const handleEdit = (event: HolidayEvent) => {
  //   if (!event) return;
  //   isEditMode.value = true;
  //   const date = dayjs(event.start);
  //
  //   holidayForm.value = {
  //     holidayId: event.holidayId || '',
  //     holidaySettingId: event.holidaySettingId || '',
  //     holidayCode: event?.holidayCode || '',
  //     holidayTypeCode: event?.holidayTypeCode || '',
  //     dayDate: date.date().toString(),
  //     holidayYear: date.year().toString(),
  //     holidayMonth: (date.month() + 1).toString(),
  //   };
  //   selectedDay.value = event.start;
  //   dialogVisible.value = true;
  // };

  // 处理添加节假日
  const handleAddHoliday = (date: string) => {
    selectedDay.value =
      dayjs(date).format('YYYY-MM-DD') || dayjs().format('YYYY-MM-DD');
    isEditMode.value = false;
    const dayjsDate = dayjs(selectedDay.value);
    holidayForm.value = {
      holidayId: '',
      holidaySettingId: '',
      holidayCode: '',
      holidayTypeCode: '',
      dayDate: dayjsDate.date().toString(),
      holidayYear: dayjsDate.year().toString(),
      holidayMonth: (dayjsDate.month() + 1).toString(),
    };
    dialogVisible.value = true;
  };

  // 处理删除节假日
  const handleDelete = async (event: HolidayEvent) => {
    if (!event) return;
    ElMessageBox.confirm(
      t('switch.ask.title', '您确定要 {{action}} {{name}}吗？', {
        action: t('global:delete'),
        name: `${event.start} ${event.title}`,
      }),
      t('global:tip'),
      {
        confirmButtonText: t('global:confirm'),
        cancelButtonText: t('global:cancel'),
        type: 'warning',
      },
    )
      .then(async () => {
        const params = {
          holidayId: event.holidayId,
          holidaySettingId: event.holidaySettingId,
        };

        const [, res] = await deleteHoliday(params);
        if (res?.success) {
          ElMessage.success('删除成功');
          await queryHolidaySettingListData();
        } else {
          ElMessage.error('删除失败');
        }
      })
      .catch((error) => {
        if (error !== 'cancel') {
          console.error('删除节假日失败:', error);
        }
      });
  };

  // 保存节假日
  const saveHolidaySetting = async () => {
    holidayFormRef.value?.validate(async (valid: boolean) => {
      if (valid) {
        try {
          const params = {
            holidayId: holidayForm.value.holidayId,
            holidayCode: holidayForm.value.holidayCode,
            holidayYear: holidayForm.value.holidayYear,
            holidayMonth: holidayForm.value.holidayMonth,
            holidaySettingList: [
              {
                holidaySettingId: holidayForm.value.holidaySettingId,
                dayDate: holidayForm.value.dayDate,
                holidayTypeCode: holidayForm.value.holidayTypeCode,
              },
            ],
          };

          let res;
          if (isEditMode.value) {
            [, res] = await editHoliday(params);
          } else {
            [, res] = await addHoliday(params);
          }

          if (res?.success) {
            ElMessage.success(isEditMode.value ? '修改成功' : '保存成功');
            dialogVisible.value = false;
            await queryHolidaySettingListData();
          }
        } catch (error) {
          console.error('保存节假日失败:', error);
          ElMessage.error('保存失败');
        }
      }
    });
  };

  const searchConfig = useHolidaySettingFormConfig();
  onMounted(async () => {
    await queryHolidaySettingListData();
    // 更新日历视图
    pageAddress.value = {
      year: searchParams.value.holidayYear || dayjs().year(),
      month: 1,
    };
  });
  const handleYearChange = (year?: AnyObject) => {
    if (year && year.length > 0) {
      currentYear.value = year[0].year;
      searchParams.value.holidayYear = year[0].year;
    }
  };

  // 监听年份变化
  watch(
    () => searchParams.value.holidayYear,
    (newYear) => {
      if (newYear) {
        // 当表单模型变化时，重新查询数据
        showCalendar.value = false;
        currentYear.value = searchParams.value.holidayYear || dayjs().year();
        // 更新日历视图
        pageAddress.value = {
          year: searchParams.value.holidayYear || dayjs().year(),
          month: 1,
        };
        // 更新日历属性
        updateCalendarAttributes();

        queryHolidaySettingListData();

        nextTick(() => {
          showCalendar.value = true;
        });
      }
    },
    { immediate: true }, // 添加immediate: true确保初始化时也执行
  );

  const rules = {
    holidayTypeCode: [
      {
        required: true,
        message: t('global:placeholder.select.template', {
          name: t('holiday:dialog.holidayType', '节假日类型'),
        }),
        trigger: ['blur', 'change'],
      },
    ],
    holidayCode: [
      {
        required: true,
        message: t('global:placeholder.select.template', {
          name: t('holiday:dialog.holidayCode', '节假日性质'),
        }),
        trigger: ['blur', 'change'],
      },
    ],
  };
</script>

<template>
  <div class="p-box flex h-full flex-col">
    <Title :title="$t('holiday.title', '节假日设置')" />

    <div class="mt-3 flex justify-start">
      <ProForm
        ref="proForm"
        v-model="searchParams"
        :data="searchConfig"
        layout-mode="inline"
      />

      <div class="flex gap-2">
        <el-button type="primary" @click="queryHolidaySettingListData">
          {{ $t('global.search', '搜索') }}
        </el-button>
        <DmlButton
          :biz-data="bizData"
          :code="BIZ_ID_TYPE_CODE.DICT_HOLIDAY"
          class="mx-3"
          @success="() => {}"
        />
      </div>
    </div>

    <div class="mb-4 text-center text-2xl font-bold">{{ currentYear }}年</div>
    <el-scrollbar>
      <div class="mt-2 flex flex-col">
        <VCalendar
          v-if="showCalendar"
          ref="calendar"
          v-model="pageAddress"
          :attributes="attributesConfig"
          :columns="calendarConfig.columns"
          :expanded="calendarConfig.isExpanded"
          :first-day-of-week="calendarConfig.firstDayOfWeek"
          :masks="calendarConfig.masks"
          :nav-visibility="calendarConfig.navVisibility"
          :popover="calendarConfig.popover"
          :rows="calendarConfig.rows"
          :selected="selectedDate"
          :show-day-popover="calendarConfig.showDayPopover"
          :show-week-numbers="calendarConfig.showWeekNumbers"
          :title-position="calendarConfig.titlePosition"
          disable-page-swipe
          timezone="Asia/Shanghai"
          @update:pages="handleYearChange"
        >
          <template #day-popover="{ day, attributes }">
            <!--          <div class="px-2 py-1 text-sm" style="pointer-events: auto">-->
            <div class="px-2 py-1 text-sm">
              <div class="text-xs mt-1 text-gray-600">
                {{
                  dayjs(day.date).format(
                    `MM月DD日 星期${
                      ['日', '一', '二', '三', '四', '五', '六'][
                        dayjs(day.date).day()
                      ]
                    }`,
                  )
                }}
              </div>
              <div class="mt-1 space-y-2 text-sm">
                <div
                  v-if="
                    attributes.length > 0 && attributes[0].popover?.customData
                  "
                >
                  <div v-for="attr in attributes" :key="attr.key">
                    <div
                      v-if="attr.popover.label"
                      class="flex items-center justify-between rounded p-1 text-gray-700 hover:bg-gray-100"
                    >
                      <div class="flex w-40 items-center gap-2">
                        <span
                          :style="{
                            backgroundColor:
                              attr.highlight?.base.color || '#8c8c8c',
                          }"
                          class="h-2 w-2 rounded-full"
                        ></span>
                        <span class="text-xs">{{ attr.popover?.label }}</span>
                      </div>
                      <div class="flex items-end">
                        <!--                        <el-button-->
                        <!--                          v-if="-->
                        <!--                            attr.popover?.customData.holidayTypeCode === '2'-->
                        <!--                          "-->
                        <!--                          link-->
                        <!--                          size="small"-->
                        <!--                          type="primary"-->
                        <!--                          @click="handleEdit(attr.popover?.customData)"-->
                        <!--                        >-->
                        <!--                          编辑-->
                        <!--                        </el-button>-->
                        <el-button
                          link
                          size="small"
                          type="danger"
                          @click="handleDelete(attr.popover?.customData)"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
                <el-button
                  class="w-full justify-center text-sm"
                  link
                  type="primary"
                  @click="handleAddHoliday(day.date)"
                >
                  添加节假日
                </el-button>
              </div>
            </div>
          </template>
        </VCalendar>
      </div>
    </el-scrollbar>

    <!-- 节假日设置弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :close-on-click-modal="false"
      :title="isEditMode ? '编辑节假日' : '设置节假日'"
      width="500px"
    >
      <el-form
        ref="holidayFormRef"
        :model="holidayForm"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="当前日期">
          <el-date-picker
            v-model="selectedDay"
            disabled
            format="YYYY-MM-DD"
            placeholder="选择日期"
            type="date"
            value-format="YYYY-MM-DD"
            @change="
              (val: string) => {
                const date = dayjs(val);
                holidayForm.dayDate = date.date().toString();
                holidayForm.holidayYear = date.year().toString();
                holidayForm.holidayMonth = (date.month() + 1).toString();
              }
            "
          />
        </el-form-item>
        <el-form-item label="节假日类型" prop="holidayTypeCode">
          <el-select
            v-model="holidayForm.holidayTypeCode"
            filterable
            placeholder="请选择节假日类型"
          >
            <el-option
              v-for="item in dataSetList?.[CodeSystemType.HOLIDAY_TYPE_CODE] ||
              []"
              :key="item.dataValueNo"
              :label="item.dataValueNameDisplay"
              :value="item.dataValueNo"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="节假日性质" prop="holidayCode">
          <el-select
            v-model="holidayForm.holidayCode"
            filterable
            placeholder="请选择节假日性质"
          >
            <el-option
              v-for="item in dataSetList?.[CodeSystemType.HOLIDAY_CODE] || []"
              :key="item.dataValueNo"
              :label="item.dataValueNameDisplay"
              :value="item.dataValueNo"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveHolidaySetting">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
  .calendar-cell {
    border: 1px solid #e0e0e0;
  }

  .other-month {
    color: #999;
    background-color: #f5f5f5;
  }

  .calendar-event {
    padding: 2px 4px;
    margin: 1px 0;
    cursor: pointer;
    border-radius: 4px;
  }

  :deep(.vc-container) {
    --vc-accent-50: #f0f9ff;
    --vc-accent-100: #e0f2fe;
    --vc-accent-200: #bae6fd;
    --vc-accent-300: #7dd3fc;
    --vc-accent-400: #38bdf8;
    --vc-accent-500: #0ea5e9;
    --vc-accent-600: #0284c7;
    --vc-accent-700: #0369a1;
    --vc-accent-800: #075985;
    --vc-accent-900: #0c4a6e;

    border-radius: 4px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
  }

  :deep(.vc-day-content) {
    position: relative !important;
    font-weight: 500;
  }

  :deep(.vc-day-content:hover) {
    background-color: var(--vc-accent-100);
    border-radius: 4px;
  }

  :deep(.vc-day.is-today) {
    background-color: transparent !important;
  }

  :deep(.vc-day.is-today .vc-day-content) {
    font-weight: bold;
    color: #1890ff !important;
  }

  :deep(.vc-dots) {
    position: relative;
    width: 28px;
    height: 28px;
  }

  :deep(.vc-dota) {
    position: absolute;
    top: 0.5px;
    right: 2px;
    box-sizing: border-box;
    width: 5px;
    height: 5px;
    border-radius: 4px;
    box-shadow: 0 0 2px rgb(0 0 0 / 20%);
  }

  :deep(.vc-dota[data-holiday-type='1'])::after {
    position: absolute;
    top: -6px;
    left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12px;
    height: 12px;
    font-size: 10px;
    color: #fff;
    content: '休';
    background-image: url('./assets/xiu.png');
    border-radius: 4px;
  }

  :deep(.vc-dota[data-holiday-type='2'])::after {
    position: absolute;
    top: -6px;
    left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12px;
    height: 12px;
    font-size: 10px;
    color: #fff;
    content: '班';
    background-image: url('./assets/ban.png');
    border-radius: 4px;
  }

  :deep(.vc-bar) {
    height: 4px;
    border-radius: 4px;
  }

  :deep(.vc-day.is-selected) {
    background-color: transparent !important;
  }

  :deep(.vc-day.is-selected .vc-day-content) {
    padding: 2px 6px;
    font-weight: normal;
    color: inherit;
    background-color: var(--vc-accent-100);
    border-radius: 4px;
  }

  :deep(.vc-day .vc-highlights .vc-day-layer .vc-highlight-bg-light) {
    border-radius: 4px;
  }

  :deep(.vc-header) {
    padding: 10px 0;
    margin-bottom: 12px;
  }

  :deep(.vc-title) {
    font-size: 1.1em;
    font-weight: 500;
  }

  :deep(.vc-popover-content) {
    color: #000;
  }
  /* 只保留外部边框 */
  :deep(.vc-weeks) {
    margin: 4px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
  }

  :deep(.vc-week) {
    border-bottom: none;
  }

  :deep(.vc-day) {
    position: relative !important;
    border-right: none;
  }

  :deep(.is-not-in-month *) {
    color: #333;
    pointer-events: all;
    opacity: 0.6 !important;
  }
</style>
