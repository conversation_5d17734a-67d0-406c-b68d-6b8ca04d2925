declare namespace Receipt {
  interface VerifyInvoiceFlagAndConvertToAllotReqParams {
    receiptIds?: string[];
    invoiceUsageCode?: string;
    invoiceMediaTypeCode?: string;
  }

  interface VerifyInvoiceFlagAndConvertToAllotReqItem {
    receiptId: string;
    invoiceFlag: number;
    invoiceUsageCode?: string;
    invoiceMediaTypeCode?: string;
  }

  interface ReceiptReqParams {
    keyWord?: string;
    receiptIds?: string[];
    invoiceUsageCode?: string;
    invoiceMediaTypeCode?: string;
  }

  interface ReceiptReqItem {
    receiptId: string;
    receiptName: string;
    receipt2ndName?: string;
    receiptExtName?: string;
    receiptNameDisplay: string;
    dataSourceTypeCode: string;
    dataSourceTypeDesc: string;
    receiptSplitTypeCode: string;
    receiptSplitTypeDesc: string;
    printQty: number;
    receiptPrintRuleCode: string;
    receiptPrintRuleDesc: string;
    receiptPrintCategCode: string;
    receiptPrintCategDesc: string;
    redPrintCategCode: string;
    redPrintCategDesc: string;
    receiptTemplateRuleCode: string;
    receiptTemplateRuleDesc: string;
    printSetupEnabledFlag: number;
    designJson: string;
    invoiceInterfaceId?: string;
    invoiceInterfaceName?: string;
    printInterfaceId?: string;
    printInterfaceName?: string;
    receiptDataSourceId: string;
    spName: string;
    invoiceFlag: number;
    chooseReceiptTemplateRuleList?: {
      receiptTemplateRuleId: string;
      receiptTemplateName: string;
      enabledFlag: number;
      defaultFlag: number;
    }[];
    menuXReceiptList: {
      menuXReceiptId: string;
      menuId: string;
      menuName: string;
      receiptPrintCategCode: string;
      receiptPrintCategDesc: string;
      enabledFlag: number;
      sort: number;
    }[];
  }

  interface ChooseReceiptTemplateRuleItem {
    receiptTemplateRuleId: string;
    receiptTemplateName: string;
    enabledFlag: number;
    defaultFlag: number;
  }

  interface MenuXReceiptItem {
    menuXReceiptId: string;
    menuId: string;
    menuName: string;
    receiptPrintCategCode: string;
    receiptPrintCategDesc: string;
    enabledFlag: number;
    sort: number;
  }

  interface AddReceiptReqParams {
    receiptName: string;
    receipt2ndName?: string;
    receiptExtName?: string;
    dataSourceTypeCode: string;
    receiptSplitTypeCode: string;
    printQty: number;
    receiptPrintRuleCode: string;
    receiptPrintCategCode: string;
    redPrintCategCode: string;
    receiptTemplateRuleCode: string;
    printSetupEnabledFlag: number;
    invoiceInterfaceId?: string;
    printInterfaceId?: string;
    spName: string;
    designJson: string;
    chooseReceiptTemplateRuleList: {
      receiptTemplateName: string;
      enabledFlag: number;
      defaultFlag: number;
    }[];
    menuXReceiptList?: {
      menuId: string;
      receiptPrintCategCode: string;
      enabledFlag: number;
      sort: number;
    }[];
  }

  interface AddReceiptReqItem {
    receiptId: string;
    receiptTemplateRuleList: ReceiptTemplateRuleReqItem[];
  }

  interface ReceiptTemplateRuleReqItem {
    receiptTemplateName: string;
    receiptTemplateRuleId: string;
  }

  interface UpdateReceiptReqParams {
    receiptId: string;
    receiptName: string;
    receipt2ndName?: string;
    receiptExtName?: string;
    dataSourceTypeCode: string;
    receiptSplitTypeCode: string;
    printQty: number;
    receiptPrintRuleCode: string;
    receiptPrintCategCode: string;
    redPrintCategCode: string;
    receiptTemplateRuleCode: string;
    printSetupEnabledFlag: number;
    invoiceInterfaceId?: string;
    printInterfaceId?: string;
    receiptDataSourceId: string;
    spName: string;
    designJson: string;
    chooseReceiptTemplateRuleList: {
      receiptTemplateRuleId?: string;
      receiptTemplateName: string;
      enabledFlag: number;
      defaultFlag: number;
    }[];
    menuXReceiptList: {
      menuXReceiptId?: string;
      menuId: string;
      receiptPrintCategCode: string;
      enabledFlag: number;
      sort: number;
    }[];
  }

  interface MenuXReceiptReqParams {
    menuId: string;
    enabledFlag?: number;
    invoiceFlag?: number;
  }

  interface MenuXReceiptReqItem {
    menuId: string;
    menuName: string;
    menuXReceiptList?: {
      menuXReceiptId: string;
      receiptId: string;
      receiptNameDisplay: string;
      receiptPrintCategCode: string;
      receiptPrintCategDesc: string;
      enabledFlag: number;
      sort: number;
    }[];
  }

  interface SaveMenuXReceiptReqParams {
    menuId: string;
    menuXReceiptList?: {
      menuXReceiptId?: string;
      receiptId: string;
      receiptPrintCategCode: string;
      enabledFlag: number;
      sort: number;
    }[];
  }

  interface MenuXReceiptListReqItem {
    menuXReceiptId: string;
    receiptId: string;
    receiptNameDisplay: string;
    receiptPrintCategCode: string;
    receiptPrintCategDesc: string;
    enabledFlag: number;
    sort: number;
  }
}
