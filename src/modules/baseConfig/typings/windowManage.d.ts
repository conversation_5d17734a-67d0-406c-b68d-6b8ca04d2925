declare namespace WindowManage {
  interface SearchForm {
    enabledFlag?: number;
    openWindowFlag?: number;
    windowIds?: string[];
    windowServiceTypeCodes?: string[];
    machineIds?: string[];
    keyWord?: string;
    hospitalId?: string;
    pageSize?: number;
    pageNumber?: number;
    orgIds?: string | string[];
  }
  interface WindowServiceTypeItem {
    windowServiceTypeId: string;
    windowServiceTypeCode: string;
    windowServiceTypeDesc: string;
    orgName?: string;
    orgId?: string;
  }
  interface WindowComputerItem {
    windowComputerId: string;
    machineId: string;
    machineName: string;
  }
  interface TableItem {
    windowId: string;
    windowNo: string;
    windowName: string;
    spellNo: string;
    wbNo: string;
    enabledFlag: number;
    windowServiceTypeList: WindowServiceTypeItem[];
    windowComputerList: WindowComputerItem[];
    windowServiceBizUnitList: windowServiceBizItem[];
  }

  interface windowServiceBizItem {
    orgId: string;
    orgName: string;
    windowServiceBizUnitId?: string;
  }

  interface DialogForm {
    windowId?: string;
    windowNo: string;
    windowName: string;
    spellNo: string;
    wbNo: string;
    enabledFlag: number;
    windowServiceTypeCodes: string[];
    machineIds: string[];
    hospitalId: string;
    orgIds?: string[];
  }
  interface DialogLogTableItem {
    windowSwitchLogId: string;
    ipAddress: string;
    macAddress: string;
    openWindowFlag: number;
    modifiedUserName: string;
    modifiedAt: string;
  }
  interface windowDisRuleDtList {
    windowDisRuleDtId: string;
    bizSceneLimitConditionId: string;
    bizSceneLimitConditionCode: string;
    bizSceneLimitConditionDesc: string;
    valueTypeCode: string;
    valueTypeDesc: string;
    dataSearchBizIdTypeCode: string;
    dataSearchBizIdTypeDesc: string;
    codeSystemNo: string;
    codeSystemName: string;
    createdAt: string;
    symbolTypeCode: string;
    symbolTypeDesc: string;
    windowDisRuleDtValueList?: { value: string }[];
    selectedList?: string[];
  }
  interface WindowDisRule {
    windowDisRuleId?: string;
    windowServiceTypeCode?: string;
    windowServiceTypeDesc?: string;
    BizSourceCode?: string;
    windowId?: string;
    enabledFlag?: number;
    windowDisRuleDtList?: windowDisRuleDtList[];
  }

  interface AdditionalParameters {
    propsType?: { value: string; label: string };
    selectedList?: { [key: string]: string }[];
    multiCheckBoxOptions?: { [key: string]: string }[];
    windowDisRuleDtId?: string;
    symbolTypeCode?: string;
  }
  interface SaveWindowDis {
    windowId: string;
    windowDisRuleList: {
      windowDisRuleId?: string;
      windowServiceTypeCode?: string;
      enabledFlag?: number;
      windowDisRuleDtList?: {
        windowDisRuleDtId?: string;
        bizSceneLimitConditionId?: string;
        symbolTypeCode: string;
        windowDisRuleDtValueList?: {
          value?: string;
          windowDisRuleDtValueId?: string;
        }[];
      }[];
    }[];
  }
}
