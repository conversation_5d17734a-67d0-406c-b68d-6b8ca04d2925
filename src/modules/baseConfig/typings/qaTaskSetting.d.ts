declare namespace MrqaTask {
  // 查询地址请求参数
  interface QueryMrqaTaskByExample {
    qaTaskCode?: string;
    enabledFlag?: number;
  }

  // 返回值
  interface MrqaTaskList {
    mrqaTaskId?: string;
    qaTaskCode?: string;
    qaTaskCodeDesc?: string;
    enabledFlag?: number;
    needModifyFlag?: number;
    needApproveFlag?: number;
    mrqaTaskSettingList?: string[];
    mrStatusSettingList?: string[];
    mrStatusCodes?: string[];
    editable?: boolean;
  }

  // 更新
  interface UpsertMrqaTask {
    qaTaskCode?: string;
    enabledFlag?: number;
    qaTaskCodeDesc?: string;
    needModifyFlag?: number;
    needApproveFlag?: number;
    mrqaTaskId?: string;
    mrStatusCodes?: string[];
    mrqaTaskSettingList?: string[];
    mrStatusSettingList?: string[];
    editable?: boolean;
  }
}
