declare namespace BizEvent {
  interface QueryParams {
    keyWord?: string;
    enabledFlag?: number;
    bizEventIds?: string[];
    hospitalId?: string;
  }

  interface BizEventInfo {
    bizEventId: string;
    bizEventName: string;
    bizEvent2ndName: string;
    bizEventExtName: string;
    bizEventNameDisplay: string;
    bizEventDesc: string;
    spellNo: string;
    wbNo: string;
    enabledFlag: number;
  }

  interface UpsertEventParams {
    bizEventId?: string;
    bizEventName?: string;
    bizEvent2ndName?: string;
    bizEventExtName?: string;
    bizEventDesc?: string;
    spellNo?: string;
    wbNo?: string;
    enabledFlag?: number;
    eventMsgSettingList?: EventMsgSettingList[];
  }

  interface EventMsgSettingList {
    eventMsgSettingId?: string;
    msgSendWayId?: string;
    msgSendWayName?: string;
    contactInfoId?: string;
    contactNo?: string;
    editable?: boolean;
    bizEventId?: string;
    startTime: string;
    endTime?: string;
  }
}
