// 主表类型定义
export interface OrderDivideRule {
  /** 医嘱分方规则标识 */
  orderDivideRuleId: string;
  /** 医嘱分方规则编码 */
  orderDivideRuleNo: string;
  /** 医嘱分方规则名称 */
  orderDivideRuleName: string;
  /** 医嘱分方规则类型代码 */
  orderDivideRuleTypeCode: string;
  /** 医嘱分方规则类型代码描述 */
  orderDivideRuleTypeCodeDesc: string;
  /** 医嘱分方规则说明 */
  orderDivideRuleDesc: string;
  /** 启用标志 */
  enabledFlag: number;
  // 编辑
  editable: boolean;
  /** 分方规则内容列表 */
  divideRuleContentList: DivideRuleContent[];
}

// 主表 Omit 类型
export type OrderDivideRuleCreate = Omit<
  OrderDivideRule,
  'orderDivideRuleId' | 'orderDivideRuleTypeCodeDesc' | 'divideRuleContentList'
>;

export type OrderDivideRuleUpdate = Omit<
  OrderDivideRule,
  'orderDivideRuleTypeCodeDesc' | 'editable' | 'divideRuleContentList'
>;

// 主表相关接口参数类型
export interface OrderDivideRuleParams extends OrderDivideRuleCreate {
  /** 分方规则内容标识 */
  orderDivideRuleId?: string;
  // 医嘱分方规则类型代码
  orderDivideRuleTypeCodeDesc?: string;
}
export interface OrderDivideRuleContentResult {
  /** 分方规则内容标识 */
  orderDivideRuleId: string;
}
export interface delOrderDivideRuleParams {
  orderDivideRuleId: string;
}
export interface queryDivideRuleAndContentByExample {
  enabledFlag?: number;
}

// 内容表类型定义
export interface DivideRuleContent {
  divideRuleContentValueDesc: string;
  /** 分方规则内容标识 */
  divideRuleContentId: string;
  /** 分方规则内容值 */
  divideRuleContentValue: string;
  /** 启用标志 */
  enabledFlag: number;
  /** 启用标志 */
  editable: boolean;
}
// 内容表 Omit 类型 新增
export type DivideRuleContentCreate = Omit<
  DivideRuleContent,
  'divideRuleContentId'
>;

// 内容表相关接口参数类型
export interface addOrderDivideRuleContentParam
  extends DivideRuleContentCreate {
  orderDivideRuleId?: string;
}
export interface addOrderDivideRuleContentReq {
  divideRuleContentId: string;
}

export interface deleteOrderDivideRuleParam {
  divideRuleContentId: string;
}
