import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10137-1]根据条件查询接口列表
 * @param params
 * @returns
 */
export const queryInterfaceListByExample = (params: Interface.QueryParams) => {
  return dictRequest<Interface.InterfaceInfo[]>(
    '/interface/queryInterfaceListByExample',
    params,
  );
};

/**
 * [1-10138-1]新增接口
 * @param params
 * @returns
 */
export const addInterface = (params: InterfaceManage.AddInterface) => {
  return dictRequest('/interface/addInterface', params);
};

/**
 * [1-10139-1]修改接口
 * @param params
 * @returns
 */
export const updateInterfaceById = (
  params: InterfaceManage.UpdateInterface,
) => {
  return dictRequest('/interface/updateInterfaceById', params);
};

/**
 * [1-10140-1]根据条件查询组织列表（平铺)
 * @param params
 * @returns
 */
export const queryFlatOrgList = (params: {
  enabledFlag: number;
  orgTypeCodes: string[];
}) => {
  return dictRequest<InterfaceManage.OrganizationInfo[]>(
    '/organization/queryOrgListByExampleFlat',
    params,
  );
};

/**
 * [1-10197-1]根据标识停启用接口
 * @param params
 * @returns
 */
export const updateInterfaceEnabledFlagById = (
  params: Interface.QueryParams,
) => {
  return dictRequest<Interface.InterfaceInfo[]>(
    '/interface/updateInterfaceEnabledFlagById',
    params,
  );
};

/**
 * [1-10193-1]根据条件查询接口的交易列表
 * @param params
 * @returns
 */
export const queryTransactionListByInterfaceId = (params: {
  interfaceId: string;
}) => {
  return dictRequest<
    (InterfaceManage.InterfaceTransactionInfo & { editable: boolean })[]
  >('/interface/queryTransactionListByInterfaceId', params);
};

/**
 * [1-10189-1]根据条件查询接口的配置
 * @param params
 * @returns
 */
export const queryInterfaceSettingListByKeys = (params: {
  interfaceId: string;
  configKeys?: string[];
}) => {
  return dictRequest<
    (InterfaceManage.InterfaceConfigInfo & { editable: boolean })[]
  >('/interface/queryInterfaceSettingListByKeys', params);
};

/**
 * [1-10190-1]保存接口的配置
 * @param params
 * @returns
 */
export const saveInterfaceSetting = (params: {
  interfaceId: string;
  interfaceSettingList: InterfaceManage.InterfaceConfigInfo[];
}) => {
  return dictRequest('/interface/saveInterfaceSetting', params, {
    successMsg: translation('global:save.success', '保存成功'),
  });
};

/**
 * [1-10191-1]根据条件查询接口类型的交易列表
 * @param params
 * @returns
 */
export const queryTransactionListByInterfaceTypeCode = (params: {
  interfaceTypeCode: string;
}) => {
  return dictRequest<InterfaceManage.InterfaceTypeCodeTranInfo[]>(
    '/interface/queryTransactionListByInterfaceTypeCode',
    params,
  );
};

/**
 * [1-10194-1]保存接口的交易
 * @param params
 * @returns
 */
export const saveInterfaceTransaction = (params: {
  interfaceId: string;
  interfaceTransactionList: InterfaceManage.InterfaceTransactionInfo[];
}) => {
  return dictRequest('/interface/saveInterfaceTransaction', params);
};
