import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10414-1]根据条件查询窗口列表
 * @param params
 * @returns
 */
export const queryWindowList = (params: WindowManage.SearchForm) => {
  return dictRequest<WindowManage.TableItem[]>(
    '/window/queryWindowList',
    params,
  );
};
/**
 * [1-10416-1] [1-10417-1]新增/编辑修改窗口弹窗内容
 * @param params
 * @returns
 */
export const addAndUpdateWindow = (
  params: WindowManage.DialogForm,
  mode: boolean,
) => {
  return dictRequest(
    `/window/${mode ? 'addWindow' : 'updateWindowById'}`,
    params,
  );
};
/**
 * [1-10415-1]根据条件查询窗口开关窗日志
 * @param params
 * @returns
 */
export const queryWindowSwitchList = (params: {
  windowId: string;
  modifiedBeginAt?: string;
  modifiedEndAt?: string;
}) => {
  return dictRequest<WindowManage.DialogLogTableItem>(
    '/window/queryWindowSwitchList',
    params,
  );
};
/**
 * [1-10418-1]根据标识停启用窗口
 * @param params
 * @returns
 */
export const updateEnabledFlagById = (params: {
  windowId: string;
  enabledFlag: number;
}) => {
  return dictRequest('/window/updateEnabledFlagById', params);
};

/**
 * [1-10481-1]根据条件查询窗口分配规则列表
 * @param params
 * @returns
 */
export const queryWindowDisRuleListById = (params: {
  windowIds: string[];
  windowServiceTypeCode?: string;
}) => {
  return dictRequest<WindowManage.WindowDisRule[]>(
    '/window/queryWindowDisRuleListById',
    params,
  );
};

/**
 * [1-10482-1]保存窗口分配规则列表
 * @param params
 * @returns
 */
export const saveWindowDisRule = (params: WindowManage.SaveWindowDis) => {
  return dictRequest('/window/saveWindowDisRule', params);
};
