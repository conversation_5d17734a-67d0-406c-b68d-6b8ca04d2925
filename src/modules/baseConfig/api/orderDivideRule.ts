import { dictRequest } from '@sun-toolkit/request';
import {
  OrderDivideRuleCreate,
  OrderDivideRuleContentResult,
  delOrderDivideRuleParams,
  OrderDivideRuleParams,
  queryDivideRuleAndContentByExample,
  OrderDivideRule,
  addOrderDivideRuleContentParam,
  addOrderDivideRuleContentReq,
  DivideRuleContent,
  deleteOrderDivideRuleParam,
} from '../typings/orderDivideRule';

/**
 * [1-10509-1]根据条件新增消息发送渠道
 * @param params
 * @returns
 */
export const addOrderDivideRule = (params: OrderDivideRuleCreate) => {
  return dictRequest<OrderDivideRuleContentResult>(
    '/OrderDivideRule/addOrderDivideRule',
    params,
  );
};

/**
 * [1-10511-1]根据条件删除消息发送渠道
 * @param params
 * @returns
 */
export const deleteOrderDivideRuleById = (params: delOrderDivideRuleParams) => {
  return dictRequest<boolean>(
    '/OrderDivideRule/deleteOrderDivideRuleById',
    params,
  );
};

/**
 * [1-10510-1]编辑医嘱渠道
 * @param params
 * @returns
 */
export const updateOrderDivideRuleById = (params: OrderDivideRuleParams) => {
  return dictRequest<boolean>(
    '/OrderDivideRule/updateOrderDivideRuleById',
    params,
  );
};

/**
 * [1-10512-1]根据条件查询医嘱分方规则及内容
 * @param params
 * @returns
 */
export const queryOrderDivideRuleAndContentByExample = (
  params: queryDivideRuleAndContentByExample,
) => {
  return dictRequest<OrderDivideRule[]>(
    '/OrderDivideRule/queryOrderDivideRuleAndContentByExample',
    params,
  );
};

/**
 * [1-10513-1]新增医嘱分方规则内容
 * @param params
 * @returns
 */
export const addOrderDivideRuleContent = (
  params: addOrderDivideRuleContentParam,
) => {
  return dictRequest<addOrderDivideRuleContentReq>(
    '/OrderDivideRule/addOrderDivideRuleContent',
    params,
  );
};
/**
 * [1-10514-1]根据标识编辑医嘱分方规则内容
 * @param params
 * @returns
 */
export const updateOrderDivideRuleContentById = (params: DivideRuleContent) => {
  return dictRequest<boolean>(
    '/OrderDivideRule/updateOrderDivideRuleContentById',
    params,
  );
};

/**
 * [1-10515-1]根据标识编辑医嘱分方规则内容
 * @param params
 * @returns
 */
export const deleteOrderDivideRuleContentById = (
  params: deleteOrderDivideRuleParam,
) => {
  return dictRequest<boolean>(
    '/OrderDivideRule/deleteOrderDivideRuleContentById',
    params,
  );
};
