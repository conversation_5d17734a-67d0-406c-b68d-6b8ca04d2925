import { dictRequest } from '@sun-toolkit/request';
import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10497-1]根据条件查询质控任务
 * @param params
 * @returns
 */
export const queryMrqaTaskByExample = (
  params: MrqaTask.QueryMrqaTaskByExample,
) => {
  return dictRequest<MrqaTask.MrqaTaskList[]>(
    '/MrqaTask/queryMrqaTaskByExample',
    params,
  );
};

/**
 * [1-10498-1]新增质控任务
 * @param params
 * @returns
 */
export const addMrqaTask = (params: MrqaTask.UpsertMrqaTask) => {
  return dictRequest<{ mrqaTaskId?: string }>('/MrqaTask/addMrqaTask', params, {
    successMsg: translation('global:add.success'),
  });
};
/**
 * [1-10499-1]编辑质控任务
 * @param params
 * @returns
 */
export const editMrqaTask = (params: MrqaTask.UpsertMrqaTask) => {
  return dictRequest<{ mrqaTaskId?: string }>(
    '/MrqaTask/editMrqaTask',
    params,
    {
      successMsg: translation('global:edit.success'),
    },
  );
};
