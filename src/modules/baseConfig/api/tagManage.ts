import { dictRequest } from '@sun-toolkit/request';

/**
 * [1-10180-1]根据条件查询标签分组列表
 * @param params
 * @returns
 */
export const queryTagGroupListByExample = (
  params: TagManage.ReqQueryTagGroupList,
) => {
  return dictRequest<TagManage.TagGroup[]>(
    '/tag/queryTagGroupListByExample',
    params,
  );
};

/**
 * [1-10180-1]根据条件查询标签分组列表
 * @param params
 * @returns
 */
export const queryTagListByExample = (params: TagManage.ReqQueryTagList) => {
  return dictRequest<TagManage.TagInfo[]>('/tag/queryTagListByExample', params);
};

/**
 * [1-10180-1]新增标签分组
 * @param params
 * @returns
 */
export const addTagGroup = (params: TagManage.ReqAddTagGroup) => {
  return dictRequest<{
    tagGroupId: string;
  }>('/tag/addTagGroup', params);
};

/**
 * [1-10180-1]新增标签
 * @param params
 * @returns
 */
export const addTag = (params: TagManage.ReqAddTag) => {
  return dictRequest<{
    tagGroupId: string;
  }>('/tag/addTag', params);
};

/**
 * [1-10184-1]根据标识修改标签分组
 * @param params
 * @returns
 */
export const updateTagGroupById = (params: TagManage.ReqUpdateTagGroup) => {
  return dictRequest('/tag/updateTagGroupById', params);
};

/**
 * [1-10185-1]根据标识修改标签
 * @param params
 * @returns
 */
export const updateTagById = (params: TagManage.ReqUpdateTag) => {
  return dictRequest('/tag/updateTagById', params);
};

/**
 * [1-10186-1]根据标识修改标签的排序
 * @param params
 * @returns
 */
export const updateTagSortByIds = (params: TagManage.ReqUpdateTagSort) => {
  return dictRequest('/tag/updateTagSortByIds', params);
};

/**
 * [1-10187-1]根据标识停启用标签分组
 * @param params
 * @returns
 */
export const updateTagGroupEnabledFlagById = (
  params: TagManage.ReqUpdateTagGroupEnabledFlag,
) => {
  return dictRequest('/tag/updateTagGroupEnabledFlagById', params);
};

/**
 * [1-10188-1]根据标识停启用标签
 * @param params
 * @returns
 */
export const updateTagEnabledFlagById = (
  params: TagManage.ReqUpdateTagEnabledFlag,
) => {
  return dictRequest('/tag/updateTagEnabledFlagById', params);
};

/**
 * [1-10469-1]根据条件查询标签分组的应用范围
 * @param params
 * @returns
 */
export const queryTagGroupScopeByExample = (params: { tagGroupId: string }) => {
  return dictRequest<TagManage.TagGroupScopeInfo[]>(
    '/tag/queryTagGroupScopeByExample',
    params,
  );
};

/**
 * [1-10470-1]保存标签分组的应用范围
 * @param params
 * @returns
 */
export const saveTagGroupScope = (params: TagManage.ReqSaveTagGroupScope) => {
  return dictRequest('/tag/saveTagGroupScope', params);
};

/**
 * [1-10471-1]根据条件查询业务标识数据
 * @param params
 * @returns
 */
export const queryBizDataByExample = (params: {
  bizIdTypeCode: string;
  keyWord?: string;
}) => {
  return dictRequest<TagManage.BizIdDataInfo[]>(
    '/tag/queryBizDataByExample',
    params,
  );
};

/**
 * [1-10472-1]删除标签分组的应用范围
 * @param params
 * @returns
 */
export const deleteTagGroupScope = (params: {
  tagGroupId: string;
  bizIdTypeCode: string;
}) => {
  return dictRequest('/tag/deleteTagGroupScope', params);
};

/**
 * [1-10476-1]保存业务标签
 * @param params
 * @returns
 */
export const saveBizTag = (params: {
  bizIdTypeCode: string;
  bizId: string;
  bizTagList: {
    bizTagId?: string;
    tagId: string;
  }[];
}) => {
  return dictRequest('/biztag/saveBizTag', params);
};

/**
 * [1-10475-1]根据条件查询标签列表
 * @param params
 * @returns
 */
export const queryBizTagListByExample = (params: {
  bizIdTypeCode: string;
  bizId: string;
}) => {
  return dictRequest<TagManage.BizTagInfo[]>(
    '/biztag/queryBizTagListByExample',
    params,
  );
};
