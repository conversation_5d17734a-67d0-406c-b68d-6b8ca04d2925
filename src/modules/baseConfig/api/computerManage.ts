import { dictRequest } from '@sun-toolkit/request';

import { translation } from '@sun-toolkit/micro-app';

/**
 * [1-10255-1]根据条件查询计算机信息
 * @param params
 * @returns
 * computerManageLevelDesc 地址级别描述
 */
export const queryComputerManageListByExample = (
  params: ComputerManage.QueryParams,
) => {
  return dictRequest<ComputerManage.ComputerInfo[]>(
    '/machinemange/queryComputerByExample',
    params,
  );
};

/**
 * [1-10256-1] 新增计算机信息
 * @param params
 * @returns
 */
export const addComputer = (params: ComputerManage.addComputerParams) => {
  return dictRequest<{ computerManageId: string }>(
    '/machinemange/addComputer',
    params,
  );
};
/**
 * [1-10257-1] 编辑计算机信息
 * @param params
 * @returns
 */
export const editComputer = (params: ComputerManage.addComputerParams) => {
  return dictRequest<{ computerManageId: string }>(
    '/machinemange/editComputer',
    params,
  );
};
/**
 * [1-10257-1] 删除计算机信息
 * @param params
 * @returns
 */
export const deleteComputer = (params: ComputerManage.ComputerInfo) => {
  return dictRequest<ComputerManage.ComputerInfo>(
    '/machinemange/deleteComputer',
    params,
  );
};

/**
 * [1-10277-1]根据条件查询计算机指标
 * @param params
 * @returns
 */
export const queryComputerIndexByExample = (
  params: ComputerManage.ComputerIndexQueryParams,
) => {
  return dictRequest<ComputerManage.ComputerIndexInfo[]>(
    '/machinemange/queryComputerIndexByExample',
    params,
  );
};

/**
 * [1-10278-1] 新增计算机指标
 * @param params
 * @returns
 */
export const addComputerIndex = (
  params: ComputerManage.UpsertComputerIndexParams,
) => {
  return dictRequest<{ computerIndexId: string }>(
    '/machinemange/addComputerIndex',
    params,
  );
};
/**
 * [1-10279-1] 编辑计算机指标
 * @param params
 * @returns
 */
export const editComputerIndex = (
  params: ComputerManage.UpsertComputerIndexParams,
) => {
  return dictRequest<{ computerManageId: string }>(
    '/machinemange/editComputerIndex',
    params,
  );
};
/**
 * [1-10280-1] 删除计算机指标
 * @param params
 * @returns
 */
export const deleteComputerIndex = (params: { computerIndexId: string }) => {
  return dictRequest('/machinemange/deleteComputerIndex', params, {
    successMsg: translation('global:delete.success'),
  });
};

/**
 * [1-10396-1] 根据条件查询计算机的指标设置（定义态）
 * @param params
 * @returns
 */
export const queryComputerIndexSettingByExample = (params: {
  computerId: string;
}) => {
  return dictRequest(
    '/machinemange/queryComputerIndexSettingByExample',
    params,
  );
};

/**
 * [1-10397-1] 保存计算机的指标设置
 * @param params
 * @returns
 */
export const saveComputerIndexSetting = (
  params: ComputerManage.SaveComputerIndexSetting,
) => {
  return dictRequest('/machinemange/saveComputerIndexSetting', params);
};

/**
 * [4-10077-1] 保存计算机的指标设置
 * @param params
 * @returns
 */
export const addBizTag = (params: ComputerManage.SaveComputerIndexSetting) => {
  return dictRequest('/biztag/addBizTag', params);
};
/**
 * [4-10077-1] 保存计算机的指标设置
 * @param params
 * @returns
 */
export const deleteBizTagByExample = (
  params: ComputerManage.SaveComputerIndexSetting,
) => {
  return dictRequest('/biztag/deleteBizTagByExample', params);
};

/**
 * [1-10492-1] 根据条件查询计算机扩展信息
 * @param params
 * @returns
 */
export const queryComputerExtInfoByExample = (
  params: ComputerManage.queryComputeIndexSetting,
) => {
  return dictRequest<ComputerManage.queryComputeIndexInfo[]>(
    '/machinemange/queryComputerExtInfoByExample',
    params,
  );
};

/**
 * [1-10493-1] 根据条件查询计算机扩展信息
 * @param params
 * @returns
 */
export const saveComputerExtInfo = (
  params: ComputerManage.ComputerExtInfoParams,
) => {
  return dictRequest<boolean>('/machinemange/saveComputerExtInfo', params);
};
