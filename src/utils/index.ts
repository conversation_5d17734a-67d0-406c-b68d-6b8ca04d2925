import { SEX_CODE } from '@sun-toolkit/enums';

/**
 * @description:大陆18位身份证校验规则；以下为elementUI表单校验函数；
 * @params:[idcode]:身份证号码
 * @params:[callback]:回调函数；
 *
 */
export const validateID = (
  rule: unknown,
  idcode: string,
  callback: (error?: Error) => void,
) => {
  // 加权因子
  const weight_factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
  // 校验码
  const check_code = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

  const code = idcode + '';
  const last = idcode[17]; //最后一位

  const seventeen = code.substring(0, 17);

  // ISO 7064:1983.MOD 11-2
  // 判断最后一位校验码是否正确
  const arr = seventeen.split('');
  const len = arr.length;
  let num = 0;
  for (let i = 0; i < len; i++) {
    num = num + Number(arr[i]) * weight_factor[i];
  }

  // 获取余数
  const resisue = num % 11;
  const last_no = check_code[resisue];

  // 格式的正则
  const idcard_patter =
    /^[1-9][0-9]{5}([1][9][0-9]{2}|[2][0][0|1][0-9])([0][1-9]|[1][0|1|2])([0][1-9]|[1|2][0-9]|[3][0|1])[0-9]{3}([0-9]|[X])$/;

  // 判断格式是否正确
  const format = idcard_patter.test(idcode);

  // 返回验证结果，校验码和格式同时正确才算是合法的身份证号码
  // return last === last_no && format ? true : false;
  if (last === last_no && format) {
    callback();
  } else {
    callback(new Error('请输入正确的身份证号码'));
  }
};

/**
 * 根据身份证号获取性别
 * @param {string} idCard - 身份证号
 * @returns {string} - 性别（'男' 或 '女'）
 */
export function getSexCode(idCard: string) {
  if (!idCard) return '';
  const sexDigit = idCard.slice(-2, -1); // 获取倒数第二位
  return parseInt(sexDigit) % 2 === 1 ? SEX_CODE.MAN : SEX_CODE.WOMAN; // 奇数为男，偶数为女
}
