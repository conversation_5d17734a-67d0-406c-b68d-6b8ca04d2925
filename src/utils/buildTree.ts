export function buildTree(
  data: CodeRepositoryManageAPIDesign.ApiRecordParaList[],
): CodeRepositoryManageAPIDesign.ApiRecordParaList[] {
  const map: {
    [key: string]: CodeRepositoryManageAPIDesign.ApiRecordParaList;
  } = {};
  const tree: CodeRepositoryManageAPIDesign.ApiRecordParaList[] = [];

  // 先将所有节点存入 map 中
  data.forEach((item: CodeRepositoryManageAPIDesign.ApiRecordParaList) => {
    if (item.apiRecordParaId) {
      map[item.apiRecordParaId] = { ...item, children: [], level: 0 };
    }
  });

  // 构建树形结构
  data.forEach((item: CodeRepositoryManageAPIDesign.ApiRecordParaList) => {
    if (!item.apiRecordParaId) return;
    const current = map[item.apiRecordParaId];
    const parentId = item.apiRecordParaIdParent;
    if (parentId === null) {
      tree.push(current);
    } else if (parentId) {
      const parent = map[parentId];
      if (parent && parent.level !== undefined && parent.children) {
        current.level = parent.level + 1;
        parent.children.push(current);
      }
    }
  });

  // 递归排序函数
  function sortChildren(node: CodeRepositoryManageAPIDesign.ApiRecordParaList) {
    if (node.children && node.children.length > 0) {
      node.children.sort((a, b) => (a.sort ?? 0) - (b.sort ?? 0));
      node.children.forEach(sortChildren);
    }
  }

  // 对根节点数组进行排序
  tree.sort((a, b) => (a.sort ?? 0) - (b.sort ?? 0));
  // 递归对每个节点的子节点数组进行排序
  tree.forEach(sortChildren);

  return tree;
}

export function buildXTree(
  data: CodeRepositoryManageAPIDesign.ApiRecordParaList[],
): CodeRepositoryManageAPIDesign.ApiRecordParaList[] {
  const map: {
    [key: string]: CodeRepositoryManageAPIDesign.ApiRecordParaList;
  } = {};
  const tree: CodeRepositoryManageAPIDesign.ApiRecordParaList[] = [];

  // 先将所有节点存入 map 中
  data.forEach((item: CodeRepositoryManageAPIDesign.ApiRecordParaList) => {
    if (item.apiXParaId) {
      map[item.apiXParaId] = { ...item, children: [], level: 0 };
    }
  });

  // 构建树形结构
  data.forEach((item: CodeRepositoryManageAPIDesign.ApiRecordParaList) => {
    if (!item.apiXParaId) return;
    const current = map[item.apiXParaId];
    const parentId = item.apiXParaIdParent;
    if (parentId === null) {
      tree.push(current);
    } else if (parentId) {
      const parent = map[parentId];
      if (parent && parent.level !== undefined && parent.children) {
        current.level = parent.level + 1;
        parent.children.push(current);
      }
    }
  });

  // 递归排序函数
  function sortChildren(node: CodeRepositoryManageAPIDesign.ApiRecordParaList) {
    if (node.children && node.children.length > 0) {
      node.children.sort((a, b) => (a.sort ?? 0) - (b.sort ?? 0));
      node.children.forEach(sortChildren);
    }
  }

  // 对根节点数组进行排序
  tree.sort((a, b) => (a.sort ?? 0) - (b.sort ?? 0));
  // 递归对每个节点的子节点数组进行排序
  tree.forEach(sortChildren);

  return tree;
}
