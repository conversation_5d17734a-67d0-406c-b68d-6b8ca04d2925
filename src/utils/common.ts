import { ref } from 'vue';
import { ORG_TYPE_CODE } from '@/typings/common';
import { queryOrgList } from '@modules/system/api/org';

const hospitalSelections = ref<Promise<Org.Item[]>>();

export const getHospitalSelections = async () => {
  if (!hospitalSelections?.value) {
    hospitalSelections.value = Promise.resolve()
      .then(async () => {
        const arr = [] as Org.Item[];
        const [, res] = await queryOrgList({});
        if (res?.success && res.data?.length) {
          res.data.forEach((item: Org.Item) => {
            if (item.orgTypeCode === ORG_TYPE_CODE.HOSPITAL) {
              arr.push(item);
            } else if (item.orgTypeCode === ORG_TYPE_CODE.GROUP) {
              if (item.subOrgList?.length) {
                item.subOrgList.forEach((subItem: Org.Item) => {
                  if (subItem.orgTypeCode === ORG_TYPE_CODE.HOSPITAL) {
                    arr.push(subItem);
                  }
                });
              }
            }
          });
        } else {
          throw new Error('search error');
        }
        return arr;
      })
      .catch((error) => {
        hospitalSelections.value = undefined;
        throw error;
      });
  }
  return hospitalSelections.value;
};

// 获取当前页面归属于哪个菜单 page-from
export function getDictPageFrom() {
  const pathname = window.location?.pathname;
  if (!pathname) return '';
  const from = pathname
    .split('/')
    ?.find((item) => item.includes('dict-'))
    ?.replace('dict-', '');
  return from || '';
}
