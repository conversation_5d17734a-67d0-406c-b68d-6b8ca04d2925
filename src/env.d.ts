/// <reference types="@rsbuild/core/types" />

declare module '*.vue' {
  import { defineComponent } from 'vue';

  // biome-ignore lint/complexity/noBannedTypes: reason
  const Component: ReturnType<typeof defineComponent>;
  export default Component;
}

declare module 'basic_provider/FormDesignRender' {
  import { DefineComponent } from 'vue';
  const component: DefineComponent<unknown, unknown, unknown>;
  export default component;
}
