/** 根据条件查询附件信息 出参 */
export interface AttachItem {
  attachId?: string;
  attachName?: string;
  attachPosition?: string;
  attachCategoryCode?: string;
  attachCategoryCodeDesc?: string;
  sort?: number;
  deleteFlag?: string;
  attachScopeList?: {
    attachScopeId?: string;
    attachScopeCode?: string;
    attachScopeCodeDesc?: string;
  }[];
}

/** 根据条件查询附件信息 入参 */
export interface AttachReqParams {
  pageNumber: number;
  pageSize: number;
  keyWord?: string;
  attachScopeCode?: string;
  deleteFlag?: number;
}

export interface TenantResItem {
  tenantId: string;
  tenantName: string;
}

export interface PrinterResItem {
  printerName: string;
}

export interface ComputerInfoResItem {
  ipAddress: string;
  macAddress: string;
  computerName: string;
}

export interface SaveReceiptXPrinterResParams {
  receiptTemplateList: {
    receiptTemplateRuleId: string;
    templateName: string;
    printerName: string;
  }[];
}

export interface GetReceiptXPrinterResParams {
  receiptTemplateRuleIds: string[];
}

export interface GetReceiptXPrinterResItem {
  receiptTemplateRuleId: string;
  printerName: string;
}

export interface DesignPreviewPrintReceiptResParams {
  formOperationType: string;
  printType: string;
  receiptId: string;
  receiptTemplateRuleId: string;
  receiptTemplateName: string;
  dataSourceContentTypeCode: string;
  dataSourceContent: string;
}

export interface DesignPreviewPrintReceiptResItem {
  pictureFileStreamList: string[];
}

export interface InterfaceReqParams {
  keyWord?: string;
  hospitalId?: string;
  enabledFlag?: number;
  invokeTypeCode?: string;
  interfaceTypeCode?: string;
  interfaceIds?: string[];
}

export interface InterfaceReqItem {
  interfaceId: string;
  interfaceName: string;
  enabledFlag: number;
  interfaceTypeCode: string;
  interfaceTypeDesc: string;
  invokeTypeCode: string;
  invokeTypeDesc: string;
  url?: string;
  dllName?: string;
  namespace?: string;
  className?: string;
  methodName?: string;
  hospitalList?: {
    interfaceXHospitalId?: string;
    hospitalId?: string;
    hospitalName?: string;
    beginDate?: string;
    endDate?: string;
  }[];
}

export interface InterfaceHospitalReqItem {
  interfaceXHospitalId?: string;
  hospitalId?: string;
  hospitalName?: string;
  beginDate?: string;
  endDate?: string;
}

/** [1-10013-1]根据条件查询用户列表 入参 */
export interface UserReqParams {
  personId?: string;
  keyWord?: string;
  enabledFlag?: number;
  hospitalId?: string;
  userNo?: string;
  userJobCodes?: string[];
  bizUnitId?: string;
  userId?: string;
  userTypeCode?: string;
  appReleaseVersionCode?: string;
  pageNumber: number;
  pageSize: number;
}

/** [1-10013-1]根据条件查询用户列表 出参 */
export interface UserReqItem {
  userId: string;
  userNo: string;
  userName: string;
  user2ndName?: string;
  userExtName?: string;
  userNameDisplay: string;
  loginFlag: number;
  adminFlag: number;
  lockedFlag: number;
  userTypeCode: string;
  userTypeDesc: string;
  enabledFlag: number;
  spellNo?: string;
  wbNo?: string;
  paySumTypeCode: string;
  paySumTypeDesc: string;
  paySumBelongUserId: string;
  paySumBelongUserName: string;
  invoiceAgentUserId: string;
  invoiceAgentUserName: string;
  lastLoginAt?: string;
  loginIp?: string;
  loginMac?: string;
  loginTypeCode?: string;
  loginTypeDesc?: string;
  personId?: string;
  titleCode?: string;
  titleDesc?: string;
  personSimpleDesc?: string;
  person2ndSimpleDesc?: string;
  personExtSimpleDesc?: string;
  simpleDescDisplay?: string;
  userJobCode: string;
  userJobDesc: string;
  genderCode?: string;
  genderDesc?: string;
  appReleaseVersionCode?: string | undefined;
  appReleaseVersionDesc?: string;
  perCertificateList: {
    perCertificateId: string;
    certificateTypeCode: string;
    certificateTypeDesc: string;
    certificateNo: string;
  }[];
  perContactList?: {
    perContactId: string;
    contactTypeCode: string;
    contactTypeDesc: string;
    contactNo: string;
  }[];
  loginOrgLocationList?: {
    userXOrgLocationId: string;
    orgLocationId: string;
    orgLocationName: string;
    sort: number;
    orgId: string;
    orgName: string;
    orgTypeCode: string;
    orgTypeDesc: string;
  }[];
  userRoleList?: {
    userRoleId: string;
    hospitalId: string;
    hospitalName: string;
    roleId: string;
    roleName: string;
    tenantId: string;
    tenantName: string;
  }[];
  bizUnitList?: {
    bizUnitId: string;
    bizUnitName: string;
    orgTypeCode: string;
    orgTypeDesc: string;
    hospitalId: string;
    hospitalName: string;
  }[];
}

export interface UpdateUserEnabledFlagReqParams {
  userId: string;
  enabledFlag: number;
}

interface UnitReqParams {
  pageNumber: number;
  pageSize: number;
  keyWord?: string;
}

interface UnitResItem {
  unitId: string;
  unitNo: string;
  unitName: string;
}
